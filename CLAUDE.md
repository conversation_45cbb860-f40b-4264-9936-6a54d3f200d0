# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
```bash
# Create and activate virtual environment
uv python install 3.12
uv venv --python 3.12
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
uv sync
```

### Running the System
```bash
# Start API server (primary method)
make serve
# or directly: uv run server.py

# Direct workflow execution (debugging)
uv run main.py "your query here"

# Interactive CLI
uv run cli.py
```

### Testing & Quality
```bash
# Format code
make format

# Check linting
make lint

# Run tests
pytest tests/
pytest tests/integration/test_workflow.py  # specific test
```

### Docker Deployment
```bash
docker-compose up --build
```

## Multi-Agent Architecture

This is a **hierarchical multi-agent system** built on LangGraph with a specific coordination pattern:

### Agent Flow
```
User Input → Coordinator → Planner → Supervisor → [Specialist Agents] → Reporter
```

### Core Components

**Workflow Agents** (`src/graph/nodes.py`):
- **Coordinator**: Entry point, decides handoff to planner or completion
- **Planner**: Generates structured execution plans (supports deep thinking mode)
- **Supervisor**: Central orchestrator using structured output routing
- **Reporter**: Final report compilation

**Specialist Agents** (`src/agents/agents.py`):
- **Researcher**: Web search, crawling, information gathering
- **Coder**: Python/bash execution, technical problem-solving  
- **Browser**: Web interaction, content extraction

### Agent-Tool Integration (MCP)

Each agent has specific tool mappings (`src/config/mcp_mapping.py`):
```python
AGENT_MCP_MAPPING = {
    "researcher": ["fetch", "exa", "desktop-commander", "pubmed-mcp-server"],
    "coder": ["context7", "desktop-commander"], 
    "browser": ["desktop-commander"]
}
```

**MCP Tool System** (`src/tools/mcp_tools.py`):
- Async MCP tools wrapped in sync interfaces using `nest_asyncio`
- Server configurations in `mcp_servers.json`
- Dynamic tool loading based on agent requirements

## State Management & Persistence

### LangGraph Checkpointing
- **SQLite-based persistence**: `data/checkpoints.db` via `AsyncSqliteSaver`
- **Thread-based conversations**: Each conversation has unique thread_id
- **Time-travel capabilities**: Resume from any checkpoint in conversation history

### Critical State Handling
- **Coordinator message caching**: Prevents premature streaming of handoff messages
- **Unique identifiers**: Always use unique `thread_id` and `run_id` to avoid state conflicts
- **Checkpoint isolation**: Each thread maintains independent conversation state

## API & Streaming Architecture

### FastAPI Server (`src/api/app.py`)
- **Main endpoint**: `POST /awp` for simplified workflow execution
- **AG-UI compatibility**: Event format conversion for frontend integration
- **Server-Sent Events (SSE)**: Real-time streaming with proper event types

### Event Streaming (`src/service/workflow_service.py`)
- **LangGraph v2 events**: Fine-grained control via `astream_events`
- **Event types**: start/end of agents, LLM streaming, tool calls
- **Coordinator caching**: Special handling to prevent partial message streaming

## Configuration

### Environment Variables (.env)
```bash
# Three-tier LLM system
REASONING_MODEL=your_reasoning_model    # Complex decision-making
BASIC_MODEL=your_basic_model           # Simple text tasks  
VL_MODEL=your_vl_model                 # Vision-language tasks

# Tool APIs
TAVILY_API_KEY=your_tavily_key
JINA_API_KEY=your_jina_key             # Optional
```

### Agent Configuration
- **LLM mappings**: `src/config/agents.py` - assigns specific LLMs to agents
- **Prompts**: `src/prompts/` - markdown templates with dynamic context injection
- **Team composition**: `src/config/__init__.py` - defines active agent roster

## Key File Locations

### Core Workflow
- `src/graph/builder.py` - Graph construction and checkpointer setup
- `src/graph/nodes.py` - All agent node implementations
- `src/service/workflow_service.py` - Main workflow execution and streaming

### Agent System
- `src/agents/agents.py` - Specialist agent creation with tool assignment
- `src/agents/llm.py` - Multi-LLM support and model selection
- `src/prompts/template.py` - Dynamic prompt generation system

### Tools & Integration
- `src/tools/mcp_tools.py` - MCP tool loading and sync wrapping
- `src/config/mcp_mapping.py` - Agent-to-tool mappings
- `mcp_servers.json` - MCP server configurations

### API & Services
- `src/api/app.py` - FastAPI server with AG-UI event conversion
- `src/service/persistence_service.py` - Checkpoint management utilities
- `src/service/time_travel_service.py` - Advanced checkpoint manipulation

## Important Implementation Details

### Unique Threading System
Always generate unique thread_id and run_id values to prevent checkpoint conflicts:
```python
thread_id = f"thread_{uuid.uuid4()}"
run_id = f"run_{uuid.uuid4()}"
```

### Coordinator Handoff Pattern
The coordinator uses `"handoff_to_planner"` in response content to trigger planning phase. This is detected in the workflow service and affects message caching behavior.

### Agent Specialization
Each agent has specific LLM types and tools. Check `AGENT_LLM_MAP` for model assignments and `AGENT_MCP_MAPPING` for tool access before modifying agent behavior.

### Development Persistence
For testing, set `SQLITE_DB_PATH=""` to disable persistence, or clear `data/checkpoints.db*` between test runs to reset state.

## System Philosophy 

### Design Goals
- **Flexibility**: Remember we want our system to work very flexibly, with any potential analysis that there could be in the bioinformatics realm, without hardcoded strategies or packages

### Human-in-the-Loop Considerations
- Remember the human in the loop approach needs to be dynamic. If the analysis strategy is clear and aligns with the user's goal we don't need to ask for approval. Otherwise we handle the human feedback semantically without hardcoded answers

### Environment Control
- Remember this environment will be exclusively used for executions of the code produced by our multi agent system. So we can have full control of the preinstalled packages that the system is using

### Generalist AI Bioinformatician
- Remember the agent is a generalist AI bioinformatician, for which we don't write specific tools, techniques or data types because we want to keep it flexible