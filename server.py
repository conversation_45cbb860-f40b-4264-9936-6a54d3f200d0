"""
Server script for running the Omiy API.
"""

import logging
import uvicorn
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# CRITICAL: Apply network fix before any langchain imports
from src.config.network_fix import apply_ipv4_fix

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

# Suppress specific warnings
logging.getLogger("langchain_google_genai._function_utils").setLevel(logging.ERROR)

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logger.info("Starting Omiy API server")
    uvicorn.run(
        "src.api.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
