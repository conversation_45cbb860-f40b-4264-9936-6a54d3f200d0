#!/usr/bin/env python3
"""
Test script to verify the complete workflow functionality.
"""

import os
import logging
from src.workflow import run_agent_workflow

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_coder_workflow():
    """Test the coder workflow by asking it to create a simple Python script."""
    print("Testing coder workflow...")

    try:
        # Test task: Create a simple Python script
        task = "Create a simple Python script that prints 'Hello World' and save it to hello_world.py"
        print(f"Task: {task}")

        # Run the workflow
        print("\nRunning workflow...")
        result = run_agent_workflow(task)

        print(f"Workflow result: {result}")

        # Check if the file was created
        expected_files = [
            "hello_world.py",
            "./hello_world.py",
            "./workspace/hello_world.py",
        ]
        file_found = False

        for file_path in expected_files:
            if os.path.exists(file_path):
                print(f"✓ Found created file: {file_path}")

                # Check file content
                with open(file_path, "r") as f:
                    content = f.read()
                    print(f"File content: {content}")

                if "Hello World" in content:
                    print("✓ File contains expected content")
                    file_found = True
                    break
                else:
                    print("✗ File does not contain expected content")

        if not file_found:
            print("✗ No hello_world.py file found in expected locations")
            return False

        print("✓ Coder workflow test completed successfully")
        return True

    except Exception as e:
        print(f"✗ Error in coder workflow test: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_coder_workflow()
    if success:
        print("\n✓ Workflow test passed!")
    else:
        print("\n✗ Workflow test failed!")
        exit(1)
