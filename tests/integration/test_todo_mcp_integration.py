import pytest
import json
import re
from unittest.mock import patch
from src.tools.mcp_tools import MCPToolManager


@pytest.mark.asyncio
async def test_todo_mcp_end_to_end():
    """
    Test the full lifecycle of a todo item using the todo-md-mcp server.
    This test requires the MCP server to be running.
    """
    # 1. Load the host config directly for testing on the host system
    with open("mcp_servers.json", "r") as f:
        host_config = json.load(f)

    # 2. Patch the _load_config method to return the host config
    with patch.object(MCPToolManager, "_load_config", return_value=host_config):
        # 3. Initialize the MCPToolManager and get the tools
        tool_manager = MCPToolManager()
        tool_manager.clear_cache()  # Ensure a clean state for the test
        all_tools = tool_manager.get_all_tools()

        # 4. Find the todo tools
        add_todo = next((t for t in all_tools if t.name == "add_todo"), None)
        list_todos = next((t for t in all_tools if t.name == "list_todos"), None)
        update_todo = next((t for t in all_tools if t.name == "update_todo"), None)
        delete_todo = next((t for t in all_tools if t.name == "delete_todo"), None)

        assert all(
            [add_todo, list_todos, update_todo, delete_todo]
        ), "Not all todo tools were found."

        # 5. Start with a clean slate
        initial_todos_raw = list_todos.invoke({})
        initial_todos = (
            json.loads(initial_todos_raw)
            if isinstance(initial_todos_raw, str)
            else initial_todos_raw
        )
        if initial_todos and "todos" in initial_todos:
            for todo in initial_todos.get("todos", []):
                delete_todo.invoke({"id": todo["id"]})

        # 6. Add a new todo
        new_todo_result_raw = add_todo.invoke(
            {"text": "Test todo from integration test"}
        )
        # Extract JSON from the raw string
        json_match = re.search(r'{\s*"id":\s*".*"\s*}', new_todo_result_raw, re.DOTALL)
        assert json_match, "Could not find JSON in the response from add_todo"
        new_todo_result = json.loads(json_match.group(0))
        assert "id" in new_todo_result, "add_todo did not return an id."
        new_todo_id = new_todo_result["id"]

        # 7. List todos to verify the new item
        todos_after_add_raw = list_todos.invoke({})
        todos_after_add = (
            json.loads(todos_after_add_raw)
            if isinstance(todos_after_add_raw, str)
            else todos_after_add_raw
        )
        assert any(
            t["id"] == new_todo_id for t in todos_after_add.get("todos", [])
        ), "Newly added todo not found."

        # 8. Update the todo
        update_todo.invoke(
            {"id": new_todo_id, "text": "Updated test todo", "completed": True}
        )

        # 9. List todos to verify the update
        todos_after_update_raw = list_todos.invoke({})
        todos_after_update = (
            json.loads(todos_after_update_raw)
            if isinstance(todos_after_update_raw, str)
            else todos_after_update_raw
        )
        updated_todo = next(
            (t for t in todos_after_update.get("todos", []) if t["id"] == new_todo_id),
            None,
        )
        assert updated_todo is not None, "Updated todo not found."
        assert updated_todo["text"] == "Updated test todo", "Todo text was not updated."
        assert updated_todo["completed"] is True, "Todo was not marked as completed."

        # 10. Delete the todo
        delete_todo.invoke({"id": new_todo_id})

        # 11. List todos to verify deletion
        todos_after_delete_raw = list_todos.invoke({})
        todos_after_delete = (
            json.loads(todos_after_delete_raw)
            if isinstance(todos_after_delete_raw, str)
            else todos_after_delete_raw
        )
        assert not any(
            t["id"] == new_todo_id for t in todos_after_delete.get("todos", [])
        ), "Deleted todo was found."
