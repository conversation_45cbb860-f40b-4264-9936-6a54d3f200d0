"""
Integration tests for session-aware document updates (Story 4.2).

These tests verify the complete session-aware workflow from reporter_node
through DocumentService to ensure proper session handling, content preservation,
and document evolution across multiple sessions.
"""

import pytest
import os
import tempfile
import shutil
import uuid
from unittest.mock import Mock, patch
from pathlib import Path

# Import the components we're testing
from src.service.document_service import DocumentService
from src.graph.nodes import (
    reporter_node,
    _generate_session_content,
    _generate_session_conclusions,
)
from src.graph.types import StructuredPlan, PlanStep


class TestSessionDocumentUpdatesIntegration:
    """Integration tests for session-aware document updates."""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        try:
            shutil.rmtree(temp_dir)
        except FileNotFoundError:
            pass

    @pytest.fixture
    def mock_state(self):
        """Create mock state for testing."""
        # Create a mock structured plan
        plan = Mock(spec=StructuredPlan)
        plan.title = "Test Analysis Plan"
        plan.steps = [
            Mock(
                step_index=1,
                title="Data Collection",
                description="Collect research data",
                status="completed",
                results={"summary": "Data collected successfully"},
            ),
            Mock(
                step_index=2,
                title="Analysis",
                description="Perform analysis",
                status="completed",
                results={"summary": "Analysis completed with findings"},
            ),
            Mock(
                step_index=3,
                title="Report Generation",
                description="Generate report",
                status="in_progress",
                results={},
            ),
        ]
        plan.get_current_step.return_value = plan.steps[2]  # Reporter step
        plan.advance_to_next_step.return_value = None

        return {
            "structured_plan": plan,
            "goal": (
                "Perform comprehensive bioinformatics analysis of protein interactions"
            ),
            "session_id": str(uuid.uuid4()),
            "messages": [],
        }

    @pytest.fixture
    def doc_service(self, temp_workspace):
        """Create DocumentService instance."""
        return DocumentService(temp_workspace)

    def test_multi_session_document_evolution(self, temp_workspace, doc_service):
        """Test complete multi-session document evolution workflow."""
        # Session 1: Create initial document
        session_1_id = "session_001"
        context = {
            "analysis_type": "ProteinAnalysis",
            "subject": "InteractionStudy",
            "domain": "bioinformatics",
        }
        session_1_content = """### New Findings & Results
Initial protein interaction analysis completed.
Found 15 significant protein-protein interactions.

### Methodology Updates
Established baseline methodology using STRING database.

### Technical Implementation
Implemented automated data retrieval from UniProt.

### Session Conclusions
Successfully initiated protein interaction analysis with preliminary results."""

        # Create first session document
        doc_path = os.path.join(
            temp_workspace, "Analysis_ProteinAnalysis_InteractionStudy.md"
        )
        result_path = doc_service.create_or_update_document(
            doc_path, session_1_content, context, session_1_id
        )

        # Verify first session document creation
        assert os.path.exists(result_path)
        with open(result_path, "r", encoding="utf-8") as f:
            content_1 = f.read()

        assert "## Session Overview" in content_1
        assert f"**Current Session:** {session_1_id}" in content_1
        assert "**Session Count:** 1" in content_1
        assert "## Session 1 -" in content_1
        assert "Initial protein interaction analysis completed" in content_1

        # Session 2: Append new session content
        session_2_id = "session_002"
        session_2_content = """### New Findings & Results
Extended analysis revealed 8 additional high-confidence interactions.
Identified key hub proteins with multiple connections.

### Methodology Updates
Integrated AlphaFold structural data for interaction validation.

### Technical Implementation
Added automated confidence scoring algorithm.

### Session Conclusions
Significant progress made with enhanced methodology and additional findings."""

        # Append second session
        result_path_2 = doc_service.create_or_update_document(
            doc_path, session_2_content, context, session_2_id
        )

        # Verify session appending
        assert result_path_2 == result_path
        with open(result_path, "r", encoding="utf-8") as f:
            content_2 = f.read()

        # Verify both sessions exist
        assert f"**Current Session:** {session_2_id}" in content_2
        assert "**Session Count:** 2" in content_2
        assert "## Session 1 -" in content_2  # Original session preserved
        assert "## Session 2 -" in content_2  # New session added
        assert (
            "Initial protein interaction analysis completed" in content_2
        )  # Original content preserved
        assert (
            "Extended analysis revealed 8 additional" in content_2
        )  # New content added
        assert content_2.count("---") >= 2  # Visual separators present

        # Session 3: Add final session
        session_3_id = "session_003"
        session_3_content = """### New Findings & Results
Completed comprehensive validation of all 23 interactions.
Generated final interaction network visualization.

### Methodology Updates
Finalized statistical analysis with p-value corrections.

### Technical Implementation
Exported results in multiple formats (CSV, JSON, GraphML).

### Session Conclusions
Analysis successfully completed with high-quality, validated results ready for publication."""

        # Append third session
        result_path_3 = doc_service.create_or_update_document(
            doc_path, session_3_content, context, session_3_id
        )

        # Verify final multi-session document
        with open(result_path, "r", encoding="utf-8") as f:
            final_content = f.read()

        assert f"**Current Session:** {session_3_id}" in final_content
        assert "**Session Count:** 3" in final_content
        assert "## Session 1 -" in final_content
        assert "## Session 2 -" in final_content
        assert "## Session 3 -" in final_content
        assert "Completed comprehensive validation" in final_content

        # Verify chronological ordering and content preservation
        session_1_pos = final_content.find("## Session 1 -")
        session_2_pos = final_content.find("## Session 2 -")
        session_3_pos = final_content.find("## Session 3 -")
        assert session_1_pos < session_2_pos < session_3_pos

    def test_session_timestamp_ordering(self, temp_workspace, doc_service):
        """Test that session timestamps are properly ordered and formatted."""
        import time

        doc_path = os.path.join(temp_workspace, "timestamp_test.md")
        context = {"analysis_type": "Test", "subject": "Timestamp"}

        # Create sessions with small delays to ensure different timestamps
        session_1_id = "ts_session_1"
        doc_service.create_or_update_document(
            doc_path, "Session 1 content", context, session_1_id
        )

        time.sleep(1)  # Ensure different timestamps

        session_2_id = "ts_session_2"
        doc_service.create_or_update_document(
            doc_path, "Session 2 content", context, session_2_id
        )

        # Verify timestamp format and ordering
        with open(doc_path, "r", encoding="utf-8") as f:
            content = f.read()

        import re

        timestamp_pattern = r"## Session \d+ - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})"
        timestamps = re.findall(timestamp_pattern, content)

        assert len(timestamps) == 2
        # Verify timestamp format (YYYY-MM-DD HH:MM:SS)
        for timestamp in timestamps:
            assert re.match(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}", timestamp)

        # Verify chronological ordering (session 1 timestamp < session 2 timestamp)
        from datetime import datetime

        ts1 = datetime.strptime(timestamps[0], "%Y-%m-%d %H:%M:%S")
        ts2 = datetime.strptime(timestamps[1], "%Y-%m-%d %H:%M:%S")
        assert ts1 < ts2

    def test_content_preservation_across_sessions(self, temp_workspace, doc_service):
        """Test that all session content is preserved across multiple updates."""
        doc_path = os.path.join(temp_workspace, "preservation_test.md")
        context = {"analysis_type": "Preservation", "subject": "Test"}

        # Define unique content for each session
        session_contents = [
            "Unique findings from session 1: discovered protein X interaction",
            "Unique findings from session 2: validated interaction using method Y",
            "Unique findings from session 3: structural analysis confirms binding site Z",
        ]

        # Add sessions sequentially
        for i, content in enumerate(session_contents, 1):
            session_id = f"preserve_session_{i}"
            doc_service.create_or_update_document(
                doc_path, content, context, session_id
            )

            # After each session, verify all previous content is still present
            with open(doc_path, "r", encoding="utf-8") as f:
                current_content = f.read()

            for j in range(i):  # Check all sessions up to current
                assert session_contents[j] in current_content
                assert f"## Session {j + 1} -" in current_content

    def test_session_visual_separation(self, temp_workspace, doc_service):
        """Test that sessions are visually separated with proper demarcation."""
        doc_path = os.path.join(temp_workspace, "visual_separation_test.md")
        context = {"analysis_type": "Visual", "subject": "Separation"}

        # Create multiple sessions
        for i in range(3):
            session_id = f"visual_session_{i + 1}"
            content = f"Content for session {i + 1} with specific data."
            doc_service.create_or_update_document(
                doc_path, content, context, session_id
            )

        # Verify visual separation elements
        with open(doc_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Check for session separators
        separator_count = content.count("---")
        assert separator_count >= 2  # At least 2 separators for 3 sessions

        # Check for session headers with timestamps
        session_header_count = len(
            [
                line
                for line in content.split("\n")
                if line.startswith("## Session ") and " - " in line
            ]
        )
        assert session_header_count == 3

        # Check for session IDs
        for i in range(3):
            assert f"**Session ID:** visual_session_{i + 1}" in content

    @patch("src.config.env.WORKSPACE_DIR")
    def test_reporter_node_session_integration(
        self, mock_workspace_dir, temp_workspace, mock_state
    ):
        """Test integration between reporter_node and session-aware DocumentService."""
        mock_workspace_dir.return_value = temp_workspace

        # Mock the helper functions that extract content
        with (
            patch("src.graph.nodes._extract_user_context") as mock_user_context,
            patch("src.graph.nodes._extract_content_from_workflow") as mock_content,
            patch("src.graph.nodes._update_todo_status") as mock_todo,
        ):

            mock_user_context.return_value = {
                "objective": "Test protein analysis",
                "research_questions": "What are the key interactions?",
            }

            mock_content.return_value = {
                "key_findings": "Significant protein interactions identified",
                "methodology": "Used computational analysis methods",
                "technical_details": "Implemented automated pipeline",
                "results_summary": "Found 20 high-confidence interactions",
            }

            # First execution - creates new document
            result_1 = reporter_node(mock_state)

            # Verify successful execution
            assert result_1["structured_plan"] == mock_state["structured_plan"]
            assert len(result_1["messages"]) == 1
            assert "session-aware updates" in result_1["messages"][0].content

            # Verify document was created
            doc_results = mock_state["structured_plan"].get_current_step().results
            assert "document_path" in doc_results
            assert "session_id" in doc_results
            assert "operation_type" in doc_results
            assert doc_results["operation_type"] == "created_with_session"

            document_path = doc_results["document_path"]
            assert os.path.exists(document_path)

            # Verify first session content
            with open(document_path, "r", encoding="utf-8") as f:
                content_1 = f.read()

            assert "## Session 1 -" in content_1
            assert "Significant protein interactions identified" in content_1

            # Second execution - appends to existing document
            mock_state["session_id"] = str(uuid.uuid4())  # New session ID
            mock_content.return_value = {
                "key_findings": "Additional validation results obtained",
                "methodology": "Enhanced with structural data",
                "technical_details": "Added confidence scoring",
                "results_summary": "Validated 18 of 20 interactions",
            }

            result_2 = reporter_node(mock_state)

            # Verify session appending
            doc_results_2 = mock_state["structured_plan"].get_current_step().results
            assert doc_results_2["operation_type"] == "session_appended"
            assert doc_results_2["backup_created"] is True

            # Verify multi-session content
            with open(document_path, "r", encoding="utf-8") as f:
                content_2 = f.read()

            assert "## Session 1 -" in content_2  # Original session preserved
            assert "## Session 2 -" in content_2  # New session added
            assert (
                "Significant protein interactions identified" in content_2
            )  # Original content
            assert "Additional validation results obtained" in content_2  # New content

    def test_session_content_generation_helpers(self, mock_state):
        """Test the session content generation helper functions."""
        content_data = {
            "key_findings": "Test findings for session",
            "methodology": "Updated methodology approach",
            "technical_details": "Technical implementation details",
            "data_analysis": "Comprehensive data analysis results",
            "results_summary": "Summary of key results",
            "next_steps": "Planned next steps for research",
        }

        context = {"analysis_type": "TestAnalysis", "subject": "TestSubject"}

        user_context = {"objective": "Test session content generation"}

        # Test session content generation
        session_content = _generate_session_content(
            content_data, context, mock_state, user_context
        )

        # Verify all expected sections are present
        assert "### New Findings & Results" in session_content
        assert "### Methodology Updates" in session_content
        assert "### Technical Implementation" in session_content
        assert "### Data Analysis" in session_content
        assert "### Results Summary" in session_content
        assert "### Session Conclusions" in session_content
        assert "### Next Steps" in session_content

        # Verify content is included
        assert "Test findings for session" in session_content
        assert "Updated methodology approach" in session_content
        assert "Technical implementation details" in session_content

        # Test session conclusions generation
        conclusions = _generate_session_conclusions(content_data, context, mock_state)
        assert "TestAnalysis" in conclusions
        assert "completed" in conclusions.lower()

    def test_error_handling_and_recovery(self, temp_workspace, doc_service):
        """Test error handling and recovery in session operations."""
        # Test appending to non-existent document
        result = doc_service.append_session_content(
            "/nonexistent/path.md", "content", "session_id"
        )
        assert result is False

        # Test creating document in non-existent directory
        doc_path = os.path.join(temp_workspace, "subdir", "test.md")
        context = {"analysis_type": "Test", "subject": "Recovery"}
        session_id = "recovery_session"

        # Should create directory and succeed
        result_path = doc_service.create_or_update_document(
            doc_path, "test content", context, session_id
        )
        assert result_path == doc_path
        assert os.path.exists(doc_path)

        # Test session tracking with malformed document
        malformed_doc = os.path.join(temp_workspace, "malformed.md")
        with open(malformed_doc, "w", encoding="utf-8") as f:
            f.write("Malformed document without proper structure")

        # Should not crash but handle gracefully
        doc_service.track_session_id(malformed_doc, "test_session")
        # File should still exist
        assert os.path.exists(malformed_doc)

    def test_concurrent_session_safety(self, temp_workspace, doc_service):
        """Test atomic operations for concurrent session access safety."""
        doc_path = os.path.join(temp_workspace, "concurrent_test.md")
        context = {"analysis_type": "Concurrent", "subject": "Safety"}

        # Create initial document
        doc_service.create_or_update_document(
            doc_path, "Initial content", context, "session_1"
        )

        # Simulate concurrent append operations
        # The atomic write mechanism should prevent corruption
        import threading
        import time

        results = []

        def append_session(session_num):
            try:
                content = f"Concurrent session {session_num} content with unique data."
                session_id = f"concurrent_session_{session_num}"
                result = doc_service.append_session_content(
                    doc_path, content, session_id
                )
                results.append((session_num, result))
            except Exception as e:
                results.append((session_num, str(e)))

        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=append_session, args=(i + 2,))
            threads.append(thread)
            thread.start()
            time.sleep(0.1)  # Small delay to increase chance of concurrency

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify results
        success_count = sum(1 for _, result in results if result is True)
        assert success_count >= 1  # At least one should succeed

        # Verify document integrity
        with open(doc_path, "r", encoding="utf-8") as f:
            final_content = f.read()

        # Should have initial content plus some concurrent sessions
        assert "Initial content" in final_content
        session_count = doc_service.get_session_count(doc_path)
        assert session_count >= 2  # Initial + at least one concurrent session
