import pytest
import json
import os
import tempfile
from unittest.mock import patch, MagicMock
from src.graph.builder import build_graph
from src.graph.types import State as AgentState, StructuredPlan, PlanStep
from src.graph.nodes import (
    planner_node,
    research_node,
    code_node,
    browser_node,
    reporter_node,
)


@pytest.mark.asyncio
async def test_complete_todo_workflow():
    """
    Test the complete todo workflow integration:
    1. Plan<PERSON> creates todos from structured plan
    2. Specialist agents update todo progress
    3. Todo.md file is created and maintained
    """
    # Create a temporary todo file for testing
    with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
        temp_todo_file = f.name

    try:
        # Mock data for MCP tools
        mock_add_todo_results = []
        mock_update_todo_calls = []

        def mock_add_todo(data):
            todo_id = f"todo_{len(mock_add_todo_results) + 1}"
            mock_add_todo_results.append(
                {
                    "id": todo_id,
                    "text": data["text"],
                    "completed": False,
                    "metadata": data.get("metadata", {}),
                }
            )
            return json.dumps({"id": todo_id, "text": data["text"], "completed": False})

        def mock_update_todo(data):
            todo_id = data["id"]
            completed = data["completed"]
            mock_update_todo_calls.append({"id": todo_id, "completed": completed})
            return json.dumps({"id": todo_id, "completed": completed})

        # Mock the MCP tool manager and tools
        with patch("src.tools.mcp_tools.MCPToolManager") as mock_tool_manager:
            # Create mock tools
            mock_add_todo_tool = MagicMock()
            mock_add_todo_tool.name = "add_todo"
            mock_add_todo_tool.invoke.side_effect = mock_add_todo

            mock_update_todo_tool = MagicMock()
            mock_update_todo_tool.name = "update_todo"
            mock_update_todo_tool.invoke.side_effect = mock_update_todo

            mock_tool_manager.return_value.get_all_tools.return_value = [
                mock_add_todo_tool,
                mock_update_todo_tool,
            ]

            # Test 1: Planner creates todos from structured plan
            initial_state = AgentState(
                structured_plan=None,
                messages=[("user", "Create a plan for data analysis")],
            )

            # Mock the LLM response for planner
            mock_plan_response = {
                "title": "Biological Data Analysis Plan",
                "thought": "This plan will analyze biological data step by step",
                "steps": [
                    {
                        "title": "Research data sources",
                        "description": "Find relevant biological databases",
                        "agent_name": "researcher",
                    },
                    {
                        "title": "Implement analysis script",
                        "description": "Code the data analysis pipeline",
                        "agent_name": "coder",
                    },
                    {
                        "title": "Generate report",
                        "description": "Create final analysis report",
                        "agent_name": "reporter",
                    },
                ],
            }

            with (
                patch("src.graph.nodes.get_llm_by_type") as mock_llm,
                patch("src.graph.nodes.apply_prompt_template") as mock_prompt,
            ):

                mock_llm.return_value.invoke.return_value.content = (
                    f"```json\n{json.dumps(mock_plan_response)}\n```"
                )
                mock_prompt.return_value = [
                    {"role": "user", "content": "Create a plan"}
                ]

                # Execute planner node
                result = planner_node(initial_state)
                plan = result["structured_plan"]

                # Verify structured plan was created
                assert plan is not None
                assert plan.title == "Biological Data Analysis Plan"
                assert len(plan.steps) == 3

                # Verify todos were created from the plan
                assert len(mock_add_todo_results) == 3
                assert (
                    mock_add_todo_results[0]["text"]
                    == "Research data sources - Find relevant biological databases"
                )
                assert (
                    mock_add_todo_results[1]["text"]
                    == "Implement analysis script - Code the data analysis pipeline"
                )
                assert (
                    mock_add_todo_results[2]["text"]
                    == "Generate report - Create final analysis report"
                )

                # Verify todo IDs were stored in plan steps
                for i, step in enumerate(plan.steps):
                    assert step.todo_id == f"todo_{i + 1}"

                print("✅ Test 1 passed: Planner creates todos from structured plan")

            # Test 2: Specialist agents update todo progress
            test_state = AgentState(structured_plan=plan, messages=[])

            # Mock agent invocations
            def mock_agent_invoke(state):
                from langchain_core.messages import HumanMessage

                return {
                    "messages": [HumanMessage(content="Task completed successfully")]
                }

            with (
                patch(
                    "src.agents.research_agent.invoke", side_effect=mock_agent_invoke
                ),
                patch("src.agents.coder_agent.invoke", side_effect=mock_agent_invoke),
                patch("src.agents.browser_agent.invoke", side_effect=mock_agent_invoke),
            ):

                # Reset update calls
                mock_update_todo_calls.clear()

                # Execute research node
                plan.current_step_index = 0
                result = research_node(test_state)

                # Verify todo was marked as in progress, then completed
                assert len(mock_update_todo_calls) >= 2
                assert mock_update_todo_calls[0]["id"] == "todo_1"
                assert mock_update_todo_calls[0]["completed"] == False  # In progress
                assert mock_update_todo_calls[1]["id"] == "todo_1"
                assert mock_update_todo_calls[1]["completed"] == True  # Completed

                print("✅ Test 2 passed: Specialist agents update todo progress")

            # Test 3: Reporter node also updates todos
            with (
                patch("src.config.env.WORKSPACE_DIR", temp_todo_file),
                patch("os.path.join", return_value=temp_todo_file),
                patch("os.makedirs"),
            ):

                # Reset update calls
                mock_update_todo_calls.clear()

                # Add reporter step to plan
                reporter_step = PlanStep(
                    step_index=3,
                    title="Generate Final Report",
                    description="Create final analysis report",
                    agent_name="reporter",
                    todo_id="todo_4",
                )
                plan.steps.append(reporter_step)
                plan.current_step_index = 3

                # Execute reporter node
                result = reporter_node(test_state)

                # Verify todo was updated
                assert len(mock_update_todo_calls) >= 2
                assert any(
                    call["id"] == "todo_4" and call["completed"] == False
                    for call in mock_update_todo_calls
                )
                assert any(
                    call["id"] == "todo_4" and call["completed"] == True
                    for call in mock_update_todo_calls
                )

                print("✅ Test 3 passed: Reporter node updates todos")

            print(
                "✅ All tests passed: Complete todo workflow integration working correctly"
            )

    finally:
        # Clean up temporary file
        if os.path.exists(temp_todo_file):
            os.unlink(temp_todo_file)


@pytest.mark.asyncio
async def test_todo_error_handling():
    """
    Test that todo operations handle errors gracefully without breaking the workflow.
    """
    # Test when todo tools are not available
    with patch("src.tools.mcp_tools.MCPToolManager") as mock_tool_manager:
        mock_tool_manager.return_value.get_all_tools.return_value = (
            []
        )  # No tools available

        # Create a simple plan
        plan = StructuredPlan(
            title="Test Plan",
            thought="Test plan for error handling",
            steps=[
                PlanStep(
                    step_index=0,
                    title="Test Step",
                    description="Test step for error handling",
                    agent_name="researcher",
                )
            ],
        )

        initial_state = AgentState(
            structured_plan=plan, messages=[("user", "Test error handling")]
        )

        # Mock the LLM response for planner
        mock_plan_response = {
            "title": "Test Plan",
            "thought": "Test plan for error handling",
            "steps": [
                {
                    "title": "Test Step",
                    "description": "Test step for error handling",
                    "agent_name": "researcher",
                }
            ],
        }

        with (
            patch("src.graph.nodes.get_llm_by_type") as mock_llm,
            patch("src.graph.nodes.apply_prompt_template") as mock_prompt,
        ):

            mock_llm.return_value.invoke.return_value.content = (
                f"```json\n{json.dumps(mock_plan_response)}\n```"
            )
            mock_prompt.return_value = [{"role": "user", "content": "Create a plan"}]

            # Execute planner node - should not raise an exception
            result = planner_node(initial_state)
            plan = result["structured_plan"]

            # Verify plan was still created despite todo tool unavailability
            assert plan is not None
            assert plan.title == "Test Plan"

            print(
                "✅ Error handling test passed: Workflow continues when todo tools are unavailable"
            )


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_complete_todo_workflow())
    asyncio.run(test_todo_error_handling())
