#!/usr/bin/env python3
"""
Test for creating and running Python scripts in the workspace directory.
"""

import os
import sys
import json
import tempfile
import subprocess
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import workspace directory configuration
try:
    from src.config.env import WORKSPACE_DIR
except ImportError:
    # Fallback to manual path construction
    WORKSPACE_DIR = os.path.join(project_root, "workspace")


def test_create_and_run_python_script():
    """
    Test creating and executing Python scripts in the workspace.
    """
    print(f"Testing Python script execution in workspace: {WORKSPACE_DIR}")

    # Ensure workspace directory exists
    os.makedirs(WORKSPACE_DIR, exist_ok=True)

    # Test 1: Simple Hello World script
    test_simple_script()

    # Test 2: Data processing script
    test_data_processing_script()

    # Test 3: File operations script
    test_file_operations_script()

    print("\n✅ All Python script tests completed successfully!")


def test_simple_script():
    """
    Test creating and running a simple Python script.
    """
    print("\n--- Test 1: Simple Hello World Script ---")

    script_content = """#!/usr/bin/env python3
import os
print("Hello from workspace Python script!")
print(f"Current working directory: {os.getcwd()}")
print(f"Script location: {__file__}")
"""

    script_path = os.path.join(WORKSPACE_DIR, "hello_world.py")

    # Create the script
    with open(script_path, "w") as f:
        f.write(script_content)

    print(f"Created script: {script_path}")

    # Run the script
    try:
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            cwd=WORKSPACE_DIR,
        )

        print(f"Exit code: {result.returncode}")
        print(f"Output: {result.stdout.strip()}")

        if result.stderr:
            print(f"Errors: {result.stderr.strip()}")

        assert result.returncode == 0, "Script execution failed"
        assert "Hello from workspace" in result.stdout, "Expected output not found"

    except Exception as e:
        print(f"Error running script: {e}")
        raise


def test_data_processing_script():
    """
    Test creating and running a data processing script.
    """
    print("\n--- Test 2: Data Processing Script ---")

    script_content = """#!/usr/bin/env python3
import os
import json

# Create some sample data
data = {
    "numbers": [1, 2, 3, 4, 5],
    "sum": sum([1, 2, 3, 4, 5]),
    "average": sum([1, 2, 3, 4, 5]) / 5
}

# Write data to JSON file
with open("sample_data.json", "w") as f:
    json.dump(data, f, indent=2)

print(f"Created sample_data.json with sum: {data['sum']} and average: {data['average']}")

# Read and verify the data
with open("sample_data.json", "r") as f:
    loaded_data = json.load(f)
    
print(f"Verified data: {loaded_data}")
print("Data processing completed successfully!")
"""

    script_path = os.path.join(WORKSPACE_DIR, "data_processor.py")

    # Create the script
    with open(script_path, "w") as f:
        f.write(script_content)

    print(f"Created script: {script_path}")

    # Run the script
    try:
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            cwd=WORKSPACE_DIR,
        )

        print(f"Exit code: {result.returncode}")
        print(f"Output: {result.stdout.strip()}")

        if result.stderr:
            print(f"Errors: {result.stderr.strip()}")

        assert result.returncode == 0, "Data processing script failed"
        assert "sum: 15" in result.stdout, "Expected sum calculation not found"
        assert "average: 3.0" in result.stdout, "Expected average calculation not found"

        # Verify the JSON file was created
        json_path = os.path.join(WORKSPACE_DIR, "sample_data.json")
        assert os.path.exists(json_path), "JSON file was not created"

        with open(json_path, "r") as f:
            data = json.load(f)
            assert data["sum"] == 15, "Incorrect sum in JSON file"
            assert data["average"] == 3.0, "Incorrect average in JSON file"

        print("✅ JSON file created and verified successfully")

    except Exception as e:
        print(f"Error running data processing script: {e}")
        raise


def test_file_operations_script():
    """
    Test creating and running a script that performs file operations.
    """
    print("\n--- Test 3: File Operations Script ---")

    script_content = """#!/usr/bin/env python3
import os

# Create a test directory
test_dir = "test_files"
os.makedirs(test_dir, exist_ok=True)
print(f"Created directory: {test_dir}")

# Create multiple test files
for i in range(3):
    filename = f"{test_dir}/test_file_{i}.txt"
    with open(filename, "w") as f:
        f.write(f"This is test file number {i}\\n")
        f.write(f"Created in workspace directory\\n")
    print(f"Created file: {filename}")

# List all files in the test directory
files = os.listdir(test_dir)
print(f"Files in {test_dir}: {sorted(files)}")

# Read and display content of first file
with open(f"{test_dir}/test_file_0.txt", "r") as f:
    content = f.read()
    print(f"Content of test_file_0.txt: {content.strip()}")

print("File operations completed successfully!")
"""

    script_path = os.path.join(WORKSPACE_DIR, "file_operations.py")

    # Create the script
    with open(script_path, "w") as f:
        f.write(script_content)

    print(f"Created script: {script_path}")

    # Run the script
    try:
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            cwd=WORKSPACE_DIR,
        )

        print(f"Exit code: {result.returncode}")
        print(f"Output: {result.stdout.strip()}")

        if result.stderr:
            print(f"Errors: {result.stderr.strip()}")

        assert result.returncode == 0, "File operations script failed"
        assert (
            "Created directory: test_files" in result.stdout
        ), "Directory creation not found"
        assert "test_file_0.txt" in result.stdout, "File creation not found"

        # Verify the test directory and files were created
        test_dir_path = os.path.join(WORKSPACE_DIR, "test_files")
        assert os.path.exists(test_dir_path), "Test directory was not created"

        files = os.listdir(test_dir_path)
        assert len(files) == 3, f"Expected 3 files, found {len(files)}"
        assert "test_file_0.txt" in files, "test_file_0.txt not found"

        print("✅ File operations verified successfully")

    except Exception as e:
        print(f"Error running file operations script: {e}")
        raise


def cleanup_test_files():
    """
    Clean up test files created during testing.
    """
    print("\n--- Cleaning up test files ---")

    test_files = [
        "hello_world.py",
        "data_processor.py",
        "file_operations.py",
        "sample_data.json",
    ]

    for filename in test_files:
        file_path = os.path.join(WORKSPACE_DIR, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Removed: {filename}")

    # Remove test directory
    test_dir_path = os.path.join(WORKSPACE_DIR, "test_files")
    if os.path.exists(test_dir_path):
        import shutil

        shutil.rmtree(test_dir_path)
        print("Removed: test_files directory")

    print("Cleanup completed")


if __name__ == "__main__":
    try:
        test_create_and_run_python_script()
    finally:
        cleanup_test_files()
