import os
import shutil
import pytest


def test_python_packages_importable():
    """
    Tests that key Python packages from the E1 environment can be imported.
    """
    try:
        import Bio
        import scanpy
        import rdkit
        import pandas
        import numpy
    except ImportError as e:
        pytest.fail(f"Failed to import a key Python package: {e}")


def test_data_lake_files_exist():
    """
    Checks for the existence of a few sample files from the Biomni data lake.
    """
    data_lake_path = "data"
    sample_files = ["affinity_capture-ms.parquet", "gwas_catalog.pkl", "hp.obo"]

    if not os.path.exists(data_lake_path):
        pytest.fail(f"Data lake directory not found at: {data_lake_path}")

    for file in sample_files:
        file_path = os.path.join(data_lake_path, file)
        assert os.path.exists(file_path), f"Data lake file not found: {file_path}"


def test_cli_tools_are_in_path():
    """
    Checks if the Biomni CLI tools are available in the system's PATH.
    """
    cli_tools = ["plink2", "iqtree2", "gcta64", "bwa", "FastTree", "muscle"]

    for tool in cli_tools:
        assert shutil.which(tool), f"CLI tool not found in PATH: {tool}"
