import pytest
import asyncio
from uuid import uuid4
from src.workflow import run_agent_workflow, get_workflow_graph
from src.service.persistence_service import (
    get_conversation_history,
    get_conversation_state,
    get_persistent_graph,
)
import os


@pytest.fixture(scope="module")
def event_loop():
    """Overrides pytest-async<PERSON>'s event_loop fixture to be module-scoped."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.mark.asyncio
async def test_checkpointing_and_resume():
    # Use an in-memory SQLite database for testing
    os.environ["SQLITE_DB_PATH"] = ":memory:"

    thread_id = f"test_thread_{uuid4()}"
    user_input = "Write a short story about a robot."

    # 1. Run the workflow to create a checkpoint
    graph = await get_persistent_graph()
    config = {"configurable": {"thread_id": thread_id}}

    # Run one step to create a checkpoint
    stream = graph.astream(
        {"goal": user_input, "messages": [("user", user_input)]}, config
    )
    try:
        await stream.__anext__()
    finally:
        await stream.aclose()

    # 2. Get the history and select a checkpoint to resume from
    history = await get_conversation_history(thread_id)
    assert len(history) > 0

    # 3. Resume (by getting state)
    state_after_resume = await get_conversation_state(thread_id)
    assert state_after_resume is not None

    # The plan should be present in the resumed state
    assert "structured_plan" in state_after_resume
    assert state_after_resume["structured_plan"] is not None

    # Clean up environment variable
    del os.environ["SQLITE_DB_PATH"]
