import pytest
from unittest.mock import patch, MagicMock
from src.graph.types import State, StructuredPlan, PlanStep
from src.graph.nodes import supervisor_node, planner_node, code_node, research_node
from src.tools.tool_retriever import biomni_tool_retriever
from langchain_core.messages import HumanMessage
import uuid


@pytest.mark.asyncio
async def test_complete_biomni_workflow_integration():
    """
    End-to-end test for Story 1.5: Biomni Tool Integration into Workflow

    Tests the complete flow:
    1. Plan<PERSON> creates a plan with bioinformatics tasks
    2. Supervisor uses tool retriever to filter relevant tools
    3. Agent executes with filtered tools context
    4. Results are captured in PlanStep.results field
    """

    # Create a bioinformatics query that should trigger biomni tools
    bioinformatics_query = "Analyze gene expression data for p53 pathway and identify differential expression patterns"

    # Step 1: Create initial state with bioinformatics query
    initial_state = State(messages=[HumanMessage(content=bioinformatics_query)])

    # Step 2: Test planner creates appropriate plan
    with (
        patch("src.graph.nodes.get_llm_by_type") as mock_llm,
        patch("src.graph.nodes.apply_prompt_template") as mock_template,
        patch("src.graph.nodes._create_todos_from_plan"),
    ):

        # Mock LLM response with bioinformatics plan
        mock_response = MagicMock()
        mock_response.content = """```json
{
    "title": "Gene Expression Analysis for p53 Pathway",
    "thought": "Analyze differential expression patterns in p53 pathway genes using bioinformatics tools",
    "steps": [
        {
            "agent_name": "researcher",
            "title": "Research p53 pathway genes",
            "description": "Find gene list and pathway information for p53 using literature search and database queries"
        },
        {
            "agent_name": "coder", 
            "title": "Analyze differential expression",
            "description": "Perform gene expression analysis using bioinformatics tools to identify differential expression patterns"
        }
    ]
}
```"""
        mock_llm.return_value.invoke.return_value = mock_response
        mock_template.return_value = [HumanMessage(content="Generate a plan")]

        # Call planner node
        planner_result = planner_node(initial_state)

        # Verify plan was created
        assert "structured_plan" in planner_result
        structured_plan = planner_result["structured_plan"]
        assert isinstance(structured_plan, StructuredPlan)
        assert len(structured_plan.steps) == 2
        assert structured_plan.steps[0].agent_name == "researcher"
        assert structured_plan.steps[1].agent_name == "coder"


@pytest.mark.asyncio
async def test_supervisor_tool_retriever_integration():
    """
    Test that supervisor properly uses tool retriever to filter biomni tools for bioinformatics tasks
    """

    # Create a plan with bioinformatics step
    plan_step = PlanStep(
        step_index=0,
        agent_name="coder",
        title="Analyze protein structures",
        description="Use PDB and UniProt tools to analyze protein structures for p53 mutations",
    )

    structured_plan = StructuredPlan(
        title="Protein Analysis Plan",
        thought="Analyze protein structures",
        steps=[plan_step],
        current_step_index=0,
    )

    state = State(
        structured_plan=structured_plan,
        messages=[HumanMessage(content="Analyze protein structures")],
    )

    # Test supervisor node with tool retriever integration
    with patch(
        "src.config.TEAM_MEMBERS", ["researcher", "coder", "browser", "reporter"]
    ):

        # Mock tool retriever to return biomni tools
        with patch("src.tools.tool_retriever.biomni_tool_retriever") as mock_retriever:
            mock_retriever.invoke.return_value = """
## Selected Tools (3 tools)
1. **query_uniprot**: Search UniProt database for protein information
2. **query_pdb**: Search PDB database for protein structures  
3. **analyze_protein_structure**: Analyze protein structure files
"""

            result = supervisor_node(state)

            # Verify supervisor called tool retriever
            mock_retriever.invoke.assert_called_once()
            call_args = mock_retriever.invoke.call_args[0][0]
            assert "Analyze protein structures" in call_args["query"]
            assert call_args["include_mcp_tools"] is True
            assert call_args["include_biomni_tools"] is True

            # Verify filtered_tools was set on current step
            current_step = state["structured_plan"].get_current_step()
            assert current_step.filtered_tools is not None
            assert "query_uniprot" in current_step.filtered_tools
            assert "query_pdb" in current_step.filtered_tools

            # Verify routing to coder agent
            assert result.goto == "coder"


@pytest.mark.asyncio
async def test_agent_biomni_tool_execution_and_result_capture():
    """
    Test that agents can execute with filtered tools context and results are captured properly
    """

    # Create a plan step with filtered tools
    plan_step = PlanStep(
        step_index=0,
        agent_name="coder",
        title="Query protein database",
        description="Use UniProt to query p53 protein information",
        filtered_tools="""
## Selected Tools (2 tools)
1. **query_uniprot**: Search UniProt database for protein information
2. **biomni_tool_retriever**: Retrieve relevant bioinformatics tools
""",
    )

    structured_plan = StructuredPlan(
        title="Protein Query Plan",
        thought="Query protein databases",
        steps=[plan_step],
        current_step_index=0,
    )

    state = State(
        structured_plan=structured_plan,
        messages=[HumanMessage(content="Query p53 protein information")],
    )

    # Mock agent execution with tool usage
    with patch("src.graph.nodes.coder_agent") as mock_agent:

        # Mock agent result with tool calls
        mock_tool_call = MagicMock()
        mock_tool_call.get.side_effect = lambda key, default=None: {
            "name": "query_uniprot",
            "args": {"protein_name": "p53", "organism": "human"},
            "id": "tool_123",
        }.get(key, default)

        mock_message_with_tools = MagicMock()
        mock_message_with_tools.tool_calls = [mock_tool_call]
        mock_message_with_tools.content = (
            "Querying UniProt for p53 protein information..."
        )

        mock_result_message = MagicMock()
        mock_result_message.content = "Found p53 protein P04637 with 393 amino acids. Selected Tools: query_uniprot returned protein structure data."
        mock_result_message.tool_calls = None

        mock_agent.invoke.return_value = {
            "messages": [mock_message_with_tools, mock_result_message]
        }

        # Execute coder node
        result = code_node(state)

        # Verify agent was called
        mock_agent.invoke.assert_called_once_with(state)

        # Verify enhanced results were captured (check the completed step)
        completed_step = state["structured_plan"].steps[
            0
        ]  # First step should be completed
        assert completed_step.results is not None
        assert completed_step.status == "completed"

        # Check enhanced result structure
        results = completed_step.results
        assert "summary" in results
        assert "step_execution" in results
        assert results["step_execution"]["agent"] == "coder"
        assert results["step_execution"]["title"] == "Query protein database"

        # Verify tool usage was captured
        assert "tool_calls" in results
        assert len(results["tool_calls"]) == 1
        assert results["tool_calls"][0]["tool_name"] == "query_uniprot"
        assert results["tool_calls"][0]["tool_args"]["protein_name"] == "p53"

        # Verify biomni tool results were detected
        assert "tool_results" in results
        assert (
            len(results["tool_results"]) >= 1
        )  # May have multiple biomni-related messages
        assert all(
            result["type"] == "biomni_related" for result in results["tool_results"]
        )
        # Check that at least one result contains p53 protein information
        assert any(
            "p53 protein" in result["content"] for result in results["tool_results"]
        )

        # Verify filtered tools information was included
        assert "filtered_tools_used" in results
        assert results["filtered_tools_used"] is True
        assert "available_tools_summary" in results


@pytest.mark.asyncio
async def test_researcher_biomni_tool_integration():
    """
    Test researcher agent with biomni tool integration for literature search
    """

    # Create a research plan step
    plan_step = PlanStep(
        step_index=0,
        agent_name="researcher",
        title="Research p53 pathway",
        description="Find literature and pathway information for p53 using PubMed and pathway databases",
        filtered_tools="""
## Selected Tools (3 tools)  
1. **search_pubmed_key_words**: Search PubMed for relevant literature
2. **query_kegg**: Query KEGG pathway database
3. **query_reactome**: Search Reactome pathway database
""",
    )

    structured_plan = StructuredPlan(
        title="Literature Research Plan",
        thought="Research p53 pathway",
        steps=[plan_step],
        current_step_index=0,
    )

    state = State(
        structured_plan=structured_plan,
        messages=[HumanMessage(content="Research p53 pathway genes and interactions")],
    )

    # Mock researcher agent execution
    with patch("src.graph.nodes.research_agent") as mock_agent:

        mock_result_message = MagicMock()
        mock_result_message.content = "Found 245 PubMed articles on p53 pathway. Selected Tools: search_pubmed_key_words returned literature data on p53 tumor suppressor gene interactions."
        mock_result_message.tool_calls = None

        mock_agent.invoke.return_value = {"messages": [mock_result_message]}

        # Execute researcher node
        result = research_node(state)

        # Verify enhanced results were captured (check the completed step)
        completed_step = state["structured_plan"].steps[
            0
        ]  # First step should be completed
        results = completed_step.results

        assert "summary" in results
        assert "step_execution" in results
        assert results["step_execution"]["agent"] == "researcher"

        # Verify biomni literature results were detected
        assert "tool_results" in results
        assert results["tool_results"][0]["type"] == "biomni_related"
        assert "pubmed" in results["tool_results"][0]["content"].lower()


@pytest.mark.asyncio
async def test_end_to_end_workflow_with_biomni_tools():
    """
    Full end-to-end test simulating complete workflow with biomni tool integration
    """

    # Create initial bioinformatics query
    bioinformatics_query = (
        "Analyze differential gene expression in cancer samples focusing on p53 pathway"
    )

    # Step 1: Test tool retriever directly
    try:
        tool_retriever_result = biomni_tool_retriever.invoke(
            {
                "query": bioinformatics_query,
                "include_mcp_tools": True,
                "include_biomni_tools": True,
            }
        )

        # Verify tool retriever works (may fail if biomni environment not available)
        assert isinstance(tool_retriever_result, str)
        if "Error in tool retrieval" not in tool_retriever_result:
            # If working, should contain tool information
            assert (
                "Tools" in tool_retriever_result
                or "No relevant tools found" in tool_retriever_result
            )

    except Exception as e:
        # Tool retriever may fail in test environment - this is acceptable
        pytest.skip(f"Tool retriever not available in test environment: {e}")

    # Step 2: Test complete workflow components work together
    plan_step = PlanStep(
        step_index=0,
        agent_name="coder",
        title="Differential expression analysis",
        description="Analyze gene expression differences focusing on p53 pathway genes",
        filtered_tools="Mock filtered tools for testing",
    )

    structured_plan = StructuredPlan(
        title="Cancer Gene Expression Analysis",
        thought="Analyze differential expression in cancer",
        steps=[plan_step],
        current_step_index=0,
    )

    state = State(
        structured_plan=structured_plan,
        messages=[HumanMessage(content=bioinformatics_query)],
    )

    # Test supervisor integration
    with patch(
        "src.config.TEAM_MEMBERS", ["researcher", "coder", "browser", "reporter"]
    ):
        with patch("src.tools.tool_retriever.biomni_tool_retriever") as mock_retriever:
            mock_retriever.invoke.return_value = "Mock tool selection result"

            supervisor_result = supervisor_node(state)

            # Verify supervisor processed the step correctly
            assert supervisor_result.goto == "coder"
            current_step = state["structured_plan"].get_current_step()
            assert current_step.filtered_tools is not None


if __name__ == "__main__":
    pytest.main([__file__])
