#!/usr/bin/env python3
"""
Test message formatting for planner to debug the "contents is not specified" error.
"""

import os
import sys
from dotenv import load_dotenv

# CRITICAL: Apply network fix before any langchain imports
from src.config.network_fix import apply_ipv4_fix

# Load environment variables
load_dotenv(override=True)


def test_prompt_template_formatting():
    """Test the apply_prompt_template function to debug message formatting."""
    print("🧪 Testing Prompt Template Formatting")
    print("=" * 60)

    try:
        print("📋 Step 1: Importing template functions...")
        from src.prompts.template import apply_prompt_template, get_prompt_template
        from langchain_core.messages import HumanMessage

        print("✅ Template functions imported successfully")

        print("\n📋 Step 2: Creating mock state...")
        # Create a realistic state object similar to what the planner receives
        mock_state = {
            "messages": [HumanMessage(content="query the uniprot db for Q86VK4")],
            "structured_plan": None,
            "user_input": "query the uniprot db for Q86VK4",
        }
        print("✅ Mock state created")

        print("\n📋 Step 3: Testing template loading...")
        template_str = get_prompt_template("planner")
        print(f"✅ Template loaded, length: {len(template_str)} characters")
        print(f"📋 Template preview: {template_str[:200]}...")

        print("\n📋 Step 4: Testing apply_prompt_template...")
        formatted_messages = apply_prompt_template("planner", mock_state)
        print(f"✅ apply_prompt_template returned {len(formatted_messages)} messages")

        print("\n📋 Step 5: Examining formatted messages...")
        for i, msg in enumerate(formatted_messages):
            print(f"Message {i+1}:")
            if hasattr(msg, "get"):  # Dictionary format
                print(f"  Type: Dictionary")
                print(f"  Role: {msg.get('role', 'N/A')}")
                content = msg.get("content", "")
                print(f"  Content length: {len(content)} characters")
                if len(content) == 0:
                    print("  ❌ WARNING: Empty content!")
                else:
                    print(f"  Content preview: {content[:100]}...")
            else:  # LangChain message object
                print(f"  Type: {type(msg).__name__}")
                content = getattr(msg, "content", "")
                print(f"  Content length: {len(content)} characters")
                if len(content) == 0:
                    print("  ❌ WARNING: Empty content!")
                else:
                    print(f"  Content preview: {content[:100]}...")

        return formatted_messages

    except Exception as e:
        print(f"❌ Template formatting test failed: {e}")
        import traceback

        traceback.print_exc()
        return None


def test_gemini_with_formatted_messages(messages):
    """Test Gemini API with the formatted messages."""
    print("\n🧪 Testing Gemini API with Formatted Messages")
    print("=" * 60)

    if not messages:
        print("❌ No messages to test")
        return False

    try:
        print("📋 Step 1: Creating Gemini LLM...")
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage, SystemMessage

        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        llm = ChatGoogleGenerativeAI(
            model=model, google_api_key=api_key, temperature=0.0, request_timeout=15
        )
        print("✅ Gemini LLM created")

        print("\n📋 Step 2: Using LangChain messages directly...")
        langchain_messages = []
        for msg in messages:
            # Messages are already LangChain objects, use them directly
            content = getattr(msg, "content", "")

            if not content:
                print(f"❌ Skipping empty message of type: {type(msg).__name__}")
                continue

            langchain_messages.append(msg)

        print(f"✅ Using {len(langchain_messages)} LangChain messages directly")

        print("\n📋 Step 3: Testing with Gemini...")
        print("🚀 Invoking Gemini API...")

        response = llm.invoke(langchain_messages)

        print("✅ SUCCESS! Gemini responded successfully")
        print(f"📝 Response content length: {len(response.content)} characters")
        print(f"📝 Response preview: {response.content[:200]}...")

        return True

    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_minimal_gemini():
    """Test Gemini with minimal message to ensure basic connectivity."""
    print("\n🧪 Testing Minimal Gemini Connectivity")
    print("=" * 60)

    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage

        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        llm = ChatGoogleGenerativeAI(
            model=model, google_api_key=api_key, temperature=0.0
        )

        # Test with minimal message
        response = llm.invoke([HumanMessage(content="Hello")])
        print("✅ Minimal Gemini test successful")
        print(f"📝 Response: {response.content}")
        return True

    except Exception as e:
        print(f"❌ Minimal Gemini test failed: {e}")
        return False


if __name__ == "__main__":
    print("🔬 Message Formatting Debugging Suite")
    print("Debugging the 'contents is not specified' error\n")

    # Test 1: Minimal connectivity check
    minimal_ok = test_minimal_gemini()

    # Test 2: Template formatting
    formatted_messages = test_prompt_template_formatting()

    # Test 3: Full message test if template worked
    full_test_ok = False
    if formatted_messages:
        full_test_ok = test_gemini_with_formatted_messages(formatted_messages)

    print("\n" + "=" * 60)
    print("📊 MESSAGE FORMATTING RESULTS:")
    print(f"Minimal Gemini Test: {'✅ PASS' if minimal_ok else '❌ FAIL'}")
    print(f"Template Formatting: {'✅ PASS' if formatted_messages else '❌ FAIL'}")
    print(f"Full Message Test: {'✅ PASS' if full_test_ok else '❌ FAIL'}")

    if minimal_ok and not full_test_ok:
        print("\n🎯 CONCLUSION: Issue is in message formatting, not connectivity")
    elif not minimal_ok:
        print("\n🎯 CONCLUSION: Basic connectivity issue still exists")
    else:
        print("\n🎯 CONCLUSION: Message formatting works correctly")
