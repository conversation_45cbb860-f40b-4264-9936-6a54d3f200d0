#!/usr/bin/env python3
"""
Debug langchain-google-genai library specific issues
"""

import os
import time
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv(override=True)


def test_raw_requests():
    """Test using raw requests library like curl does."""
    print("🧪 Testing Raw Requests (Python equivalent of curl)")
    print("=" * 60)

    api_key = os.getenv("REASONING_API_KEY")
    model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

    url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"
    headers = {"Content-Type": "application/json", "x-goog-api-key": api_key}
    payload = {"contents": [{"parts": [{"text": "Hello! Just say hi back."}]}]}

    try:
        print("🚀 Making request with Python requests library...")
        start_time = time.time()

        response = requests.post(url, headers=headers, json=payload, timeout=30)

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ SUCCESS! Response in {duration:.2f}s")
        print(f"📝 Status: {response.status_code}")
        print(f"📝 Response: {response.json()}")

        return True

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time if "start_time" in locals() else 0
        print(f"❌ FAILED after {duration:.2f}s: {e}")
        return False


def test_langchain_versions():
    """Test different langchain configurations."""
    print("\n🧪 Testing Langchain Configuration Issues")
    print("=" * 60)

    try:
        print("📋 Checking library versions...")
        import langchain_google_genai
        import langchain_core

        print(f"✅ langchain-google-genai: {langchain_google_genai.__version__}")
        print(f"✅ langchain-core: {langchain_core.__version__}")

        print("\n📋 Testing with explicit timeout configuration...")
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage

        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        # Try with explicit request timeout
        llm = ChatGoogleGenerativeAI(
            model=model,
            google_api_key=api_key,
            temperature=0.0,
            request_timeout=10,  # 10 second timeout
            max_retries=1,
        )

        messages = [HumanMessage(content="Hi")]

        print("🚀 Testing with explicit timeout...")
        start_time = time.time()

        response = llm.invoke(messages)

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ SUCCESS with timeout! Response in {duration:.2f}s")
        print(f"📝 Response: {response.content}")

        return True

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time if "start_time" in locals() else 0
        print(f"❌ FAILED after {duration:.2f}s: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_async_version():
    """Test if async version works differently."""
    print("\n🧪 Testing Async Langchain Version")
    print("=" * 60)

    try:
        import asyncio
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage

        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        async def async_test():
            llm = ChatGoogleGenerativeAI(
                model=model, google_api_key=api_key, temperature=0.0
            )

            messages = [HumanMessage(content="Hi")]

            print("🚀 Testing async invoke...")
            start_time = time.time()

            response = await llm.ainvoke(messages)

            end_time = time.time()
            duration = end_time - start_time

            print(f"✅ SUCCESS with async! Response in {duration:.2f}s")
            print(f"📝 Response: {response.content}")

            return True

        # Run async test with timeout
        return asyncio.run(asyncio.wait_for(async_test(), timeout=30))

    except Exception as e:
        print(f"❌ ASYNC FAILED: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🔬 Langchain Debug Testing Suite")
    print("Investigating langchain-specific issues\n")

    # Test 1: Raw requests
    result1 = test_raw_requests()

    # Test 2: Langchain versions and config
    result2 = test_langchain_versions()

    # Test 3: Async version
    result3 = test_async_version()

    print("\n" + "=" * 60)
    print("📊 LANGCHAIN DEBUG RESULTS:")
    print(f"Raw Requests: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"Langchain Config: {'✅ PASS' if result2 else '❌ FAIL'}")
    print(f"Async Version: {'✅ PASS' if result3 else '❌ FAIL'}")

    if result1 and not result2 and not result3:
        print("\n🎯 CONCLUSION: Langchain library issue - use raw requests!")
    elif result1:
        print("\n🎯 CONCLUSION: API works - configuration issue in langchain!")
