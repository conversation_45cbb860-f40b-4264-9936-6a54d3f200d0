#!/usr/bin/env python3
"""
Test IPv6/gRPC fix for langchain-google-genai
"""

import os
import time
from dotenv import load_dotenv

# CRITICAL FIX: Force IPv4 for gRPC connections
os.environ["GRPC_DNS_RESOLVER"] = "native"
os.environ["GRPC_CHANNEL_ARGS"] = (
    "grpc.so_reuseaddr:1,grpc.keepalive_time_ms:30000,grpc.keepalive_timeout_ms:5000,grpc.keepalive_permit_without_calls:1,grpc.http2.max_pings_without_data:0"
)

# Alternative approach - disable IPv6 for Python socket
import socket

original_getaddrinfo = socket.getaddrinfo


def ipv4_only_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
    """Force IPv4 only for all network connections."""
    return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)


# Monkey patch socket to use IPv4 only
socket.getaddrinfo = ipv4_only_getaddrinfo

# Load environment variables
load_dotenv(override=True)


def test_langchain_with_ipv4_fix():
    """Test langchain with IPv4-only fix."""
    print("🧪 Testing Langchain with IPv4-only Fix")
    print("=" * 60)

    try:
        print("📋 Step 1: Applying IPv4-only fix...")
        print("✅ Environment variables set for IPv4-only gRPC")
        print("✅ Socket monkey-patched for IPv4-only")

        print("\n📋 Step 2: Loading langchain...")
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage

        print("✅ Langchain imports successful")

        print("\n📋 Step 3: Creating LLM instance...")
        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        # Try with explicit timeout and IPv4 configuration
        llm = ChatGoogleGenerativeAI(
            model=model,
            google_api_key=api_key,
            temperature=0.0,
            request_timeout=15,  # 15 second timeout
            max_retries=1,
        )
        print("✅ LLM instance created")

        print("\n📋 Step 4: Testing LLM invocation with IPv4 fix...")
        messages = [HumanMessage(content="Hello! Just say hi back.")]

        start_time = time.time()
        print("🚀 Invoking LLM with IPv4-only configuration...")

        response = llm.invoke(messages)

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ SUCCESS! Response received in {duration:.2f}s")
        print(f"📝 Response: {response.content}")

        return True

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time if "start_time" in locals() else 0
        print(f"❌ FAILED after {duration:.2f}s: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_alternative_configuration():
    """Test alternative gRPC configuration."""
    print("\n🧪 Testing Alternative gRPC Configuration")
    print("=" * 60)

    try:
        # Additional gRPC environment variables
        os.environ["GRPC_POLL_STRATEGY"] = "poll"
        os.environ["GRPC_ENABLE_FORK_SUPPORT"] = "0"

        print("📋 Applied additional gRPC configuration...")

        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage

        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        llm = ChatGoogleGenerativeAI(
            model=model, google_api_key=api_key, temperature=0.0, request_timeout=10
        )

        messages = [HumanMessage(content="Test")]

        start_time = time.time()
        response = llm.invoke(messages)
        end_time = time.time()

        duration = end_time - start_time
        print(f"✅ Alternative config SUCCESS! Response in {duration:.2f}s")
        print(f"📝 Response: {response.content}")

        return True

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time if "start_time" in locals() else 0
        print(f"❌ Alternative config FAILED after {duration:.2f}s: {e}")
        return False


if __name__ == "__main__":
    print("🔬 IPv6/gRPC Fix Testing Suite")
    print("Testing fixes for langchain IPv6 connectivity issues\n")

    # Test 1: IPv4-only fix
    result1 = test_langchain_with_ipv4_fix()

    # Test 2: Alternative configuration
    result2 = test_alternative_configuration()

    print("\n" + "=" * 60)
    print("📊 IPv6 FIX RESULTS:")
    print(f"IPv4-only Fix: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"Alternative Config: {'✅ PASS' if result2 else '❌ FAIL'}")

    if result1 or result2:
        print("\n🎯 CONCLUSION: IPv6 fix works! Apply to multi-agent system.")
    else:
        print("\n🎯 CONCLUSION: IPv6 fix failed - need alternative approach.")
