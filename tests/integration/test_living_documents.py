"""
Integration tests for Living Documents System

Tests end-to-end functionality including:
- Reporter node integration with document service
- Template-based document generation
- Semantic naming system integration
- Document backup functionality
- Complete workflow from context identification to document creation
"""

import pytest
import os
import tempfile
import shutil
import datetime
from unittest.mock import Mock, patch
from pathlib import Path

from src.service.document_service import DocumentService
from src.graph.types import State, PlanStep, StructuredPlan
from src.graph.nodes import (
    reporter_node,
    _extract_content_from_workflow,
    _populate_template,
)


class TestLivingDocumentsIntegration:
    """Integration test suite for Living Documents System."""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def mock_state(self):
        """Create mock state for testing."""
        # Create mock plan steps
        step1 = PlanStep(
            step_index=0,
            agent_name="researcher",
            title="Literature Research",
            description="Research differential expression analysis methods",
            status="completed",
            results={
                "summary": (
                    "Found significant research on breast cancer differential expression"
                ),
                "key_findings": ["DESeq2 is gold standard", "RNA-seq widely used"],
                "tool_calls": [
                    {"name": "pubmed_search"},
                    {"name": "literature_review"},
                ],
                "references": ["Smith et al. 2023", "Johnson et al. 2022"],
            },
        )

        step2 = PlanStep(
            step_index=1,
            agent_name="coder",
            title="Data Analysis",
            description="Perform differential expression analysis",
            status="completed",
            results={
                "summary": (
                    "Completed differential expression analysis with significant results"
                ),
                "code": (
                    "library(DESeq2)\nresults <- DESeq(dds)\nsignificant_genes <- results[results$padj < 0.05,]"
                ),
                "statistics": {
                    "significant_genes": 1250,
                    "p_value": 0.001,
                    "fold_change": 2.5,
                },
                "output_files": ["de_results.csv", "volcano_plot.png"],
                "performance_metrics": {
                    "execution_time": 45.2,
                    "memory_usage": "2.1GB",
                },
            },
        )

        step3 = PlanStep(
            step_index=2,
            agent_name="reporter",
            title="Generate Report",
            description="Create analysis report",
            status="pending",
        )

        # Create structured plan
        plan = StructuredPlan(
            title="Breast Cancer Differential Expression Analysis",
            thought="Analyze gene expression differences in breast cancer samples",
            steps=[step1, step2, step3],
            current_step_index=2,
            start_time=datetime.datetime.now(),
        )

        # Create mock state
        state = {
            "goal": "Analyze differential gene expression in breast cancer samples",
            "structured_plan": plan,
            "session_id": "test_session_123",
            "messages": [],
        }

        return state

    @pytest.fixture
    def template_content(self):
        """Create template content for testing."""
        return """# {document_title}

## Document Metadata
- **Analysis Type:** {analysis_type}
- **Subject:** {subject}
- **Domain:** {domain}

## Executive Summary
{executive_summary}

## Key Findings
{key_insights}

## Technical Details
{technical_details}

## Supporting Evidence
{supporting_evidence}

## Statistical Analysis
{statistical_analysis}
"""

    def test_end_to_end_living_document_creation(
        self, mock_state, temp_workspace, template_content
    ):
        """Test complete end-to-end living document creation process."""
        with patch("src.config.env.WORKSPACE_DIR", temp_workspace):
            # Create template file in expected location relative to nodes.py
            nodes_dir = os.path.dirname(
                os.path.abspath(__file__ + "/../../src/graph/nodes.py")
            )
            template_dir = os.path.join(nodes_dir, "..", "templates")
            os.makedirs(template_dir, exist_ok=True)
            template_path = os.path.join(template_dir, "living_document.md")
            with open(template_path, "w") as f:
                f.write(template_content)

            try:
                # Execute reporter node
                result = reporter_node(mock_state)

                # Verify result structure
                assert "structured_plan" in result
                assert "messages" in result
                assert len(result["messages"]) == 1
                assert result["messages"][0].name == "reporter"

                # Verify plan step completion
                plan = result["structured_plan"]
                reporter_step = plan.steps[2]
                assert reporter_step.status == "completed"
                assert "document_path" in reporter_step.results
                assert "semantic_filename" in reporter_step.results
                assert "context" in reporter_step.results

                # Verify semantic filename format
                semantic_filename = reporter_step.results["semantic_filename"]
                assert semantic_filename.startswith("Analysis_")
                assert semantic_filename.endswith(".md")
                assert "DifferentialExpression" in semantic_filename

                # Verify document was created
                document_path = reporter_step.results["document_path"]
                assert os.path.exists(document_path)

                # Verify document content
                with open(document_path, "r") as f:
                    content = f.read()
                assert "Analysis Report:" in content
                assert "DifferentialExpression" in content
                assert "bioinformatics" in content
                assert "differential expression analysis" in content.lower()
            finally:
                # Clean up template file
                try:
                    os.remove(template_path)
                    os.rmdir(template_dir)
                except (FileNotFoundError, OSError):
                    pass

    def test_document_service_integration(self, temp_workspace):
        """Test document service integration and semantic context identification."""
        doc_service = DocumentService(temp_workspace)

        # Test bioinformatics context
        context = doc_service.identify_semantic_context(
            goal="Perform protein analysis of cancer cell interactions",
            plan_title="Protein Analysis Network Study",
            plan_steps=[
                {
                    "description": "Extract protein analysis data",
                    "title": "Data extraction",
                },
                {
                    "description": "Build protein analysis network",
                    "title": "Network analysis",
                },
            ],
        )

        assert context["analysis_type"] == "ProteinAnalysis"
        assert context["subject"] == "Protein"
        assert context["domain"] == "bioinformatics"

        # Test filename generation
        filename = doc_service.generate_semantic_filename(context)
        assert filename == "Analysis_ProteinAnalysis_Protein.md"

    def test_template_based_document_generation(self, mock_state, template_content):
        """Test template-based document generation with real content extraction."""
        # Extract content from workflow
        plan = mock_state["structured_plan"]
        content_data = _extract_content_from_workflow(mock_state, plan)

        # Verify content extraction
        assert "executive_summary" in content_data
        assert "key_insights" in content_data
        assert "technical_details" in content_data
        assert "supporting_evidence" in content_data
        assert "statistical_analysis" in content_data

        # Verify actual content (not placeholders)
        assert (
            "breast cancer differential expression"
            in content_data["executive_summary"].lower()
        )
        assert (
            "deseq2" in content_data["key_insights"].lower()
            or "significant" in content_data["key_insights"].lower()
        )
        assert (
            "library(DESeq2)" in content_data["technical_details"]
            or "Used tools:" in content_data["technical_details"]
        )

        # Test template population
        context = {
            "analysis_type": "DifferentialExpression",
            "subject": "BreastCancer",
            "domain": "bioinformatics",
        }

        populated_content = _populate_template(
            template_content, content_data, context, mock_state
        )

        # Verify template was populated
        assert "{document_title}" not in populated_content
        assert "{analysis_type}" not in populated_content
        assert "{subject}" not in populated_content
        assert "DifferentialExpression" in populated_content
        assert "BreastCancer" in populated_content
        assert "bioinformatics" in populated_content

    def test_backup_system_functionality(self, temp_workspace):
        """Test backup system for existing documents."""
        doc_service = DocumentService(temp_workspace)

        # Create existing document
        existing_filename = "Analysis_DifferentialExpression_Breast.md"
        existing_path = os.path.join(temp_workspace, existing_filename)
        original_content = "# Original Analysis\nThis is the original content."
        with open(existing_path, "w") as f:
            f.write(original_content)

        # Create backup
        backup_path = doc_service.create_document_backup(existing_path)

        # Verify backup was created
        assert os.path.exists(backup_path)
        assert "backup" in backup_path

        # Verify backup content matches original
        with open(backup_path, "r") as f:
            backup_content = f.read()
        assert backup_content == original_content

        # Verify original file still exists
        assert os.path.exists(existing_path)

    def test_semantic_similarity_matching(self, temp_workspace):
        """Test semantic similarity matching for existing documents."""
        doc_service = DocumentService(temp_workspace)

        # Create existing document with similar name
        existing_filename = "Analysis_DifferentialExpression_BreastCancer.md"
        existing_path = os.path.join(temp_workspace, existing_filename)
        with open(existing_path, "w") as f:
            f.write("# Existing Analysis")

        # Test similarity matching
        target_filename = "Analysis_DifferentialExpression_Breast.md"
        found_path = doc_service.find_existing_document(target_filename, {})

        # Should find the similar document
        assert found_path == existing_path

    def test_multi_domain_support(self, temp_workspace):
        """Test multi-domain analysis support."""
        doc_service = DocumentService(temp_workspace)

        # Test different domains
        domains_tests = [
            {
                "goal": "Build machine learning model for cancer prediction",
                "expected_domain": "machine_learning",
                "expected_type": "MachineLearning",
            },
            {
                "goal": "Analyze software architecture patterns",
                "expected_domain": "software_engineering",
                "expected_type": "General",
            },
            {
                "goal": "Perform statistical data analysis",
                "expected_domain": "data_science",
                "expected_type": "DataAnalysis",
            },
            {
                "goal": "Perform genomic analysis of population variants",
                "expected_domain": "bioinformatics",
                "expected_type": "GenomicAnalysis",
            },
        ]

        for test_case in domains_tests:
            context = doc_service.identify_semantic_context(
                goal=test_case["goal"], plan_title="", plan_steps=[]
            )
            assert context["domain"] == test_case["expected_domain"]
            assert context["analysis_type"] == test_case["expected_type"]

    def test_intelligent_content_extraction(self, mock_state):
        """Test intelligent content extraction from step results."""
        plan = mock_state["structured_plan"]
        content_data = _extract_content_from_workflow(mock_state, plan)

        # Verify executive summary contains real content
        executive_summary = content_data["executive_summary"]
        assert "Analyze differential gene expression" in executive_summary
        assert (
            "Literature Research:" in executive_summary
            or "Data Analysis:" in executive_summary
        )

        # Verify key insights extraction
        key_insights = content_data["key_insights"]
        assert "DESeq2" in key_insights or "significant" in key_insights.lower()

        # Verify technical details include code
        technical_details = content_data["technical_details"]
        assert (
            "library(DESeq2)" in technical_details or "Used tools:" in technical_details
        )

        # Verify supporting evidence includes files
        supporting_evidence = content_data["supporting_evidence"]
        assert (
            "de_results.csv" in supporting_evidence
            or "volcano_plot.png" in supporting_evidence
        )

        # Verify statistical analysis
        statistical_analysis = content_data["statistical_analysis"]
        assert (
            "1250" in statistical_analysis
            or "0.001" in statistical_analysis
            or "2.5" in statistical_analysis
        )

    def test_document_metadata_extraction(self, temp_workspace):
        """Test document metadata extraction and validation."""
        doc_service = DocumentService(temp_workspace)

        # Create document with metadata
        doc_filename = "Analysis_Test_Metadata.md"
        doc_path = os.path.join(temp_workspace, doc_filename)
        doc_content = """# Test Analysis Report

This is a comprehensive analysis with multiple sections.
The document contains detailed findings and results.
Statistical analysis shows significant patterns.
"""
        with open(doc_path, "w") as f:
            f.write(doc_content)

        # Extract metadata
        metadata = doc_service.get_document_metadata(doc_path)

        # Verify metadata structure
        assert "file_size" in metadata
        assert "last_modified" in metadata
        assert "word_count" in metadata
        assert "line_count" in metadata
        assert "title" in metadata

        # Verify content
        assert metadata["title"] == "Test Analysis Report"
        assert metadata["word_count"] > 10
        assert metadata["line_count"] > 3

        # Test content validation
        is_valid = doc_service.validate_document_content(doc_content)
        assert is_valid is True

    def test_error_handling_and_recovery(self, mock_state, temp_workspace):
        """Test error handling and recovery mechanisms."""
        with patch("src.config.env.WORKSPACE_DIR", temp_workspace):
            # Test with missing template
            with patch(
                "builtins.open", side_effect=FileNotFoundError("Template not found")
            ):
                result = reporter_node(mock_state)

                # Verify error handling
                plan = result["structured_plan"]
                reporter_step = plan.steps[2]
                assert reporter_step.status == "failed"
                assert reporter_step.error_message is not None

    def test_document_content_validation_integration(self, temp_workspace):
        """Test document content validation in integration context."""
        doc_service = DocumentService(temp_workspace)

        # Test valid content
        valid_content = """# Comprehensive Analysis Report

## Executive Summary
This analysis successfully identified significant differential expression patterns
in breast cancer samples compared to normal tissue controls.

## Key Findings
- 1,250 genes showed significant differential expression (padj < 0.05)
- Upregulated genes are enriched in cell proliferation pathways
- Downregulated genes include tumor suppressor candidates

## Statistical Results
- Mean fold change: 2.5
- P-value threshold: 0.001
- False discovery rate: 5%

## Conclusions
The analysis provides strong evidence for distinct molecular signatures
in breast cancer that could inform therapeutic targeting strategies.
"""

        assert doc_service.validate_document_content(valid_content) is True

        # Test placeholder-heavy content
        placeholder_content = """# Analysis Report

Results will be added later.
Findings to be determined.
Analysis will be completed.
Data will be synthesized.
Conclusions will be generated.
"""

        assert doc_service.validate_document_content(placeholder_content) is False

    def test_concurrent_document_operations(self, temp_workspace):
        """Test concurrent document operations and backup numbering."""
        doc_service = DocumentService(temp_workspace)

        # Create base document
        base_filename = "Analysis_Concurrent_Test.md"
        base_path = os.path.join(temp_workspace, base_filename)
        with open(base_path, "w") as f:
            f.write("# Base Document")

        # Create multiple backups (simulating concurrent operations)
        backup_paths = []
        for i in range(5):
            backup_path = doc_service.create_document_backup(base_path)
            backup_paths.append(backup_path)
            assert os.path.exists(backup_path)

        # Verify all backups are unique
        assert len(set(backup_paths)) == 5

        # Test cleanup functionality
        doc_service.cleanup_old_backups(max_backups=3)

        # Count remaining backups
        remaining_backups = [f for f in os.listdir(temp_workspace) if "backup" in f]
        assert len(remaining_backups) <= 3
