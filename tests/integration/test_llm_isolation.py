#!/usr/bin/env python3
"""
Phase 1.1: Pure LLM Isolation Test
Tests Gemini API in complete isolation from our multi-agent architecture.
"""

import os
import time
from dotenv import load_dotenv

# Load environment variables - CRITICAL FIX
print("🔧 Loading environment variables...")
dotenv_loaded = load_dotenv(override=True)
print(f"✅ .env file loaded: {dotenv_loaded}")

# Debug environment variable loading
api_key = os.getenv("REASONING_API_KEY")
print(f"🔑 API Key length: {len(api_key) if api_key else 0}")
print(f"🔑 API Key present: {'Yes' if api_key else 'No'}")

if not api_key:
    print("❌ CRITICAL: No API key found! Checking alternative locations...")
    # Try manual loading
    import pathlib

    env_file = pathlib.Path(".env")
    if env_file.exists():
        print(f"📁 .env file exists at: {env_file.absolute()}")
        with open(env_file) as f:
            for line in f:
                if line.startswith("REASONING_API_KEY"):
                    print(f"🔑 Found API key line: {line.strip()[:20]}...")
                    key_value = line.strip().split("=", 1)[1]
                    os.environ["REASONING_API_KEY"] = key_value
                    print(f"🔧 Manually set API key length: {len(key_value)}")
                    break
    else:
        print(f"❌ .env file not found at: {env_file.absolute()}")
        exit(1)


def test_pure_gemini():
    """Test Gemini API with absolute minimal setup."""
    print("🧪 Phase 1.1: Testing Pure Gemini API in Isolation")
    print("=" * 60)

    start_time = time.time()

    try:
        print("📋 Step 1: Loading minimal imports...")
        step_time = time.time()
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage

        print(f"✅ Imports successful ({time.time() - step_time:.2f}s)")

        print("\n📋 Step 2: Creating LLM instance...")
        step_time = time.time()
        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        if not api_key:
            print("❌ No REASONING_API_KEY found in environment")
            return False

        print(f"🔧 Using model: {model}")
        print(f"🔧 API key length: {len(api_key)}")

        llm = ChatGoogleGenerativeAI(
            model=model, google_api_key=api_key, temperature=0.0
        )
        print(f"✅ LLM instance created ({time.time() - step_time:.2f}s)")

        print("\n📋 Step 3: Creating message...")
        step_time = time.time()
        messages = [
            HumanMessage(
                content="Hello! Just respond with 'Hi' - this is a connection test."
            )
        ]
        print(f"✅ Message created ({time.time() - step_time:.2f}s)")

        print("\n📋 Step 4: Invoking LLM...")
        step_time = time.time()
        print("🚀 About to call llm.invoke() - this is where it might hang...")
        print("🕐 Starting LLM invocation timer...")

        # Add periodic progress updates during invoke
        import threading
        import sys

        def progress_monitor():
            elapsed = 0
            while elapsed < 30:
                time.sleep(5)
                elapsed += 5
                print(f"⏱️ Still waiting for LLM response... {elapsed}s elapsed")
                sys.stdout.flush()

        monitor_thread = threading.Thread(target=progress_monitor, daemon=True)
        monitor_thread.start()

        response = llm.invoke(messages)

        end_time = time.time()
        duration = end_time - step_time
        total_duration = end_time - start_time

        print(
            f"✅ SUCCESS! Response received in {duration:.2f}s (total: {total_duration:.2f}s)"
        )
        print(f"📝 Response: {response.content}")

        return True

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ FAILED after {duration:.2f}s: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_with_timeout():
    """Test with explicit timeout to see if it's a timeout issue."""
    print("\n🧪 Phase 1.1b: Testing with explicit timeout...")
    print("=" * 60)

    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage
        import signal

        # Set up timeout handler
        def timeout_handler(signum, frame):
            raise TimeoutError("LLM call timed out after 30 seconds")

        api_key = os.getenv("REASONING_API_KEY")
        model = os.getenv("REASONING_MODEL", "gemini-1.5-flash")

        llm = ChatGoogleGenerativeAI(
            model=model, google_api_key=api_key, temperature=0.0
        )

        messages = [HumanMessage(content="Hello")]

        print("🚀 Invoking LLM with 30s timeout...")
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(30)  # 30 second timeout

        start_time = time.time()
        response = llm.invoke(messages)
        signal.alarm(0)  # Cancel timeout

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ SUCCESS with timeout! Response in {duration:.2f}s")
        print(f"📝 Response: {response.content}")
        return True

    except TimeoutError as e:
        print(f"⏰ TIMEOUT: {e}")
        return False
    except Exception as e:
        signal.alarm(0)  # Cancel timeout
        print(f"❌ ERROR: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🔬 LLM Isolation Testing Suite")
    print("Testing Gemini API outside of multi-agent architecture\n")

    # Test 1: Pure isolation
    result1 = test_pure_gemini()

    # Test 2: With timeout
    result2 = test_with_timeout()

    print("\n" + "=" * 60)
    print("📊 ISOLATION TEST RESULTS:")
    print(f"Pure LLM Test: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"Timeout Test: {'✅ PASS' if result2 else '❌ FAIL'}")

    if result1 and result2:
        print(
            "\n🎯 CONCLUSION: Gemini API works in isolation - issue is architectural!"
        )
    elif not result1 and not result2:
        print("\n🎯 CONCLUSION: Gemini API issue - not architectural!")
    else:
        print("\n🎯 CONCLUSION: Mixed results - need deeper analysis!")
