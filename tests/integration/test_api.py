import pytest
from fastapi.testclient import Test<PERSON>lient
from src.api.app import app
import io

client = TestClient(app)


def test_agent_endpoint():
    # Create a dummy file
    file_content = b"dummy file content"
    file = ("test.txt", io.BytesIO(file_content), "text/plain")

    response = client.post(
        "/agent", data={"request": "test request"}, files={"files": file}
    )
    assert response.status_code == 200
    assert "task_id" in response.json()
