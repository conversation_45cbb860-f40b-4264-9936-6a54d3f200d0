#!/usr/bin/env python3
"""Time Travel Example for Multi-Agent LangGraph System.

This example demonstrates how to use the time-travel functionality to:
1. Run a workflow and capture checkpoints
2. Explore the conversation timeline
3. Modify state at specific checkpoints
4. Resume execution from alternative states
5. Compare different execution paths

Based on the LangGraph time-travel documentation:
https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/time-travel/
"""

import asyncio
import uuid
import json
from typing import Dict, Any

# Import our services
from src.service.workflow_service import run_agent_workflow, initialize_graph
from src.service.time_travel_service import create_time_travel_session
from src.service.persistence_service import get_conversation_history


async def demonstrate_time_travel():
    """Demonstrate the complete time-travel workflow."""
    print("🚀 Time Travel Demo for Multi-Agent LangGraph System")
    print("=" * 60)

    # Initialize the graph
    await initialize_graph()

    # Step 1: Run initial workflow
    print("\n📝 Step 1: Running initial workflow...")
    thread_id = str(uuid.uuid4())

    user_messages = [
        {
            "role": "user",
            "content": (
                "Research the latest developments in AI agents and write a brief summary"
            ),
        }
    ]

    # Collect workflow events
    events = []
    async for event in run_agent_workflow(
        user_input_messages=user_messages, thread_id=thread_id, debug=True
    ):
        events.append(event)
        if event.get("event") == "message":
            content = event.get("data", {}).get("delta", {}).get("content", "")
            if content:
                print(f"💬 {content}", end="", flush=True)

    print(f"\n✅ Initial workflow completed. Thread ID: {thread_id}")

    # Step 2: Explore conversation timeline
    print("\n🕐 Step 2: Exploring conversation timeline...")
    time_travel = await create_time_travel_session(thread_id)
    timeline = await time_travel.get_conversation_timeline()

    print(f"\nFound {len(timeline)} checkpoints in the conversation:")
    for item in timeline:
        print(
            f"  Step {item['step']}: {item['next_agent']} - {item['checkpoint_id'][:8]}..."
        )
        if item["last_message"]:
            print(f"    Last message: {item['last_message']['content'][:50]}...")

    # Step 3: List available checkpoints
    print("\n📋 Step 3: Listing available checkpoints...")
    checkpoints = await time_travel.list_checkpoints()

    print(f"\nAvailable checkpoints ({len(checkpoints)}):")
    for i, cp in enumerate(checkpoints[:5]):  # Show first 5
        print(
            f"  {i+1}. {cp['checkpoint_id'][:12]}... - Next: {cp['next']} - Messages: {cp['message_count']}"
        )

    if not checkpoints:
        print("❌ No checkpoints found. Cannot proceed with time travel demo.")
        return

    # Step 4: Select a checkpoint to modify
    print("\n🔄 Step 4: Selecting checkpoint for modification...")
    # Choose a checkpoint from the middle of the conversation
    selected_checkpoint = (
        checkpoints[len(checkpoints) // 2] if len(checkpoints) > 1 else checkpoints[0]
    )
    checkpoint_id = selected_checkpoint["checkpoint_id"]

    print(f"Selected checkpoint: {checkpoint_id[:12]}...")

    # Get detailed checkpoint information
    checkpoint_details = await time_travel.get_checkpoint(checkpoint_id)
    if checkpoint_details:
        print(f"Checkpoint details:")
        print(f"  - Next agent: {checkpoint_details['next']}")
        print(f"  - Created: {checkpoint_details['created_at']}")
        print(f"  - Messages: {len(checkpoint_details['values'].get('messages', []))}")

    # Step 5: Modify state and explore alternative
    print("\n🌟 Step 5: Creating alternative timeline...")

    # Example modification: Change the research focus
    modifications = {
        "messages": (
            checkpoint_details["values"].get("messages", [])
            + [
                {
                    "role": "user",
                    "content": (
                        "Actually, focus specifically on multi-agent systems and their coordination mechanisms"
                    ),
                }
            ]
        )
    }

    print("Exploring alternative path with modified focus...")
    alternative_events = []

    async for event in time_travel.explore_alternative(checkpoint_id, modifications):
        alternative_events.append(event)
        if event.get("event") == "message":
            content = event.get("data", {}).get("delta", {}).get("content", "")
            if content:
                print(f"🔀 {content}", end="", flush=True)
        elif event.get("error"):
            print(f"❌ Error: {event['error']}")

    print(
        f"\n✅ Alternative path explored. Generated {len(alternative_events)} events."
    )

    # Step 6: Compare timelines
    print("\n📊 Step 6: Comparing original and alternative timelines...")

    # Get updated timeline after modification
    updated_timeline = await time_travel.get_conversation_timeline()

    print(f"\nTimeline comparison:")
    print(f"  Original checkpoints: {len(timeline)}")
    print(f"  Updated checkpoints: {len(updated_timeline)}")
    print(f"  New branches created: {len(updated_timeline) - len(timeline)}")

    # Step 7: Demonstrate resumption from specific checkpoint
    print("\n⏯️  Step 7: Demonstrating checkpoint resumption...")

    if len(checkpoints) > 2:
        # Resume from an earlier checkpoint
        earlier_checkpoint = checkpoints[-2]["checkpoint_id"]
        print(f"Resuming from earlier checkpoint: {earlier_checkpoint[:12]}...")

        resume_events = []
        async for event in time_travel.resume_from(earlier_checkpoint):
            resume_events.append(event)
            if len(resume_events) > 10:  # Limit output for demo
                break

        print(f"✅ Resumed execution generated {len(resume_events)} events.")

    print("\n🎉 Time Travel Demo Complete!")
    print("\nKey capabilities demonstrated:")
    print("  ✓ Checkpoint creation and listing")
    print("  ✓ State modification at specific points")
    print("  ✓ Alternative path exploration")
    print("  ✓ Timeline comparison")
    print("  ✓ Checkpoint resumption")

    return thread_id


async def interactive_time_travel_session():
    """Interactive session for exploring time travel functionality."""
    print("\n🎮 Interactive Time Travel Session")
    print("Enter a thread ID to explore, or press Enter to create a new conversation:")

    thread_input = input("> ").strip()

    if thread_input:
        thread_id = thread_input
        print(f"Using existing thread: {thread_id}")
    else:
        # Create new conversation
        thread_id = str(uuid.uuid4())
        print(f"Creating new conversation: {thread_id}")

        user_query = input("Enter your query: ").strip()
        if not user_query:
            user_query = "Explain the benefits of multi-agent AI systems"

        user_messages = [{"role": "user", "content": user_query}]

        # Run workflow
        async for event in run_agent_workflow(
            user_input_messages=user_messages, thread_id=thread_id
        ):
            if event.get("event") == "message":
                content = event.get("data", {}).get("delta", {}).get("content", "")
                if content:
                    print(content, end="", flush=True)

    # Interactive exploration
    time_travel = await create_time_travel_session(thread_id)

    while True:
        print("\n" + "=" * 50)
        print("Time Travel Options:")
        print("1. List checkpoints")
        print("2. View timeline")
        print("3. Examine checkpoint")
        print("4. Modify state")
        print("5. Resume from checkpoint")
        print("6. Exit")

        choice = input("\nSelect option (1-6): ").strip()

        if choice == "1":
            checkpoints = await time_travel.list_checkpoints()
            print(f"\nFound {len(checkpoints)} checkpoints:")
            for i, cp in enumerate(checkpoints):
                print(f"  {i+1}. {cp['checkpoint_id']} - Next: {cp['next']}")

        elif choice == "2":
            timeline = await time_travel.get_conversation_timeline()
            print(f"\nConversation Timeline ({len(timeline)} steps):")
            for item in timeline:
                print(
                    f"  Step {item['step']}: {item['next_agent']} at {item['created_at']}"
                )

        elif choice == "3":
            checkpoint_id = input("Enter checkpoint ID: ").strip()
            details = await time_travel.get_checkpoint(checkpoint_id)
            if details:
                print(f"\nCheckpoint Details:")
                print(json.dumps(details, indent=2, default=str))
            else:
                print("Checkpoint not found.")

        elif choice == "4":
            checkpoint_id = input("Enter checkpoint ID to modify: ").strip()
            print("Enter new state (JSON format):")
            try:
                new_state = json.loads(input("> "))
                new_checkpoint = await time_travel.modify_state(
                    checkpoint_id, new_state
                )
                if new_checkpoint:
                    print(f"✅ Created new checkpoint: {new_checkpoint}")
                else:
                    print("❌ Failed to modify state.")
            except json.JSONDecodeError:
                print("❌ Invalid JSON format.")

        elif choice == "5":
            checkpoint_id = input("Enter checkpoint ID to resume from: ").strip()
            print("Resuming execution...")
            async for event in time_travel.resume_from(checkpoint_id):
                if event.get("event") == "message":
                    content = event.get("data", {}).get("delta", {}).get("content", "")
                    if content:
                        print(content, end="", flush=True)

        elif choice == "6":
            print("Goodbye!")
            break

        else:
            print("Invalid option. Please try again.")


# Pytest test functions
import pytest


@pytest.mark.asyncio
async def test_time_travel_functionality():
    """Test the complete time-travel workflow."""
    await initialize_graph()
    thread_id = str(uuid.uuid4())
    user_messages = [
        {
            "role": "user",
            "content": (
                "Research the latest developments in AI agents and write a brief summary"
            ),
        }
    ]
    events = []
    async for event in run_agent_workflow(
        user_input_messages=user_messages, thread_id=thread_id, debug=True
    ):
        events.append(event)

    assert thread_id is not None
    print(f"✅ Time travel test completed successfully for thread: {thread_id}")


@pytest.mark.asyncio
async def test_time_travel_basic_operations():
    """Test basic time travel operations."""
    # Initialize the graph
    await initialize_graph()

    # Create a simple workflow
    thread_id = str(uuid.uuid4())
    user_messages = [{"role": "user", "content": "Hello, test message"}]

    # Run a minimal workflow
    events = []
    async for event in run_agent_workflow(
        user_input_messages=user_messages, thread_id=thread_id, debug=False
    ):
        events.append(event)

    # Test time travel operations
    time_travel = await create_time_travel_session(thread_id)

    # Test listing checkpoints
    checkpoints = await time_travel.list_checkpoints()
    assert isinstance(checkpoints, list)

    # Test getting timeline
    timeline = await time_travel.get_conversation_timeline()
    assert isinstance(timeline, list)

    print(f"✅ Basic time travel operations test completed for thread: {thread_id}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        asyncio.run(interactive_time_travel_session())
    else:
        asyncio.run(demonstrate_time_travel())
