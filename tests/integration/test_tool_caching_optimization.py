"""
Integration tests for tool caching optimization in supervisor node.

Tests validate the 40-60% API reduction target during retry scenarios
and ensure cache hit rate tracking works correctly.
"""

import unittest
import time
from unittest.mock import patch, MagicMock

from src.graph.types import State, StructuredPlan, PlanStep
from src.graph.nodes import supervisor_node
from src.tools.performance_monitor import (
    get_tool_retriever_cache_metrics,
    performance_tracker,
)


class TestToolCachingOptimization(unittest.TestCase):
    """Integration tests for tool caching optimization."""

    def setUp(self):
        """Set up test environment."""
        # Clear performance tracker metrics for clean test state
        performance_tracker.execution_metrics.clear()
        performance_tracker.tool_metrics.clear()
        performance_tracker.strategy_metrics.clear()

    @patch("src.tools.tool_retriever.biomni_tool_retriever")
    def test_retry_scenario_api_reduction(self, mock_tool_retriever):
        """Test that retry scenarios achieve 40-60% API reduction."""
        mock_tool_retriever.invoke.return_value = "filtered tools for step"

        # Create a plan step that will be "retried" multiple times
        plan = StructuredPlan(
            title="Test Plan with Retries",
            thought="Testing retry scenario optimization",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Research with Retries",
                    description="This step will be retried to test caching",
                    filtered_tools=None,  # Start without cached tools
                    retry_count=0,
                )
            ],
        )

        # Simulate first execution (new step) - should call tool retriever
        state = State(messages=[], structured_plan=plan)
        result1 = supervisor_node(state)

        # Verify tool was retrieved and cached
        mock_tool_retriever.invoke.assert_called_once()
        self.assertEqual(plan.steps[0].filtered_tools, "filtered tools for step")

        # Reset mock to track subsequent calls
        mock_tool_retriever.reset_mock()

        # Simulate retry scenarios (5 retries) - should use cached tools
        for retry_num in range(1, 6):
            plan.steps[0].retry_count = retry_num
            plan.current_step_index = 0  # Reset to retry the same step

            result = supervisor_node(state)

            # Should NOT call tool retriever for retries
            mock_tool_retriever.invoke.assert_not_called()

        # Analyze cache metrics
        cache_metrics = get_tool_retriever_cache_metrics(time_window_hours=1)

        # Verify cache performance
        self.assertEqual(cache_metrics["total_executions"], 6)  # 1 new + 5 retries
        self.assertEqual(cache_metrics["new_retrievals"], 1)  # Only first call
        self.assertEqual(cache_metrics["cached_retrievals"], 5)  # All retries

        # Verify API reduction target (83.3% = 5/6 cache hits)
        api_reduction = cache_metrics["api_reduction_percentage"]
        self.assertGreaterEqual(
            api_reduction, 40.0, "API reduction should be at least 40%"
        )
        self.assertLessEqual(
            api_reduction, 100.0, "API reduction should not exceed 100%"
        )

        # For this specific test, we expect 83.3% reduction (5 cached out of 6 total)
        expected_reduction = (5 / 6) * 100
        self.assertAlmostEqual(api_reduction, expected_reduction, places=1)

    @patch("src.tools.tool_retriever.biomni_tool_retriever")
    def test_mixed_new_and_retry_scenarios(self, mock_tool_retriever):
        """Test API reduction with mix of new steps and retries."""
        mock_tool_retriever.invoke.return_value = "filtered tools"

        # Create plan with multiple steps
        plan = StructuredPlan(
            title="Mixed Scenario Test",
            thought="Testing mixed new and retry scenarios",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Step 1",
                    description="First step",
                    filtered_tools=None,
                ),
                PlanStep(
                    step_index=1,
                    agent_name="coder",
                    title="Step 2",
                    description="Second step",
                    filtered_tools=None,
                ),
            ],
        )

        # Execute each step once (new) + retry each step twice
        call_count = 0

        for step_index in range(2):
            plan.current_step_index = step_index

            # First execution (new step)
            state = State(messages=[], structured_plan=plan)
            supervisor_node(state)
            call_count += 1

            # Two retries per step
            for retry in range(1, 3):
                plan.steps[step_index].retry_count = retry
                supervisor_node(state)
                call_count += 1

        # Should have called tool retriever only for new steps (2 times)
        self.assertEqual(mock_tool_retriever.invoke.call_count, 2)

        # Analyze performance
        cache_metrics = get_tool_retriever_cache_metrics(time_window_hours=1)

        self.assertEqual(cache_metrics["total_executions"], 6)  # 2 new + 4 retries
        self.assertEqual(cache_metrics["new_retrievals"], 2)  # 2 new steps
        self.assertEqual(cache_metrics["cached_retrievals"], 4)  # 4 retries

        # Should achieve target reduction (66.7% = 4/6 cache hits)
        api_reduction = cache_metrics["api_reduction_percentage"]
        self.assertGreaterEqual(api_reduction, 40.0)
        expected_reduction = (4 / 6) * 100
        self.assertAlmostEqual(api_reduction, expected_reduction, places=1)

    @patch("src.tools.tool_retriever.biomni_tool_retriever")
    def test_cache_hit_rate_tracking(self, mock_tool_retriever):
        """Test accurate cache hit rate tracking."""
        mock_tool_retriever.invoke.return_value = "test tools"

        plan = StructuredPlan(
            title="Cache Hit Rate Test",
            thought="Testing cache hit rate tracking",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Test Step",
                    description="Step for cache testing",
                    filtered_tools=None,
                )
            ],
        )

        state = State(messages=[], structured_plan=plan)

        # Execute once (miss) then 9 times (hits) for 90% cache hit rate
        supervisor_node(state)  # Miss - tools retrieved

        for i in range(9):
            plan.steps[0].retry_count = i + 1
            supervisor_node(state)  # Hits - tools cached

        # Verify metrics
        cache_metrics = get_tool_retriever_cache_metrics(time_window_hours=1)

        self.assertEqual(cache_metrics["cache_hit_rate"], 0.9)  # 90% hit rate
        self.assertEqual(cache_metrics["api_reduction_percentage"], 90.0)
        self.assertEqual(mock_tool_retriever.invoke.call_count, 1)

    @patch("src.tools.tool_retriever.biomni_tool_retriever")
    def test_cache_persistence_across_different_steps(self, mock_tool_retriever):
        """Test that different steps maintain their own cached tools."""
        mock_tool_retriever.invoke.side_effect = [
            "tools for step 1",
            "tools for step 2",
        ]

        plan = StructuredPlan(
            title="Multi-Step Cache Test",
            thought="Testing cache persistence across steps",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Research Step",
                    description="Research task",
                    filtered_tools=None,
                ),
                PlanStep(
                    step_index=1,
                    agent_name="coder",
                    title="Coding Step",
                    description="Coding task",
                    filtered_tools=None,
                ),
            ],
        )

        state = State(messages=[], structured_plan=plan)

        # Execute step 1
        plan.current_step_index = 0
        supervisor_node(state)
        self.assertEqual(plan.steps[0].filtered_tools, "tools for step 1")

        # Execute step 2
        plan.current_step_index = 1
        supervisor_node(state)
        self.assertEqual(plan.steps[1].filtered_tools, "tools for step 2")

        # Retry step 1 - should use cached tools
        plan.current_step_index = 0
        plan.steps[0].retry_count = 1
        supervisor_node(state)
        self.assertEqual(plan.steps[0].filtered_tools, "tools for step 1")

        # Retry step 2 - should use cached tools
        plan.current_step_index = 1
        plan.steps[1].retry_count = 1
        supervisor_node(state)
        self.assertEqual(plan.steps[1].filtered_tools, "tools for step 2")

        # Should have called tool retriever only twice (once per step)
        self.assertEqual(mock_tool_retriever.invoke.call_count, 2)

    def test_performance_monitoring_integration(self):
        """Test integration with performance monitoring system."""
        # This test uses real performance tracker without mocking tool retriever
        # to verify end-to-end monitoring integration

        plan = StructuredPlan(
            title="Performance Integration Test",
            thought="Testing performance monitoring integration",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Monitored Step",
                    description="Step with performance monitoring",
                    filtered_tools="pre-cached tools",  # Pre-cache to test cache hit tracking
                )
            ],
        )

        state = State(messages=[], structured_plan=plan)

        # Execute step - should record cache hit metric
        supervisor_node(state)

        # Verify performance metrics were recorded
        cache_metrics = get_tool_retriever_cache_metrics(time_window_hours=1)

        self.assertEqual(cache_metrics["total_executions"], 1)
        self.assertEqual(cache_metrics["cached_retrievals"], 1)
        self.assertEqual(cache_metrics["new_retrievals"], 0)
        self.assertEqual(cache_metrics["cache_hit_rate"], 1.0)
        self.assertEqual(cache_metrics["api_reduction_percentage"], 100.0)


if __name__ == "__main__":
    unittest.main()
