import pytest
import json
from unittest.mock import patch, MagicMock
from src.graph.builder import build_graph
from src.graph.types import State as AgentState, StructuredPlan, PlanStep


@pytest.mark.asyncio
async def test_supervisor_decomposes_plan_and_executes_subtasks():
    """
    Test that the supervisor can decompose a plan using todo-md-mcp
    and then orchestrate the execution of the resulting sub-tasks.
    """
    # 1. Mock the todo-md-mcp server's response for plan decomposition
    decomposed_plan = {
        "todos": [
            {"id": "sub1", "text": "Sub-task 1", "completed": False},
            {"id": "sub2", "text": "Sub-task 2", "completed": False},
        ]
    }

    # 2. Define the initial high-level plan
    initial_plan = StructuredPlan(
        title="High-level plan",
        thought="This plan will be decomposed by the supervisor.",
        steps=[
            PlanStep(
                step_index=0,
                title="Decompose this step",
                description="Use todo-md-mcp to get sub-tasks.",
                agent_name="supervisor",
            )
        ],
    )

    initial_state = AgentState(
        structured_plan=initial_plan,
        messages=[("user", "Decompose the plan and execute it.")],
    )

    # 3. Patch the MCPToolManager and the agents
    with (
        patch("src.tools.mcp_tools.MCPToolManager") as mock_tool_manager,
        patch("src.graph.nodes.coder_agent") as mock_coder_agent,
        patch("src.graph.nodes.research_agent") as mock_research_agent,
        patch("src.graph.nodes.browser_agent") as mock_browser_agent,
    ):

        # Mock the list_todos tool to return the decomposed plan with proper name attribute
        mock_list_todos_tool = MagicMock()
        mock_list_todos_tool.name = "list_todos"  # Set the name attribute correctly
        mock_list_todos_tool.invoke.return_value = json.dumps(decomposed_plan)

        mock_tool_manager.return_value.get_all_tools.return_value = [
            mock_list_todos_tool
        ]

        # Mock the agents to simply complete the step
        def mock_agent_invoke(state):
            from langchain_core.messages import HumanMessage

            plan = state["structured_plan"]
            current_step = plan.get_current_step()
            if current_step:
                current_step.status = "completed"
                plan.advance_to_next_step()
            return {
                "structured_plan": plan,
                "messages": [HumanMessage(content="Task completed successfully")],
            }

        mock_coder_agent.invoke.side_effect = mock_agent_invoke
        mock_research_agent.invoke.side_effect = mock_agent_invoke
        mock_browser_agent.invoke.side_effect = mock_agent_invoke

        # 4. Build and run the graph, starting from the supervisor
        graph = build_graph(start_node="supervisor")
        final_state = None

        # Collect all events to analyze the final state
        events = []
        async for event in graph.astream(initial_state):
            events.append(event)

        # The final state should be from the last event
        if events:
            final_state = events[-1]

        # 5. Assertions
        assert final_state is not None

        # Extract the updated plan from the final state
        # The state should contain the structured_plan
        updated_plan = None
        for event in events:
            if isinstance(event, dict):
                for node_name, node_output in event.items():
                    if (
                        isinstance(node_output, dict)
                        and "structured_plan" in node_output
                    ):
                        updated_plan = node_output["structured_plan"]
                        break

        assert updated_plan is not None, f"No structured_plan found in events: {events}"

        # Check that the plan was updated with the sub-tasks
        # The system automatically adds a final reporting step, so we expect 3 steps total
        assert (
            len(updated_plan.steps) == 3
        ), f"Expected 3 steps (2 decomposed + 1 report), got {len(updated_plan.steps)}"
        assert updated_plan.steps[0].title == "Sub-task 1"
        assert updated_plan.steps[1].title == "Sub-task 2"
        assert updated_plan.steps[2].title == "Generate Final Report"

        # Check that the sub-tasks were marked as completed
        assert updated_plan.steps[0].status == "completed"
        assert updated_plan.steps[1].status == "completed"
        assert updated_plan.steps[2].status == "completed"
