import unittest
from unittest.mock import patch, MagicMock
from cli import run_analysis


class TestCli(unittest.TestCase):

    @patch("cli.requests.post")
    @patch("builtins.input")
    @patch("cli.console.print")
    def test_run_analysis_success(self, mock_print, mock_input, mock_post):
        # Arrange
        mock_input.side_effect = ["test input", "test/dir"]

        mock_response = MagicMock()
        mock_response.json.return_value = {"task_id": "12345"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        # Act
        run_analysis()

        # Assert
        mock_post.assert_called_once_with(
            "http://localhost:8000/agent",
            json={"input": "test input", "dir": "test/dir"},
        )
        mock_print.assert_called_with(
            "[green]Successfully started analysis. Task ID: 12345[/green]"
        )

    @patch("cli.requests.post")
    @patch("builtins.input")
    @patch("cli.console.print")
    def test_run_analysis_failure(self, mock_print, mock_input, mock_post):
        # Arrange
        import requests

        mock_input.side_effect = ["test input", "test/dir"]
        mock_post.side_effect = requests.exceptions.RequestException("Test error")

        # Act
        run_analysis()

        # Assert
        mock_print.assert_called_with("[red]Error starting analysis: Test error[/red]")


if __name__ == "__main__":
    unittest.main()
