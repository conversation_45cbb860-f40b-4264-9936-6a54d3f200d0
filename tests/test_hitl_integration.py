"""
Integration tests for HITL (Human-in-the-Loop) functionality.

These tests verify that the HITL system works correctly with 
the existing SQLite checkpointing system and can properly 
persist and resume interrupted workflows.
"""

import pytest
import asyncio
import uuid
import tempfile
import os
from unittest.mock import patch, MagicMock

# Import the modules we need to test
from src.config.hitl_config import H<PERSON><PERSON><PERSON>, get_hitl_config, set_hitl_mode
from src.graph.hitl_handlers import HITLInterruptHandler
from src.service.workflow_service import initialize_graph, handle_hitl_resumption
from src.utils.hitl_setup import HITLWorkflowConfigurator
from src.graph.types import State, PlanStep, StructuredPlan


class TestHITLIntegration:
    """Test suite for HITL integration with LangGraph checkpointing."""
    
    @pytest.fixture
    async def test_graph(self):
        """Create a test graph with temporary SQLite checkpointing."""
        # Use temporary database for testing
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as temp_db:
            temp_db_path = temp_db.name
        
        # Mock environment variable for test database
        with patch.dict(os.environ, {"SQLITE_DB_PATH": temp_db_path}):
            graph = await initialize_graph()
            yield graph
        
        # Clean up temporary database
        try:
            os.unlink(temp_db_path)
        except FileNotFoundError:
            pass
    
    @pytest.fixture
    def test_thread_id(self):
        """Generate a unique thread ID for testing."""
        return f"test_thread_{uuid.uuid4()}"
    
    @pytest.fixture
    def mock_plan_step(self):
        """Create a mock plan step for testing."""
        return PlanStep(
            step_index=0,
            agent_name="coder",
            title="Test Code Execution",
            description="Execute test code for validation"
        )
    
    @pytest.fixture
    def mock_structured_plan(self, mock_plan_step):
        """Create a mock structured plan for testing."""
        return StructuredPlan(
            title="Test Analysis Plan",
            thought="This is a test plan for validation",
            steps=[mock_plan_step]
        )
    
    def test_hitl_config_initialization(self):
        """Test that HITL configuration initializes correctly."""
        config = get_hitl_config()
        
        assert config is not None
        assert config.mode in [HITLMode.FULL_MANUAL, HITLMode.SEMI_AUTONOMOUS, HITLMode.FULLY_AUTONOMOUS]
        assert config.trigger_config is not None
        assert config.risk_config is not None
        assert config.interrupt_config is not None
    
    def test_hitl_mode_switching(self):
        """Test switching between different HITL modes."""
        original_mode = get_hitl_config().mode
        
        try:
            # Test switching to full manual
            set_hitl_mode(HITLMode.FULL_MANUAL)
            assert get_hitl_config().mode == HITLMode.FULL_MANUAL
            
            # Test switching to semi-autonomous
            set_hitl_mode(HITLMode.SEMI_AUTONOMOUS)
            assert get_hitl_config().mode == HITLMode.SEMI_AUTONOMOUS
            
            # Test switching to fully autonomous
            set_hitl_mode(HITLMode.FULLY_AUTONOMOUS)
            assert get_hitl_config().mode == HITLMode.FULLY_AUTONOMOUS
            
        finally:
            # Restore original mode
            set_hitl_mode(original_mode)
    
    def test_interrupt_handler_initialization(self):
        """Test that interrupt handler initializes correctly."""
        handler = HITLInterruptHandler()
        
        assert handler is not None
        assert handler.config is not None
    
    def test_should_interrupt_logic(self, mock_plan_step):
        """Test the logic for determining when to interrupt."""
        handler = HITLInterruptHandler()
        
        # Create a mock state using State object
        from src.graph.types import State
        mock_state = State(
            hitl_mode=HITLMode.FULL_MANUAL.value,
            structured_plan=None
        )
        
        # Test with different trigger types
        from src.config.hitl_config import InterruptTrigger
        
        # In full manual mode, should interrupt for state transitions  
        should_interrupt = handler.should_interrupt_for_step(
            mock_state, mock_plan_step, InterruptTrigger.STATE_TRANSITION
        )
        assert should_interrupt == True  # Full manual mode should always interrupt
        
        # Test with fully autonomous mode
        autonomous_state = State(
            hitl_mode=HITLMode.FULLY_AUTONOMOUS.value,
            structured_plan=None
        )
        should_interrupt = handler.should_interrupt_for_step(
            autonomous_state, mock_plan_step, InterruptTrigger.STATE_TRANSITION
        )
        assert should_interrupt == False  # Fully autonomous should never interrupt
    
    def test_risk_assessment(self, mock_plan_step):
        """Test risk assessment functionality."""
        handler = HITLInterruptHandler()
        
        # Test tool risk assessment
        low_risk_tool = handler.config.assess_risk(
            agent_name="researcher", 
            tool_name="query_uniprot"
        )
        assert low_risk_tool.name in ["LOW", "MEDIUM"]  # Should be low or medium risk
        
        high_risk_tool = handler.config.assess_risk(
            agent_name="coder",
            tool_name="execute_command"
        )
        assert high_risk_tool.name in ["MEDIUM", "HIGH"]  # Should be medium or high risk
    
    @pytest.mark.asyncio
    async def test_workflow_configurator(self, test_thread_id):
        """Test the workflow configurator utility."""
        configurator = HITLWorkflowConfigurator()
        
        # Test development configuration
        dev_config = await configurator.configure_for_development()
        assert dev_config["hitl_mode"] == HITLMode.FULL_MANUAL.value
        
        # Test production configuration
        prod_config = await configurator.configure_for_production()
        assert prod_config["hitl_mode"] == HITLMode.SEMI_AUTONOMOUS.value
        
        # Test automation configuration
        auto_config = await configurator.configure_for_automation()
        assert auto_config["hitl_mode"] == HITLMode.FULLY_AUTONOMOUS.value
    
    @pytest.mark.asyncio
    async def test_state_persistence(self, test_graph, test_thread_id):
        """Test that HITL state is properly persisted in SQLite."""
        config = {"configurable": {"thread_id": test_thread_id}}
        
        # Initialize state with HITL data
        initial_state = {
            "hitl_mode": HITLMode.SEMI_AUTONOMOUS.value,
            "awaiting_human_input": False,
            "interrupt_context": None,
            "human_feedback": None,
            "interrupt_history": []
        }
        
        # Update state in graph
        await test_graph.aupdate_state(config, initial_state, as_node="__start__")
        
        # Retrieve state and verify persistence
        current_state = await test_graph.aget_state(config)
        assert current_state is not None
        assert current_state.values["hitl_mode"] == HITLMode.SEMI_AUTONOMOUS.value
        assert current_state.values["awaiting_human_input"] == False
        assert current_state.values["interrupt_history"] == []
    
    @pytest.mark.asyncio 
    async def test_interrupt_state_persistence(self, test_graph, test_thread_id):
        """Test that interrupt state is properly persisted."""
        config = {"configurable": {"thread_id": test_thread_id}}
        
        # Simulate an interrupt state
        interrupt_payload = {
            "action_description": "Test interrupt",
            "requires_approval": True,
            "risk_level": "medium"
        }
        
        interrupt_state = {
            "hitl_mode": HITLMode.FULL_MANUAL.value,
            "awaiting_human_input": True,
            "interrupt_context": interrupt_payload,
            "human_feedback": None,
            "interrupt_history": []
        }
        
        # Update state
        await test_graph.aupdate_state(config, interrupt_state, as_node="__start__")
        
        # Verify persistence
        current_state = await test_graph.aget_state(config)
        assert current_state.values["awaiting_human_input"] == True
        assert current_state.values["interrupt_context"] == interrupt_payload
    
    @pytest.mark.asyncio
    async def test_resumption_after_persistence(self, test_graph, test_thread_id):
        """Test that workflows can be resumed after being persisted."""
        config = {"configurable": {"thread_id": test_thread_id}}
        
        # Set up interrupted state
        interrupt_payload = {
            "action_description": "Test resumption",
            "requires_approval": True
        }
        
        interrupt_state = {
            "hitl_mode": HITLMode.FULL_MANUAL.value,
            "awaiting_human_input": True,
            "interrupt_context": interrupt_payload,
            "interrupt_history": []
        }
        
        await test_graph.aupdate_state(config, interrupt_state, as_node="__start__")
        
        # Simulate human response
        human_response = {
            "action": "approve",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        # Test resumption (mock the actual resumption since it requires full workflow)
        try:
            async for event in handle_hitl_resumption(test_thread_id, human_response):
                # Just verify that the function can be called without errors
                break
        except Exception as e:
            # Expected since we don't have a full workflow running or proper LLM context
            expected_errors = [
                "No state found",
                "not waiting for human input", 
                "Ambiguous update",
                "contents is not specified",  # Gemini API error
                "Invalid argument provided to Gemini"  # Gemini API error
            ]
            assert any(error in str(e) for error in expected_errors)
    
    def test_interrupt_payload_creation(self, mock_plan_step, mock_structured_plan):
        """Test creation of interrupt payloads."""
        handler = HITLInterruptHandler()
        
        from src.graph.types import State
        mock_state = State(
            structured_plan=mock_structured_plan,
            hitl_mode=HITLMode.SEMI_AUTONOMOUS.value
        )
        
        from src.config.hitl_config import InterruptTrigger
        
        payload = handler.create_interrupt_for_step(
            mock_state,
            mock_plan_step,
            InterruptTrigger.TOOL_EXECUTION,
            "Test action description"
        )
        
        assert payload is not None
        assert "action_description" in payload
        assert "step_context" in payload
        assert "plan_context" in payload
        assert payload["action_description"] == "Test action description"
    
    @pytest.mark.asyncio
    async def test_full_hitl_workflow_simulation(self, test_graph, test_thread_id):
        """Test a complete HITL workflow simulation."""
        config = {"configurable": {"thread_id": test_thread_id}}
        
        # Phase 1: Initialize workflow with HITL
        initial_state = {
            "hitl_mode": HITLMode.SEMI_AUTONOMOUS.value,
            "awaiting_human_input": False,
            "interrupt_history": []
        }
        
        await test_graph.aupdate_state(config, initial_state, as_node="__start__")
        
        # Phase 2: Simulate interrupt
        interrupt_payload = {
            "action_description": "Execute bioinformatics analysis",
            "risk_level": "high",
            "requires_approval": True
        }
        
        interrupted_state = initial_state.copy()
        interrupted_state.update({
            "awaiting_human_input": True,
            "interrupt_context": interrupt_payload
        })
        
        await test_graph.aupdate_state(config, interrupted_state, as_node="__start__")
        
        # Phase 3: Verify interrupt state persisted
        current_state = await test_graph.aget_state(config)
        assert current_state.values["awaiting_human_input"] == True
        assert current_state.values["interrupt_context"]["action_description"] == "Execute bioinformatics analysis"
        
        # Phase 4: Simulate human response
        human_response = {
            "action": "approve",
            "timestamp": "2024-01-01T00:00:00Z",
            "feedback": "Approved for execution"
        }
        
        resumed_state = interrupted_state.copy()
        resumed_state.update({
            "awaiting_human_input": False,
            "interrupt_context": None,
            "human_feedback": human_response,
            "interrupt_history": [{
                "timestamp": human_response["timestamp"],
                "interrupt_payload": interrupt_payload,
                "human_response": human_response
            }]
        })
        
        await test_graph.aupdate_state(config, resumed_state, as_node="__start__")
        
        # Phase 5: Verify resumption state persisted
        final_state = await test_graph.aget_state(config)
        assert final_state.values["awaiting_human_input"] == False
        assert final_state.values["human_feedback"]["action"] == "approve"
        assert len(final_state.values["interrupt_history"]) == 1


# Additional integration tests for specific scenarios
class TestHITLBioinformaticsScenarios:
    """Test HITL functionality in bioinformatics-specific scenarios."""
    
    def test_high_sensitivity_data_configuration(self):
        """Test configuration for high-sensitivity bioinformatics data."""
        handler = HITLInterruptHandler()
        
        # Simulate high-sensitivity context
        sensitive_context = {
            "data_type": "patient_genomic_data",
            "contains_pii": True,
            "regulatory_compliance": "HIPAA"
        }
        
        risk_level = handler.config.assess_risk(
            agent_name="coder",
            tool_name="process_genomic_data",
            step_context=sensitive_context
        )
        
        # Should be medium or high risk due to sensitive data
        assert risk_level.name in ["MEDIUM", "HIGH", "CRITICAL"]
    
    def test_low_risk_query_operations(self):
        """Test that low-risk query operations are handled appropriately."""
        handler = HITLInterruptHandler()
        
        # Simulate low-risk query context
        query_context = {
            "operation_type": "database_query",
            "data_access": "read_only",
            "public_database": True
        }
        
        risk_level = handler.config.assess_risk(
            agent_name="researcher",
            tool_name="query_uniprot",
            step_context=query_context
        )
        
        # Should be low or medium risk
        assert risk_level.name in ["LOW", "MEDIUM"]


if __name__ == "__main__":
    # Run tests directly if executed as a script
    pytest.main([__file__, "-v"])