import unittest
from unittest.mock import patch, MagicMock
from src.graph.nodes import (
    planner_node,
    supervisor_node,
    execution_evaluator_node,
    _get_available_tools_for_planning,
    _inject_tools_context_into_messages,
)
from src.graph.types import State, StructuredPlan, PlanStep
from src.graph.builder import build_graph
from langchain_core.messages import HumanMessage


class TestGraph(unittest.TestCase):

    def test_state_initialization(self):
        """Test that the State object initializes correctly."""
        state = State(messages=[])
        self.assertEqual(state["messages"], [])
        self.assertIsNone(state.get("structured_plan"))

    def test_structured_plan_creation(self):
        """Test the creation of a StructuredPlan."""
        plan = StructuredPlan(
            title="Test Plan",
            thought="This is a test plan.",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="TestAgent",
                    title="Step 1",
                    description="First step",
                    requires_evaluation=False,
                )
            ],
        )
        self.assertEqual(plan.title, "Test Plan")
        self.assertEqual(len(plan.steps), 1)
        self.assertEqual(plan.steps[0].title, "Step 1")

    def test_graph_build(self):
        """Test that the graph is built without errors."""
        graph = build_graph()
        self.assertIsNotNone(graph)
        # Check for a few expected nodes
        expected_nodes = [
            "planner",
            "supervisor",
            "researcher",
            "coder",
            "browser",
            "reporter",
            "execution_evaluator",
        ]
        for node in expected_nodes:
            self.assertIn(node, graph.nodes)

    @patch("src.graph.nodes.get_llm_by_type")
    @patch("src.tools.tool_retriever.list_all_available_tools")
    def test_planner_node(self, mock_list_tools, mock_get_llm):
        """Test the planner_node functionality."""
        # Mock the tool listing response
        mock_list_tools.invoke.return_value = (
            "## Available Tools\n1. **test_tool**: A test tool for testing"
        )

        # Mock the LLM response
        mock_llm = MagicMock()
        mock_response = MagicMock()
        mock_response.content = """
```json
{
  "thought": "The user wants to create a simple plot. I will use the coder agent to write and execute python code to generate the plot.",
  "title": "Create a simple plot",
  "steps": [
    {
      "agent_name": "coder",
      "title": "Generate plot",
      "description": "Write a python script to generate a simple plot and save it as plot.png"
    }
  ]
}
```
"""
        mock_llm_with_tools = MagicMock()
        mock_llm_with_tools.invoke.return_value = mock_response
        mock_llm.bind_tools.return_value = mock_llm_with_tools
        mock_get_llm.return_value = mock_llm

        # Initial state
        initial_state = State(messages=[("user", "create a simple plot")])

        # Execute the planner node
        result_state = planner_node(initial_state)

        # Assertions
        self.assertIn("structured_plan", result_state)
        plan = result_state["structured_plan"]
        self.assertIsInstance(plan, StructuredPlan)
        self.assertEqual(plan.title, "Create a simple plot")
        self.assertEqual(len(plan.steps), 1)
        self.assertEqual(plan.steps[0].agent_name, "coder")
        self.assertEqual(plan.steps[0].title, "Generate plot")

        # Verify that list_all_available_tools was called
        mock_list_tools.invoke.assert_called_once_with({})

        # Verify that bind_tools was called with the correct tool
        mock_llm.bind_tools.assert_called_once()
        args, kwargs = mock_llm.bind_tools.call_args
        self.assertEqual(len(args[0]), 1)  # Should have one tool

    @patch("src.graph.nodes.get_llm_by_type")
    @patch("src.tools.tool_retriever.list_all_available_tools")
    def test_planner_node_tool_error_handling(self, mock_list_tools, mock_get_llm):
        """Test planner_node handles tool retrieval errors gracefully."""
        # Mock tool listing to raise an exception
        mock_list_tools.invoke.side_effect = Exception("Tool retrieval failed")

        # Mock the LLM response
        mock_llm = MagicMock()
        mock_response = MagicMock()
        mock_response.content = """
```json
{
  "thought": "Testing error handling",
  "title": "Test Plan",
  "steps": [
    {
      "agent_name": "coder",
      "title": "Test step",
      "description": "A test step"
    }
  ]
}
```
"""
        mock_llm_with_tools = MagicMock()
        mock_llm_with_tools.invoke.return_value = mock_response
        mock_llm.bind_tools.return_value = mock_llm_with_tools
        mock_get_llm.return_value = mock_llm

        # Initial state
        initial_state = State(messages=[("user", "test")])

        # Execute the planner node
        result_state = planner_node(initial_state)

        # Should still create a plan despite tool error
        self.assertIn("structured_plan", result_state)
        plan = result_state["structured_plan"]
        self.assertIsInstance(plan, StructuredPlan)
        self.assertEqual(plan.title, "Test Plan")

        # Verify that list_all_available_tools was attempted
        mock_list_tools.invoke.assert_called_once_with({})

    @patch("src.tools.tool_retriever.list_all_available_tools")
    def test_get_available_tools_for_planning(self, mock_list_tools):
        """Test the helper function for retrieving available tools."""
        # Test successful retrieval
        mock_list_tools.invoke.return_value = "Available tools list"
        result = _get_available_tools_for_planning()
        self.assertEqual(result, "Available tools list")
        mock_list_tools.invoke.assert_called_once_with({})

        # Test error handling
        mock_list_tools.invoke.side_effect = Exception("Test error")
        result = _get_available_tools_for_planning()
        self.assertEqual(result, "No tools available due to retrieval error.")

    def test_inject_tools_context_into_messages(self):
        """Test the helper function for injecting tools context."""
        # Test with valid message having content attribute
        mock_message = MagicMock()
        mock_message.content = "Original content"
        messages = [mock_message]
        tools = "Available tools"

        _inject_tools_context_into_messages(messages, tools)

        expected_content = "Original content\n\nAvailable Tools:\nAvailable tools"
        self.assertEqual(mock_message.content, expected_content)

        # Test with empty messages
        _inject_tools_context_into_messages([], "tools")
        # Should not raise any errors

        # Test with empty tools
        messages = [mock_message]
        mock_message.content = "Original"
        _inject_tools_context_into_messages(messages, "")
        self.assertEqual(mock_message.content, "Original")  # Should remain unchanged

    @patch("src.tools.performance_monitor.record_execution_metric")
    @patch("src.tools.tool_retriever.biomni_tool_retriever")
    def test_supervisor_node_tool_caching(
        self, mock_tool_retriever, mock_record_metric
    ):
        """Test the supervisor_node tool caching optimization."""
        # Mock tool retriever response
        mock_tool_retriever.invoke.return_value = "mocked filtered tools"

        # Test 1: New step without cached tools - should call tool retriever
        plan_new_step = StructuredPlan(
            title="Test Plan",
            thought="This is a test plan.",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Research Task",
                    description="Research something new",
                    filtered_tools=None,  # No cached tools
                )
            ],
        )
        state_new = State(messages=[], structured_plan=plan_new_step)

        result_new = supervisor_node(state_new)

        # Verify tool retriever was called for new step
        mock_tool_retriever.invoke.assert_called_once_with(
            {
                "query": "Research Task Research something new",
                "include_mcp_tools": True,
                "include_biomni_tools": True,
            }
        )

        # Verify performance metric was recorded for new retrieval
        mock_record_metric.assert_called()
        call_args = mock_record_metric.call_args[1]
        self.assertEqual(call_args["tool_name"], "biomni_tool_retriever")
        self.assertEqual(call_args["strategy_used"], "new_step_retrieval")
        self.assertTrue(call_args["success"])

        # Verify tools were cached in the step
        self.assertEqual(plan_new_step.steps[0].filtered_tools, "mocked filtered tools")

        # Test 2: Step with cached tools - should NOT call tool retriever again
        mock_tool_retriever.reset_mock()
        mock_record_metric.reset_mock()

        plan_cached_step = StructuredPlan(
            title="Test Plan",
            thought="This is a test plan.",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Research Task",
                    description="Research something cached",
                    filtered_tools="previously cached tools",  # Already has cached tools
                )
            ],
        )
        state_cached = State(messages=[], structured_plan=plan_cached_step)

        result_cached = supervisor_node(state_cached)

        # Verify tool retriever was NOT called for cached step
        mock_tool_retriever.invoke.assert_not_called()

        # Verify cache hit metric was recorded
        mock_record_metric.assert_called_once()
        call_args = mock_record_metric.call_args[1]
        self.assertEqual(call_args["tool_name"], "biomni_tool_retriever")
        self.assertEqual(call_args["strategy_used"], "cached_retrieval")
        self.assertTrue(call_args["success"])
        self.assertEqual(call_args["execution_time"], 0.001)  # Minimal cache hit time

        # Verify cached tools were preserved
        self.assertEqual(
            plan_cached_step.steps[0].filtered_tools, "previously cached tools"
        )

    @patch("src.tools.performance_monitor.record_execution_metric")
    @patch("src.tools.tool_retriever.biomni_tool_retriever")
    def test_supervisor_node_tool_retrieval_error_handling(
        self, mock_tool_retriever, mock_record_metric
    ):
        """Test supervisor_node handles tool retrieval errors gracefully."""
        # Mock tool retriever to raise an exception
        mock_tool_retriever.invoke.side_effect = Exception("Tool retrieval failed")

        plan = StructuredPlan(
            title="Test Plan",
            thought="This is a test plan.",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Research Task",
                    description="Research something",
                    filtered_tools=None,
                )
            ],
        )
        state = State(messages=[], structured_plan=plan)

        # Should not raise exception
        result = supervisor_node(state)

        # Verify error metric was recorded
        mock_record_metric.assert_called()
        call_args = mock_record_metric.call_args[1]
        self.assertEqual(call_args["tool_name"], "biomni_tool_retriever")
        self.assertEqual(call_args["strategy_used"], "new_step_retrieval")
        self.assertFalse(call_args["success"])
        self.assertEqual(call_args["error_message"], "Tool retrieval failed")

        # Verify filtered_tools is set to None on error
        self.assertIsNone(plan.steps[0].filtered_tools)

    def test_supervisor_node_routing(self):
        """Test the supervisor_node routing logic with Command pattern."""
        from langgraph.graph import Command, END

        # Test routing to researcher
        plan_research = StructuredPlan(
            title="Test Plan",
            thought="This is a test plan.",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="researcher",
                    title="Research",
                    description="Research description",
                    filtered_tools="cached tools",  # Pre-cached to avoid tool retrieval
                )
            ],
        )
        state_research = State(messages=[], structured_plan=plan_research)
        result_research = supervisor_node(state_research)
        self.assertIsInstance(result_research, Command)
        self.assertEqual(result_research.goto, "researcher")

        # Test ending the workflow when the plan is complete
        plan_done = StructuredPlan(
            title="Test Plan", thought="This is a test plan.", steps=[]
        )
        plan_done.status = "completed"
        state_done = State(messages=[], structured_plan=plan_done)
        result_done = supervisor_node(state_done)
        self.assertIsInstance(result_done, Command)
        self.assertEqual(result_done.goto, "reporter")

        # Test handling of invalid agent name
        plan_invalid = StructuredPlan(
            title="Test Plan",
            thought="This is a test plan.",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="invalid_agent",
                    title="Invalid",
                    description="Invalid description",
                )
            ],
        )
        state_invalid = State(messages=[], structured_plan=plan_invalid)
        result_invalid = supervisor_node(state_invalid)
        self.assertIsInstance(result_invalid, Command)
        self.assertEqual(result_invalid.goto, END)

    @patch("src.graph.nodes.apply_prompt_template")
    @patch("src.agents.agents.execution_evaluator_agent")
    def test_execution_evaluator_node(self, mock_evaluator_agent, mock_apply_prompt):
        """Test the execution_evaluator_node functionality."""
        # Mock the agent's response
        mock_evaluator_agent.invoke.return_value = {
            "messages": [MagicMock(content="approved")]
        }
        mock_apply_prompt.return_value = [("user", "prompt")]

        # Create a plan with a step to evaluate
        plan = StructuredPlan(
            title="Test Plan",
            thought="This is a test plan.",
            steps=[
                PlanStep(
                    step_index=0,
                    agent_name="coder",
                    title="Code",
                    description="Code something",
                    results={"summary": "some output"},
                )
            ],
        )
        plan.current_step_index = 0
        initial_state = State(messages=[], structured_plan=plan)

        # Execute the node
        result_state = execution_evaluator_node(initial_state)

        # Assertions
        self.assertEqual(result_state["structured_plan"].steps[0].status, "completed")
        self.assertEqual(result_state["structured_plan"].current_step_index, -1)

        # Test rejection case
        mock_evaluator_agent.invoke.return_value = {
            "messages": [MagicMock(content="rejected")]
        }
        plan.steps[0].status = "pending"
        plan.current_step_index = 0
        result_state = execution_evaluator_node(initial_state)
        self.assertEqual(result_state["structured_plan"].steps[0].status, "pending")
        self.assertEqual(result_state["structured_plan"].steps[0].retry_count, 1)


if __name__ == "__main__":
    unittest.main()
