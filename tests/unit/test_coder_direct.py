#!/usr/bin/env python3
"""
Test script to verify the coder agent functionality directly.
"""

import os
import logging
from langchain_core.messages import HumanMessage
from src.agents.agents import coder_agent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_coder_agent_direct():
    """Test the coder agent directly by asking it to create a simple Python script."""
    print("Testing coder agent directly...")

    try:
        # Create a message for the coder agent
        message = HumanMessage(
            content="Create a simple Python script that prints 'Hello World' and save it to hello_world.py"
        )

        print(f"Sending message to coder agent: {message.content}")

        # Invoke the coder agent
        print("\nInvoking coder agent...")
        response = coder_agent.invoke({"messages": [message]})

        print(f"Coder agent response: {response}")

        # Check if the file was created
        expected_files = [
            "hello_world.py",
            "./hello_world.py",
            "./workspace/hello_world.py",
        ]
        file_found = False

        for file_path in expected_files:
            if os.path.exists(file_path):
                print(f"✓ Found created file: {file_path}")

                # Check file content
                with open(file_path, "r") as f:
                    content = f.read()
                    print(f"File content: {content}")

                if "Hello World" in content:
                    print("✓ File contains expected content")
                    file_found = True
                    break
                else:
                    print("✗ File does not contain expected content")

        if not file_found:
            print("✗ No hello_world.py file found in expected locations")
            return False

        print("✓ Coder agent direct test completed successfully")
        return True

    except Exception as e:
        print(f"✗ Error in coder agent direct test: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_coder_agent_direct()
    if success:
        print("\n✓ Coder agent direct test passed!")
    else:
        print("\n✗ Coder agent direct test failed!")
        exit(1)
