"""
Unit tests for message types defined in src.graph.messages.

Tests JSON serialization, deserialization, and ag-ui protocol compliance
for all message types defined in the messages module.
"""

import json
import pytest
from datetime import datetime, UTC
from src.graph.messages import (
    BaseMessage,
    FinalAnswerMessage,
    AgentStepMessage,
    ToolOutputMessage,
    ToDoListUpdateMessage,
    create_message_from_dict,
    get_message_type_name,
)


class TestBaseMessage:
    """Test the BaseMessage class functionality."""

    def test_base_message_creation(self):
        """Test basic message creation with defaults."""
        message = BaseMessage(role="test")

        assert message.role == "test"
        assert message.id is not None
        assert message.timestamp is not None
        assert message.content is None
        assert message.name is None

    def test_base_message_json_serialization(self):
        """Test that BaseMessage can be serialized to JSON."""
        message = BaseMessage(role="test", content="Test content", name="test_sender")

        # Serialize to JSON
        json_str = message.model_dump_json()
        json_data = json.loads(json_str)

        # Verify required fields
        assert json_data["role"] == "test"
        assert json_data["content"] == "Test content"
        assert json_data["name"] == "test_sender"
        assert "id" in json_data
        assert "timestamp" in json_data

    def test_base_message_round_trip_serialization(self):
        """Test round-trip JSON serialization."""
        original = BaseMessage(role="test", content="Test content", name="test_sender")

        # Serialize and deserialize
        json_str = original.model_dump_json()
        restored = BaseMessage.model_validate_json(json_str)

        assert restored.role == original.role
        assert restored.content == original.content
        assert restored.name == original.name
        assert restored.id == original.id


class TestFinalAnswerMessage:
    """Test the FinalAnswerMessage class."""

    def test_final_answer_message_creation(self):
        """Test FinalAnswerMessage creation with required fields."""
        message = FinalAnswerMessage(
            content="This is the final answer to your query.",
            agent_name="reporter",
            confidence_level=0.95,
        )

        assert message.role == "assistant"
        assert message.content == "This is the final answer to your query."
        assert message.agent_name == "reporter"
        assert message.confidence_level == 0.95
        assert message.supporting_evidence == []

    def test_final_answer_message_json_serialization(self):
        """Test JSON serialization with all fields."""
        message = FinalAnswerMessage(
            content="Final analysis complete.",
            agent_name="reporter",
            analysis_summary="Comprehensive data analysis performed",
            confidence_level=0.88,
            supporting_evidence=["Source 1", "Source 2", "Analysis results"],
        )

        json_str = message.model_dump_json()
        json_data = json.loads(json_str)

        assert json_data["role"] == "assistant"
        assert json_data["content"] == "Final analysis complete."
        assert json_data["agent_name"] == "reporter"
        assert json_data["confidence_level"] == 0.88
        assert len(json_data["supporting_evidence"]) == 3


class TestAgentStepMessage:
    """Test the AgentStepMessage class."""

    def test_agent_step_message_creation(self):
        """Test AgentStepMessage creation."""
        message = AgentStepMessage(
            agent_name="researcher",
            step_description="Gathering data from external sources",
            step_status="in_progress",
            step_index=2,
        )

        assert message.role == "agent"
        assert message.agent_name == "researcher"
        assert message.step_description == "Gathering data from external sources"
        assert message.step_status == "in_progress"
        assert message.step_index == 2
        assert message.results == {}

    def test_agent_step_message_with_results(self):
        """Test AgentStepMessage with results and error handling."""
        results = {
            "documents_found": 15,
            "sources": ["PubMed", "ArXiv"],
            "execution_time": "45.2s",
        }

        message = AgentStepMessage(
            agent_name="coder",
            step_description="Executing analysis script",
            step_status="completed",
            results=results,
            duration_ms=45200,
        )

        json_str = message.model_dump_json()
        json_data = json.loads(json_str)

        assert json_data["role"] == "agent"
        assert json_data["results"]["documents_found"] == 15
        assert json_data["duration_ms"] == 45200


class TestToolOutputMessage:
    """Test the ToolOutputMessage class."""

    def test_tool_output_message_creation(self):
        """Test ToolOutputMessage creation with required tool_name field."""
        message = ToolOutputMessage(
            tool_name="web_search",
            execution_status="success",
            tool_output="Search completed successfully",
            tool_call_id="call_123",
        )

        assert message.role == "tool"
        assert message.tool_name == "web_search"  # Required field from AC
        assert message.execution_status == "success"
        assert message.tool_call_id == "call_123"
        assert message.tool_input == {}

    def test_tool_output_message_with_error(self):
        """Test ToolOutputMessage with error scenario."""
        tool_input = {"query": "machine learning", "max_results": 10}

        message = ToolOutputMessage(
            tool_name="database_query",
            tool_input=tool_input,
            execution_status="error",
            error_details="Connection timeout after 30 seconds",
            execution_time_ms=30000,
        )

        json_str = message.model_dump_json()
        json_data = json.loads(json_str)

        assert json_data["tool_name"] == "database_query"
        assert json_data["execution_status"] == "error"
        assert json_data["tool_input"]["query"] == "machine learning"
        assert json_data["error_details"] == "Connection timeout after 30 seconds"


class TestToDoListUpdateMessage:
    """Test the ToDoListUpdateMessage class."""

    def test_todo_list_update_message_creation(self):
        """Test ToDoListUpdateMessage creation."""
        message = ToDoListUpdateMessage(
            action_type="add",
            todo_title="Analyze protein sequences",
            todo_status="pending",
            todo_priority="high",
            list_name="bioinformatics_tasks",
        )

        assert message.role == "system"
        assert message.action_type == "add"
        assert message.todo_title == "Analyze protein sequences"
        assert message.todo_priority == "high"
        assert message.metadata == {}

    def test_todo_list_update_with_metadata(self):
        """Test ToDoListUpdateMessage with metadata."""
        metadata = {
            "assigned_agent": "researcher",
            "estimated_duration": "2 hours",
            "dependencies": ["task_1", "task_2"],
        }

        message = ToDoListUpdateMessage(
            action_type="update",
            todo_id="task_456",
            todo_status="completed",
            metadata=metadata,
        )

        json_str = message.model_dump_json()
        json_data = json.loads(json_str)

        assert json_data["action_type"] == "update"
        assert json_data["todo_id"] == "task_456"
        assert json_data["metadata"]["assigned_agent"] == "researcher"
        assert len(json_data["metadata"]["dependencies"]) == 2


class TestMessageJSONCompliance:
    """Test overall JSON serialization compliance for all message types."""

    def test_all_message_types_json_serializable(self):
        """Test that all message types can be serialized to valid JSON."""
        messages = [
            BaseMessage(role="test", content="Test message"),
            FinalAnswerMessage(content="Final answer", confidence_level=0.9),
            AgentStepMessage(
                agent_name="test_agent",
                step_description="Test step",
                step_status="completed",
            ),
            ToolOutputMessage(
                tool_name="test_tool", execution_status="success", tool_output="Success"
            ),
            ToDoListUpdateMessage(action_type="add", todo_title="Test todo"),
        ]

        for message in messages:
            # Should not raise an exception
            json_str = message.model_dump_json()

            # Should be valid JSON
            json_data = json.loads(json_str)

            # Should have required ag-ui protocol fields
            assert "id" in json_data
            assert "role" in json_data
            assert "timestamp" in json_data

    def test_message_validation_errors(self):
        """Test that invalid messages raise appropriate validation errors."""
        # Test missing required field
        with pytest.raises(ValueError):
            ToolOutputMessage()  # Missing required tool_name

        # Test invalid enum value
        with pytest.raises(ValueError):
            AgentStepMessage(
                agent_name="test",
                step_description="test",
                step_status="invalid_status",  # Invalid enum value
            )

        # Test invalid confidence level range
        with pytest.raises(ValueError):
            FinalAnswerMessage(
                content="test", confidence_level=1.5  # Should be 0.0 to 1.0
            )


class TestMessageFactory:
    """Test the message factory functions."""

    def test_create_message_from_dict_final_answer(self):
        """Test factory creates FinalAnswerMessage when appropriate."""
        data = {
            "role": "assistant",
            "content": "Final answer",
            "confidence_level": 0.9,
            "agent_name": "reporter",
        }

        message = create_message_from_dict(data)
        assert isinstance(message, FinalAnswerMessage)
        assert message.confidence_level == 0.9
        assert message.agent_name == "reporter"

    def test_create_message_from_dict_agent_step(self):
        """Test factory creates AgentStepMessage for agent role."""
        data = {
            "role": "agent",
            "agent_name": "researcher",
            "step_description": "Analyzing data",
            "step_status": "in_progress",
        }

        message = create_message_from_dict(data)
        assert isinstance(message, AgentStepMessage)
        assert message.agent_name == "researcher"
        assert message.step_status == "in_progress"

    def test_create_message_from_dict_tool_output(self):
        """Test factory creates ToolOutputMessage for tool role."""
        data = {
            "role": "tool",
            "tool_name": "web_search",
            "execution_status": "success",
        }

        message = create_message_from_dict(data)
        assert isinstance(message, ToolOutputMessage)
        assert message.tool_name == "web_search"
        assert message.execution_status == "success"

    def test_create_message_from_dict_todo_update(self):
        """Test factory creates ToDoListUpdateMessage when appropriate."""
        data = {"role": "system", "action_type": "add", "todo_title": "New task"}

        message = create_message_from_dict(data)
        assert isinstance(message, ToDoListUpdateMessage)
        assert message.action_type == "add"
        assert message.todo_title == "New task"

    def test_create_message_from_dict_base_message(self):
        """Test factory creates BaseMessage for unknown roles or simple cases."""
        data = {"role": "user", "content": "Hello world"}

        message = create_message_from_dict(data)
        assert isinstance(message, BaseMessage)
        assert message.role == "user"
        assert message.content == "Hello world"

    def test_get_message_type_name(self):
        """Test getting message type names."""
        messages = [
            BaseMessage(role="test"),
            FinalAnswerMessage(content="Final answer"),
            AgentStepMessage(
                agent_name="test", step_description="test", step_status="completed"
            ),
            ToolOutputMessage(tool_name="test", execution_status="success"),
            ToDoListUpdateMessage(action_type="add"),
        ]

        expected_names = [
            "BaseMessage",
            "FinalAnswerMessage",
            "AgentStepMessage",
            "ToolOutputMessage",
            "ToDoListUpdateMessage",
        ]

        for message, expected_name in zip(messages, expected_names):
            assert get_message_type_name(message) == expected_name


def test_message_imports():
    """Test that all required message types are properly exported."""
    from src.graph.messages import __all__

    expected_exports = [
        "BaseMessage",
        "FinalAnswerMessage",
        "AgentStepMessage",
        "ToolOutputMessage",
        "ToDoListUpdateMessage",
        "create_message_from_dict",
        "get_message_type_name",
    ]

    for expected in expected_exports:
        assert expected in __all__, f"{expected} not found in __all__ exports"
