import pytest
import os
import asyncio
from src.service import workflow_service


@pytest.fixture(scope="function", autouse=True)
async def reset_graph_state():
    """Resets the global graph and checkpointer before and after each test."""
    # Reset before the test
    workflow_service.graph = None
    workflow_service.checkpointer = None

    # Delete the database file to ensure a clean state
    db_path = os.path.join(os.getcwd(), "data", "checkpoints.db")
    if os.path.exists(db_path):
        os.remove(db_path)

    yield

    # Teardown after the test
    workflow_service.graph = None
    workflow_service.checkpointer = None
