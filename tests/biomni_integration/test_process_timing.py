#!/usr/bin/env python3
"""
Test process timing and output capture.
"""

import sys
import os

sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def test_process_timing():
    """Test process timing and output capture."""
    print("🧪 Testing Process Timing and Output Capture...")

    try:
        from src.tools.mcp_tools import MCPToolManager

        manager = MCPToolManager()
        tools = manager.get_all_tools()

        # Find the process tools
        start_process_tool = None
        read_process_tool = None

        for tool in tools:
            if tool.name == "start_process":
                start_process_tool = tool
            elif tool.name == "read_process_output":
                read_process_tool = tool

        if not start_process_tool or not read_process_tool:
            print("❌ Desktop commander process tools not found")
            return False

        # Test 1: Immediate output capture
        print("🔧 Testing immediate output capture...")
        result = start_process_tool.invoke(
            {
                "command": "bash",
                "args": ["-c", "echo 'test123' && sleep 1"],
                "timeout_ms": 5000,
            }
        )

        result_str = str(result)
        print(f"Start result: {result_str}")

        # Extract PID
        import re

        process_id_match = re.search(r"Process started with PID (\d+)", result_str)
        if process_id_match:
            process_id = int(process_id_match.group(1))
            print(f"Process ID: {process_id}")

            # Try reading immediately
            try:
                output_result = read_process_tool.invoke({"pid": process_id})
                print(f"Immediate read: {output_result}")
            except Exception as e:
                print(f"Immediate read error: {e}")

            # Try reading after sleep
            import time

            time.sleep(0.5)

            try:
                output_result = read_process_tool.invoke({"pid": process_id})
                print(f"After 0.5s read: {output_result}")
            except Exception as e:
                print(f"After 0.5s read error: {e}")

            # Try reading after longer sleep
            time.sleep(1.5)

            try:
                output_result = read_process_tool.invoke({"pid": process_id})
                print(f"After 2s read: {output_result}")
            except Exception as e:
                print(f"After 2s read error: {e}")

        # Test 2: Longer running process
        print("🔧 Testing longer running process...")
        result = start_process_tool.invoke(
            {
                "command": "bash",
                "args": ["-c", "echo 'starting'; sleep 5; echo 'finished'"],
                "timeout_ms": 10000,
            }
        )

        result_str = str(result)
        process_id_match = re.search(r"Process started with PID (\d+)", result_str)
        if process_id_match:
            process_id = int(process_id_match.group(1))
            print(f"Long process ID: {process_id}")

            # Try reading at different intervals
            for i in range(6):
                time.sleep(1)
                try:
                    output_result = read_process_tool.invoke({"pid": process_id})
                    print(f"After {i+1}s read: {output_result}")
                except Exception as e:
                    print(f"After {i+1}s read error: {e}")

    except Exception as e:
        print(f"❌ Process timing test failed: {e}")
        return False

    return True


if __name__ == "__main__":
    success = test_process_timing()

    if success:
        print("\n🎉 Process timing tests completed!")
    else:
        print("\n❌ Process timing tests failed!")
        sys.exit(1)
