#!/usr/bin/env python3
"""
Test utilities for biomni reliability assessment.

Provides common utilities and path resolution for biomni integration tests.
"""

import os
import sys
from pathlib import Path


def get_project_root() -> Path:
    """Get the project root directory dynamically."""
    # Start from current file and walk up to find project root
    current_path = Path(__file__).resolve()

    # Look for key project files to identify root
    for parent in current_path.parents:
        if (parent / "src").exists() and (parent / "tests").exists():
            return parent

    # Fallback to current directory
    return Path.cwd()


def setup_test_environment():
    """Setup test environment with proper path configuration."""
    project_root = get_project_root()

    # Add project root to Python path if not already present
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    return project_root


def get_test_data_path() -> Path:
    """Get path to test data directory."""
    return get_project_root() / "tests" / "biomni_integration"


def get_results_file_path(filename: str) -> Path:
    """Get path for results files with proper directory creation."""
    results_dir = get_test_data_path()
    results_dir.mkdir(exist_ok=True)
    return results_dir / filename


class TestConfig:
    """Configuration class for biomni reliability tests."""

    def __init__(self):
        self.project_root = get_project_root()
        self.test_data_dir = get_test_data_path()

        # Default test parameters
        self.default_timeout = 30
        self.max_retry_attempts = 2
        self.sample_protein_id = "Q86VK4"
        self.sample_gene_name = "TP53"
        self.sample_doi = "10.1038/nature12373"
        self.sample_dna_sequence = "ATGCGTACGCGTACGCGT"
        self.sample_protein_sequence = "MTEYKLVVVGAGGVGKSALTIQLIQNHFVDEYDPTIEDSYRKQVVIDGETCLLDILDTAGQEEYSAMRDQYMRTGEGFLCVFAINNTKSFEDIHQYREQIKRVKDSDDVPMVLVGNKCDLPARTVETRQAQDLARSYGIPYIETSAKTRQGVEDAFYTLVREIRQHKLRKLNPPDESGPGCMSCKCVLS"

    def get_test_parameters_for_tool(self, tool_name: str) -> dict:
        """Get appropriate test parameters for a specific tool."""
        # Tool-specific parameter mapping
        if "uniprot" in tool_name.lower():
            return {"prompt": self.sample_protein_id}
        elif "alphafold" in tool_name.lower():
            return {"uniprot_id": self.sample_protein_id}
        elif "gene" in tool_name.lower():
            return {"gene_name": self.sample_gene_name}
        elif "doi" in tool_name.lower():
            return {"doi": self.sample_doi}
        elif "sequence" in tool_name.lower():
            return {"sequence": self.sample_dna_sequence}
        else:
            return {"prompt": self.sample_protein_id}


# Global configuration instance
TEST_CONFIG = TestConfig()
