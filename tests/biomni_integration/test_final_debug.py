#!/usr/bin/env python3
"""
Final debug test with environment loading.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def test_with_env_loaded():
    """Test with environment variables properly loaded."""
    print("🔍 Final Debug Test with Environment Variables Loaded")

    # Check environment variables
    print(f"✅ REASONING_PROVIDER: {os.environ.get('REASONING_PROVIDER', 'Not set')}")
    print(
        f"✅ REASONING_API_KEY: {os.environ.get('REASONING_API_KEY', 'Not set')[:20]}..."
    )
    print(f"✅ REASONING_MODEL: {os.environ.get('REASONING_MODEL', 'Not set')}")

    try:
        from src.tools.biomni_tools import _execute_biomni_tool_direct

        print("\n🧪 Testing direct execution with environment loaded...")
        result = _execute_biomni_tool_direct("query_uniprot", prompt="Q86VK4")

        print(f"📋 Raw result: {result[:500]}...")

        # Parse result
        import json

        try:
            parsed = json.loads(result)
            if parsed.get("success"):
                print("✅ SUCCESS: Direct execution worked!")
                print(f"   Tool: {parsed.get('tool')}")
                if parsed.get("result"):
                    result_data = parsed.get("result")
                    if isinstance(result_data, dict) and result_data.get("success"):
                        print("✅ SUCCESS: Biomni tool executed successfully!")
                        query_info = result_data.get("query_info", {})
                        print(f"   Endpoint: {query_info.get('endpoint', 'N/A')}")
                        protein_info = result_data.get("result", {})
                        if isinstance(protein_info, dict):
                            protein_name = (
                                protein_info.get("proteinDescription", {})
                                .get("recommendedName", {})
                                .get("fullName", {})
                                .get("value", "N/A")
                            )
                            print(f"   Protein: {protein_name}")
                        else:
                            print(f"   Result type: {type(protein_info)}")
                    else:
                        print(
                            f"⚠️  Biomni tool returned error: {result_data.get('error', 'Unknown error')}"
                        )
            else:
                print(f"❌ Direct execution returned error: {parsed.get('error')}")

        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"   Raw result sample: {result[:200]}...")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = test_with_env_loaded()

    if success:
        print("\n🎉 Final debug test completed!")
    else:
        print("\n❌ Final debug test failed!")
        sys.exit(1)
