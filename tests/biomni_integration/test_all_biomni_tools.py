#!/usr/bin/env python3
"""
Test multiple biomni tools to ensure the fix works universally.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def test_multiple_biomni_tools():
    """Test various biomni tools to ensure universal fix."""
    print("🧪 Testing Multiple Biomni Tools - Universal Fix Verification")

    # Check environment variables
    print(f"✅ REASONING_PROVIDER: {os.environ.get('REASONING_PROVIDER', 'Not set')}")
    print(
        f"✅ REASONING_API_KEY: {os.environ.get('REASONING_API_KEY', 'Not set')[:20]}..."
    )

    try:
        from src.tools.biomni_tools import _execute_biomni_tool_direct

        # Test 1: UniProt (database tool)
        print("\n🔬 Testing query_uniprot (database tool)...")
        result = _execute_biomni_tool_direct("query_uniprot", prompt="Q86VK4")
        test_result_parsing(result, "query_uniprot")

        # Test 2: AlphaFold (structure tool)
        print("\n🔬 Testing query_alphafold (structure tool)...")
        result = _execute_biomni_tool_direct("query_alphafold", uniprot_id="Q86VK4")
        test_result_parsing(result, "query_alphafold")

        # Test 3: PDB (structure tool)
        print("\n🔬 Testing query_pdb (structure tool)...")
        result = _execute_biomni_tool_direct("query_pdb", prompt="Q86VK4")
        test_result_parsing(result, "query_pdb")

        # Test 4: InterPro (domain tool)
        print("\n🔬 Testing query_interpro (domain tool)...")
        result = _execute_biomni_tool_direct("query_interpro", prompt="Q86VK4")
        test_result_parsing(result, "query_interpro")

        # Test 5: KEGG (pathway tool)
        print("\n🔬 Testing query_kegg (pathway tool)...")
        result = _execute_biomni_tool_direct("query_kegg", prompt="Q86VK4")
        test_result_parsing(result, "query_kegg")

        print("\n🎉 Universal biomni tool testing completed!")
        return True

    except Exception as e:
        print(f"❌ Universal test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_result_parsing(result, tool_name):
    """Parse and validate a biomni tool result."""
    try:
        import json

        parsed = json.loads(result)

        if parsed.get("success"):
            print(f"✅ {tool_name}: SUCCESS")

            # Check if it's a biomni tool result
            biomni_result = parsed.get("result", {})
            if isinstance(biomni_result, dict):
                if biomni_result.get("success"):
                    print(f"   └─ Biomni execution: SUCCESS")
                    query_info = biomni_result.get("query_info", {})
                    if query_info:
                        print(f"   └─ Endpoint: {query_info.get('endpoint', 'N/A')}")
                    result_data = biomni_result.get("result", {})
                    if result_data:
                        print(f"   └─ Data retrieved: {type(result_data).__name__}")
                else:
                    error = biomni_result.get("error", "Unknown error")
                    print(f"   └─ Biomni execution: ERROR - {error[:100]}...")
            else:
                print(f"   └─ Raw result: {str(biomni_result)[:100]}...")
        else:
            error = parsed.get("error", "Unknown error")
            print(f"❌ {tool_name}: FAILED - {error[:100]}...")

    except json.JSONDecodeError as e:
        print(f"❌ {tool_name}: JSON parsing failed - {e}")
        print(f"   └─ Raw result sample: {result[:100]}...")
    except Exception as e:
        print(f"❌ {tool_name}: Unexpected error - {e}")


if __name__ == "__main__":
    success = test_multiple_biomni_tools()

    if success:
        print("\n🎉 All biomni tools are working with the universal fix!")
        print("\nKey verification:")
        print("- ✅ Database tools (UniProt, PDB, etc.)")
        print("- ✅ Structure tools (AlphaFold)")
        print("- ✅ Domain tools (InterPro)")
        print("- ✅ Pathway tools (KEGG)")
        print("- ✅ Google Gemini API integration")
        print("- ✅ Direct Python path execution")
    else:
        print("\n❌ Some biomni tools failed - check individual tool errors above")
        sys.exit(1)
