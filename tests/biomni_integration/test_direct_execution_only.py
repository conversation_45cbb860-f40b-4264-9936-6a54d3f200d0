#!/usr/bin/env python3
"""
Test only the direct execution method to isolate issues.
"""

import sys
import os

sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def test_direct_execution_only():
    """Test only the direct execution method."""
    print("🧪 Testing Direct Execution Method Only...")

    try:
        from src.tools.biomni_tools import _execute_biomni_tool_direct

        print("✅ Imported direct execution function")

        # Test with a simple query
        print("🔧 Testing direct execution with query_uniprot...")
        result = _execute_biomni_tool_direct("query_uniprot", prompt="Q86VK4")

        print(f"📋 Raw result: {result}")

        # Parse result
        import json

        try:
            parsed = json.loads(result)
            if parsed.get("success"):
                print("✅ Direct execution successful!")
                print(f"   Tool: {parsed.get('tool')}")
                if parsed.get("result"):
                    print(f"   Result preview: {str(parsed.get('result'))[:200]}...")
            else:
                print(f"⚠️  Direct execution returned error: {parsed.get('error')}")
                if "Import error" in str(parsed.get("error")):
                    print("   This suggests an import/dependency issue")

        except json.JSONDecodeError as e:
            print(f"⚠️  Direct execution returned non-JSON: {result[:500]}...")

    except Exception as e:
        print(f"❌ Direct execution test failed: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = test_direct_execution_only()

    if success:
        print("\n🎉 Direct execution test completed!")
    else:
        print("\n❌ Direct execution test failed!")
        sys.exit(1)
