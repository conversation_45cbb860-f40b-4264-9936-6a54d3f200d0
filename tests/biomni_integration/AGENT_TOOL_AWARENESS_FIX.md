# Agent Tool Awareness Fix - KEGG Database Access

## Problem Identified

After successfully implementing the biomni tool infrastructure and resolving desktop-commander execution issues, a **tool awareness problem** was discovered:

- **Infrastructure worked**: KEGG tools like `query_kegg` were successfully retrieved by the tool retriever
- **Agent limitation**: The `coder` agent reported that KEGG tools weren't available despite successful tool execution
- **Root cause**: Agent tool filtering configuration didn't include KEGG tools for coder and browser agents

## Specific Issue

In `src/tools/biomni_tools.py`, the `get_biomni_tools_for_agent()` function filters available tools per agent:

**Before Fix:**
```python
agent_tool_filters = {
    'researcher': [
        'query_uniprot', 'query_alphafold', 'query_pdb', 'query_kegg',  # ✅ Had KEGG
        'search_pubmed', 'get_gene_info', 'get_protein_info',
        'literature_search', 'database_search'
    ],
    'coder': [
        'query_uniprot', 'query_alphafold', 'query_pdb',  # ❌ Missing KEGG
        'process_sequence', 'analyze_structure', 'compute_features', 'run_analysis'
    ],
    'browser': [
        'query_uniprot', 'query_pdb', 'search_database'  # ❌ Missing KEGG
    ]
}
```

**Problem:** When workflows assigned KEGG queries to the `coder` agent (common in bioinformatics analysis), the coder would report tools unavailable.

## Solution Implemented

**After Fix:**
```python
agent_tool_filters = {
    'researcher': [
        'query_uniprot', 'query_alphafold', 'query_pdb', 'query_kegg',  # ✅ Still has KEGG
        'search_pubmed', 'get_gene_info', 'get_protein_info',
        'literature_search', 'database_search'
    ],
    'coder': [
        'query_uniprot', 'query_alphafold', 'query_pdb', 'query_kegg',  # ✅ Now has KEGG
        'process_sequence', 'analyze_structure', 'compute_features', 'run_analysis'
    ],
    'browser': [
        'query_uniprot', 'query_pdb', 'query_kegg', 'search_database'  # ✅ Now has KEGG
    ]
}
```

## Validation Results

**All agents now have KEGG access:**
- ✅ researcher: 1/5 tools are KEGG tools  
- ✅ coder: 1/5 tools are KEGG tools
- ✅ browser: 1/4 tools are KEGG tools

## Impact

**Before Fix:**
- KEGG database queries would fail when assigned to coder agents
- Generated reports would incorrectly claim KEGG tools unavailable
- Bioinformatics workflows requiring pathway analysis would be incomplete

**After Fix:**
- All agents can now access KEGG pathway databases
- Comprehensive bioinformatics analysis capabilities across all agent types
- Workflow reports will accurately reflect available tool capabilities

## Files Modified

- **src/tools/biomni_tools.py** (lines 642-648):
  - Added `'query_kegg'` to coder agent tool filters
  - Added `'query_kegg'` to browser agent tool filters

## Test Results

- ✅ Coder agent: Successfully loads `query_kegg` tool
- ✅ Browser agent: Successfully loads `query_kegg` tool  
- ✅ Infrastructure: All 181 biomni tools remain accessible
- ✅ Execution: Direct subprocess execution continues to work correctly

This fix ensures that agent-level tool awareness matches the actual tool infrastructure capabilities, preventing false negative reports about tool availability.