#!/usr/bin/env python3
"""
Comprehensive Biomni Tool Reliability Assessment Framework

This test suite provides systematic testing of all 181 biomni tools with:
- Execution success/failure tracking
- Failure pattern analysis
- Tool-specific requirement documentation
- Reliability metrics per tool category
- Integration with pytest framework
"""

import os
import sys
import json
import time
import pytest
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup test environment with proper path resolution
from test_utils import setup_test_environment, get_results_file_path, TEST_CONFIG

setup_test_environment()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ToolTestResult:
    """Data class to track individual tool test results."""

    tool_name: str
    module: str
    test_status: str  # 'success', 'failure', 'timeout', 'error'
    execution_time: float
    error_message: Optional[str] = None
    error_type: Optional[str] = None
    output_data: Optional[Dict] = None
    test_timestamp: str = ""

    def __post_init__(self):
        if not self.test_timestamp:
            self.test_timestamp = datetime.now().isoformat()


@dataclass
class ModuleTestSummary:
    """Data class to track module-level test results."""

    module_name: str
    total_tools: int
    successful_tools: int
    failed_tools: int
    timeout_tools: int
    error_tools: int
    success_rate: float
    average_execution_time: float
    tool_results: List[ToolTestResult]


class BiomniReliabilityTester:
    """
    Comprehensive testing framework for biomni tool reliability assessment.
    """

    def __init__(self, test_timeout: int = 30, max_retry_attempts: int = 2):
        """
        Initialize the reliability tester.

        Args:
            test_timeout: Maximum time (seconds) to wait for tool execution
            max_retry_attempts: Number of retry attempts for failed tools
        """
        self.test_timeout = test_timeout
        self.max_retry_attempts = max_retry_attempts
        self.test_results: List[ToolTestResult] = []
        self.module_summaries: Dict[str, ModuleTestSummary] = {}

        # Load tool discovery data
        self.tool_data = self._load_tool_discovery_data()

        # Initialize biomni tools
        self._initialize_biomni_tools()

    def _load_tool_discovery_data(self) -> Dict:
        """Load tool discovery results from JSON file."""
        try:
            discovery_file = get_results_file_path("tool_discovery_results.json")
            with open(discovery_file, "r") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load tool discovery data: {e}")
            return {"total_tools": 0, "modules": {}, "tool_details": []}

    def _initialize_biomni_tools(self):
        """Initialize biomni tools and registry."""
        try:
            from src.tools.biomni.tool_modules import load_all_tools
            from src.tools.biomni.tool_registry import ToolRegistry
            from src.tools.biomni_tools import _execute_biomni_tool_direct

            self.all_tools = load_all_tools()
            self.registry = ToolRegistry(self.all_tools)
            self.execute_tool = _execute_biomni_tool_direct

            logger.info(
                f"Initialized biomni testing framework with {len(self.registry.tools)} tools"
            )

        except Exception as e:
            logger.error(f"Failed to initialize biomni tools: {e}")
            raise

    def _create_test_parameters(self, tool_info: Dict) -> Dict:
        """
        Create appropriate test parameters for a tool based on its requirements.

        Args:
            tool_info: Tool information from registry

        Returns:
            Dictionary of test parameters
        """
        params = {}
        tool_name = tool_info.get("name", "")

        # Handle required parameters with sensible defaults
        for param in tool_info.get("required_parameters", []):
            param_name = param["name"]
            param_type = param.get("type", "str")

            # Use centralized test configuration for parameter defaults
            if param_name.lower() in ["prompt", "query", "search_term"]:
                params[param_name] = TEST_CONFIG.sample_protein_id
            elif param_name.lower() in ["uniprot_id", "protein_id"]:
                params[param_name] = TEST_CONFIG.sample_protein_id
            elif param_name.lower() in ["gene_id", "gene_name"]:
                params[param_name] = TEST_CONFIG.sample_gene_name
            elif param_name.lower() in ["doi"]:
                params[param_name] = TEST_CONFIG.sample_doi
            elif param_name.lower() in ["sequence", "dna_sequence"]:
                params[param_name] = TEST_CONFIG.sample_dna_sequence
            elif param_name.lower() in ["protein_sequence"]:
                params[param_name] = TEST_CONFIG.sample_protein_sequence
            elif param_type == "int":
                params[param_name] = 5  # Default integer value
            elif param_type == "bool":
                params[param_name] = False  # Default boolean value
            elif param_type == "str":
                params[param_name] = "test_value"  # Generic string value
            else:
                params[param_name] = "test_value"  # Fallback

        return params

    def _classify_failure_type(self, error_message: str) -> str:
        """
        Classify failure type based on error message patterns.

        Args:
            error_message: Error message from failed execution

        Returns:
            Classification of failure type
        """
        error_lower = error_message.lower()

        if "timeout" in error_lower or "timed out" in error_lower:
            return "timeout"
        elif "import" in error_lower or "module" in error_lower:
            return "dependency"
        elif "environment" in error_lower or "conda" in error_lower:
            return "environment"
        elif "permission" in error_lower or "access" in error_lower:
            return "permission"
        elif "network" in error_lower or "connection" in error_lower:
            return "network"
        elif "parameter" in error_lower or "argument" in error_lower:
            return "parameter"
        elif "api" in error_lower or "key" in error_lower:
            return "api_auth"
        else:
            return "execution"

    def test_single_tool(
        self, tool_info: Dict, retry_attempt: int = 0
    ) -> ToolTestResult:
        """
        Test a single biomni tool with comprehensive error handling.

        Args:
            tool_info: Tool information from registry
            retry_attempt: Current retry attempt number

        Returns:
            ToolTestResult with execution details
        """
        tool_name = tool_info["name"]
        module = tool_info.get("module", "unknown")

        start_time = time.time()

        try:
            # Create test parameters
            test_params = self._create_test_parameters(tool_info)

            logger.debug(
                f"Testing {tool_name} with params: {test_params} (attempt {retry_attempt + 1}/{self.max_retry_attempts + 1})"
            )

            # Execute the tool with timeout handling
            result = self.execute_tool(tool_name, **test_params)
            execution_time = time.time() - start_time

            # Parse result
            if result:
                try:
                    parsed_result = json.loads(result)
                    if parsed_result.get("success"):
                        return ToolTestResult(
                            tool_name=tool_name,
                            module=module,
                            test_status="success",
                            execution_time=execution_time,
                            output_data=parsed_result,
                        )
                    else:
                        error_msg = parsed_result.get("error", "Unknown error")
                        return ToolTestResult(
                            tool_name=tool_name,
                            module=module,
                            test_status="failure",
                            execution_time=execution_time,
                            error_message=error_msg,
                            error_type=self._classify_failure_type(error_msg),
                        )
                except json.JSONDecodeError:
                    return ToolTestResult(
                        tool_name=tool_name,
                        module=module,
                        test_status="error",
                        execution_time=execution_time,
                        error_message="Failed to parse tool output as JSON",
                        error_type="parsing",
                    )
            else:
                return ToolTestResult(
                    tool_name=tool_name,
                    module=module,
                    test_status="failure",
                    execution_time=execution_time,
                    error_message="Tool returned empty result",
                    error_type="execution",
                )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)

            # Check if this is a timeout
            if execution_time >= self.test_timeout:
                return ToolTestResult(
                    tool_name=tool_name,
                    module=module,
                    test_status="timeout",
                    execution_time=execution_time,
                    error_message=f"Tool execution timed out after {self.test_timeout}s",
                    error_type="timeout",
                )
            else:
                return ToolTestResult(
                    tool_name=tool_name,
                    module=module,
                    test_status="error",
                    execution_time=execution_time,
                    error_message=error_msg,
                    error_type=self._classify_failure_type(error_msg),
                )

    def test_tools_by_module(self, module_name: str) -> ModuleTestSummary:
        """
        Test all tools in a specific module.

        Args:
            module_name: Name of the module to test

        Returns:
            ModuleTestSummary with results for all tools in the module
        """
        logger.info(f"Testing module: {module_name}")

        # Filter tools for this module
        module_tools = [
            tool
            for tool in self.tool_data["tool_details"]
            if tool["module"] == module_name
        ]

        module_results = []

        for tool_info in module_tools:
            # Try up to max_retry_attempts + 1 times
            result = None
            for attempt in range(self.max_retry_attempts + 1):
                result = self.test_single_tool(tool_info, attempt)

                # If successful, break out of retry loop
                if result.test_status == "success":
                    break

                # If not the last attempt, wait before retry
                if attempt < self.max_retry_attempts:
                    time.sleep(1)

            module_results.append(result)
            self.test_results.append(result)

        # Calculate module summary statistics
        total_tools = len(module_results)
        successful_tools = len(
            [r for r in module_results if r.test_status == "success"]
        )
        failed_tools = len([r for r in module_results if r.test_status == "failure"])
        timeout_tools = len([r for r in module_results if r.test_status == "timeout"])
        error_tools = len([r for r in module_results if r.test_status == "error"])

        success_rate = (successful_tools / total_tools * 100) if total_tools > 0 else 0
        avg_execution_time = (
            sum(r.execution_time for r in module_results) / total_tools
            if total_tools > 0
            else 0
        )

        summary = ModuleTestSummary(
            module_name=module_name,
            total_tools=total_tools,
            successful_tools=successful_tools,
            failed_tools=failed_tools,
            timeout_tools=timeout_tools,
            error_tools=error_tools,
            success_rate=success_rate,
            average_execution_time=avg_execution_time,
            tool_results=module_results,
        )

        self.module_summaries[module_name] = summary

        logger.info(
            f"Module {module_name} completed: {successful_tools}/{total_tools} tools successful ({success_rate:.1f}%)"
        )

        return summary

    def run_comprehensive_assessment(self) -> Dict:
        """
        Run comprehensive reliability assessment across all tool modules.

        Returns:
            Complete assessment results
        """
        logger.info("Starting comprehensive biomni tool reliability assessment")

        assessment_start_time = time.time()

        # Test all modules
        for module_name in sorted(self.tool_data["modules"].keys()):
            if (
                self.tool_data["modules"][module_name] > 0
            ):  # Only test modules with tools
                self.test_tools_by_module(module_name)

        total_assessment_time = time.time() - assessment_start_time

        # Generate overall statistics
        total_tools = len(self.test_results)
        successful_tools = len(
            [r for r in self.test_results if r.test_status == "success"]
        )
        failed_tools = len([r for r in self.test_results if r.test_status == "failure"])
        timeout_tools = len(
            [r for r in self.test_results if r.test_status == "timeout"]
        )
        error_tools = len([r for r in self.test_results if r.test_status == "error"])

        overall_success_rate = (
            (successful_tools / total_tools * 100) if total_tools > 0 else 0
        )

        # Create comprehensive results
        results = {
            "assessment_summary": {
                "total_tools_tested": total_tools,
                "successful_tools": successful_tools,
                "failed_tools": failed_tools,
                "timeout_tools": timeout_tools,
                "error_tools": error_tools,
                "overall_success_rate": overall_success_rate,
                "total_assessment_time": total_assessment_time,
                "assessment_timestamp": datetime.now().isoformat(),
            },
            "module_summaries": {
                name: asdict(summary) for name, summary in self.module_summaries.items()
            },
            "detailed_results": [asdict(result) for result in self.test_results],
            "failure_analysis": self._analyze_failure_patterns(),
            "recommendations": self._generate_recommendations(),
        }

        # Save results to file
        results_file = get_results_file_path(
            f"reliability_assessment_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)

        logger.info(
            f"Comprehensive assessment completed in {total_assessment_time:.1f}s"
        )
        logger.info(
            f"Overall success rate: {overall_success_rate:.1f}% ({successful_tools}/{total_tools} tools)"
        )
        logger.info(f"Results saved to: {results_file}")

        return results

    def _analyze_failure_patterns(self) -> Dict:
        """Analyze failure patterns across all test results."""
        failed_results = [r for r in self.test_results if r.test_status != "success"]

        # Group by error type
        error_type_counts = {}
        for result in failed_results:
            error_type = result.error_type or "unknown"
            if error_type not in error_type_counts:
                error_type_counts[error_type] = []
            error_type_counts[error_type].append(result.tool_name)

        # Group by module
        module_failure_counts = {}
        for result in failed_results:
            module = result.module
            if module not in module_failure_counts:
                module_failure_counts[module] = 0
            module_failure_counts[module] += 1

        return {
            "failure_by_error_type": {
                k: {"count": len(v), "tools": v} for k, v in error_type_counts.items()
            },
            "failure_by_module": module_failure_counts,
            "total_failures": len(failed_results),
            "most_common_error_type": (
                max(error_type_counts.keys(), key=lambda k: len(error_type_counts[k]))
                if error_type_counts
                else None
            ),
        }

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []

        # Analyze results and generate actionable recommendations
        if self.test_results:
            success_rate = (
                len([r for r in self.test_results if r.test_status == "success"])
                / len(self.test_results)
                * 100
            )

            if success_rate < 70:
                recommendations.append(
                    "Overall success rate is below 70% - significant reliability improvements needed"
                )
            elif success_rate < 90:
                recommendations.append(
                    "Success rate could be improved - focus on addressing common failure patterns"
                )
            else:
                recommendations.append(
                    "Good overall reliability - continue monitoring for edge cases"
                )

        # Module-specific recommendations
        for module_name, summary in self.module_summaries.items():
            if summary.success_rate < 50:
                recommendations.append(
                    f"Module '{module_name}' has critical reliability issues ({summary.success_rate:.1f}% success rate)"
                )
            elif summary.success_rate < 80:
                recommendations.append(
                    f"Module '{module_name}' needs reliability improvements ({summary.success_rate:.1f}% success rate)"
                )

        return recommendations


# pytest integration
class TestBiomniReliabilityAssessment:
    """Pytest test class for biomni reliability assessment."""

    @pytest.fixture(scope="class")
    def reliability_tester(self):
        """Create reliability tester fixture."""
        return BiomniReliabilityTester(test_timeout=30, max_retry_attempts=1)

    def test_database_tools_reliability(self, reliability_tester):
        """Test reliability of database tools (AC: 1, 2, 4)."""
        summary = reliability_tester.test_tools_by_module("database")

        # Assert basic reliability requirements
        assert summary.total_tools > 0, "Database module should have tools"
        assert summary.success_rate > 0, "At least some database tools should work"

        # Log results for analysis
        logger.info(
            f"Database tools: {summary.successful_tools}/{summary.total_tools} successful ({summary.success_rate:.1f}%)"
        )

    def test_genomics_tools_reliability(self, reliability_tester):
        """Test reliability of genomics analysis tools (AC: 1, 2, 4)."""
        summary = reliability_tester.test_tools_by_module("genomics")

        assert summary.total_tools > 0, "Genomics module should have tools"
        assert summary.success_rate >= 0, "Success rate should be measurable"

        logger.info(
            f"Genomics tools: {summary.successful_tools}/{summary.total_tools} successful ({summary.success_rate:.1f}%)"
        )

    def test_systems_biology_tools_reliability(self, reliability_tester):
        """Test reliability of systems biology and pathway tools (AC: 1, 2, 4)."""
        summary = reliability_tester.test_tools_by_module("systems_biology")

        assert summary.total_tools > 0, "Systems biology module should have tools"
        assert summary.success_rate >= 0, "Success rate should be measurable"

        logger.info(
            f"Systems biology tools: {summary.successful_tools}/{summary.total_tools} successful ({summary.success_rate:.1f}%)"
        )

    def test_biochemistry_structural_tools_reliability(self, reliability_tester):
        """Test reliability of biochemistry and structural biology tools (AC: 1, 2, 4)."""
        for module in ["biochemistry", "biophysics"]:
            summary = reliability_tester.test_tools_by_module(module)

            assert summary.total_tools > 0, f"{module} module should have tools"
            assert summary.success_rate >= 0, "Success rate should be measurable"

            logger.info(
                f"{module} tools: {summary.successful_tools}/{summary.total_tools} successful ({summary.success_rate:.1f}%)"
            )

    def test_microbiology_bioengineering_tools_reliability(self, reliability_tester):
        """Test reliability of microbiology and bioengineering tools (AC: 1, 2, 4)."""
        for module in ["microbiology", "bioengineering"]:
            summary = reliability_tester.test_tools_by_module(module)

            assert summary.total_tools > 0, f"{module} module should have tools"
            assert summary.success_rate >= 0, "Success rate should be measurable"

            logger.info(
                f"{module} tools: {summary.successful_tools}/{summary.total_tools} successful ({summary.success_rate:.1f}%)"
            )

    def test_comprehensive_reliability_assessment(self, reliability_tester):
        """Run comprehensive assessment across all tools (AC: 1, 2, 3, 4, 5)."""
        results = reliability_tester.run_comprehensive_assessment()

        # Verify assessment completeness
        assert (
            results["assessment_summary"]["total_tools_tested"] == 181
        ), "Should test all 181 tools"
        assert len(results["module_summaries"]) > 0, "Should have module summaries"
        assert (
            len(results["detailed_results"]) == 181
        ), "Should have detailed results for all tools"
        assert "failure_analysis" in results, "Should include failure pattern analysis"
        assert "recommendations" in results, "Should include recommendations"

        # Assert backward compatibility with existing testing
        assert (
            results["assessment_summary"]["total_tools_tested"] > 0
        ), "Should test tools successfully"

        # Log final results
        summary = results["assessment_summary"]
        logger.info(f"Comprehensive Assessment Complete:")
        logger.info(f"  Total tools: {summary['total_tools_tested']}")
        logger.info(f"  Success rate: {summary['overall_success_rate']:.1f}%")
        logger.info(f"  Assessment time: {summary['total_assessment_time']:.1f}s")


def main():
    """Main function for running the reliability assessment directly."""
    tester = BiomniReliabilityTester(test_timeout=30, max_retry_attempts=1)
    results = tester.run_comprehensive_assessment()

    # Print summary
    print(f"\n🎯 BIOMNI TOOL RELIABILITY ASSESSMENT COMPLETE")
    print(f"📊 Overall Results:")
    print(
        f"  • Total tools tested: {results['assessment_summary']['total_tools_tested']}"
    )
    print(
        f"  • Success rate: {results['assessment_summary']['overall_success_rate']:.1f}%"
    )
    print(f"  • Successful tools: {results['assessment_summary']['successful_tools']}")
    print(f"  • Failed tools: {results['assessment_summary']['failed_tools']}")
    print(f"  • Timeout tools: {results['assessment_summary']['timeout_tools']}")
    print(f"  • Error tools: {results['assessment_summary']['error_tools']}")

    # Print module breakdown
    print("\n📁 Module Success Rates:")
    for module_name, summary in results["module_summaries"].items():
        print(
            f"  • {module_name}: {summary['success_rate']:.1f}% ({summary['successful_tools']}/{summary['total_tools']})"
        )

    # Print failure analysis
    if results["failure_analysis"]["total_failures"] > 0:
        print("\n⚠️  Failure Analysis:")
        for error_type, info in results["failure_analysis"][
            "failure_by_error_type"
        ].items():
            print(f"  • {error_type}: {info['count']} tools")

    # Print recommendations
    if results["recommendations"]:
        print("\n💡 Recommendations:")
        for rec in results["recommendations"]:
            print(f"  • {rec}")

    return results


if __name__ == "__main__":
    main()
