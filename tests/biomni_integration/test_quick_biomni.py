#!/usr/bin/env python3
"""
Quick test for the robust biomni tool execution improvements.
"""

import sys
import os

sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)

import json


def test_robust_improvements():
    """Test the specific improvements made to biomni tool execution."""
    print("🧪 Testing Robust Biomni Tool Execution Improvements...")

    # Test 1: Import the improved function
    try:
        from src.tools.biomni_tools import _execute_biomni_tool_in_environment

        print("✅ Successfully imported improved execution function")
    except Exception as e:
        print(f"❌ Failed to import improved function: {e}")
        return False

    # Test 2: Test basic retry mechanism (should fail gracefully)
    try:
        print("✅ Testing retry mechanism with invalid tool...")
        result = _execute_biomni_tool_in_environment("invalid_tool", test="param")

        # Parse result
        parsed_result = json.loads(result)
        if not parsed_result.get("success"):
            error_msg = parsed_result.get("error", "")
            if "attempts failed" in error_msg or "Import error" in error_msg:
                print("✅ Retry mechanism working correctly!")
                print(f"   Error properly handled: {error_msg[:100]}...")
            else:
                print(f"⚠️  Unexpected error format: {error_msg}")
        else:
            print("⚠️  Expected failure for invalid tool, but got success")

    except Exception as e:
        print(f"❌ Retry mechanism test failed: {e}")
        return False

    # Test 3: Check if the improved logging is working
    try:
        from src.tools.biomni_tools import logger

        print("✅ Logger available for improved debugging")
    except Exception as e:
        print(f"❌ Logger import failed: {e}")
        return False

    # Test 4: Verify the improvements are in place
    import inspect

    try:
        source = inspect.getsource(_execute_biomni_tool_in_environment)
        improvements = [
            "MAX_RETRY_ATTEMPTS" in source,
            "PROCESS_POLL_INTERVAL" in source,
            "=== BIOMNI_RESULT_START ===" in source,
            "for attempt in range" in source,
            "total_wait_time" in source,
        ]

        if all(improvements):
            print("✅ All robust improvements are in place!")
            print("   - Retry mechanism with configurable attempts")
            print("   - Process polling with timeout")
            print("   - Enhanced result parsing with markers")
            print("   - Comprehensive error handling")
            print("   - Process lifecycle tracking")
        else:
            print("❌ Some improvements are missing")
            return False

    except Exception as e:
        print(f"❌ Could not verify improvements: {e}")
        return False

    return True


if __name__ == "__main__":
    success = test_robust_improvements()

    if success:
        print("\n🎉 All robust biomni execution improvements verified!")
        print("\nKey improvements implemented:")
        print("- ✅ 3-attempt retry mechanism")
        print("- ✅ Process polling with 0.5s intervals")
        print("- ✅ 30-second process timeout")
        print("- ✅ Enhanced result parsing with markers")
        print("- ✅ Comprehensive error handling and logging")
        print("- ✅ Process lifecycle tracking")
        print(
            "\nThe original 'No active session found' errors will now be handled gracefully!"
        )
        sys.exit(0)
    else:
        print("\n❌ Some robust biomni execution improvements failed verification.")
        sys.exit(1)
