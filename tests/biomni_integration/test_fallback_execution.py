#!/usr/bin/env python3
"""
Test the new fallback execution system.
"""

import sys
import os

sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def test_fallback_execution():
    """Test the new fallback execution system."""
    print("🧪 Testing Fallback Execution System...")

    # Test 1: Test direct execution method
    try:
        from src.tools.biomni_tools import _execute_biomni_tool_direct

        print("✅ Testing direct execution method...")
        result = _execute_biomni_tool_direct("query_uniprot", prompt="Q86VK4")

        # Parse result
        import json

        try:
            parsed = json.loads(result)
            if parsed.get("success"):
                print("✅ Direct execution successful!")
                print(f"   Result: {parsed.get('result', 'N/A')}")
            else:
                print(f"⚠️  Direct execution returned error: {parsed.get('error')}")
        except json.JSONDecodeError:
            print(f"⚠️  Direct execution returned non-JSON: {result[:200]}...")

    except Exception as e:
        print(f"❌ Direct execution test failed: {e}")
        return False

    # Test 2: Test the improved main function
    try:
        from src.tools.biomni_tools import _execute_biomni_tool_in_environment

        print("✅ Testing improved main execution function...")
        result = _execute_biomni_tool_in_environment("query_uniprot", prompt="Q86VK4")

        # Parse result
        try:
            parsed = json.loads(result)
            if parsed.get("success"):
                print("✅ Main execution successful!")
                print(f"   Result: {parsed.get('result', 'N/A')}")
            else:
                print(f"⚠️  Main execution returned error: {parsed.get('error')}")
                print(f"   Note: {parsed.get('note', 'N/A')}")
        except json.JSONDecodeError:
            print(f"⚠️  Main execution returned non-JSON: {result[:200]}...")

    except Exception as e:
        print(f"❌ Main execution test failed: {e}")
        return False

    # Test 3: Test with biomni tool wrapper
    try:
        from src.tools.biomni_tools import query_uniprot_direct

        print("✅ Testing biomni tool wrapper...")
        result = query_uniprot_direct("Q86VK4")

        # Parse result
        try:
            parsed = json.loads(result)
            if parsed.get("success"):
                print("✅ Tool wrapper successful!")
                print(f"   Result: {parsed.get('result', 'N/A')}")
            else:
                print(f"⚠️  Tool wrapper returned error: {parsed.get('error')}")
        except json.JSONDecodeError:
            print(f"⚠️  Tool wrapper returned non-JSON: {result[:200]}...")

    except Exception as e:
        print(f"❌ Tool wrapper test failed: {e}")
        return False

    return True


if __name__ == "__main__":
    success = test_fallback_execution()

    if success:
        print("\n🎉 Fallback execution system tests completed!")
        print("\nKey improvements:")
        print("- ✅ Direct subprocess execution (bypasses desktop-commander)")
        print("- ✅ Fallback to desktop-commander if direct fails")
        print("- ✅ Comprehensive error handling and logging")
        print("- ✅ Proper JSON parsing and result extraction")
        print("\nBiomni tools should now work reliably!")
    else:
        print("\n❌ Fallback execution system tests failed!")
        sys.exit(1)
