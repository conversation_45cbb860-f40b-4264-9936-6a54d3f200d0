#!/usr/bin/env python3
"""
Test desktop-commander basic functionality.
"""

import sys
import os

sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def test_desktop_commander():
    """Test desktop-commander basic functionality."""
    print("🧪 Testing Desktop Commander Basic Functionality...")

    # Test 1: Basic bash command
    try:
        from src.tools.mcp_tools import MCPToolManager

        manager = MCPToolManager()
        tools = manager.get_all_tools()

        # Find the process tools
        start_process_tool = None
        read_process_tool = None

        for tool in tools:
            if tool.name == "start_process":
                start_process_tool = tool
            elif tool.name == "read_process_output":
                read_process_tool = tool

        if not start_process_tool or not read_process_tool:
            print("❌ Desktop commander process tools not found")
            return False

        print("✅ Desktop commander process tools found")

        # Test 2: Simple bash command
        print("🔧 Testing simple bash command...")
        result = start_process_tool.invoke(
            {
                "command": "bash",
                "args": ["-c", "echo 'Hello World'"],
                "timeout_ms": 10000,
            }
        )

        result_str = str(result)
        print(f"Start process result: {result_str}")

        # Extract PID
        import re

        process_id_match = re.search(r"Process started with PID (\d+)", result_str)
        if process_id_match:
            process_id = int(process_id_match.group(1))
            print(f"✅ Process started with PID: {process_id}")

            # Try to read output
            import time

            time.sleep(2)

            try:
                output_result = read_process_tool.invoke({"pid": process_id})
                print(f"✅ Process output: {output_result}")
            except Exception as e:
                print(f"⚠️ Process output read error: {e}")

        else:
            print("❌ Could not extract process ID")
            return False

    except Exception as e:
        print(f"❌ Desktop commander basic test failed: {e}")
        return False

    # Test 3: Python version check
    print("🔧 Testing Python version check...")
    try:
        result = start_process_tool.invoke(
            {
                "command": "bash",
                "args": ["-c", "python3 --version"],
                "timeout_ms": 10000,
            }
        )

        result_str = str(result)
        process_id_match = re.search(r"Process started with PID (\d+)", result_str)
        if process_id_match:
            process_id = int(process_id_match.group(1))
            time.sleep(2)

            try:
                output_result = read_process_tool.invoke({"pid": process_id})
                print(f"✅ Python version: {output_result}")
            except Exception as e:
                print(f"⚠️ Python version read error: {e}")

    except Exception as e:
        print(f"❌ Python version test failed: {e}")
        return False

    # Test 4: Environment activation test
    print("🔧 Testing conda environment activation...")
    try:
        result = start_process_tool.invoke(
            {
                "command": "bash",
                "args": [
                    "-c",
                    "source /Users/<USER>/miniforge3/bin/activate biomni_e1 && python3 --version",
                ],
                "timeout_ms": 15000,
            }
        )

        result_str = str(result)
        print(f"Environment activation result: {result_str}")

        process_id_match = re.search(r"Process started with PID (\d+)", result_str)
        if process_id_match:
            process_id = int(process_id_match.group(1))
            time.sleep(3)

            try:
                output_result = read_process_tool.invoke({"pid": process_id})
                print(f"✅ Environment activated Python: {output_result}")
            except Exception as e:
                print(f"⚠️ Environment activation read error: {e}")
                # This is the core issue we need to solve
                return False

    except Exception as e:
        print(f"❌ Environment activation test failed: {e}")
        return False

    return True


if __name__ == "__main__":
    success = test_desktop_commander()

    if success:
        print("\n🎉 Desktop Commander basic functionality works!")
    else:
        print("\n❌ Desktop Commander has issues - this is the root cause!")
        sys.exit(1)
