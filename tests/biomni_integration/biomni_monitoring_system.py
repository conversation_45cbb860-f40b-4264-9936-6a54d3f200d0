#!/usr/bin/env python3
"""
Biomni Tool Continuous Monitoring System

Provides continuous monitoring capabilities for biomni tool reliability including:
- Health check scheduling
- Performance metrics tracking
- Alerting for critical failures
- Historical reliability trends
"""

import os
import sys
import json
import time
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class HealthCheckResult:
    """Result of a tool health check."""

    tool_name: str
    status: str  # 'healthy', 'degraded', 'failing'
    response_time: float
    timestamp: str
    error_message: Optional[str] = None


@dataclass
class MonitoringMetrics:
    """Monitoring metrics for a time period."""

    total_checks: int
    healthy_checks: int
    degraded_checks: int
    failing_checks: int
    average_response_time: float
    timestamp: str


class BiomniMonitoringSystem:
    """
    Continuous monitoring system for biomni tool reliability.
    """

    def __init__(
        self,
        check_interval: int = 300,  # 5 minutes
        health_check_timeout: int = 30,
        critical_tools: List[str] = None,
    ):
        """
        Initialize monitoring system.

        Args:
            check_interval: Seconds between health checks
            health_check_timeout: Timeout for individual health checks
            critical_tools: List of critical tools to monitor closely
        """
        self.check_interval = check_interval
        self.health_check_timeout = health_check_timeout
        self.critical_tools = critical_tools or [
            "query_uniprot",
            "query_alphafold",
            "query_pdb",
            "query_kegg",
        ]

        self.monitoring_active = False
        self.health_history: List[HealthCheckResult] = []
        self.metrics_history: List[MonitoringMetrics] = []

        # Initialize biomni tools
        self._initialize_monitoring()

    def _initialize_monitoring(self):
        """Initialize monitoring components."""
        try:
            from src.tools.biomni.tool_modules import load_all_tools
            from src.tools.biomni.tool_registry import ToolRegistry
            from src.tools.biomni_tools import _execute_biomni_tool_direct

            self.all_tools = load_all_tools()
            self.registry = ToolRegistry(self.all_tools)
            self.execute_tool = _execute_biomni_tool_direct

            logger.info(
                f"Monitoring system initialized for {len(self.registry.tools)} tools"
            )

        except Exception as e:
            logger.error(f"Failed to initialize monitoring system: {e}")
            raise

    def perform_health_check(self, tool_name: str) -> HealthCheckResult:
        """
        Perform health check on a single tool.

        Args:
            tool_name: Name of tool to check

        Returns:
            HealthCheckResult with status and metrics
        """
        start_time = time.time()
        timestamp = datetime.now().isoformat()

        try:
            # Use lightweight parameters for health checks
            if "alphafold" in tool_name.lower():
                result = self.execute_tool(tool_name, uniprot_id="Q86VK4")
            else:
                result = self.execute_tool(tool_name, prompt="Q86VK4")

            response_time = time.time() - start_time

            if result:
                try:
                    parsed = json.loads(result)
                    if parsed.get("success"):
                        status = "healthy"
                        error_msg = None
                    else:
                        # Tool executed but returned error
                        status = "degraded"
                        error_msg = parsed.get("error", "Unknown error")
                except json.JSONDecodeError:
                    status = "degraded"
                    error_msg = "JSON parsing failed"
            else:
                status = "failing"
                error_msg = "No result returned"

            return HealthCheckResult(
                tool_name=tool_name,
                status=status,
                response_time=response_time,
                timestamp=timestamp,
                error_message=error_msg,
            )

        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                tool_name=tool_name,
                status="failing",
                response_time=response_time,
                timestamp=timestamp,
                error_message=str(e),
            )

    def run_health_checks(
        self, tools_to_check: List[str] = None
    ) -> List[HealthCheckResult]:
        """
        Run health checks on specified tools.

        Args:
            tools_to_check: List of tool names to check (defaults to critical tools)

        Returns:
            List of health check results
        """
        if tools_to_check is None:
            tools_to_check = self.critical_tools

        logger.info(f"Running health checks on {len(tools_to_check)} tools")

        results = []
        for tool_name in tools_to_check:
            try:
                result = self.perform_health_check(tool_name)
                results.append(result)
                self.health_history.append(result)

                # Log status
                status_icon = (
                    "✅"
                    if result.status == "healthy"
                    else "⚠️" if result.status == "degraded" else "❌"
                )
                logger.info(
                    f"{status_icon} {tool_name}: {result.status} ({result.response_time:.2f}s)"
                )

            except Exception as e:
                logger.error(f"Health check failed for {tool_name}: {e}")

        return results

    def calculate_metrics(self, time_window_hours: int = 1) -> MonitoringMetrics:
        """
        Calculate monitoring metrics for a time window.

        Args:
            time_window_hours: Hours to look back for metrics

        Returns:
            MonitoringMetrics for the time window
        """
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)

        # Filter health history to time window
        recent_checks = [
            check
            for check in self.health_history
            if datetime.fromisoformat(check.timestamp) >= cutoff_time
        ]

        if not recent_checks:
            return MonitoringMetrics(
                total_checks=0,
                healthy_checks=0,
                degraded_checks=0,
                failing_checks=0,
                average_response_time=0.0,
                timestamp=datetime.now().isoformat(),
            )

        # Calculate metrics
        total_checks = len(recent_checks)
        healthy_checks = len([c for c in recent_checks if c.status == "healthy"])
        degraded_checks = len([c for c in recent_checks if c.status == "degraded"])
        failing_checks = len([c for c in recent_checks if c.status == "failing"])

        avg_response_time = sum(c.response_time for c in recent_checks) / total_checks

        metrics = MonitoringMetrics(
            total_checks=total_checks,
            healthy_checks=healthy_checks,
            degraded_checks=degraded_checks,
            failing_checks=failing_checks,
            average_response_time=avg_response_time,
            timestamp=datetime.now().isoformat(),
        )

        self.metrics_history.append(metrics)
        return metrics

    def check_alerts(self, metrics: MonitoringMetrics) -> List[str]:
        """
        Check if any alert conditions are met.

        Args:
            metrics: Current monitoring metrics

        Returns:
            List of alert messages
        """
        alerts = []

        if metrics.total_checks > 0:
            health_rate = metrics.healthy_checks / metrics.total_checks
            failure_rate = metrics.failing_checks / metrics.total_checks

            # Alert conditions
            if health_rate < 0.5:
                alerts.append(f"🚨 CRITICAL: Health rate below 50% ({health_rate:.1%})")
            elif health_rate < 0.7:
                alerts.append(f"⚠️ WARNING: Health rate below 70% ({health_rate:.1%})")

            if failure_rate > 0.3:
                alerts.append(
                    f"🚨 CRITICAL: Failure rate above 30% ({failure_rate:.1%})"
                )
            elif failure_rate > 0.1:
                alerts.append(f"⚠️ WARNING: Failure rate above 10% ({failure_rate:.1%})")

            if metrics.average_response_time > 30:
                alerts.append(
                    f"⚠️ WARNING: High response time ({metrics.average_response_time:.1f}s)"
                )

        return alerts

    def start_continuous_monitoring(self):
        """Start continuous monitoring in background thread."""
        self.monitoring_active = True

        def monitoring_loop():
            logger.info(
                f"Starting continuous monitoring (interval: {self.check_interval}s)"
            )

            while self.monitoring_active:
                try:
                    # Run health checks
                    self.run_health_checks()

                    # Calculate metrics
                    metrics = self.calculate_metrics()

                    # Check for alerts
                    alerts = self.check_alerts(metrics)
                    for alert in alerts:
                        logger.warning(alert)

                    # Log current status
                    health_rate = (
                        metrics.healthy_checks / metrics.total_checks
                        if metrics.total_checks > 0
                        else 0
                    )
                    logger.info(
                        f"📊 Health Status: {health_rate:.1%} healthy, {metrics.average_response_time:.1f}s avg response"
                    )

                    # Save monitoring data
                    self.save_monitoring_data()

                    # Wait for next check
                    time.sleep(self.check_interval)

                except Exception as e:
                    logger.error(f"Monitoring loop error: {e}")
                    time.sleep(60)  # Wait 1 minute on error

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()

        logger.info("Continuous monitoring started")

    def stop_continuous_monitoring(self):
        """Stop continuous monitoring."""
        self.monitoring_active = False
        logger.info("Continuous monitoring stopped")

    def save_monitoring_data(self):
        """Save monitoring data to file."""
        monitoring_data = {
            "health_history": [
                asdict(check) for check in self.health_history[-100:]
            ],  # Keep last 100
            "metrics_history": [
                asdict(metric) for metric in self.metrics_history[-24:]
            ],  # Keep last 24 hours
            "last_updated": datetime.now().isoformat(),
        }

        data_file = "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/tests/biomni_integration/monitoring_data.json"
        with open(data_file, "w") as f:
            json.dump(monitoring_data, f, indent=2)

    def generate_status_report(self) -> Dict:
        """Generate comprehensive status report."""
        recent_metrics = self.calculate_metrics(time_window_hours=24)

        # Tool-specific status
        tool_status = {}
        for tool_name in self.critical_tools:
            recent_checks = [
                check
                for check in self.health_history[-20:]  # Last 20 checks
                if check.tool_name == tool_name
            ]

            if recent_checks:
                latest_check = recent_checks[-1]
                healthy_count = len([c for c in recent_checks if c.status == "healthy"])
                tool_status[tool_name] = {
                    "current_status": latest_check.status,
                    "last_check": latest_check.timestamp,
                    "recent_health_rate": healthy_count / len(recent_checks),
                    "avg_response_time": (
                        sum(c.response_time for c in recent_checks) / len(recent_checks)
                    ),
                }

        report = {
            "report_timestamp": datetime.now().isoformat(),
            "overall_metrics": asdict(recent_metrics),
            "tool_status": tool_status,
            "alerts": self.check_alerts(recent_metrics),
            "monitoring_active": self.monitoring_active,
        }

        return report


def main():
    """Main function for running monitoring system."""
    print("🔍 Biomni Tool Monitoring System")

    # Create monitoring system
    monitor = BiomniMonitoringSystem(
        check_interval=60,  # 1 minute for demo
        critical_tools=["query_alphafold", "query_uniprot", "query_pdb"],
    )

    # Run immediate health check
    print("\n📋 Running initial health check...")
    results = monitor.run_health_checks()

    # Calculate metrics
    metrics = monitor.calculate_metrics()
    print(f"\n📊 Current Metrics:")
    print(f"  Total checks: {metrics.total_checks}")
    print(f"  Healthy: {metrics.healthy_checks}")
    print(f"  Degraded: {metrics.degraded_checks}")
    print(f"  Failing: {metrics.failing_checks}")
    print(f"  Avg response time: {metrics.average_response_time:.2f}s")

    # Check alerts
    alerts = monitor.check_alerts(metrics)
    if alerts:
        print(f"\n🚨 Alerts:")
        for alert in alerts:
            print(f"  {alert}")
    else:
        print(f"\n✅ No alerts")

    # Generate status report
    report = monitor.generate_status_report()

    # Save report
    report_file = f"/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/tests/biomni_integration/monitoring_status_report.json"
    with open(report_file, "w") as f:
        json.dump(report, f, indent=2)

    print(f"\n💾 Status report saved to: monitoring_status_report.json")

    return report


if __name__ == "__main__":
    report = main()

    print(f"\n🎯 Monitoring System Ready!")
    print(f"📈 System capable of continuous monitoring with alerting")
    print(f"⚡ Health checks every 5 minutes on critical tools")
    print(f"🔔 Automatic alerting for degraded performance")
