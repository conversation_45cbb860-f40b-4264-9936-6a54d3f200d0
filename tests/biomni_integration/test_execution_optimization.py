"""
Tests for cross-environment execution optimization system.

Tests the enhanced desktop-commander bridge reliability, subprocess execution
strategies, communication protocols, and execution strategy framework.
"""

import asyncio
import pytest
import json
import time
from unittest.mock import Mock, patch, AsyncMock

from src.tools.strategy_framework import (
    get_strategy_manager,
    ExecutionContext,
    HybridExecutionStrategy,
    DirectSubprocessStrategy,
    MCPExecutionStrategy,
    OptimizationStrategy,
)
from src.tools.execution_fallback import get_fallback_manager
from src.tools.performance_monitor import get_performance_tracker, ExecutionMetric
from src.tools.error_recovery import get_error_detector, ErrorCategory
from src.tools.connection_pool import get_connection_pool
from src.config.execution_config import (
    get_execution_config,
    ToolComplexity,
    ExecutionStrategy,
)


class TestExecutionStrategies:
    """Test execution strategies and optimization."""

    @pytest.fixture
    def mock_successful_result(self):
        """Mock successful execution result."""
        return json.dumps(
            {
                "success": True,
                "result": "Test result",
                "tool": "test_tool",
                "execution_method": "test",
            }
        )

    @pytest.fixture
    def mock_failed_result(self):
        """Mock failed execution result."""
        return json.dumps(
            {"success": False, "error": "Test error", "tool": "test_tool"}
        )

    @pytest.mark.asyncio
    async def test_hybrid_strategy_execution(self, mock_successful_result):
        """Test hybrid execution strategy."""
        strategy = HybridExecutionStrategy()

        context = ExecutionContext(
            tool_name="test_tool",
            tool_type="biomni",
            complexity=ToolComplexity.MODERATE,
            user_priority="normal",
        )

        with patch.object(
            strategy.fallback_manager,
            "execute_with_fallback",
            return_value=mock_successful_result,
        ) as mock_execute:
            result = await strategy.execute(context, arg1="value1")

            assert result == mock_successful_result
            mock_execute.assert_called_once_with("test_tool", arg1="value1")

    @pytest.mark.asyncio
    async def test_direct_subprocess_strategy(self, mock_successful_result):
        """Test direct subprocess execution strategy."""
        strategy = DirectSubprocessStrategy()

        context = ExecutionContext(
            tool_name="test_tool",
            tool_type="biomni",
            complexity=ToolComplexity.SIMPLE,
            user_priority="normal",
        )

        with patch.object(
            strategy.direct_executor,
            "execute_tool",
            return_value=mock_successful_result,
        ) as mock_execute:
            result = await strategy.execute(context, arg1="value1")

            assert result == mock_successful_result
            mock_execute.assert_called_once_with("test_tool", arg1="value1")

    @pytest.mark.asyncio
    async def test_mcp_strategy_execution(self, mock_successful_result):
        """Test MCP execution strategy."""
        strategy = MCPExecutionStrategy()

        context = ExecutionContext(
            tool_name="test_tool",
            tool_type="biomni",
            complexity=ToolComplexity.COMPLEX,
            user_priority="normal",
        )

        with patch.object(
            strategy.mcp_executor, "execute_tool", return_value=mock_successful_result
        ) as mock_execute:
            result = await strategy.execute(context, arg1="value1")

            assert result == mock_successful_result
            mock_execute.assert_called_once_with("test_tool", arg1="value1")

    def test_strategy_performance_estimation(self):
        """Test strategy performance estimation."""
        strategy = HybridExecutionStrategy()

        context = ExecutionContext(
            tool_name="unknown_tool",
            tool_type="biomni",
            complexity=ToolComplexity.MODERATE,
            user_priority="normal",
        )

        performance = strategy.estimate_performance(context)

        assert "estimated_success_rate" in performance
        assert "estimated_execution_time" in performance
        assert "estimated_retry_rate" in performance
        assert 0 <= performance["estimated_success_rate"] <= 1
        assert performance["estimated_execution_time"] > 0


class TestExecutionStrategyManager:
    """Test the execution strategy manager."""

    def test_strategy_selection_optimization(self):
        """Test strategy selection with different optimization strategies."""
        manager = get_strategy_manager()

        context = ExecutionContext(
            tool_name="test_tool",
            tool_type="biomni",
            complexity=ToolComplexity.MODERATE,
            user_priority="normal",
        )

        # Test different optimization strategies
        for opt_strategy in OptimizationStrategy:
            manager.set_optimization_strategy(opt_strategy)

            strategy = manager._select_optimal_strategy(context)
            assert strategy is not None
            assert strategy.get_strategy_name() in [
                "hybrid",
                "direct_subprocess",
                "desktop_commander",
            ]

    def test_strategy_score_calculation(self):
        """Test strategy scoring algorithm."""
        manager = get_strategy_manager()
        strategy = HybridExecutionStrategy()

        context = ExecutionContext(
            tool_name="test_tool",
            tool_type="biomni",
            complexity=ToolComplexity.MODERATE,
            user_priority="normal",
        )

        score = manager._calculate_strategy_score(strategy, context)
        assert isinstance(score, float)
        assert score >= 0  # Scores should be non-negative

    def test_context_specific_adjustments(self):
        """Test context-specific score adjustments."""
        manager = get_strategy_manager()
        strategy = DirectSubprocessStrategy()

        # Test critical priority adjustment
        critical_context = ExecutionContext(
            tool_name="test_tool",
            tool_type="biomni",
            complexity=ToolComplexity.SIMPLE,
            user_priority="critical",
        )

        normal_context = ExecutionContext(
            tool_name="test_tool",
            tool_type="biomni",
            complexity=ToolComplexity.SIMPLE,
            user_priority="normal",
        )

        base_score = 80.0
        critical_adjusted = manager._apply_context_adjustments(
            base_score, strategy, critical_context
        )
        normal_adjusted = manager._apply_context_adjustments(
            base_score, strategy, normal_context
        )

        # Adjustments should modify the score
        assert critical_adjusted != base_score or normal_adjusted != base_score

    @pytest.mark.asyncio
    async def test_optimized_tool_execution(self):
        """Test optimized tool execution through strategy manager."""
        manager = get_strategy_manager()

        mock_result = json.dumps(
            {"success": True, "result": "Test execution result", "tool": "test_tool"}
        )

        # Mock the strategy execution
        with patch.object(
            HybridExecutionStrategy, "execute", return_value=mock_result
        ) as mock_execute:

            result = await manager.execute_tool(
                tool_name="test_tool",
                tool_type="biomni",
                user_priority="normal",
                arg1="value1",
            )

            assert result == mock_result

    def test_strategy_recommendations(self):
        """Test strategy recommendations system."""
        manager = get_strategy_manager()

        recommendations = manager.get_strategy_recommendations("test_tool")

        assert "tool_name" in recommendations
        assert "recommended_strategy" in recommendations
        assert "strategy_analysis" in recommendations
        assert "ranked_strategies" in recommendations

        # Check that all strategies are analyzed
        assert len(recommendations["strategy_analysis"]) == 3  # hybrid, direct, mcp

        # Check ranking format
        for strategy_name, score in recommendations["ranked_strategies"]:
            assert isinstance(strategy_name, str)
            assert isinstance(score, (int, float))

    def test_performance_dashboard(self):
        """Test performance dashboard data generation."""
        manager = get_strategy_manager()

        dashboard = manager.get_performance_dashboard()

        required_keys = [
            "overall_performance",
            "strategy_comparison",
            "tool_health_overview",
            "recent_alerts",
            "optimization_strategy",
            "cache_status",
        ]

        for key in required_keys:
            assert key in dashboard


class TestPerformanceMonitoring:
    """Test performance monitoring and metrics collection."""

    def test_execution_metric_recording(self):
        """Test recording of execution metrics."""
        tracker = get_performance_tracker()

        metric = ExecutionMetric(
            tool_name="test_tool",
            execution_time=5.0,
            success=True,
            strategy_used="hybrid",
            retry_count=0,
        )

        initial_count = len(tracker.execution_metrics)
        tracker.record_execution(metric)

        assert len(tracker.execution_metrics) == initial_count + 1
        assert tracker.tool_metrics["test_tool"][-1] == metric
        assert tracker.strategy_metrics["hybrid"][-1] == metric

    def test_tool_statistics_calculation(self):
        """Test tool performance statistics calculation."""
        tracker = get_performance_tracker()

        # Add some test metrics
        for i in range(5):
            metric = ExecutionMetric(
                tool_name="stats_test_tool",
                execution_time=float(i + 1),
                success=i < 4,  # 4 successes, 1 failure
                strategy_used="hybrid",
                retry_count=0 if i < 3 else 1,
                timestamp=time.time() - (5 - i) * 60,  # Spread over 5 minutes
            )
            tracker.record_execution(metric)

        stats = tracker.get_tool_statistics("stats_test_tool", time_window_hours=1)

        assert stats["total_executions"] == 5
        assert stats["successful_executions"] == 4
        assert stats["failed_executions"] == 1
        assert stats["success_rate"] == 0.8
        assert stats["execution_time_stats"]["min"] == 1.0
        assert stats["execution_time_stats"]["max"] == 5.0

    def test_performance_alerts(self):
        """Test performance alerting system."""
        tracker = get_performance_tracker()

        # Create a slow execution that should trigger an alert
        slow_metric = ExecutionMetric(
            tool_name="slow_tool",
            execution_time=35.0,  # Above threshold
            success=True,
            strategy_used="hybrid",
        )

        initial_alerts = len(tracker.alerts)
        tracker.record_execution(slow_metric)

        # Should have triggered an execution time alert
        assert len(tracker.alerts) > initial_alerts

        recent_alerts = tracker.get_recent_alerts(hours=1)
        alert_types = [alert.alert_type for alert in recent_alerts]
        assert "execution_time" in alert_types

    def test_tool_health_assessment(self):
        """Test tool health status assessment."""
        tracker = get_performance_tracker()

        # Add metrics for a healthy tool
        for i in range(10):
            metric = ExecutionMetric(
                tool_name="healthy_tool",
                execution_time=2.0,  # Fast
                success=True,
                strategy_used="direct_subprocess",
                retry_count=0,
            )
            tracker.record_execution(metric)

        health = tracker.get_tool_health_status("healthy_tool")

        assert health["tool_name"] == "healthy_tool"
        assert health["health_status"] in [
            "excellent",
            "good",
            "fair",
            "poor",
            "unknown",
        ]
        assert health["success_rate"] == 1.0
        assert len(health["issues"]) == 0  # No issues for healthy tool


class TestErrorRecovery:
    """Test error detection and recovery mechanisms."""

    def test_error_detection(self):
        """Test error pattern detection."""
        detector = get_error_detector()

        # Test connection error detection
        connection_error = "No active session found for process"
        error_instance = detector.detect_error(connection_error, "test_tool")

        assert error_instance is not None
        assert error_instance.category == ErrorCategory.CONNECTION_ERROR
        assert error_instance.tool_name == "test_tool"

    def test_timeout_error_detection(self):
        """Test timeout error detection."""
        detector = get_error_detector()

        timeout_error = "Process execution timed out after 30 seconds"
        error_instance = detector.detect_error(timeout_error, "slow_tool")

        assert error_instance is not None
        assert error_instance.category == ErrorCategory.TIMEOUT_ERROR

    def test_error_frequency_tracking(self):
        """Test error frequency calculation."""
        detector = get_error_detector()

        # Generate multiple errors for the same tool
        for i in range(3):
            error_instance = detector.detect_error(
                "Connection timeout", "frequent_error_tool"
            )

        frequency = detector.get_error_frequency(
            "frequent_error_tool", time_window=3600
        )
        assert frequency == 3

    def test_recovery_decision_logic(self):
        """Test recovery attempt decision logic."""
        detector = get_error_detector()

        error_instance = detector.detect_error(
            "Import error: module not found", "missing_dep_tool"
        )

        # Should allow recovery for dependency errors
        should_recover = detector.should_attempt_recovery(
            error_instance, max_attempts=3
        )
        assert isinstance(should_recover, bool)


class TestConnectionPooling:
    """Test connection pooling and session management."""

    def test_connection_pool_initialization(self):
        """Test connection pool initialization."""
        pool = get_connection_pool()

        assert pool.max_connections_per_server > 0
        assert pool.connection_timeout > 0
        assert pool.idle_timeout > 0

    def test_connection_stats(self):
        """Test connection pool statistics."""
        pool = get_connection_pool()

        stats = pool.get_connection_stats()

        assert isinstance(stats, dict)
        assert "total_connections" in stats
        assert "active_connections" in stats
        assert "connection_reuses" in stats

    def test_server_connection_count(self):
        """Test server-specific connection counting."""
        pool = get_connection_pool()

        server_stats = pool.get_server_connection_count("desktop-commander")

        assert isinstance(server_stats, dict)
        assert "total" in server_stats
        assert "active" in server_stats
        assert "idle" in server_stats
        assert "failed" in server_stats


class TestExecutionConfiguration:
    """Test execution configuration system."""

    def test_timeout_configuration(self):
        """Test configurable timeout values."""
        config = get_execution_config()

        # Test different tool complexities
        simple_timeouts = config.get_timeout_config("simple_tool")
        complex_timeouts = config.get_timeout_config("complex_analysis_tool")

        assert (
            simple_timeouts["execution_timeout"] < complex_timeouts["execution_timeout"]
        )
        assert "connection_timeout" in simple_timeouts
        assert "total_timeout" in simple_timeouts

    def test_retry_configuration(self):
        """Test configurable retry settings."""
        config = get_execution_config()

        retry_config = config.get_retry_config("test_tool")

        assert "max_retries" in retry_config
        assert "backoff_factor" in retry_config
        assert "initial_delay" in retry_config
        assert retry_config["max_retries"] >= 0
        assert retry_config["backoff_factor"] >= 1.0

    def test_tool_complexity_detection(self):
        """Test tool complexity detection."""
        config = get_execution_config()

        # Test pattern-based detection
        simple_complexity = config.get_tool_complexity("get_protein_info")
        complex_complexity = config.get_tool_complexity(
            "differential_expression_analysis"
        )

        assert simple_complexity in [ToolComplexity.SIMPLE, ToolComplexity.MODERATE]
        assert complex_complexity in [ToolComplexity.COMPLEX, ToolComplexity.INTENSIVE]

    def test_execution_strategy_preference(self):
        """Test execution strategy preferences."""
        config = get_execution_config()

        biomni_strategy = config.get_execution_strategy("query_uniprot", "biomni")
        filesystem_strategy = config.get_execution_strategy("read_file", "filesystem")

        # Should return valid strategy enums
        assert isinstance(biomni_strategy, ExecutionStrategy)
        assert isinstance(filesystem_strategy, ExecutionStrategy)


@pytest.mark.integration
class TestIntegrationScenarios:
    """Integration tests for complete execution optimization system."""

    @pytest.mark.asyncio
    async def test_end_to_end_optimized_execution(self):
        """Test complete optimized execution flow."""
        # This would test the full integration but requires actual biomni tools
        # For now, we'll test the framework integration

        manager = get_strategy_manager()

        # Mock the underlying execution
        mock_result = json.dumps(
            {
                "success": True,
                "result": {"protein_id": "TEST123", "data": "test_data"},
                "tool": "query_uniprot",
                "execution_time": 3.5,
            }
        )

        with patch.object(HybridExecutionStrategy, "execute", return_value=mock_result):
            result = await manager.execute_tool(
                tool_name="query_uniprot",
                tool_type="biomni",
                user_priority="normal",
                uniprot_id="TEST123",
            )

            assert result == mock_result

    def test_performance_monitoring_integration(self):
        """Test integration between strategy manager and performance monitoring."""
        manager = get_strategy_manager()
        tracker = get_performance_tracker()

        # The strategy manager should be using the performance tracker
        assert manager.performance_tracker is tracker

        # Performance data should influence strategy selection
        dashboard = manager.get_performance_dashboard()
        assert "overall_performance" in dashboard
        assert "strategy_comparison" in dashboard

    def test_error_recovery_integration(self):
        """Test integration between execution strategies and error recovery."""
        manager = get_strategy_manager()
        detector = get_error_detector()

        # Error patterns should be detected and influence strategy selection
        test_error = "Connection timeout occurred"
        error_instance = detector.detect_error(test_error, "test_tool")

        assert error_instance is not None

        # Error history should be accessible for strategy optimization
        frequency = detector.get_error_frequency("test_tool")
        assert isinstance(frequency, int)


if __name__ == "__main__":
    # Run a simple test to verify the system works
    async def simple_test():
        """Simple test to verify system functionality."""
        manager = get_strategy_manager()

        # Test strategy selection
        recommendations = manager.get_strategy_recommendations("test_tool")
        print(f"Strategy recommendations: {recommendations['recommended_strategy']}")

        # Test performance dashboard
        dashboard = manager.get_performance_dashboard()
        print(f"Dashboard keys: {list(dashboard.keys())}")

        print("✅ Basic system tests passed!")

    asyncio.run(simple_test())
