#!/usr/bin/env python3
"""
Test shell escaping issues.
"""

import subprocess
import json


def test_shell_escaping():
    """Test shell escaping with subprocess."""
    print("🔍 Testing shell escaping with subprocess...")

    # Test the exact same command structure used in the direct execution
    kwargs = {"prompt": "Q86VK4"}
    kwargs_str = repr(kwargs)
    tool_name = "query_uniprot"

    python_code = f"""
import sys
import os
import json
import traceback

# Add only the biomni module directory to avoid project dependency issues
biomni_module_path = "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
if biomni_module_path not in sys.path:
    sys.path.insert(0, biomni_module_path)

try:
    # Import the specific tool function directly from biomni module
    from biomni.database import {tool_name}
    
    # Execute the tool with provided arguments
    tool_kwargs = {kwargs_str}
    result = {tool_name}(**tool_kwargs)
    
    # Return the result as JSON with success marker
    output = {{"success": True, "result": result, "tool": "{tool_name}"}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(output, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except ImportError as e:
    error_result = {{"success": False, "error": f"Import error: {{str(e)}}", "tool": "{tool_name}", "args": {kwargs_str}}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except Exception as e:
    error_result = {{"success": False, "error": str(e), "traceback": traceback.format_exc(), "tool": "{tool_name}", "args": {kwargs_str}}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
"""

    print("📜 Testing with subprocess...")

    # Test the exact command structure
    cmd = [
        "bash",
        "-c",
        f"source /Users/<USER>/miniforge3/bin/activate biomni_e1 && python3 -c '{python_code}'",
    ]

    print(f"Command: {cmd}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")

    except Exception as e:
        print(f"❌ Subprocess failed: {e}")


if __name__ == "__main__":
    test_shell_escaping()
