#!/usr/bin/env python3
"""
Simple validation test for biomni integration.
"""

import sys
import os

sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def test_biomni_integration():
    """Test that biomni tools are properly integrated"""
    print("🧪 Testing Biomni Integration...")

    # Test 1: Check if biomni tools can be loaded
    try:
        from src.tools.biomni_tools import get_biomni_tools

        tools = get_biomni_tools()
        print(f"✅ Successfully loaded {len(tools)} biomni tools")

        # Find query_uniprot
        uniprot_tool = None
        for tool in tools:
            if tool.name == "query_uniprot":
                uniprot_tool = tool
                break

        if uniprot_tool:
            print(f"✅ Found query_uniprot tool: {uniprot_tool.description}")
        else:
            print("❌ query_uniprot tool not found")
            return False

    except Exception as e:
        print(f"❌ Failed to load biomni tools: {e}")
        return False

    # Test 2: Check if tool retriever works
    try:
        from src.tools.tool_retriever import biomni_tool_retriever

        result = biomni_tool_retriever.invoke(
            {
                "query": "query uniprot for Q9Y2R9",
                "include_mcp_tools": False,
                "include_biomni_tools": True,
            }
        )
        if "query_uniprot" in result:
            print("✅ Tool retriever successfully found biomni tools")
        else:
            print("❌ Tool retriever did not find biomni tools")
            return False

    except Exception as e:
        print(f"❌ Tool retriever test failed: {e}")
        return False

    # Test 3: Check agent initialization
    try:
        from src.agents.agents import agents, agent_tools

        if "researcher" in agents:
            researcher_tools = agent_tools.get("researcher", [])

            # Check if agent has biomni tools
            biomni_tools = [
                tool
                for tool in researcher_tools
                if hasattr(tool, "name") and "query_" in tool.name
            ]
            if biomni_tools:
                print(
                    f"✅ Agent has {len(biomni_tools)} database tools including biomni tools"
                )
            else:
                print("❌ Agent does not have biomni tools")
                return False
        else:
            print("❌ Researcher agent not found")
            return False

    except Exception as e:
        print(f"❌ Agent initialization test failed: {e}")
        return False

    return True


if __name__ == "__main__":
    success = test_biomni_integration()
    if success:
        print("\n🎉 All tests passed! Biomni integration is working correctly.")
        print("\nUsers can now use commands like:")
        print("- 'query the uniprot database for Q9Y2R9'")
        print("- 'get protein structure from alphafold for P53_HUMAN'")
        print("- 'search pdb for insulin structures'")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the integration.")
        sys.exit(1)
