#!/usr/bin/env python3
"""
Debug conda environment paths.
"""

import subprocess
import tempfile
import os
from dotenv import load_dotenv

load_dotenv()


def debug_conda_paths():
    """Debug conda environment activation paths."""
    print("🔍 Debugging conda environment paths...")

    # Test 1: Check which conda
    print("\n📋 Testing 'which conda'...")
    result = subprocess.run(["which", "conda"], capture_output=True, text=True)
    print(f"which conda: {result.stdout.strip()}")
    print(f"conda stderr: {result.stderr}")

    # Test 2: Check conda info
    print("\n📋 Testing 'conda info'...")
    result = subprocess.run(["conda", "info", "--base"], capture_output=True, text=True)
    print(f"conda base: {result.stdout.strip()}")
    print(f"conda info stderr: {result.stderr}")

    # Test 3: Check if biomni_e1 exists
    print("\n📋 Testing conda env list...")
    result = subprocess.run(["conda", "env", "list"], capture_output=True, text=True)
    print(f"conda envs:\n{result.stdout}")

    # Test 4: Test the exact command used in biomni_tools.py
    print("\n📋 Testing the exact activation command...")

    diagnostic_code = """
import sys
print(f"Python executable: {sys.executable}")
try:
    import Bio
    print(f"✅ Bio found: {Bio.__file__}")
except ImportError as e:
    print(f"❌ Bio not found: {e}")
"""

    with tempfile.NamedTemporaryFile(mode="w", suffix=".py", delete=False) as temp_file:
        temp_file.write(diagnostic_code)
        temp_file_path = temp_file.name

    try:
        # Test the exact command structure
        cmd = [
            "bash",
            "-c",
            f"source /Users/<USER>/miniforge3/bin/activate biomni_e1 && python3 {temp_file_path}",
        ]

        print(f"Command: {' '.join(cmd)}")

        # Create environment with current env plus our specific variables
        env = os.environ.copy()
        env.update(
            {
                "REASONING_PROVIDER": os.environ.get("REASONING_PROVIDER", "gemini"),
                "REASONING_API_KEY": os.environ.get("REASONING_API_KEY", ""),
                "REASONING_BASE_URL": os.environ.get("REASONING_BASE_URL", ""),
                "REASONING_MODEL": os.environ.get("REASONING_MODEL", "gemini-2.5-pro"),
            }
        )

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=30, env=env
        )
        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        if result.stderr:
            print(f"Stderr: {result.stderr}")

    finally:
        try:
            os.unlink(temp_file_path)
        except:
            pass


if __name__ == "__main__":
    debug_conda_paths()
