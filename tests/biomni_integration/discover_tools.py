#!/usr/bin/env python3
"""
Discover all available biomni tools to understand the scope of testing needed.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)


def discover_biomni_tools():
    """Discover all available biomni tools."""
    print("🔍 Discovering Biomni Tools...")

    try:
        from src.tools.biomni.tool_modules import load_all_tools
        from src.tools.biomni.tool_registry import ToolRegistry

        # Load all tools
        all_tools = load_all_tools()
        print(f"✅ Found {len(all_tools)} tool modules")

        # Create registry
        registry = ToolRegistry(all_tools)
        print(f"✅ Registered {len(registry.tools)} individual tools")

        # Group tools by module
        tools_by_module = {}
        for tool in registry.tools:
            # Extract module name from tool metadata or default categorization
            module_name = "unknown"
            for module_key in all_tools.keys():
                if any(t["name"] == tool["name"] for t in all_tools[module_key]):
                    module_name = module_key.split(".")[
                        -1
                    ]  # Get the last part like 'database'
                    break

            if module_name not in tools_by_module:
                tools_by_module[module_name] = []
            tools_by_module[module_name].append(tool)

        # Print summary by module
        print("\n📊 Tools by Module:")
        total_tools = 0
        for module, tools_list in sorted(tools_by_module.items()):
            print(f"  {module}: {len(tools_list)} tools")
            total_tools += len(tools_list)

        print(f"\n🎯 Total tools discovered: {total_tools}")

        # Save tool discovery results
        discovery_results = {
            "total_tools": total_tools,
            "modules": {
                module: len(tools_list)
                for module, tools_list in tools_by_module.items()
            },
            "tool_details": [],
        }

        # Add tool details
        for tool in registry.tools:
            tool_detail = {
                "name": tool["name"],
                "description": tool["description"],
                "required_parameters": tool.get("required_parameters", []),
                "optional_parameters": tool.get("optional_parameters", []),
                "module": next(
                    (
                        module.split(".")[-1]
                        for module_key in all_tools.keys()
                        for module in [module_key]
                        if any(t["name"] == tool["name"] for t in all_tools[module_key])
                    ),
                    "unknown",
                ),
            }
            discovery_results["tool_details"].append(tool_detail)

        # Save to file
        results_file = "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/tests/biomni_integration/tool_discovery_results.json"
        with open(results_file, "w") as f:
            json.dump(discovery_results, f, indent=2)

        print(f"💾 Discovery results saved to: tool_discovery_results.json")

        # Show sample tools from each category
        print("\n🔬 Sample Tools by Category:")
        for module, tools_list in sorted(tools_by_module.items()):
            if tools_list:  # Only show if there are tools
                print(f"\n  {module.upper()}:")
                for i, tool in enumerate(tools_list[:3]):  # Show first 3 tools
                    print(f"    - {tool['name']}: {tool['description'][:60]}...")
                if len(tools_list) > 3:
                    print(f"    ... and {len(tools_list) - 3} more")

        return discovery_results

    except Exception as e:
        print(f"❌ Tool discovery failed: {e}")
        import traceback

        traceback.print_exc()
        return None


if __name__ == "__main__":
    results = discover_biomni_tools()

    if results:
        print(f"\n🎉 Successfully discovered {results['total_tools']} biomni tools!")
        print(f"📁 Results saved for testing framework development")
    else:
        print("\n❌ Tool discovery failed")
        sys.exit(1)
