#!/usr/bin/env python3
"""
Debug subprocess environment activation.
"""

import subprocess
import tempfile
import os


def debug_subprocess_environment():
    """Debug what's happening in the subprocess execution."""
    print("🔍 Debugging subprocess environment activation...")

    # Create a diagnostic script
    diagnostic_code = """
import sys
import os
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path}")
print(f"CONDA_DEFAULT_ENV: {os.environ.get('CONDA_DEFAULT_ENV', 'Not set')}")
print(f"CONDA_PREFIX: {os.environ.get('CONDA_PREFIX', 'Not set')}")

# Test Bio import
try:
    import Bio
    print(f"✅ Bio module found: {Bio.__file__}")
    print(f"✅ Bio version: {Bio.__version__}")
except ImportError as e:
    print(f"❌ Bio import failed: {e}")

# Test environment variables that we're setting
print(f"REASONING_PROVIDER: {os.environ.get('REASONING_PROVIDER', 'Not set')}")
print(f"REASONING_API_KEY: {os.environ.get('REASONING_API_KEY', 'Not set')[:10]}...")
"""

    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode="w", suffix=".py", delete=False) as temp_file:
        temp_file.write(diagnostic_code)
        temp_file_path = temp_file.name

    try:
        # Test the exact command structure used in biomni_tools.py
        cmd = [
            "bash",
            "-c",
            f"source /Users/<USER>/miniforge3/bin/activate biomni_e1 && python3 {temp_file_path}",
        ]

        print(f"📋 Running command: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        print(f"\n📤 Return code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📤 STDERR:\n{result.stderr}")

    finally:
        # Clean up
        try:
            os.unlink(temp_file_path)
        except:
            pass


if __name__ == "__main__":
    debug_subprocess_environment()
