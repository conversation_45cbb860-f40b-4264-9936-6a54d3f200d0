#!/usr/bin/env python3
"""
Debug the kwargs generation issue.
"""

import json


def debug_kwargs():
    """Debug how kwargs are being processed."""
    print("🔍 Debugging kwargs processing...")

    # Simulate the same kwargs that would be passed
    kwargs = {"prompt": "Q86VK4"}
    kwargs_str = repr(kwargs)
    tool_name = "query_uniprot"

    print(f"Original kwargs: {kwargs}")
    print(f"kwargs_str: {kwargs_str}")
    print(f"tool_name: {tool_name}")

    # Generate the same Python code that would be created
    python_code = f"""
import sys
import os
import json
import traceback

# Add only the biomni module directory to avoid project dependency issues
biomni_module_path = "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
if biomni_module_path not in sys.path:
    sys.path.insert(0, biomni_module_path)

try:
    # Import the specific tool function directly from biomni module
    from biomni.database import {tool_name}
    
    # Execute the tool with provided arguments
    tool_kwargs = {kwargs_str}
    result = {tool_name}(**tool_kwargs)
    
    # Return the result as JSON with success marker
    output = {{"success": True, "result": result, "tool": "{tool_name}"}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(output, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except ImportError as e:
    error_result = {{"success": False, "error": f"Import error: {{str(e)}}", "tool": "{tool_name}", "args": {kwargs_str}}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except Exception as e:
    error_result = {{"success": False, "error": str(e), "traceback": traceback.format_exc(), "tool": "{tool_name}", "args": {kwargs_str}}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
"""

    print("\n📜 Generated Python code:")
    print("=" * 80)
    print(python_code)
    print("=" * 80)

    # Test that the kwargs can be properly evaluated
    try:
        test_kwargs = eval(kwargs_str)
        print(f"\n✅ kwargs_str can be evaluated: {test_kwargs}")
    except Exception as e:
        print(f"\n❌ kwargs_str evaluation failed: {e}")


if __name__ == "__main__":
    debug_kwargs()
