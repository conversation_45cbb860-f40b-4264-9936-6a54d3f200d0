#!/usr/bin/env python3
"""
Database Tools Reliability Assessment

Test all database tools (31 tools) to assess reliability and identify patterns.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)

from test_biomni_reliability_assessment import BiomniReliabilityTester


def run_database_assessment():
    """Run comprehensive assessment of database tools."""
    print("🧬 Starting Database Tools Reliability Assessment")

    # Create tester with shorter timeout for faster testing
    tester = BiomniReliabilityTester(test_timeout=20, max_retry_attempts=1)

    # Test database module specifically
    print("📊 Testing database tools...")
    summary = tester.test_tools_by_module("database")

    # Print detailed results
    print(f"\n📈 Database Tools Assessment Results:")
    print(f"  Total tools: {summary.total_tools}")
    print(f"  Successful: {summary.successful_tools}")
    print(f"  Failed: {summary.failed_tools}")
    print(f"  Timeouts: {summary.timeout_tools}")
    print(f"  Errors: {summary.error_tools}")
    print(f"  Success rate: {summary.success_rate:.1f}%")
    print(f"  Average execution time: {summary.average_execution_time:.2f}s")

    # Show individual tool results
    print(f"\n🔬 Individual Tool Results:")
    successful_tools = []
    failed_tools = []

    for result in summary.tool_results:
        status_icon = "✅" if result.test_status == "success" else "❌"
        print(
            f"  {status_icon} {result.tool_name}: {result.test_status} ({result.execution_time:.2f}s)"
        )

        if result.test_status == "success":
            successful_tools.append(result.tool_name)
        else:
            failed_tools.append(
                {
                    "name": result.tool_name,
                    "error": result.error_message,
                    "type": result.error_type,
                }
            )

    # Analyze failure patterns
    if failed_tools:
        print(f"\n⚠️  Failure Analysis:")
        error_types = {}
        for tool in failed_tools:
            error_type = tool["type"] or "unknown"
            if error_type not in error_types:
                error_types[error_type] = []
            error_types[error_type].append(tool["name"])

        for error_type, tools in error_types.items():
            print(f"  • {error_type}: {len(tools)} tools")
            for tool_name in tools[:3]:  # Show first 3
                print(f"    - {tool_name}")
            if len(tools) > 3:
                print(f"    ... and {len(tools) - 3} more")

    # Generate recommendations
    print(f"\n💡 Recommendations:")
    if summary.success_rate > 80:
        print("  • Database tools show good reliability")
    elif summary.success_rate > 60:
        print("  • Database tools need moderate reliability improvements")
    else:
        print("  • Database tools require significant reliability improvements")

    if summary.timeout_tools > 0:
        print(
            f"  • Consider increasing timeout for {summary.timeout_tools} tools that timeout"
        )

    # Save detailed results
    results_data = {
        "module": "database",
        "summary": {
            "total_tools": summary.total_tools,
            "successful_tools": summary.successful_tools,
            "failed_tools": summary.failed_tools,
            "timeout_tools": summary.timeout_tools,
            "error_tools": summary.error_tools,
            "success_rate": summary.success_rate,
            "average_execution_time": summary.average_execution_time,
        },
        "successful_tools": successful_tools,
        "failed_tools": failed_tools,
        "individual_results": [
            {
                "tool_name": r.tool_name,
                "status": r.test_status,
                "execution_time": r.execution_time,
                "error_message": r.error_message,
                "error_type": r.error_type,
            }
            for r in summary.tool_results
        ],
    }

    results_file = f"/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/tests/biomni_integration/database_tools_assessment.json"
    with open(results_file, "w") as f:
        json.dump(results_data, f, indent=2)

    print(f"\n💾 Detailed results saved to: database_tools_assessment.json")

    return results_data


if __name__ == "__main__":
    results = run_database_assessment()

    # Print final summary
    print(f"\n🎯 Database Tools Assessment Complete!")
    print(
        f"📊 {results['summary']['successful_tools']}/{results['summary']['total_tools']} tools working ({results['summary']['success_rate']:.1f}%)"
    )

    if results["summary"]["success_rate"] >= 70:
        print("🟢 Database tools reliability is acceptable")
    elif results["summary"]["success_rate"] >= 50:
        print("🟡 Database tools reliability needs improvement")
    else:
        print("🔴 Database tools reliability requires urgent attention")
