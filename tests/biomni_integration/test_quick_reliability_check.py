#!/usr/bin/env python3
"""
Quick Biomni Tool Reliability Check

A lightweight version to test the framework with a subset of tools
for faster validation and development.
"""

import os
import sys
import json
import time
import pytest
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(
    0,
    "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QuickReliabilityChecker:
    """Quick reliability checker for testing framework validation."""

    def __init__(self, test_timeout: int = 15):
        self.test_timeout = test_timeout
        self.test_results = []

        # Initialize biomni tools
        self._initialize_biomni_tools()

    def _initialize_biomni_tools(self):
        """Initialize biomni tools and registry."""
        try:
            from src.tools.biomni.tool_modules import load_all_tools
            from src.tools.biomni.tool_registry import ToolRegistry
            from src.tools.biomni_tools import _execute_biomni_tool_direct

            self.all_tools = load_all_tools()
            self.registry = ToolRegistry(self.all_tools)
            self.execute_tool = _execute_biomni_tool_direct

            logger.info(f"Initialized with {len(self.registry.tools)} tools")

        except Exception as e:
            logger.error(f"Failed to initialize biomni tools: {e}")
            raise

    def get_sample_tools_by_module(
        self, module_name: str, max_tools: int = 3
    ) -> List[Dict]:
        """Get a sample of tools from a specific module for quick testing."""
        module_tools = []

        for tool in self.registry.tools:
            # Find module for this tool
            tool_module = "unknown"
            for module_key in self.all_tools.keys():
                if any(t["name"] == tool["name"] for t in self.all_tools[module_key]):
                    tool_module = module_key.split(".")[-1]
                    break

            if tool_module == module_name:
                tool["module"] = tool_module
                module_tools.append(tool)

                if len(module_tools) >= max_tools:
                    break

        return module_tools

    def test_tool_execution(self, tool_name: str) -> Dict:
        """Test execution of a single tool with basic parameters."""
        start_time = time.time()

        try:
            # Use basic parameters that work for most tools
            if "query" in tool_name.lower() or "search" in tool_name.lower():
                if "uniprot" in tool_name.lower():
                    result = self.execute_tool(tool_name, prompt="Q86VK4")
                elif "alphafold" in tool_name.lower():
                    result = self.execute_tool(tool_name, uniprot_id="Q86VK4")
                else:
                    result = self.execute_tool(tool_name, prompt="Q86VK4")
            else:
                # For other tools, try with basic parameters
                result = self.execute_tool(tool_name, prompt="test")

            execution_time = time.time() - start_time

            if result:
                try:
                    parsed = json.loads(result)
                    success = parsed.get("success", False)
                    return {
                        "tool_name": tool_name,
                        "status": "success" if success else "failure",
                        "execution_time": execution_time,
                        "error": (
                            None if success else parsed.get("error", "Unknown error")
                        ),
                    }
                except json.JSONDecodeError:
                    return {
                        "tool_name": tool_name,
                        "status": "parse_error",
                        "execution_time": execution_time,
                        "error": "Failed to parse JSON result",
                    }
            else:
                return {
                    "tool_name": tool_name,
                    "status": "no_result",
                    "execution_time": execution_time,
                    "error": "No result returned",
                }

        except Exception as e:
            execution_time = time.time() - start_time
            return {
                "tool_name": tool_name,
                "status": "exception",
                "execution_time": execution_time,
                "error": str(e),
            }

    def run_quick_check(
        self, modules_to_test: List[str] = None, tools_per_module: int = 3
    ) -> Dict:
        """Run quick reliability check on sample tools."""
        if modules_to_test is None:
            modules_to_test = ["database", "biochemistry", "genomics"]

        logger.info(f"Running quick reliability check on modules: {modules_to_test}")

        results = {"modules_tested": {}, "overall_stats": {}, "test_results": []}

        total_tested = 0
        total_successful = 0

        for module_name in modules_to_test:
            logger.info(f"Testing module: {module_name}")

            sample_tools = self.get_sample_tools_by_module(
                module_name, tools_per_module
            )
            module_results = []
            module_successful = 0

            for tool_info in sample_tools:
                result = self.test_tool_execution(tool_info["name"])
                module_results.append(result)
                results["test_results"].append(result)

                if result["status"] == "success":
                    module_successful += 1
                    total_successful += 1

                total_tested += 1

                logger.info(
                    f"  {tool_info['name']}: {result['status']} ({result['execution_time']:.2f}s)"
                )

            results["modules_tested"][module_name] = {
                "tools_tested": len(sample_tools),
                "successful": module_successful,
                "success_rate": (
                    (module_successful / len(sample_tools) * 100) if sample_tools else 0
                ),
                "results": module_results,
            }

        results["overall_stats"] = {
            "total_tested": total_tested,
            "total_successful": total_successful,
            "overall_success_rate": (
                (total_successful / total_tested * 100) if total_tested > 0 else 0
            ),
        }

        return results


def test_quick_biomni_reliability():
    """Quick test function for development."""
    checker = QuickReliabilityChecker(test_timeout=15)
    results = checker.run_quick_check(["database", "biochemistry"], tools_per_module=2)

    print(f"\n🚀 Quick Reliability Check Results:")
    print(
        f"📊 Overall: {results['overall_stats']['total_successful']}/{results['overall_stats']['total_tested']} tools successful ({results['overall_stats']['overall_success_rate']:.1f}%)"
    )

    for module, stats in results["modules_tested"].items():
        print(
            f"📁 {module}: {stats['successful']}/{stats['tools_tested']} successful ({stats['success_rate']:.1f}%)"
        )

    # Basic assertions
    assert results["overall_stats"]["total_tested"] > 0, "Should test some tools"
    assert (
        results["overall_stats"]["overall_success_rate"] >= 0
    ), "Should have measurable success rate"

    return results


class TestQuickReliability:
    """Pytest test class for quick reliability validation."""

    def test_framework_initialization(self):
        """Test that the reliability framework can be initialized."""
        checker = QuickReliabilityChecker()
        assert hasattr(checker, "registry")
        assert len(checker.registry.tools) == 181

    def test_sample_database_tools(self):
        """Test a sample of database tools."""
        checker = QuickReliabilityChecker()
        results = checker.run_quick_check(["database"], tools_per_module=2)

        assert results["overall_stats"]["total_tested"] > 0
        assert "database" in results["modules_tested"]

        # Log results
        stats = results["modules_tested"]["database"]
        logger.info(
            f"Database sample: {stats['successful']}/{stats['tools_tested']} successful"
        )

    def test_sample_biochemistry_tools(self):
        """Test a sample of biochemistry tools."""
        checker = QuickReliabilityChecker()
        results = checker.run_quick_check(["biochemistry"], tools_per_module=2)

        assert results["overall_stats"]["total_tested"] > 0
        assert "biochemistry" in results["modules_tested"]

        # Log results
        stats = results["modules_tested"]["biochemistry"]
        logger.info(
            f"Biochemistry sample: {stats['successful']}/{stats['tools_tested']} successful"
        )


if __name__ == "__main__":
    test_quick_biomni_reliability()
