"""
Test suite for interactive debugging functionality.

This test validates the interactive debugging implementation,
API call reduction, and error recovery capabilities.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from src.tools.interactive_debugging import (
    InteractiveDebugger,
    InteractiveSession,
    SessionStatus,
    get_interactive_debugger
)


class TestInteractiveDebugging:
    """Test suite for interactive debugging functionality."""
    
    @pytest.fixture
    def debugger(self):
        """Create a fresh debugger instance for testing."""
        return InteractiveDebugger()
    
    @pytest.fixture
    def mock_desktop_tools(self):
        """Mock desktop-commander tools for testing."""
        tools = {
            'start_process': <PERSON>ck(),
            'interact_with_process': <PERSON>ck(),
            'read_process_output': <PERSON><PERSON>(),
            'force_terminate': <PERSON>ck()
        }
        
        # Configure start_process mock
        tools['start_process'].invoke.return_value = "Process started with PID 12345"
        
        # Configure interact_with_process mock for success
        tools['interact_with_process'].invoke.return_value = ">>> 42\n42\n>>>"
        
        return tools
    
    @pytest.mark.asyncio
    async def test_session_creation(self, debugger, mock_desktop_tools):
        """Test interactive debugging session creation."""
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            assert session.session_id is not None
            assert session.process_id == 12345
            assert session.status == SessionStatus.ACTIVE
            assert session.session_id in debugger.active_sessions
            
            # Verify metrics updated
            assert debugger.performance_metrics['total_sessions'] == 1
    
    @pytest.mark.asyncio
    async def test_code_validation_success(self, debugger, mock_desktop_tools):
        """Test successful code validation step."""
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            # Test successful code execution
            success, output, error_info = await debugger.validate_code_step(
                session, "print('Hello, World!')"
            )
            
            assert success is True
            assert "42" in output
            assert error_info is None
            assert len(session.validated_steps) == 1
            assert session.api_calls_saved >= 1
    
    @pytest.mark.asyncio
    async def test_code_validation_error(self, debugger, mock_desktop_tools):
        """Test code validation with error."""
        # Configure mock to return error
        mock_desktop_tools['interact_with_process'].invoke.return_value = (
            ">>> invalid_syntax\n"
            "  File \"<stdin>\", line 1\n"
            "    invalid_syntax\n"
            "                 ^\n"
            "SyntaxError: invalid syntax\n"
            ">>>"
        )
        
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            success, output, error_info = await debugger.validate_code_step(
                session, "invalid_syntax"
            )
            
            assert success is False
            assert "SyntaxError" in output
            assert error_info is not None
            assert error_info['type'] == 'execution_error'
            assert len(session.errors_encountered) == 1
    
    @pytest.mark.asyncio
    async def test_caching_functionality(self, debugger, mock_desktop_tools):
        """Test code caching for performance optimization."""
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            code = "print('test caching')"
            
            # First execution - should cache result
            success1, output1, _ = await debugger.validate_code_step(session, code)
            assert success1 is True
            
            # Second execution - should use cache
            success2, output2, _ = await debugger.validate_code_step(session, code)
            assert success2 is True
            assert session.cache_hits >= 1
            
            # Verify cache contains the result
            cached_entry = debugger._get_cached_result(code, session.session_context)
            assert cached_entry is not None
            assert cached_entry.success is True
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, debugger, mock_desktop_tools):
        """Test error recovery mechanisms."""
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            error_info = {
                'code': 'undefined_variable',
                'error': 'NameError: name "undefined_variable" is not defined',
                'type': 'execution_error'
            }
            
            recovery_code = "undefined_variable = 42"
            
            # Configure mock for successful recovery
            mock_desktop_tools['interact_with_process'].invoke.return_value = ">>> 42\n>>>"
            
            success, message = await debugger.recover_from_error(
                session, error_info, recovery_code
            )
            
            assert success is True
            assert "Recovery successful" in message
            assert session.retry_count >= 1
    
    @pytest.mark.asyncio
    async def test_session_checkpointing(self, debugger, mock_desktop_tools):
        """Test session checkpointing functionality."""
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            # Add some validated steps
            session.add_validated_step("import pandas as pd")
            session.add_validated_step("df = pd.DataFrame({'A': [1, 2, 3]})")
            
            # Create checkpoint
            checkpoint = debugger._create_checkpoint(session)
            
            assert checkpoint.session_id == session.session_id
            assert len(checkpoint.validated_steps) == 2
            assert session.session_id in debugger.checkpoints
            
            # Test restoration
            restored_session = debugger._restore_from_checkpoint(session.session_id)
            assert restored_session is not None
            assert len(restored_session.validated_steps) == 2
    
    @pytest.mark.asyncio
    async def test_performance_metrics(self, debugger, mock_desktop_tools):
        """Test performance metrics tracking."""
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            # Execute several code steps
            for i in range(5):
                await debugger.validate_code_step(session, f"x = {i}")
            
            await debugger.terminate_session(session)
            
            metrics = debugger.get_performance_metrics()
            
            assert metrics['session_metrics']['total_sessions'] == 1
            assert metrics['session_metrics']['successful_sessions'] == 1
            assert metrics['efficiency_metrics']['total_api_calls_saved'] >= 5
            assert metrics['cache_metrics']['cache_size'] >= 0
    
    @pytest.mark.asyncio
    async def test_api_call_reduction(self, debugger, mock_desktop_tools):
        """Test API call reduction measurement."""
        baseline_calls = 0
        interactive_calls = 0
        
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            session = await debugger.start_debugging_session()
            
            # Simulate baseline approach (one call per step)
            test_code_blocks = [
                "import pandas as pd",
                "df = pd.DataFrame({'A': [1, 2, 3]})",
                "print(df.head())",
                "result = df['A'].sum()",
                "print(f'Sum: {result}')"
            ]
            
            baseline_calls = len(test_code_blocks)  # Traditional approach: 1 call per block
            
            # Interactive debugging approach
            for code in test_code_blocks:
                success, output, error_info = await debugger.validate_code_step(session, code)
                if success:
                    interactive_calls += 1
            
            # Calculate reduction
            api_calls_saved = session.api_calls_saved
            reduction_percentage = (api_calls_saved / max(1, baseline_calls)) * 100
            
            # Should achieve some API call reduction due to step-by-step validation
            # and potential caching
            assert api_calls_saved > 0
            assert reduction_percentage >= 0  # Any reduction is beneficial
            
            await debugger.terminate_session(session)
    
    def test_code_extraction_patterns(self):
        """Test code block extraction from text."""
        from src.graph.nodes import _extract_code_blocks
        
        # Test with code blocks
        text_with_blocks = """
        Here's some analysis:
        
        ```python
        import pandas as pd
        df = pd.read_csv('data.csv')
        ```
        
        And then:
        
        ```
        print(df.head())
        ```
        """
        
        blocks = _extract_code_blocks(text_with_blocks)
        # The implementation combines blocks, so we expect at least 1 block
        assert len(blocks) >= 1
        assert "import pandas" in blocks[0]
        assert "print(df.head())" in blocks[0]
        
        # Test with inline code
        text_inline = """
        Let's start with:
        import numpy as np
        x = np.array([1, 2, 3])
        print(x.mean())
        """
        
        blocks = _extract_code_blocks(text_inline)
        assert len(blocks) >= 1
    
    def test_recovery_code_generation(self):
        """Test recovery code generation for common errors."""
        from src.graph.nodes import _generate_recovery_code
        
        # Test module not found error
        error_info = {
            'error': 'ModuleNotFoundError: No module named "pandas"'
        }
        recovery = _generate_recovery_code(error_info, "import pandas as pd")
        assert recovery is not None
        assert "pandas" in recovery
        
        # Test name error
        error_info = {
            'error': 'NameError: name "pd" is not defined'
        }
        recovery = _generate_recovery_code(error_info, "df = pd.DataFrame()")
        assert recovery is not None
        assert "import pandas as pd" in recovery
        
        # Test syntax error
        error_info = {
            'error': 'SyntaxError: invalid syntax'
        }
        recovery = _generate_recovery_code(error_info, "print('test'")
        assert recovery is not None
        assert recovery == "print('test')"
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, debugger, mock_desktop_tools):
        """Test complete end-to-end interactive debugging workflow."""
        # Configure mock responses for different scenarios
        responses = [
            "Process started with PID 12345",  # start_process
            ">>> \n>>> ",  # initial interaction
            ">>> import pandas as pd\n>>> ",  # import success
            ">>> df = pd.DataFrame({'A': [1, 2, 3]})\n>>> ",  # dataframe creation
            ">>> print(df.head())\n   A\n0  1\n1  2\n2  3\n>>> ",  # print output
        ]
        
        mock_desktop_tools['start_process'].invoke.return_value = responses[0]
        mock_desktop_tools['interact_with_process'].invoke.side_effect = responses[1:]
        
        with patch.object(debugger, '_get_desktop_tools', return_value=mock_desktop_tools):
            # Start session
            session = await debugger.start_debugging_session()
            assert session.status == SessionStatus.ACTIVE
            
            # Execute code steps
            test_steps = [
                "import pandas as pd",
                "df = pd.DataFrame({'A': [1, 2, 3]})",
                "print(df.head())"
            ]
            
            successful_steps = 0
            for step in test_steps:
                success, output, error_info = await debugger.validate_code_step(session, step)
                if success:
                    successful_steps += 1
            
            # Verify session state
            assert successful_steps >= 1
            assert len(session.validated_steps) >= 1
            assert session.api_calls_saved > 0
            
            # Get final metrics
            metrics = debugger.get_session_metrics(session)
            assert metrics['api_calls_saved'] > 0
            assert metrics['validated_steps'] >= 1
            
            # Terminate session
            await debugger.terminate_session(session)
            assert session.status == SessionStatus.TERMINATED
            assert session.session_id not in debugger.active_sessions


if __name__ == "__main__":
    # Run basic smoke test
    async def smoke_test():
        debugger = get_interactive_debugger()
        print("✅ Interactive debugger initialized")
        
        metrics = debugger.get_performance_metrics()
        print(f"✅ Performance metrics: {metrics}")
        
        print("✅ Interactive debugging implementation ready")
    
    asyncio.run(smoke_test())