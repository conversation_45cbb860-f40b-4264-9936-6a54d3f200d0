"""
Unit tests for Document Management Service

Tests the semantic context identification, document existence checking,
intelligent naming functionality, and executive summary management of the living documents system.

Coverage:
- Semantic context identification accuracy
- Document existence checking reliability
- Semantic naming generation consistency
- Analysis type detection precision
- Multi-domain support validation
- Subject extraction with biological term recognition
- Document backup system functionality
- Executive summary management functionality (Story 4.3)
- Summary update triggers and metadata tracking
- Error handling and edge cases
"""

import pytest
import os
import tempfile
import shutil
from unittest.mock import patch, mock_open
from pathlib import Path

from src.service.document_service import DocumentService


class TestDocumentService:
    """Test suite for DocumentService class with comprehensive coverage."""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        # Handle case where directory might have been removed in test
        try:
            shutil.rmtree(temp_dir)
        except FileNotFoundError:
            pass

    @pytest.fixture
    def doc_service(self, temp_workspace):
        """Create DocumentService instance with temporary workspace."""
        return DocumentService(temp_workspace)

    def test_init_creates_workspace_directory(self, temp_workspace):
        """Test that DocumentService creates workspace directory on initialization."""
        # Remove the directory to test creation
        shutil.rmtree(temp_workspace)
        assert not os.path.exists(temp_workspace)

        # Initialize service should create directory
        service = DocumentService(temp_workspace)
        assert os.path.exists(temp_workspace)
        assert os.path.isdir(temp_workspace)

    def test_semantic_context_identification_bioinformatics(self, doc_service):
        """Test semantic context identification for bioinformatics domain."""
        goal = "Perform differential expression analysis of breast cancer samples"
        plan_title = "RNA-seq Differential Expression Analysis"
        plan_steps = [
            {"description": "Download gene expression data", "title": "Data retrieval"},
            {
                "description": "Analyze differential expression using DESeq2",
                "title": "DE analysis",
            },
        ]

        context = doc_service.identify_semantic_context(goal, plan_title, plan_steps)

        assert context["analysis_type"] == "DifferentialExpression"
        assert context["subject"] in ["Breast", "Cancer"]  # Either is acceptable
        assert context["domain"] == "bioinformatics"

    def test_semantic_context_identification_protein_analysis(self, doc_service):
        """Test semantic context identification for protein analysis."""
        goal = "Analyze protein structure and function interactions"
        plan_title = "Protein Function Analysis"
        plan_steps = [
            {
                "description": "Extract protein sequences",
                "title": "Sequence extraction",
            },
            {
                "description": "Perform structural analysis",
                "title": "Structure analysis",
            },
        ]

        context = doc_service.identify_semantic_context(goal, plan_title, plan_steps)

        assert context["analysis_type"] == "ProteinAnalysis"
        assert context["subject"] == "Protein"
        assert context["domain"] == "bioinformatics"

    def test_semantic_context_identification_machine_learning(self, doc_service):
        """Test semantic context identification for machine learning domain."""
        goal = "Build classification model for cancer prediction"
        plan_title = "Machine Learning Classification"
        plan_steps = [
            {"description": "Prepare training data", "title": "Data preparation"},
            {"description": "Train neural network model", "title": "Model training"},
        ]

        context = doc_service.identify_semantic_context(goal, plan_title, plan_steps)

        assert context["analysis_type"] == "MachineLearning"
        assert context["subject"] == "Cancer"
        assert context["domain"] == "machine_learning"

    def test_semantic_context_identification_genomic_analysis(self, doc_service):
        """Test semantic context identification for genomic analysis."""
        goal = "Perform genome-wide association study analysis"
        plan_title = "GWAS Analysis Pipeline"
        plan_steps = [
            {"description": "Quality control of genomic data", "title": "QC"},
            {"description": "Association analysis", "title": "GWAS"},
        ]

        context = doc_service.identify_semantic_context(goal, plan_title, plan_steps)

        assert context["analysis_type"] == "GenomicAnalysis"
        assert context["domain"] == "bioinformatics"

    def test_analysis_type_detection_pathway_analysis(self, doc_service):
        """Test analysis type detection for pathway analysis."""
        text = "pathway enrichment analysis using KEGG database"
        analysis_type = doc_service._detect_analysis_type(text)
        assert analysis_type == "PathwayAnalysis"

    def test_analysis_type_detection_data_analysis(self, doc_service):
        """Test analysis type detection for general data analysis."""
        text = "statistical analysis and correlation study"
        analysis_type = doc_service._detect_analysis_type(text)
        assert analysis_type == "DataAnalysis"

    def test_analysis_type_detection_fallback(self, doc_service):
        """Test analysis type detection fallback to General."""
        text = "some random text without specific analysis keywords"
        analysis_type = doc_service._detect_analysis_type(text)
        assert analysis_type == "General"

    def test_subject_extraction_biological_terms(self, doc_service):
        """Test subject extraction with biological terms."""
        text = "breast cancer tumor analysis with protein markers"
        subject = doc_service._extract_subject(text)
        assert subject in ["Breast", "Cancer", "Tumor", "Protein"]

    def test_subject_extraction_exclusion_terms(self, doc_service):
        """Test subject extraction excludes common terms."""
        text = "analysis study research investigation of important data"
        subject = doc_service._extract_subject(text)
        # Should not return excluded terms
        excluded = {"analysis", "study", "research", "investigation", "data"}
        assert subject.lower() not in excluded

    def test_subject_extraction_fallback(self, doc_service):
        """Test subject extraction fallback mechanism."""
        text = "the and with from that"  # Only stop words
        subject = doc_service._extract_subject(text)
        assert subject == "Analysis"

    def test_domain_determination_bioinformatics(self, doc_service):
        """Test domain determination for bioinformatics."""
        text = "gene expression protein analysis dna sequence"
        analysis_type = "DifferentialExpression"
        domain = doc_service._determine_domain(text, analysis_type)
        assert domain == "bioinformatics"

    def test_domain_determination_machine_learning(self, doc_service):
        """Test domain determination for machine learning."""
        text = "neural network deep learning classification algorithm"
        analysis_type = "MachineLearning"
        domain = doc_service._determine_domain(text, analysis_type)
        assert domain == "machine_learning"

    def test_domain_determination_data_science(self, doc_service):
        """Test domain determination for data science."""
        text = "statistical analysis correlation big data visualization"
        analysis_type = "DataAnalysis"
        domain = doc_service._determine_domain(text, analysis_type)
        assert domain == "data_science"

    def test_domain_determination_software_engineering(self, doc_service):
        """Test domain determination for software engineering."""
        text = "software architecture api development framework"
        analysis_type = "General"
        domain = doc_service._determine_domain(text, analysis_type)
        assert domain == "software_engineering"

    def test_semantic_filename_generation(self, doc_service):
        """Test semantic filename generation."""
        context = {
            "analysis_type": "DifferentialExpression",
            "subject": "BreastCancer",
            "domain": "bioinformatics",
        }
        filename = doc_service.generate_semantic_filename(context)
        assert filename == "Analysis_DifferentialExpression_BreastCancer.md"

    def test_filename_component_sanitization(self, doc_service):
        """Test filename component sanitization removes invalid characters."""
        component = 'Test<>:"/\\|?*Component  With Spaces'
        sanitized = doc_service._sanitize_component(component)
        assert "<" not in sanitized
        assert ">" not in sanitized
        assert ":" not in sanitized
        assert '"' not in sanitized
        assert "/" not in sanitized
        assert "\\" not in sanitized
        assert "|" not in sanitized
        assert "?" not in sanitized
        assert "*" not in sanitized
        assert " " not in sanitized

    def test_filename_component_length_limit(self, doc_service):
        """Test filename component length limitation."""
        long_component = "a" * 100  # 100 characters
        sanitized = doc_service._sanitize_component(long_component)
        assert len(sanitized) <= 50

    def test_filename_component_empty_fallback(self, doc_service):
        """Test filename component empty fallback."""
        empty_component = ""
        sanitized = doc_service._sanitize_component(empty_component)
        assert sanitized == "Analysis"

        invalid_component = "<>?*"  # Only invalid characters
        sanitized = doc_service._sanitize_component(invalid_component)
        assert sanitized == "Analysis"

    def test_find_existing_document_exact_match(self, doc_service, temp_workspace):
        """Test finding existing document with exact match."""
        # Create existing document
        existing_file = "Analysis_DifferentialExpression_Breast.md"
        existing_path = os.path.join(temp_workspace, existing_file)
        with open(existing_path, "w") as f:
            f.write("# Existing Document")

        # Test exact match
        found_path = doc_service.find_existing_document(existing_file, {})
        assert found_path == existing_path

    def test_find_existing_document_semantic_similarity(
        self, doc_service, temp_workspace
    ):
        """Test finding existing document with semantic similarity."""
        # Create existing document with similar name
        existing_file = "Analysis_DifferentialExpression_BreastCancer.md"
        existing_path = os.path.join(temp_workspace, existing_file)
        with open(existing_path, "w") as f:
            f.write("# Existing Document")

        # Search for similar file
        target_file = "Analysis_DifferentialExpression_Breast.md"
        found_path = doc_service.find_existing_document(target_file, {})
        assert found_path == existing_path

    def test_find_existing_document_no_match(self, doc_service, temp_workspace):
        """Test finding existing document returns None when no match."""
        # Create unrelated document
        existing_file = "Analysis_ProteinAnalysis_Mouse.md"
        existing_path = os.path.join(temp_workspace, existing_file)
        with open(existing_path, "w") as f:
            f.write("# Unrelated Document")

        # Search for different file
        target_file = "Analysis_GenomicAnalysis_Human.md"
        found_path = doc_service.find_existing_document(target_file, {})
        assert found_path is None

    def test_parse_filename_components(self, doc_service):
        """Test parsing filename into components."""
        filename = "Analysis_DifferentialExpression_BreastCancer.md"
        components = doc_service._parse_filename_components(filename)
        assert components["analysis_type"] == "differentialexpression"
        assert components["subject"] == "breastcancer"

    def test_parse_filename_components_invalid(self, doc_service):
        """Test parsing invalid filename."""
        filename = "InvalidFormat.md"
        components = doc_service._parse_filename_components(filename)
        assert components["analysis_type"] == ""
        assert components["subject"] == ""

    def test_calculate_semantic_similarity(self, doc_service):
        """Test semantic similarity calculation."""
        components1 = {"analysis_type": "differentialexpression", "subject": "breast"}
        components2 = {
            "analysis_type": "differentialexpression",
            "subject": "breastcancer",
        }

        similarity = doc_service._calculate_semantic_similarity(
            components1, components2
        )
        assert 0.0 <= similarity <= 1.0
        assert similarity > 0.5  # Should be similar due to matching analysis type

    def test_create_document_backup(self, doc_service, temp_workspace):
        """Test creating document backup."""
        # Create original document
        original_file = "Analysis_Test_Document.md"
        original_path = os.path.join(temp_workspace, original_file)
        original_content = "# Original Document\nThis is the original content."
        with open(original_path, "w") as f:
            f.write(original_content)

        # Create backup
        backup_path = doc_service.create_document_backup(original_path)

        # Verify backup exists and has same content
        assert os.path.exists(backup_path)
        assert "backup" in backup_path
        with open(backup_path, "r") as f:
            backup_content = f.read()
        assert backup_content == original_content

    def test_create_document_backup_file_not_found(self, doc_service):
        """Test creating backup for non-existent file raises error."""
        non_existent_path = "/path/to/non/existent/file.md"
        with pytest.raises(FileNotFoundError):
            doc_service.create_document_backup(non_existent_path)

    def test_get_document_metadata(self, doc_service, temp_workspace):
        """Test extracting document metadata."""
        # Create document with title
        doc_file = "Analysis_Test_Metadata.md"
        doc_path = os.path.join(temp_workspace, doc_file)
        doc_content = (
            "# Test Document Title\n\nThis is test content with multiple words."
        )
        with open(doc_path, "w") as f:
            f.write(doc_content)

        metadata = doc_service.get_document_metadata(doc_path)

        assert "file_size" in metadata
        assert "last_modified" in metadata
        assert "word_count" in metadata
        assert "line_count" in metadata
        assert "title" in metadata
        assert metadata["title"] == "Test Document Title"
        assert metadata["word_count"] > 0
        assert metadata["line_count"] > 0

    def test_get_document_metadata_file_not_found(self, doc_service):
        """Test getting metadata for non-existent file returns empty dict."""
        non_existent_path = "/path/to/non/existent/file.md"
        metadata = doc_service.get_document_metadata(non_existent_path)
        assert metadata == {}

    def test_validate_document_content_valid(self, doc_service):
        """Test document content validation for valid content."""
        valid_content = """
        # Analysis Report
        
        This is a comprehensive analysis report with meaningful content.
        The report contains detailed findings and substantial information
        that demonstrates real analytical work has been completed.
        
        ## Results
        - Finding 1: Significant correlation observed
        - Finding 2: Statistical significance achieved
        - Finding 3: Biological relevance confirmed
        """

        is_valid = doc_service.validate_document_content(valid_content)
        assert is_valid is True

    def test_validate_document_content_too_short(self, doc_service):
        """Test document content validation for content too short."""
        short_content = "Short content"
        is_valid = doc_service.validate_document_content(short_content)
        assert is_valid is False

    def test_validate_document_content_placeholder_heavy(self, doc_service):
        """Test document content validation for placeholder-heavy content."""
        placeholder_content = """
        This will be added later.
        Results to be determined.
        Analysis will be generated.
        Content will be synthesized.
        Findings will be compiled.
        Data to be completed.
        """

        is_valid = doc_service.validate_document_content(placeholder_content)
        assert is_valid is False

    def test_cleanup_old_backups(self, doc_service, temp_workspace):
        """Test cleanup of old backup files."""
        # Create multiple backup files for same document
        base_name = "Analysis_Test_Document"
        for i in range(15):  # Create 15 backups (more than max_backups=10)
            backup_file = f"{base_name}_backup_20240101_12000{i:02d}_1.md"
            backup_path = os.path.join(temp_workspace, backup_file)
            with open(backup_path, "w") as f:
                f.write(f"Backup {i}")

        # Verify all backups exist
        backup_files = [f for f in os.listdir(temp_workspace) if "backup" in f]
        assert len(backup_files) == 15

        # Cleanup old backups
        doc_service.cleanup_old_backups(max_backups=10)

        # Verify only 10 backups remain
        remaining_backups = [f for f in os.listdir(temp_workspace) if "backup" in f]
        assert len(remaining_backups) == 10

    def test_cleanup_old_backups_no_workspace(self, temp_workspace):
        """Test cleanup when workspace doesn't exist."""
        service = DocumentService(temp_workspace)

        # Remove the workspace directory after creation
        shutil.rmtree(temp_workspace)

        # Should not raise error when workspace doesn't exist
        service.cleanup_old_backups()

    def test_multiple_analysis_types_in_text(self, doc_service):
        """Test analysis type detection with multiple types present."""
        text = "differential expression analysis and protein analysis study"
        analysis_type = doc_service._detect_analysis_type(text)
        # Should return the highest scoring type
        assert analysis_type in ["DifferentialExpression", "ProteinAnalysis"]

    def test_subject_extraction_complex_biological_terms(self, doc_service):
        """Test subject extraction with complex biological terms."""
        text = "covid-19 sars coronavirus analysis in lung tissue samples"
        subject = doc_service._extract_subject(text)
        assert subject in ["Covid", "Sars", "Coronavirus", "Lung"]

    def test_context_identification_empty_inputs(self, doc_service):
        """Test context identification with empty inputs."""
        context = doc_service.identify_semantic_context("", "", [])
        assert context["analysis_type"] == "General"
        assert context["subject"] == "Analysis"
        assert context["domain"] == "data_science"


class TestDocumentServiceSessionHandling:
    """Test suite for DocumentService session handling functionality (Story 4.2)."""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        try:
            shutil.rmtree(temp_dir)
        except FileNotFoundError:
            pass

    @pytest.fixture
    def doc_service(self, temp_workspace):
        """Create DocumentService instance with temporary workspace."""
        return DocumentService(temp_workspace)

    def test_session_tracking_new_document(self, doc_service, temp_workspace):
        """Test session tracking for new documents."""
        doc_path = os.path.join(temp_workspace, "test_session_doc.md")
        session_id = "session_123"

        # Create a document with session overview section
        doc_content = """# Test Document

## Document Metadata
- **Analysis Type:** Test
- **Subject:** TestSubject
- **Domain:** test_domain
- **Created:** 2023-01-01 12:00:00
- **Last Updated:** 2023-01-01 12:00:00
- **Version:** 1.0

## Executive Summary
Test summary
"""
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(doc_content)

        # Track session ID
        doc_service.track_session_id(doc_path, session_id)

        # Verify session tracking was added
        with open(doc_path, "r", encoding="utf-8") as f:
            updated_content = f.read()

        assert "## Session Overview" in updated_content
        assert f"**Current Session:** {session_id}" in updated_content
        assert "**Session Count:** 1" in updated_content
        assert "**Last Updated:**" in updated_content

    def test_session_tracking_existing_metadata(self, doc_service, temp_workspace):
        """Test session tracking updates existing session metadata."""
        doc_path = os.path.join(temp_workspace, "test_existing_session.md")
        initial_session_id = "session_old"
        new_session_id = "session_new"

        # Create document with existing session overview
        doc_content = f"""# Test Document

## Session Overview
- **Current Session:** {initial_session_id}
- **Session Count:** 2
- **Last Updated:** 2023-01-01 10:00:00

## Executive Summary
Test summary
"""
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(doc_content)

        # Update session tracking
        doc_service.track_session_id(doc_path, new_session_id)

        # Verify session tracking was updated
        with open(doc_path, "r", encoding="utf-8") as f:
            updated_content = f.read()

        assert f"**Current Session:** {new_session_id}" in updated_content
        assert initial_session_id not in updated_content

    def test_get_session_count_empty_document(self, doc_service, temp_workspace):
        """Test session count for document without sessions."""
        doc_path = os.path.join(temp_workspace, "no_sessions.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Document without sessions\n\nContent without session headers.")

        count = doc_service.get_session_count(doc_path)
        assert count == 0

    def test_get_session_count_with_sessions(self, doc_service, temp_workspace):
        """Test session count for document with multiple sessions."""
        doc_path = os.path.join(temp_workspace, "with_sessions.md")
        doc_content = """# Document with sessions

## Session 1 - 2023-01-01 10:00:00
**Session ID:** session_1

Some content from session 1.

---

## Session 2 - 2023-01-01 11:00:00  
**Session ID:** session_2

Some content from session 2.

---

## Session 3 - 2023-01-01 12:00:00
**Session ID:** session_3

Some content from session 3.
"""
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(doc_content)

        count = doc_service.get_session_count(doc_path)
        assert count == 3

    def test_get_session_count_nonexistent_document(self, doc_service):
        """Test session count for non-existent document returns 0."""
        count = doc_service.get_session_count("/path/to/nonexistent.md")
        assert count == 0

    def test_generate_session_header(self, doc_service):
        """Test session header generation with timestamp."""
        session_count = 3
        session_id = "test_session_456"

        header = doc_service.generate_session_header(session_count, session_id)

        assert f"## Session {session_count} -" in header
        assert f"**Session ID:** {session_id}" in header
        assert "---" in header
        # Verify timestamp format (YYYY-MM-DD HH:MM:SS)
        import re

        timestamp_pattern = r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}"
        assert re.search(timestamp_pattern, header)

    def test_append_session_content_success(self, doc_service, temp_workspace):
        """Test successful session content appending."""
        doc_path = os.path.join(temp_workspace, "append_test.md")
        session_id = "append_session"

        # Create initial document
        initial_content = """# Test Document

## Document Metadata
- Analysis Type: Test

## Session 1 - 2023-01-01 10:00:00
**Session ID:** initial_session

Initial session content.
"""
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(initial_content)

        # Append new session content
        new_content = "This is new session content with important findings."
        result = doc_service.append_session_content(doc_path, new_content, session_id)

        assert result is True

        # Verify content was appended
        with open(doc_path, "r", encoding="utf-8") as f:
            updated_content = f.read()

        assert initial_content.strip() in updated_content
        assert new_content in updated_content
        assert "## Session 2 -" in updated_content
        assert f"**Session ID:** {session_id}" in updated_content

    def test_append_session_content_empty_content(self, doc_service, temp_workspace):
        """Test session content appending with empty content fails."""
        doc_path = os.path.join(temp_workspace, "empty_append_test.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Test Document\n\nInitial content.")

        result = doc_service.append_session_content(doc_path, "", "session_id")
        assert result is False

        result = doc_service.append_session_content(doc_path, "   ", "session_id")
        assert result is False

    def test_append_session_content_nonexistent_document(self, doc_service):
        """Test session content appending to non-existent document fails."""
        result = doc_service.append_session_content(
            "/nonexistent/path.md", "content", "session_id"
        )
        assert result is False

    def test_create_or_update_document_new_document(self, doc_service, temp_workspace):
        """Test creating new document with session awareness."""
        doc_path = os.path.join(temp_workspace, "new_session_doc.md")
        content = "This is the initial session content."
        context = {
            "analysis_type": "TestAnalysis",
            "subject": "TestSubject",
            "domain": "test_domain",
        }
        session_id = "new_doc_session"

        result_path = doc_service.create_or_update_document(
            doc_path, content, context, session_id
        )

        assert result_path == doc_path
        assert os.path.exists(doc_path)

        # Verify document contains session information
        with open(doc_path, "r", encoding="utf-8") as f:
            doc_content = f.read()

        assert "## Session Overview" in doc_content
        assert f"**Current Session:** {session_id}" in doc_content
        assert "**Session Count:** 1" in doc_content
        assert content in doc_content
        assert "## Session 1 -" in doc_content

    def test_create_or_update_document_existing_document(
        self, doc_service, temp_workspace
    ):
        """Test updating existing document with session awareness."""
        doc_path = os.path.join(temp_workspace, "existing_session_doc.md")

        # Create initial document
        initial_content = """# Existing Document

## Document Metadata
- Analysis Type: Existing

## Session Overview
- **Current Session:** old_session
- **Session Count:** 1
- **Last Updated:** 2023-01-01 10:00:00

## Session 1 - 2023-01-01 10:00:00
**Session ID:** old_session

Initial content.
"""
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(initial_content)

        # Update with new session
        new_content = "New session content with additional findings."
        context = {"analysis_type": "Updated", "subject": "UpdatedSubject"}
        new_session_id = "update_session"

        result_path = doc_service.create_or_update_document(
            doc_path, new_content, context, new_session_id
        )

        assert result_path == doc_path

        # Verify both old and new content exist
        with open(doc_path, "r", encoding="utf-8") as f:
            updated_content = f.read()

        assert "Initial content." in updated_content
        assert new_content in updated_content
        assert "## Session 2 -" in updated_content
        assert f"**Session ID:** {new_session_id}" in updated_content

    def test_create_new_document_with_session_fallback_template(
        self, doc_service, temp_workspace
    ):
        """Test creating new document with fallback template when template file doesn't exist."""
        doc_path = os.path.join(temp_workspace, "fallback_template_doc.md")
        content = "Test content for fallback template."
        context = {
            "analysis_type": "FallbackTest",
            "subject": "FallbackSubject",
            "domain": "fallback_domain",
        }
        session_id = "fallback_session"

        result_path = doc_service._create_new_document_with_session(
            doc_path, content, context, session_id
        )

        assert result_path == doc_path
        assert os.path.exists(doc_path)

        # Verify fallback template was used
        with open(doc_path, "r", encoding="utf-8") as f:
            doc_content = f.read()

        assert "Analysis: FallbackTest - FallbackSubject" in doc_content
        assert "## Session Overview" in doc_content
        assert f"**Current Session:** {session_id}" in doc_content
        assert content in doc_content

    def test_session_content_field_mapping(self, doc_service, temp_workspace):
        """Test that session content properly maps fields from workflow extraction."""
        from src.graph.nodes import _generate_session_content

        # Mock realistic content data from _extract_content_from_workflow
        realistic_content_data = {
            "key_results": (
                "Found 15 significant protein interactions with high confidence scores"
            ),
            "primary_results": "Primary analysis identified hub proteins X, Y, Z",
            "methodology_overview": (
                "Used STRING database with AlphaFold structural validation"
            ),
            "technical_details": (
                "Implemented automated scoring algorithm with 95% accuracy"
            ),
            "statistical_analysis": "P-values < 0.05 for all significant interactions",
            "executive_summary": (
                "Comprehensive protein interaction analysis completed successfully"
            ),
            "future_directions": (
                "Extend to larger protein complexes and validate experimentally"
            ),
        }

        context = {"analysis_type": "ProteinAnalysis", "subject": "Interactions"}
        state = {"goal": "Analyze protein interactions"}
        user_context = {"objective": "Find key protein interactions"}

        # Generate session content
        session_content = _generate_session_content(
            realistic_content_data, context, state, user_context
        )

        # Verify that actual extracted content appears (not placeholders)
        assert "Found 15 significant protein interactions" in session_content
        assert "Used STRING database with AlphaFold" in session_content
        assert "Implemented automated scoring algorithm" in session_content
        assert "P-values < 0.05 for all significant" in session_content
        assert "Comprehensive protein interaction analysis" in session_content
        assert "Extend to larger protein complexes" in session_content

        # Verify sections are present
        assert "### New Findings & Results" in session_content
        assert "### Methodology Updates" in session_content
        assert "### Technical Implementation" in session_content
        assert "### Data Analysis" in session_content
        assert "### Results Summary" in session_content
        assert "### Next Steps" in session_content


class TestExecutiveSummaryManagement:
    """Test suite for executive summary management functionality (Story 4.3)"""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        try:
            shutil.rmtree(temp_dir)
        except FileNotFoundError:
            pass

    @pytest.fixture
    def doc_service(self, temp_workspace):
        """Create DocumentService instance with temporary workspace."""
        return DocumentService(temp_workspace)

    @pytest.fixture
    def sample_document_with_summary(self):
        """Sample document content with executive summary section"""
        return """# Analysis: GenomicAnalysis - Cancer

## Document Metadata
- **Analysis Type:** GenomicAnalysis
- **Subject:** Cancer
- **Domain:** bioinformatics
- **Created:** 2025-01-01 10:00:00
- **Last Updated:** 2025-01-01 12:00:00
- **Version:** 1.0

## Executive Summary

**Analysis Overview:** Current genomic analysis of cancer samples shows promising patterns.

**Key Findings:**
- **Results:** 150 differentially expressed genes identified
- **Significance:** High statistical confidence in findings

**Current Status:** Analysis in progress with validated methodology.

## Session Overview
- **Current Session:** session_1
- **Session Count:** 1
- **Last Updated:** 2025-01-01 12:00:00

## Session Content
Analysis results and findings here.
"""

    @pytest.fixture
    def sample_document_without_summary(self):
        """Sample document content without executive summary section"""
        return """# Analysis: ProteinAnalysis - Interactions

## Document Metadata
- **Analysis Type:** ProteinAnalysis
- **Subject:** Interactions
- **Domain:** bioinformatics
- **Created:** 2025-01-01 10:00:00
- **Last Updated:** 2025-01-01 12:00:00
- **Version:** 1.0

## Session Overview
- **Current Session:** session_1
- **Session Count:** 1
- **Last Updated:** 2025-01-01 12:00:00

## Session Content
Protein interaction analysis results here.
"""

    def test_has_executive_summary_section_true(
        self, doc_service, temp_workspace, sample_document_with_summary
    ):
        """Test detection of existing executive summary section"""
        doc_path = os.path.join(temp_workspace, "with_summary.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_with_summary)

        assert doc_service.has_executive_summary_section(doc_path) is True

    def test_has_executive_summary_section_false(
        self, doc_service, temp_workspace, sample_document_without_summary
    ):
        """Test detection when executive summary section is missing"""
        doc_path = os.path.join(temp_workspace, "without_summary.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_without_summary)

        assert doc_service.has_executive_summary_section(doc_path) is False

    def test_has_executive_summary_section_nonexistent_file(
        self, doc_service, temp_workspace
    ):
        """Test behavior with nonexistent document"""
        doc_path = os.path.join(temp_workspace, "nonexistent.md")
        assert doc_service.has_executive_summary_section(doc_path) is False

    def test_get_executive_summary_content_existing(
        self, doc_service, temp_workspace, sample_document_with_summary
    ):
        """Test extraction of existing executive summary content"""
        doc_path = os.path.join(temp_workspace, "with_summary.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_with_summary)

        content = doc_service.get_executive_summary_content(doc_path)

        assert content is not None
        assert "Analysis Overview" in content
        assert "Key Findings" in content
        assert "150 differentially expressed genes" in content

    def test_get_executive_summary_content_missing(
        self, doc_service, temp_workspace, sample_document_without_summary
    ):
        """Test extraction when executive summary section is missing"""
        doc_path = os.path.join(temp_workspace, "without_summary.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_without_summary)

        content = doc_service.get_executive_summary_content(doc_path)
        assert content is None

    def test_update_executive_summary_existing_section(
        self, doc_service, temp_workspace, sample_document_with_summary
    ):
        """Test updating existing executive summary section"""
        doc_path = os.path.join(temp_workspace, "update_existing.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_with_summary)

        new_summary = """**Updated Analysis Overview:** Significant progress made in genomic analysis.

**Key Findings:**
- **Results:** 200 differentially expressed genes now identified
- **Validation:** 85% validation rate achieved
- **Significance:** Multiple pathways confirmed

**Current Status:** Analysis nearing completion with high confidence results."""

        metadata = {"session_id": "session_2", "update_type": "automatic"}

        success = doc_service.update_executive_summary(doc_path, new_summary, metadata)
        assert success is True

        # Verify content was updated
        updated_content = doc_service.get_executive_summary_content(doc_path)
        assert "Updated Analysis Overview" in updated_content
        assert "200 differentially expressed genes" in updated_content
        assert "85% validation rate" in updated_content

        # Verify other sections were preserved
        with open(doc_path, "r", encoding="utf-8") as f:
            full_content = f.read()
        assert "## Session Overview" in full_content
        assert "## Session Content" in full_content

    def test_update_executive_summary_add_new_section(
        self, doc_service, temp_workspace, sample_document_without_summary
    ):
        """Test adding executive summary section to document without one"""
        doc_path = os.path.join(temp_workspace, "add_new.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_without_summary)

        new_summary = """**Analysis Overview:** Protein interaction analysis reveals important patterns.

**Key Findings:**
- **Results:** 50 high-confidence interactions identified
- **Networks:** 3 major interaction clusters discovered

**Current Status:** Initial analysis complete, validation in progress."""

        success = doc_service.update_executive_summary(doc_path, new_summary)
        assert success is True

        # Verify section was added
        assert doc_service.has_executive_summary_section(doc_path) is True

        content = doc_service.get_executive_summary_content(doc_path)
        assert "Protein interaction analysis" in content
        assert "50 high-confidence interactions" in content

    def test_track_summary_metadata(self, doc_service, temp_workspace):
        """Test summary metadata tracking functionality"""
        doc_path = os.path.join(temp_workspace, "metadata_test.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Test Document\nContent here.")

        metadata = {
            "session_id": "session_1",
            "session_count": 1,
            "update_trigger": "manual",
            "summary_length": 50,
        }

        # Track metadata (called internally by update_executive_summary)
        doc_service._track_summary_metadata(doc_path, metadata)

        # Verify metadata was stored
        stored_metadata = doc_service.get_summary_metadata(doc_path)
        assert len(stored_metadata) == 1
        assert stored_metadata[0]["session_id"] == "session_1"
        assert stored_metadata[0]["update_trigger"] == "manual"
        assert "timestamp" in stored_metadata[0]

    def test_get_summary_metadata_empty(self, doc_service, temp_workspace):
        """Test getting metadata for document with no updates"""
        doc_path = os.path.join(temp_workspace, "no_metadata.md")
        metadata = doc_service.get_summary_metadata(doc_path)
        assert metadata == []

    def test_should_update_summary_new_document(self, doc_service, temp_workspace):
        """Test update trigger for document with no previous summary updates"""
        doc_path = os.path.join(temp_workspace, "new_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# New Document\nContent here.")

        assert doc_service.should_update_summary(doc_path, "session_1") is True

    def test_should_update_summary_modified_document(self, doc_service, temp_workspace):
        """Test update trigger for document modified after last summary update"""
        doc_path = os.path.join(temp_workspace, "modified_doc.md")

        # Create document
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Modified Document\nOriginal content.")

        # Add some metadata (simulate previous update)
        import datetime

        past_time = datetime.datetime.now() - datetime.timedelta(hours=1)
        metadata = {
            "session_id": "session_1",
            "timestamp": past_time.isoformat(),
            "session_count": 1,
        }
        doc_service._track_summary_metadata(doc_path, metadata)

        # Modify document (this changes modification time)
        with open(doc_path, "a", encoding="utf-8") as f:
            f.write("\nNew content added.")

        # Should trigger update due to modification
        assert doc_service.should_update_summary(doc_path, "session_2") is True

    @patch("src.service.summary_service.SummaryService")
    def test_trigger_summary_update_success(
        self, mock_summary_service_class, doc_service, temp_workspace
    ):
        """Test successful automatic summary update trigger"""
        doc_path = os.path.join(temp_workspace, "trigger_test.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Test Document\n\n## Session 1\nContent here.")

        # Mock summary service
        mock_summary_service = mock_summary_service_class.return_value
        mock_summary_service.generate_executive_summary.return_value = (
            "Generated summary content"
        )

        # Mock should_update_summary to return True
        with (
            patch.object(doc_service, "should_update_summary", return_value=True),
            patch.object(doc_service, "update_executive_summary", return_value=True),
        ):

            result = doc_service.trigger_summary_update(doc_path, "session_1")
            assert result is True

    @patch("src.service.summary_service.SummaryService")
    def test_trigger_summary_update_not_needed(
        self, mock_summary_service_class, doc_service, temp_workspace
    ):
        """Test summary update trigger when update is not needed"""
        doc_path = os.path.join(temp_workspace, "no_update_needed.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Test Document\nContent here.")

        # Mock should_update_summary to return False
        with patch.object(doc_service, "should_update_summary", return_value=False):
            result = doc_service.trigger_summary_update(doc_path, "session_1")
            assert result is False

    def test_atomic_summary_update(
        self, doc_service, temp_workspace, sample_document_with_summary
    ):
        """Test that summary updates are atomic (all-or-nothing)"""
        doc_path = os.path.join(temp_workspace, "atomic_test.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_with_summary)

        # Get original content
        with open(doc_path, "r", encoding="utf-8") as f:
            original_content = f.read()

        new_summary = "**Updated Summary:** New content here."

        # Simulate atomic update
        success = doc_service.update_executive_summary(doc_path, new_summary)
        assert success is True

        # Verify content is properly updated
        with open(doc_path, "r", encoding="utf-8") as f:
            updated_content = f.read()

        assert "Updated Summary" in updated_content
        assert "## Session Overview" in updated_content  # Other sections preserved
        assert updated_content != original_content

    def test_summary_metadata_cleanup(self, doc_service, temp_workspace):
        """Test that summary metadata is cleaned up to prevent excessive growth"""
        doc_path = os.path.join(temp_workspace, "cleanup_test.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Test Document\nContent here.")

        # Add 52 metadata entries (more than the 50 limit)
        for i in range(52):
            metadata = {"session_id": f"session_{i}", "update_number": i}
            doc_service._track_summary_metadata(doc_path, metadata)

        # Verify cleanup occurred (should keep only last 50)
        stored_metadata = doc_service.get_summary_metadata(doc_path)
        assert len(stored_metadata) == 50

        # Verify newest entries were kept
        assert stored_metadata[-1]["session_id"] == "session_51"
        assert (
            stored_metadata[0]["session_id"] == "session_2"
        )  # session_0 and session_1 removed
