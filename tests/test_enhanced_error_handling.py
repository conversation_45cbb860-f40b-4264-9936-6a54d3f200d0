"""
Tests for enhanced error handling and user feedback system.

This module tests the comprehensive error classification, user-friendly error messages,
and intelligent recovery strategies implemented in story 6.3.
"""

import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch

from src.tools.error_recovery import (
    ErrorDetector,
    ErrorMessageGenerator,
    EnvironmentValidator,
    classify_and_report_error,
    validate_environment,
    format_error_for_user,
    format_error_for_json,
    ErrorCategory,
    ErrorSeverity,
)
from src.tools.error_tracking import ErrorTracker, log_error
from src.tools.biomni_tools import (
    _execute_biomni_tool_in_environment,
    _create_fallback_tool_wrapper,
)


class TestErrorClassification:
    """Test error classification and categorization."""

    def test_timeout_error_classification(self):
        """Test timeout error classification."""
        detector = ErrorDetector()

        timeout_errors = [
            "Connection timed out after 30 seconds",
            "subprocess.TimeoutExpired: Command timed out",
            "asyncio.timeout occurred",
        ]

        for error_text in timeout_errors:
            context = detector.classify_error_to_context(error_text, "test_tool")
            assert context.category == ErrorCategory.TIMEOUT
            assert context.severity == ErrorSeverity.HIGH
            assert "timeout" in context.error_code.lower()

    def test_dependency_error_classification(self):
        """Test dependency error classification."""
        detector = ErrorDetector()

        dependency_errors = [
            "ModuleNotFoundError: No module named 'pandas'",
            "ImportError: cannot import name 'numpy'",
            "command not found: conda",
        ]

        for error_text in dependency_errors:
            context = detector.classify_error_to_context(error_text, "test_tool")
            assert context.category == ErrorCategory.DEPENDENCY
            assert context.severity == ErrorSeverity.CRITICAL

    def test_tool_specific_error_classification(self):
        """Test tool-specific error classification."""
        detector = ErrorDetector()

        uniprot_error = "UniProt service not responding"
        context = detector.classify_error_to_context(uniprot_error, "query_uniprot")

        assert context.category == ErrorCategory.TOOL_SPECIFIC
        assert context.tool_name == "query_uniprot"
        assert "UNIPROT_SERVICE_ERROR" in context.error_code


class TestErrorMessageGeneration:
    """Test user-friendly error message generation."""

    def test_user_friendly_message_generation(self):
        """Test generation of user-friendly error messages."""
        generator = ErrorMessageGenerator()

        # Create a sample error context
        from src.tools.error_recovery import ErrorContext

        context = ErrorContext(
            category=ErrorCategory.TIMEOUT,
            severity=ErrorSeverity.HIGH,
            tool_name="query_uniprot",
            error_code="EXECUTION_TIMEOUT",
            original_error="Connection timed out",
            execution_method="direct_subprocess",
        )

        report = generator.generate_error_report(context)

        assert "🕐" in report.user_message  # Timeout emoji
        assert "query_uniprot" in report.user_message
        assert "timed out" in report.user_message.lower()
        assert len(report.recovery_actions) > 0
        assert report.recovery_actions[0].priority == 1  # Highest priority first

    def test_recovery_action_generation(self):
        """Test recovery action generation."""
        generator = ErrorMessageGenerator()

        from src.tools.error_recovery import ErrorContext

        context = ErrorContext(
            category=ErrorCategory.DEPENDENCY,
            severity=ErrorSeverity.CRITICAL,
            tool_name="test_tool",
            error_code="MISSING_DEPENDENCY",
            original_error="ImportError: No module named 'test'",
            execution_method="direct",
        )

        report = generator.generate_error_report(context)

        # Should have dependency-specific recovery actions
        action_types = [action.action_type for action in report.recovery_actions]
        assert "install_dependencies" in action_types

        # Actions should be sorted by priority
        priorities = [action.priority for action in report.recovery_actions]
        assert priorities == sorted(priorities)


class TestEnvironmentValidation:
    """Test environment and dependency validation."""

    def test_environment_validation_structure(self):
        """Test environment validation returns proper structure."""
        validator = EnvironmentValidator()
        results = validator.validate_environment()

        required_keys = [
            "overall_status",
            "python_environment",
            "conda_environment",
            "package_dependencies",
            "network_connectivity",
            "recommendations",
        ]

        for key in required_keys:
            assert key in results

        # Test python environment validation structure
        python_env = results["python_environment"]
        assert "status" in python_env
        assert "python_path" in python_env
        assert "exists" in python_env

    @patch("subprocess.run")
    def test_python_environment_validation(self, mock_subprocess):
        """Test Python environment validation."""
        # Mock successful Python version check
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "Python 3.9.0"

        validator = EnvironmentValidator()

        with patch("os.path.exists", return_value=True):
            result = validator._validate_python_environment()

        assert result["status"] == "healthy"
        assert result["exists"] is True
        assert result["executable"] is True
        assert "Python 3.9.0" in result["version"]


class TestErrorTracking:
    """Test error logging and tracking system."""

    def test_error_tracker_initialization(self):
        """Test error tracker database initialization."""
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as tmp_file:
            db_path = tmp_file.name

        try:
            tracker = ErrorTracker(db_path=db_path)

            # Check that database file was created
            assert os.path.exists(db_path)

            # Test basic logging functionality
            from src.tools.error_recovery import ErrorContext, ErrorReport

            context = ErrorContext(
                category=ErrorCategory.TIMEOUT,
                severity=ErrorSeverity.HIGH,
                tool_name="test_tool",
                error_code="TEST_ERROR",
                original_error="Test error message",
            )

            report = ErrorReport(
                context=context,
                user_message="Test message",
                technical_details="Test details",
                recovery_actions=[],
            )

            error_id = tracker.log_error(report, session_id="test_session")
            assert error_id != "unknown"

        finally:
            if os.path.exists(db_path):
                os.unlink(db_path)

    def test_error_frequency_tracking(self):
        """Test error frequency tracking."""
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as tmp_file:
            db_path = tmp_file.name

        try:
            tracker = ErrorTracker(db_path=db_path)

            # Log multiple errors
            from src.tools.error_recovery import ErrorContext, ErrorReport

            for i in range(3):
                context = ErrorContext(
                    category=ErrorCategory.TIMEOUT,
                    severity=ErrorSeverity.HIGH,
                    tool_name="test_tool",
                    error_code="TEST_ERROR",
                    original_error=f"Test error {i}",
                )

                report = ErrorReport(
                    context=context,
                    user_message="Test message",
                    technical_details="Test details",
                    recovery_actions=[],
                )

                tracker.log_error(report)

            # Check frequency tracking
            frequency = tracker.get_error_frequency(tool_name="test_tool")
            assert "test_tool:timeout" in frequency
            assert frequency["test_tool:timeout"] == 3

        finally:
            if os.path.exists(db_path):
                os.unlink(db_path)


class TestBiomniToolsErrorHandling:
    """Test enhanced error handling in biomni tools."""

    @patch("src.tools.biomni_tools.get_fallback_manager")
    def test_enhanced_error_response(self, mock_fallback_manager):
        """Test that biomni tools return enhanced error responses."""
        # Mock fallback manager to return a failure response (more realistic)
        mock_fallback_manager.return_value.execute_with_fallback.return_value = (
            json.dumps(
                {
                    "success": False,
                    "error": "Mock execution failed",
                    "tool": "test_tool",
                    "execution_method": "mock_failed",
                }
            )
        )

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.is_running.return_value = False

            with patch("asyncio.run") as mock_run:
                mock_run.return_value = json.dumps(
                    {
                        "success": False,
                        "error": "Mock execution failed",
                        "tool": "test_tool",
                        "execution_method": "mock_failed",
                    }
                )

                result = _execute_biomni_tool_in_environment("test_tool", param="value")

                # Parse the JSON response
                response = json.loads(result)

                assert response["success"] is False
                assert "error" in response
                assert "category" in response["error"]
                assert "recovery_actions" in response["error"]

    def test_fallback_tool_creation(self):
        """Test creation of fallback tools when biomni is unavailable."""
        fallback_tool = _create_fallback_tool_wrapper("test_tool", "Test error reason")

        assert fallback_tool.name == "test_tool"
        assert "fallback" in fallback_tool.description.lower()

        # Test fallback tool execution
        result = fallback_tool.func(query="test query")

        assert "test_tool" in result
        assert "test query" in result
        assert "unavailable" in result.lower()


class TestIntegrationScenarios:
    """Test end-to-end error handling scenarios."""

    def test_complete_error_handling_flow(self):
        """Test complete error handling flow from classification to user response."""
        error_text = "ModuleNotFoundError: No module named 'pandas'"
        tool_name = "query_uniprot"

        # Classify and report error
        error_report = classify_and_report_error(error_text, tool_name, "test_method")

        # Verify classification
        assert error_report.context.category == ErrorCategory.DEPENDENCY
        assert error_report.context.severity == ErrorSeverity.CRITICAL

        # Verify user-friendly formatting
        user_message = format_error_for_user(error_report)
        assert "📦" in user_message  # Dependency emoji
        assert "Recommended Actions:" in user_message

        # Verify JSON formatting
        json_response = format_error_for_json(error_report)
        response_data = json.loads(json_response)

        assert response_data["success"] is False
        assert response_data["error"]["category"] == "dependency"
        assert response_data["error"]["severity"] == "critical"
        assert len(response_data["error"]["recovery_actions"]) > 0

    def test_graceful_degradation_scenario(self):
        """Test graceful degradation when tools are unavailable."""
        from src.tools.biomni_tools import _create_fallback_tools

        fallback_tools = _create_fallback_tools()

        # Should have essential tools
        tool_names = [tool.name for tool in fallback_tools]
        assert "query_uniprot" in tool_names
        assert "query_alphafold" in tool_names
        assert "query_pdb" in tool_names

        # Test that fallback tools provide helpful information
        uniprot_tool = next(
            tool for tool in fallback_tools if tool.name == "query_uniprot"
        )
        result = uniprot_tool.func(query="test protein")

        assert "unavailable" in result.lower()
        assert "alternative approaches" in result.lower()
        assert "test protein" in result


if __name__ == "__main__":
    pytest.main([__file__])
