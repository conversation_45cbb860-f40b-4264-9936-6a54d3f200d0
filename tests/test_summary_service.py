"""
Unit tests for Executive Summary Generation Service

Tests cover:
- Session content extraction and analysis
- Key finding identification and categorization
- Multi-session synthesis logic
- BMAD-style formatting validation
- LLM integration and prompt generation
- Summary generation accuracy and consistency
"""

import pytest
import os
import tempfile
import json
import datetime
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from src.service.summary_service import SummaryService


class TestSummaryService:
    """Test suite for SummaryService class"""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def summary_service(self, temp_workspace):
        """Create SummaryService instance for testing"""
        return SummaryService(temp_workspace)

    @pytest.fixture
    def sample_document_content(self):
        """Sample document content for testing"""
        return """# Analysis: Genomic Analysis - Cancer
        
## Document Metadata
- **Analysis Type:** GenomicAnalysis
- **Subject:** Cancer
- **Domain:** bioinformatics
- **Created:** 2025-01-01 10:00:00
- **Last Updated:** 2025-01-01 12:00:00
- **Version:** 1.0

## Session Overview
- **Current Session:** session_1
- **Session Count:** 2
- **Last Updated:** 2025-01-01 12:00:00

## Executive Summary

Current analysis placeholder.

---

## Session 1 - 2025-01-01 10:00:00
**Session ID:** session_1

### Primary Results
- **Key Finding:** Identified 150 differentially expressed genes
- **Significance:** FDR < 0.05 for all findings
- **Methodology:** Used DESeq2 for differential expression analysis

### Technical Details
Analysis performed using RNA-seq data from 50 cancer samples and 30 control samples.
Quality control metrics show high data integrity.

---

## Session 2 - 2025-01-01 12:00:00
**Session ID:** session_2

### Pathway Analysis
- **Enriched Pathways:** Cell cycle regulation, DNA repair mechanisms
- **Statistical Significance:** p-value < 0.001 for top pathways
- **Biological Relevance:** Strong connection to cancer progression

### Validation Results
Experimental validation confirmed 85% of predicted gene targets.
"""

    @pytest.fixture
    def mock_llm_response(self):
        """Mock LLM response for testing"""
        mock_response = Mock()
        mock_response.content = """**Analysis Overview:** This genomic analysis has identified significant patterns in cancer-related gene expression across 2 analysis sessions.

**Key Findings:**
- **Results:** 150 differentially expressed genes identified with high statistical significance
- **Biological Significance:** Enriched pathways include cell cycle regulation and DNA repair mechanisms
- **Validation:** 85% experimental validation rate confirms analysis reliability

**Current Status:** Analysis progressing with strong biological relevance and experimental validation supporting computational findings."""
        return mock_response

    def test_extract_session_content_single_session(
        self, summary_service, temp_workspace
    ):
        """Test extraction of content from single session document"""
        # Create test document
        doc_path = os.path.join(temp_workspace, "test_doc.md")
        content = """# Test Analysis

## Results
Key finding from analysis session.
"""
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(content)

        sessions = summary_service.extract_session_content(doc_path)

        assert len(sessions) == 1
        assert sessions[0]["session_number"] == 1
        assert sessions[0]["session_id"] == "legacy"
        assert "Key finding" in sessions[0]["content"]
        assert sessions[0]["word_count"] > 0

    def test_extract_session_content_multiple_sessions(
        self, summary_service, temp_workspace, sample_document_content
    ):
        """Test extraction of content from multi-session document"""
        doc_path = os.path.join(temp_workspace, "multi_session_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_content)

        sessions = summary_service.extract_session_content(doc_path)

        assert len(sessions) == 2
        assert sessions[0]["session_number"] == 1
        assert sessions[1]["session_number"] == 2
        assert "session_1" in sessions[0]["session_id"]
        assert "session_2" in sessions[1]["session_id"]
        assert all(s["word_count"] > 0 for s in sessions)

    def test_extract_key_sections(self, summary_service):
        """Test extraction of key sections from session content"""
        content = """### Primary Results
Key findings from analysis.

### Technical Details
Implementation details here.

### Validation Results
Validation outcomes documented.
"""

        sections = summary_service._extract_key_sections(content)

        assert "Primary Results" in sections
        assert "Technical Details" in sections
        assert "Validation Results" in sections
        assert "Key findings" in sections["Primary Results"]
        assert "Implementation details" in sections["Technical Details"]

    def test_extract_key_findings(
        self, summary_service, temp_workspace, sample_document_content
    ):
        """Test extraction and categorization of key findings"""
        doc_path = os.path.join(temp_workspace, "findings_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_content)

        sessions = summary_service.extract_session_content(doc_path)
        findings = summary_service.extract_key_findings(sessions)

        assert len(findings) > 0

        # Check finding structure
        for finding in findings:
            assert "category" in finding
            assert "content" in finding
            assert "session_number" in finding
            assert "timestamp" in finding
            assert "priority_score" in finding

        # Check categorization
        categories = [f["category"] for f in findings]
        assert any(
            cat in ["results", "methodology", "biological_significance"]
            for cat in categories
        )

    def test_categorize_finding(self, summary_service):
        """Test finding categorization logic"""
        # Test results categorization
        result_finding = summary_service._categorize_finding(
            "results", "Found 150 differentially expressed genes"
        )
        assert result_finding == "results"

        # Test methodology categorization
        method_finding = summary_service._categorize_finding(
            "methodology", "Used DESeq2 analysis approach"
        )
        assert method_finding == "methodology"

        # Test biological significance
        bio_finding = summary_service._categorize_finding(
            "analysis", "pathway enrichment shows biological significance"
        )
        assert bio_finding == "biological_significance"

    def test_deduplicate_findings(self, summary_service):
        """Test deduplication of similar findings"""
        findings = [
            {
                "content": "Found 150 differentially expressed genes",
                "category": "results",
            },
            {
                "content": "Identified 150 differential expression genes",
                "category": "results",
            },
            {"content": "Used RNA-seq methodology", "category": "methodology"},
        ]

        deduplicated = summary_service._deduplicate_findings(findings)

        # Should remove similar findings (may be 2 or 3 depending on similarity threshold)
        assert len(deduplicated) >= 2
        contents = [f["content"] for f in deduplicated]
        assert "Used RNA-seq methodology" in contents

    def test_prioritize_findings(self, summary_service):
        """Test finding prioritization logic"""
        findings = [
            {
                "content": "General observation",
                "category": "general",
                "session_number": 1,
            },
            {
                "content": (
                    "Key biological discovery with significant pathway enrichment"
                ),
                "category": "biological_significance",
                "session_number": 2,
            },
            {
                "content": "Important results found",
                "category": "results",
                "session_number": 1,
            },
        ]

        prioritized = summary_service._prioritize_findings(findings)

        # Check priority scores were assigned
        for finding in prioritized:
            assert "priority_score" in finding

        # Biological significance should rank high
        bio_finding = next(
            f for f in prioritized if f["category"] == "biological_significance"
        )
        general_finding = next(f for f in prioritized if f["category"] == "general")
        assert bio_finding["priority_score"] > general_finding["priority_score"]

    def test_prepare_summary_context(
        self, summary_service, temp_workspace, sample_document_content
    ):
        """Test preparation of context for summary generation"""
        doc_path = os.path.join(temp_workspace, "context_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_content)

        sessions = summary_service.extract_session_content(doc_path)
        key_findings = summary_service.extract_key_findings(sessions)

        context = summary_service._prepare_summary_context(
            doc_path, sessions, key_findings
        )

        assert "document_name" in context
        assert "analysis_type" in context
        assert "subject" in context
        assert "total_sessions" in context
        assert "total_words" in context
        assert "findings_by_category" in context
        assert "key_findings" in context

        assert context["total_sessions"] == 2
        assert context["analysis_type"] == "General"  # Default analysis type
        assert context["total_words"] > 0

    def test_create_summary_prompt(self, summary_service):
        """Test LLM prompt generation for summary"""
        context = {
            "analysis_type": "GenomicAnalysis",
            "subject": "Cancer",
            "total_sessions": 2,
            "latest_timestamp": "2025-01-01 12:00:00",
            "total_words": 250,
            "key_findings": [
                {"category": "results", "content": "Found 150 genes"},
                {
                    "category": "biological_significance",
                    "content": "Pathway enrichment significant",
                },
            ],
        }

        prompt = summary_service._create_summary_prompt(context)

        assert "GenomicAnalysis" in prompt
        assert "Cancer" in prompt
        assert "2" in prompt  # total sessions
        assert "150 genes" in prompt
        assert "BMAD-style formatting" in prompt
        assert "maximum 150 words" in prompt

    @patch("src.service.summary_service.get_llm_by_type")
    def test_generate_summary_content(
        self,
        mock_get_llm,
        summary_service,
        temp_workspace,
        sample_document_content,
        mock_llm_response,
    ):
        """Test LLM-based summary content generation"""
        # Setup mock
        mock_llm = Mock()
        mock_llm.invoke.return_value = mock_llm_response
        mock_get_llm.return_value = mock_llm

        # Create summary service with mocked LLM
        summary_service.llm = mock_llm

        # Create test document
        doc_path = os.path.join(temp_workspace, "summary_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_content)

        sessions = summary_service.extract_session_content(doc_path)
        key_findings = summary_service.extract_key_findings(sessions)

        summary_content = summary_service.generate_summary_content(
            doc_path, sessions, key_findings
        )

        assert "Analysis Overview" in summary_content
        assert "Key Findings" in summary_content
        assert "**" in summary_content  # BMAD-style formatting
        assert len(summary_content) > 50  # Reasonable length

    def test_apply_bmad_formatting(self, summary_service):
        """Test BMAD-style formatting application"""
        raw_content = """This analysis shows key finding about cancer genes.
        The primary outcome was significant results.
        Important result shows pathway enrichment."""

        formatted = summary_service._apply_bmad_formatting(raw_content)

        # Should have bold formatting for key phrases
        assert "**key finding**" in formatted or "key finding" in formatted
        assert "**significant**" in formatted or "significant" in formatted

        # Should maintain structure
        assert len(formatted.split("\n")) >= len(raw_content.split("\n"))

    def test_generate_fallback_summary(self, summary_service):
        """Test fallback summary generation"""
        sessions = [
            {
                "session_number": 1,
                "timestamp": "2025-01-01 10:00:00",
                "word_count": 100,
            },
            {
                "session_number": 2,
                "timestamp": "2025-01-01 12:00:00",
                "word_count": 150,
            },
        ]

        key_findings = [
            {
                "category": "results",
                "content": "Important finding about gene expression",
            },
            {"category": "methodology", "content": "Used advanced analysis techniques"},
        ]

        fallback_summary = summary_service._generate_fallback_summary(
            sessions, key_findings
        )

        assert "Analysis Overview" in fallback_summary
        assert "2 analysis sessions" in fallback_summary
        assert "Key Findings" in fallback_summary
        assert "gene expression" in fallback_summary
        assert "analysis techniques" in fallback_summary

    def test_update_document_summary(self, summary_service, temp_workspace):
        """Test updating executive summary in document"""
        # Create test document with existing summary
        doc_content = """# Test Analysis

## Executive Summary

Old summary content here.

## Other Section

Content continues here.
"""

        doc_path = os.path.join(temp_workspace, "update_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(doc_content)

        new_summary = "**Updated Summary:** New findings show significant improvements."

        success = summary_service.update_document_summary(doc_path, new_summary)

        assert success

        # Verify update
        with open(doc_path, "r", encoding="utf-8") as f:
            updated_content = f.read()

        assert "Updated Summary" in updated_content
        assert "Old summary content" not in updated_content
        assert "## Other Section" in updated_content  # Other sections preserved

    @patch("src.service.summary_service.get_llm_by_type")
    def test_generate_executive_summary(
        self,
        mock_get_llm,
        summary_service,
        temp_workspace,
        sample_document_content,
        mock_llm_response,
    ):
        """Test complete executive summary generation"""
        # Setup mock
        mock_llm = Mock()
        mock_llm.invoke.return_value = mock_llm_response
        mock_get_llm.return_value = mock_llm
        summary_service.llm = mock_llm

        # Create test document
        doc_path = os.path.join(temp_workspace, "complete_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(sample_document_content)

        summary = summary_service.generate_executive_summary(doc_path)

        assert summary is not None
        assert len(summary) > 0
        assert "Analysis Overview" in summary
        assert "**" in summary  # BMAD formatting

    def test_get_summary_metadata(self, summary_service, temp_workspace):
        """Test summary metadata retrieval"""
        doc_path = os.path.join(temp_workspace, "metadata_doc.md")

        # Create test document
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write("# Test Document\n\nContent here.")

        # Generate metadata by creating sessions and findings
        sessions = [{"session_number": 1, "word_count": 50}]
        key_findings = [{"category": "results", "content": "Test finding"}]

        # Mock the methods to return test data
        with (
            patch.object(
                summary_service, "extract_session_content", return_value=sessions
            ),
            patch.object(
                summary_service, "extract_key_findings", return_value=key_findings
            ),
            patch.object(
                summary_service, "generate_summary_content", return_value="Test summary"
            ),
        ):

            metadata = summary_service.get_summary_metadata(doc_path)

            assert "total_sessions" in metadata
            assert "total_findings" in metadata
            assert "last_updated" in metadata
            assert "word_count" in metadata
            assert "finding_categories" in metadata
            assert "summary_length" in metadata


class TestBMADStyleFormatting:
    """Test suite for BMAD-style formatting validation"""

    @pytest.fixture
    def summary_service(self):
        """Create SummaryService for formatting tests"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield SummaryService(temp_dir)

    def test_bmad_style_elements(self, summary_service):
        """Test presence of BMAD-style elements"""
        content = "Analysis shows significant results with important findings."
        formatted = summary_service._apply_bmad_formatting(content)

        # Should apply BMAD-style formatting
        assert "**significant**" in formatted or "significant" in formatted

    def test_bullet_point_formatting(self, summary_service):
        """Test bullet point formatting compliance"""
        content = """Key finding about genes
        Important result about pathways
        Significant discovery about proteins"""

        formatted = summary_service._apply_bmad_formatting(content)

        # Should maintain or create structure (flexible assertion)
        assert len(formatted) > 0
        assert "genes" in formatted and "pathways" in formatted

    def test_professional_tone_preservation(self, summary_service):
        """Test that professional, scientific tone is maintained"""
        content = (
            "The analysis revealed differential expression patterns in cancer samples."
        )
        formatted = summary_service._apply_bmad_formatting(content)

        # Should preserve scientific terminology
        assert "differential expression" in formatted
        assert "analysis" in formatted
        assert "cancer samples" in formatted


class TestSessionHandling:
    """Test suite for multi-session analysis"""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def summary_service(self, temp_workspace):
        """Create SummaryService for session tests"""
        return SummaryService(temp_workspace)

    def test_session_progression_tracking(self, summary_service, temp_workspace):
        """Test tracking of analysis progression across sessions"""
        multi_session_content = """# Analysis

---

## Session 1 - 2025-01-01 10:00:00
**Session ID:** session_1

Initial findings show promise.

---

## Session 2 - 2025-01-01 11:00:00
**Session ID:** session_2

Validation confirms initial results.

---

## Session 3 - 2025-01-01 12:00:00
**Session ID:** session_3

Extended analysis reveals new patterns.
"""

        doc_path = os.path.join(temp_workspace, "progression_doc.md")
        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(multi_session_content)

        sessions = summary_service.extract_session_content(doc_path)

        assert len(sessions) == 3
        assert sessions[0]["session_number"] == 1
        assert sessions[1]["session_number"] == 2
        assert sessions[2]["session_number"] == 3

        # Check progression narrative
        contents = [s["content"] for s in sessions]
        assert "Initial findings" in contents[0]
        assert "Validation confirms" in contents[1]
        assert "Extended analysis" in contents[2]

    def test_cumulative_knowledge_synthesis(self, summary_service, temp_workspace):
        """Test synthesis of cumulative knowledge across sessions"""
        doc_path = os.path.join(temp_workspace, "cumulative_doc.md")

        cumulative_content = """# Cumulative Analysis

---

## Session 1 - 2025-01-01 10:00:00
**Session ID:** session_1

- **Methodology:** Established RNA-seq analysis pipeline
- **Results:** Initial gene list of 200 candidates

---

## Session 2 - 2025-01-01 11:00:00
**Session ID:** session_2

- **Validation:** Confirmed 150 genes from initial list
- **Expansion:** Added pathway analysis component

---

## Session 3 - 2025-01-01 12:00:00  
**Session ID:** session_3

- **Integration:** Combined gene and pathway data
- **Discovery:** Identified 3 key regulatory networks
"""

        with open(doc_path, "w", encoding="utf-8") as f:
            f.write(cumulative_content)

        sessions = summary_service.extract_session_content(doc_path)
        key_findings = summary_service.extract_key_findings(sessions)

        # Should capture cumulative progression
        finding_contents = [f["content"] for f in key_findings]

        # Check for cumulative elements
        assert any("pipeline" in content for content in finding_contents)
        assert any("150 genes" in content for content in finding_contents)
        assert any("regulatory networks" in content for content in finding_contents)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
