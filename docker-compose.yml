
services:
  omiy-standalone:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: omiy-multiagent
    ports:
      - "8000:8000"
    environment:
      - DEFAULT_WORKSPACE=/app/workspace
      # Reasoning LLM (for complex reasoning tasks)
      - REASONING_PROVIDER=${REASONING_PROVIDER:-gemini}
      - REASONING_API_KEY=${REASONING_API_KEY}
      - REASONING_BASE_URL=${REASONING_BASE_URL}
      - REASONING_MODEL=${REASONING_MODEL:-gemini-2.5-pro}
      # Non-reasoning LLM (for straightforward tasks)
      - BASIC_PROVIDER=${BASIC_PROVIDER:-gemini}
      - BASIC_API_KEY=${BASIC_API_KEY}
      - BASIC_BASE_URL=${BASIC_BASE_URL}
      - BASIC_MODEL=${BASIC_MODEL:-gemini-2.5-flash}
      # Vision-language LLM (for tasks requiring visual understanding)
      - VL_PROVIDER=${VL_PROVIDER:-gemini}
      - VL_API_KEY=${VL_API_KEY}
      - VL_BASE_URL=${VL_BASE_URL}
      - VL_MODEL=${VL_MODEL:-gemini-2.5-flash}
      # Application Settings
      - DEBUG=${DEBUG}
      - APP_ENV=${APP_ENV}
      # Other environment variables
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      # R Environment Configuration
      - R_ENV_PATH=${R_ENV_PATH:-/Users/<USER>/miniforge3/envs/r_env}
    volumes:
      - ./workspace:/app/workspace
      - ./data:/app/data
      # R is now installed directly in the container, no need to mount host environment
    restart: unless-stopped
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
    #   interval: 30s
    #   timeout: 10sa
    #   retries: 3