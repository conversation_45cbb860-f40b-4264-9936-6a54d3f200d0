# Python-generated files
__pycache__/
*.py[oc]
build/
dist/
wheels/
*.egg-info
.coverage
agent_history.gif

# Virtual environments
.venv
.venv_*/
venv/
env/

# Environment variables
.env

# Database files
data/checkpoints.db*

# Documentation
docs/
!docs/stories/
!docs/stories/AG_UI_PROTOCOL_COMPLIANCE_REVIEW.md

# IDE and tool configurations
.bmad-core/
.claude/
.clinerules/
.cursor/
.gemini/
.qodo/
.trae/
.vscode/
.idea/

# System files
.DS_Store
.dockerenv
.env.docker

# Todo file
workspace/todo.md
Biomni/
