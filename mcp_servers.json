{"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "transport": "stdio"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}, "transport": "stdio"}, "exa": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "exa", "--key", "0cf3c1ca-e6aa-4a05-8c1f-35286528a5a7"], "transport": "stdio"}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander@latest"], "env": {"DESKTOP_COMMANDER_CWD": "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph", "DESKTOP_COMMANDER_WORKSPACE": "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace"}, "transport": "stdio"}, "pubmed-mcp-server": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@JackKuo666/pubmed-mcp-server", "--key", "0cf3c1ca-e6aa-4a05-8c1f-35286528a5a7"], "transport": "stdio"}, "todo-md-mcp": {"command": "npx", "args": ["-y", "@danjdewhurst/todo-md-mcp"], "transport": "stdio", "env": {"TODO_FILE_PATH": "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/todo.md"}}}