# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation (exclude README.md as it's needed for build)
CONTRIBUTING*
LICENSE*

# Tests
tests/
.pytest_cache/
.coverage
.tox/

# Data and workspace files
data/
workspace/
assets/
docs/

# Configuration
.qodo/
mcp_servers.json
pre-commit
Makefile

# Environment files (keep .env.example for reference)
.env

# Lock files and cache
.python-version
