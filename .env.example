# LLM Environment variables

REASONING_PROVIDER=e.g.gemini
REASONING_API_KEY=your-gemini-api-key
REASONING_BASE_URL=https://generativelanguage.googleapis.com/v1beta
REASONING_MODEL=gemini-2.5-pro

# Non-reasoning LLM (for straightforward tasks)
BASIC_PROVIDER=gemini
BASIC_API_KEY=your-gemini-api-key
BASIC_BASE_URL=https://generativelanguage.googleapis.com/v1beta
BASIC_MODEL=gemini-2.5-flash

# Vision-language LLM (for tasks requiring visual understanding)
VL_PROVIDER=gemini
VL_API_KEY=your-gemini-api-key
VL_BASE_URL=https://generativelanguage.googleapis.com/v1beta
VL_MODEL=gemini-2.5-flash

# Application Settings
DEBUG=True
APP_ENV=development

# Add other environment variables as needed
TAVILY_API_KEY=your-tavily-api-key
CHROME_INSTANCE_PATH=path-to/Chrome

# R Environment Path
# For local development, use the path to your local conda installation
# Examples:
# R_ENV_PATH=/Users/<USER>/anaconda3           # macOS Anaconda
# R_ENV_PATH=/opt/anaconda3                      # Linux Anaconda  
# R_ENV_PATH=/home/<USER>/miniconda3           # Linux Miniconda

# Default for most systems (update to match your conda installation)
R_ENV_PATH=/opt/anaconda3

# Python Environment Path (optional, defaults to R_ENV_PATH if not set)
# PYTHON_ENV_PATH=/opt/anaconda3

# R Conda Environment Name (the environment containing your R installation)
R_CONDA_ENV=r-env
