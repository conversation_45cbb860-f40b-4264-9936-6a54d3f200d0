"""
Tool modules loader for biomni toolkit.
This module provides functionality to load all available tools from the biomni toolkit.
"""

import importlib
import os
from typing import Dict, List, Any
from biomni.utils import get_tool_decorated_functions


def load_all_tools() -> Dict[str, List[Any]]:
    """
    Load all tools from the biomni toolkit.

    Returns:
        Dictionary mapping module names to lists of tool functions
    """
    # Get the directory of the current file (tool_modules.py)
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Define the tool modules to load (they're in the same directory as tool_modules.py)
    tool_modules = [
        "biochemistry.py",
        "bioengineering.py",
        "biophysics.py",
        "cancer_biology.py",
        "cell_biology.py",
        "database.py",
        "genetics.py",
        "genomics.py",
        "immunology.py",
        "literature.py",
        "microbiology.py",
        "molecular_biology.py",
        "pathology.py",
        "pharmacology.py",
        "physiology.py",
        "support_tools.py",
        "synthetic_biology.py",
        "systems_biology.py",
    ]

    all_tools = {}

    for module_file in tool_modules:
        try:
            # Load the tool functions from each module
            module_name = module_file.replace(".py", "")
            # The relative path from utils.py to the tool module
            relative_path = f"tool/{module_file}"
            tool_functions = get_tool_decorated_functions(relative_path)

            if tool_functions:
                all_tools[module_name] = tool_functions

        except Exception as e:
            # Skip modules that fail to load
            print(f"Warning: Could not load tools from {module_file}: {e}")
            continue

    return all_tools


def get_tool_count() -> int:
    """
    Get the total number of available tools.

    Returns:
        Total number of tools across all modules
    """
    all_tools = load_all_tools()
    return sum(len(tools) for tools in all_tools.values())


def get_tool_modules() -> List[str]:
    """
    Get list of available tool module names.

    Returns:
        List of module names that contain tools
    """
    all_tools = load_all_tools()
    return list(all_tools.keys())
