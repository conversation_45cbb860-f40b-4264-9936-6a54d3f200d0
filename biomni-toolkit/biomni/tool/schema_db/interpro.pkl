���      }�(�metadata�}�(�name��InterPro REST API��base_url��"https://www.ebi.ac.uk/interpro/api��description��OAPI for accessing InterPro protein domains, families, and functional sites data�u�main_data_types�}�(�entry�}�(h�7Predicted functional and structural domains on proteins��sources�]�(�interpro��cath-gene3d��cdd��hamap��panther��pfam��pirsf��prints��prosite��smart��sfld��superfamily��ncbifam�e�example_ids�}�(h�	IPR023411�h�PF06235�uu�protein�}�(h�Protein sequence�h]��uniprot�a�subtypes�]�(�reviewed��
unreviewed�eh}�h%�P04637�su�	structure�}�(h�,Macromolecular structures involving proteins�h]��pdb�ah}�h0�1ABC�su�set�}�(h�-Sets describing relationships between entries�h]�(hheh}�h�cl0001�su�taxonomy�}�(h�$Taxonomic information about proteins�h]�h%ah}�h%�9606�su�proteome�}�(h�<Collections of proteins defined from whole genome sequencing�h]�h%ah}�h%�UP000005640�suu�	endpoints�}�(�basic�}�(h�7Basic endpoints to retrieve counts or lists of entities��patterns�]�(}�(�path��/{data_type}�h� Get counts of entities by source��example��/entry��
response_type��
count_list�u}�(hM�/{data_type}/{source}�h�-Get a list of entities from a specific source�hP�/entry/interpro�hR�entity_list�u}�(hM�!/{data_type}/{source}/{accession}�h�0Get detailed information about a specific entity�hP�/entry/interpro/IPR023411�hR�detailed_object�ueu�filtered�}�(h�-Endpoints with filters to narrow down results�hJ]�(}�(hM�/{main_type}/{filter_type}�h�Filter by another data type�hP�/entry/protein�hRhSu}�(hM�*/{main_type}/{filter_type}/{filter_source}�h�2Filter by another data type from a specific source�hP�/entry/protein/uniprot�hRhSu}�(hM�G/{main_type}/{main_source}/protein/{protein_source}/{protein_accession}�h�BGet entities from a specific source that map to a specific protein�hP�&/entry/interpro/protein/uniprot/P04637�hRhXu}�(hM�@/protein/{protein_source}/entry/{entry_source}/{entry_accession}�h�(Get proteins containing a specific entry�hP�*/protein/reviewed/entry/interpro/ipr002117�hRhXu}�(hM�B/taxonomy/{taxonomy_source}/entry/{entry_source}/{entry_accession}�h�7Get organisms with proteins containing a specific entry�hP�*/taxonomy/uniprot/entry/interpro/IPR026381�hRhXueuu�response_types�}�(hS�1List of counts of entities grouped by data source�hX�1Paginated list of entities with basic information�h]�,Detailed information about a specific entity�u�examples�}�(�get_all_interpro_entries�}�(h�"Get a list of all InterPro entries��url��1https://www.ebi.ac.uk/interpro/api/entry/interpro�u�get_entry_details�}�(h�8Get detailed information about a specific InterPro entry�h��;https://www.ebi.ac.uk/interpro/api/entry/interpro/IPR023411�u�get_proteins_with_domain�}�(h�;Get reviewed proteins containing a specific InterPro domain�h��Lhttps://www.ebi.ac.uk/interpro/api/protein/reviewed/entry/interpro/ipr002117�u�get_organisms_with_entry�}�(h�@Get organisms with proteins containing a specific InterPro entry�h��Lhttps://www.ebi.ac.uk/interpro/api/taxonomy/uniprot/entry/interpro/IPR026381�u�get_entries_for_protein�}�(h�0Get InterPro entries found in a specific protein�h��Hhttps://www.ebi.ac.uk/interpro/api/entry/interpro/protein/uniprot/P04637�uuu.