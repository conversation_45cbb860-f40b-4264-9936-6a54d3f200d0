��l      }�(�metadata�}�(�name��cBioPortal REST API��version��
1.0 (beta)��base_url��https://www.cbioportal.org/api��description��EA web service for supplying JSON formatted data to cBioPortal clients��status��Alpha/Beta - subject to change�u�
categories�}�(�health�}�(h	�API health check endpoint��	endpoints�]�}�(�path��/health��method��GET�h	�$Get the running status of the server�uau�
clinical_data�}�(h	�&Clinical data for patients and samples�h]�(}�(h�3/studies/{studyId}/samples/{sampleId}/clinical-data�hhh	�,Get all clinical data of a sample in a study�u}�(h�5/studies/{studyId}/patients/{patientId}/clinical-data�hhh	�-Get all clinical data of a patient in a study�u}�(h� /studies/{studyId}/clinical-data�hhh	� Get all clinical data in a study�ueu�studies�}�(h	�Cancer studies�h]�(}�(h�/studies�hhh	�Get all studies�u}�(h�/studies/{studyId}�hhh	�Get a study by <PERSON>�u}�(h�/studies/{studyId}/tags�hhh	�Get the tags of a study�ueu�samples�}�(h	�Samples from cancer studies�h]�(}�(h�/studies/{studyId}/samples�hhh	�Get all samples in a study�u}�(h�%/studies/{studyId}/samples/{sampleId}�hhh	�Get a sample in a study�u}�(h�//studies/{studyId}/patients/{patientId}/samples�hhh	�'Get all samples of a patient in a study�u}�(h�/samples�hhh	�Get all samples�ueu�sample_lists�}�(h	�7Lists of samples (e.g., all samples with mutation data)�h]�(}�(h�/studies/{studyId}/sample-lists�hhh	�Get all sample lists in a study�u}�(h�
/sample-lists�hhh	�Get all sample lists�u}�(h�/sample-lists/{sampleListId}�hhh	�Get a sample list�u}�(h�'/sample-lists/{sampleListId}/sample-ids�hhh	�#Get all sample IDs in a sample list�ueu�patients�}�(h	�Patients from cancer studies�h]�(}�(h�/studies/{studyId}/patients�hhh	�Get all patients in a study�u}�(h�'/studies/{studyId}/patients/{patientId}�hhh	�Get a patient in a study�u}�(h�	/patients�hhh	�Get all patients�ueu�	mutations�}�(h	�Genetic mutations�h]�}�(h�2/molecular-profiles/{molecularProfileId}/mutations�hhh	�(Get all mutations in a molecular profile�uau�molecular_data�}�(h	�3Molecular data (gene expression, methylation, etc.)�h]�}�(h�7/molecular-profiles/{molecularProfileId}/molecular-data�hhh	�-Get all molecular data in a molecular profile�uau�discrete_copy_number�}�(h	� Discrete copy number alterations�h]�}�(h�=/molecular-profiles/{molecularProfileId}/discrete-copy-number�hhh	�?Get all discrete copy number alterations in a molecular profile�uau�molecular_profiles�}�(h	�5Molecular profiles (e.g., mutations, CNA, expression)�h]�(}�(h�%/studies/{studyId}/molecular-profiles�hhh	�%Get all molecular profiles in a study�u}�(h�/molecular-profiles�hhh	�Get all molecular profiles�u}�(h�(/molecular-profiles/{molecularProfileId}�hhh	�Get a molecular profile�ueu�genes�}�(h	�Genes�h]�(}�(h�/genes�hhh	�
Get all genes�u}�(h�/genes/{geneId}�hhh	�
Get a gene�u}�(h�/genes/{geneId}/aliases�hhh	�Get all aliases of a gene�ueu�generic_assays�}�(h	�/Generic assays (e.g., proteomics, metabolomics)�h]�(}�(h�(/generic-assay-meta/{molecularProfileId}�hhh	�1Get all generic assay meta in a molecular profile�u}�(h�8/generic-assay-meta/generic-assay/{genericAssayStableId}�hhh	�Get generic assay meta by ID�ueu�generic_assay_data�}�(h	�8Generic assay data (e.g., proteomics, metabolomics data)�h]�}�(h�M/generic-assay-data/{molecularProfileId}/generic-assay/{genericAssayStableId}�hhh	�Get generic assay data by ID�uau�gene_panels�}�(h	�"Gene panels (collections of genes)�h]�(}�(h�/gene-panels�hhh	�Get all gene panels�u}�(h�/gene-panels/{genePanelId}�hhh	�Get a gene panel�ueu�copy_number_segments�}�(h	�Copy number segments�h]�}�(h�:/studies/{studyId}/samples/{sampleId}/copy-number-segments�hhh	�3Get all copy number segments in a sample in a study�uau�clinical_attributes�}�(h	�1Clinical attributes (e.g., age, gender, survival)�h]�(}�(h�&/studies/{studyId}/clinical-attributes�hhh	�&Get all clinical attributes in a study�u}�(h�</studies/{studyId}/clinical-attributes/{clinicalAttributeId}�hhh	�#Get a clinical attribute in a study�u}�(h�/clinical-attributes�hhh	�Get all clinical attributes�ueu�info�}�(h	�API information�h]�}�(h�/info�hhh	�Get information about the API�uau�cancer_types�}�(h	�Cancer types�h]�(}�(h�
/cancer-types�hhh	�Get all cancer types�u}�(h�/cancer-types/{cancerTypeId}�hhh	�Get a cancer type�ueuuu.