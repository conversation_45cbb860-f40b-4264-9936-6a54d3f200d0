��      }�(�base_url��https://rest.kegg.jp��version��December 1, 2024��
categories�}�(�Info�}�(�description��DDisplay database release information and linked database information��	endpoints�}��GET /info/<database>�}�(h	�TDisplays the database release information with statistics for the specified database��required_params�]��database�a�optional_params�]��example��
/info/kegg�usu�List�}�(h	�7Obtain a list of entry identifiers and associated names�h}�(�GET /list/<database>�}�(h	�6Obtain a list of all entries in the specified database�h]�hah]�h�
/list/pathway�u�GET /list/pathway/<org>�}�(h	�+Obtain a list of organism-specific pathways�h]��org�ah]�h�/list/pathway/hsa�u�GET /list/brite/<option>�}�(h	�<Obtain a list of brite hierarchies with the specified option�h]��option�ah]�h�/list/brite/br�u�GET /list/<dbentries>�}�(h	�JObtain a list of definitions for a given set of database entry identifiers�h]��	dbentries�ah]�h�/list/hsa:10458+ece:Z5100�uuu�Find�}�(h	�<Find entries with matching query keyword or other query data�h}�(�GET /find/<database>/<query>�}�(h	�ESearches entry identifier and associated fields for matching keywords�h]�(h�query�eh]�h�/find/genes/shiga+toxin�u�%GET /find/<database>/<query>/<option>�}�(h	�4Searches chemical compounds with specific properties�h]�(hh>h,eh]�h�/find/compound/C7H10O5/formula�uuu�Get�}�(h	�Retrieve given database entries�h}�(�GET /get/<dbentries>�}�(h	�.Retrieves database entries in flat file format�h]�h3ah]�h�/get/C01290+G00092�u�GET /get/<dbentries>/<option>�}�(h	�ARetrieves database entries in specific format based on the option�h]�h3ah]�h,ah�/get/hsa:10458+ece:Z5100/aaseq�uuu�Conv�}�(h	�4Convert KEGG identifiers to/from outside identifiers�h}�(�!GET /conv/<target_db>/<source_db>�}�(h	�&Converts identifiers between databases�h]�(�	target_db��	source_db�eh]�h�/conv/eco/ncbi-geneid�u�!GET /conv/<target_db>/<dbentries>�}�(h	�)Converts identifiers for specific entries�h]�(h_h3eh]�h�(/conv/ncbi-proteinid/hsa:10458+ece:Z5100�uuu�Link�}�(h	�7Find related entries by using database cross-references�h}�(�!GET /link/<target_db>/<source_db>�}�(h	�/Retrieves database to database cross-references�h]�(h_h`eh]�h�/link/pathway/hsa�u�!GET /link/<target_db>/<dbentries>�}�(h	�/Retrieves cross-references for specific entries�h]�(h_h3eh]�h�!/link/pathway/hsa:10458+ece:Z5100�u�*GET /link/<target_db>/<source_db>/<option>�}�(h	�=Retrieves database to database cross-references in RDF format�h]�(h_h`eh]�h,ah�/link/atc/D01441/n-triple�u�*GET /link/<target_db>/<dbentries>/<option>�}�(h	�=Retrieves cross-references for specific entries in RDF format�h]�(h_h3eh]�h,ah�/link/jtc/D01441/turtle�uuu�DDI�}�(h	�#Find adverse drug-drug interactions�h}�(�GET /ddi/<dbentry>�}�(h	�/Reports all known interactions for a given drug�h]��dbentry�ah]�h�/ddi/D00564�u�GET /ddi/<dbentries>�}�(h	�@Checks if any drug pair in a given set of drugs has interactions�h]�h3ah]�h�/ddi/D00564+D00100+D00109�uuuu�	databases�}�(�KEGG databases�}�(�pathway�}�(h	�KEGG pathway maps��
kid_prefix�]�(�map��ko��ec��rn��<org>�eu�brite�}�(h	�BRITE functional hierarchies�h�]�(�br��jp�h�h�eu�module�}�(h	�KEGG modules�h�]�(�M��<org>_M�eu�	orthology�}�(h	�KO functional orthologs�h�]��K�au�genes�}�(h	�Genes in KEGG organisms�h�]�u�genome�}�(h	�KEGG organisms�h�]��T�au�compound�}�(h	�Small molecules�h�]��C�au�glycan�}�(h	�Glycans�h�]��G�au�reaction�}�(h	�Biochemical reactions�h�]��R�au�rclass�}�(h	�Reaction class�h�]��RC�au�enzyme�}�(h	�Enzyme nomenclature�h�]�u�network�}�(h	�Network elements�h�]��N�au�variant�}�(h	�Human gene variants�h�]�u�disease�}�(h	�Human diseases�h�]��H�au�drug�}�(h	�Drugs�h�]��D�au�dgroup�}�(h	�Drug groups�h�]��DG�auu�Outside databases�}�(�pubmed�}�(h	�NCBI PubMed��	id_format��	PubMed ID�u�ncbi-geneid�}�(h	�	NCBI Gene�h�Gene ID��usage��	conv only�u�ncbi-proteinid�}�(h	�NCBI Protein�h�
Protein ID�h�h�u�uniprot�}�(h	�UniProt�h�UniProt Accession�h�h�u�pubchem�}�(h	�NCBI PubChem�h�PubChem SID�h�h�u�chebi�}�(h	�ChEBI�h�ChEBI ID�h�h�u�atc�}�(h	�ATC classification�h�7-letter ATC code�h��	link only�u�jtc�}�(h	�Therapeutic category in Japan�h�Therapeutic category code�h�j  u�ndc�}�(h	�Drug products in the USA�h�National Drug Code�h��link, ddi only�u�yj�}�(h	�Drug products in Japan�h�YJ code�h��ddi only�u�yk�}�(h	j  h�Part of Korosho code�h�j  uuu�options�}�(�get_options�}�(�aaseq��Amino acid sequence��ntseq��Nucleotide sequence��mol��Molecular structure data��kcf��KEGG Chemical Function format��image��GIF/PNG image files��image2x��)Double-sized PNG image (for pathway maps)��conf��%Configuration file (for pathway maps)��kgml��,KEGG Markup Language file (for pathway maps)��json��!JSON file (for brite hierarchies)�u�find_options�}�(�formula��Chemical formula search��
exact_mass��Search by exact mass��
mol_weight��Search by molecular weight��nop�� Disable partial match processing�u�link_rdf_options�}�(�turtle��Turtle RDF format��n-triple��N-Triples RDF format�uuu.