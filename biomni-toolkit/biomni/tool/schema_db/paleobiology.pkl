���      }�(�base_url��https://paleobiodb.org/data1.2��formats�}�(�json��.json��txt��.txt��csv��.csv��tsv��.tsv��larkin��.larkin��ris��.ris��png��.png�u�vocabularies�}�(�pbdb�h�com�h�bibjson�hu�	endpoints�}�(�occurrences_list�}�(�url��{base_url}/occs/list{format}��description��$Returns a list of fossil occurrences��required�]��optional�]�(�	base_name��
taxon_name��id��coll_id��interval��max_interval��min_interval��level��country��	continent��state��county��	formation��member��group��	lithology��environment��bbox��loc��lngmin��lngmax��latmin��latmax��show��vocab�e�method��GET�u�occurrences_single�}�(h�{base_url}/occs/single{format}�h�"Returns a single fossil occurrence�h ]�h&ah"]�(h;h<eh=h>u�occurrences_refs�}�(h�{base_url}/occs/refs{format}�h�5Returns references associated with fossil occurrences�h ]�h"]�(h$h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<eh=h>u�collections_list�}�(h�{base_url}/colls/list{format}�h�$Returns a list of fossil collections�h ]�h"]�(h$h%h&h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<eh=h>u�collections_single�}�(h�{base_url}/colls/single{format}�h�"Returns a single fossil collection�h ]�h&ah"]�(h;h<eh=h>u�collections_refs�}�(h�{base_url}/colls/refs{format}�h�5Returns references associated with fossil collections�h ]�h"]�(h$h%h&h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<eh=h>u�specimens_list�}�(h�{base_url}/specs/list{format}�h�Returns a list of specimens�h ]�h"]�(h$h%h&h'�specimen_id�h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<eh=h>u�specimens_single�}�(h�{base_url}/specs/single{format}�h�Returns a single specimen�h ]�h&ah"]�(h;h<eh=h>u�measurements_list�}�(h�{base_url}/meas/list{format}�h�Returns a list of measurements�h ]�h"]�(h$h%h&h'hch(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<eh=h>u�	taxa_list�}�(h�{base_url}/taxa/list{format}�h�!Returns a list of taxonomic names�h ]�h"]�(�name�h&�rank�h;h<eh=h>u�taxa_single�}�(h�{base_url}/taxa/single{format}�h�1Returns information about a single taxonomic name�h ]�hvah"]�(h;h<eh=h>u�	taxa_auto�}�(h�{base_url}/taxa/auto{format}�h�'Auto-completes a partial taxonomic name�h ]�hvah"]�(h;h<eh=h>u�	taxa_refs�}�(h�{base_url}/taxa/refs{format}�h�2Returns references associated with taxonomic names�h ]�h"]�(hvh&hwh;h<eh=h>u�
opinions_list�}�(h� {base_url}/opinions/list{format}�h�$Returns a list of taxonomic opinions�h ]�h"]�(hvh&hw�ref_id�h;h<eh=h>u�opinions_single�}�(h�"{base_url}/opinions/single{format}�h�"Returns a single taxonomic opinion�h ]�h&ah"]�(h;h<eh=h>u�intervals_list�}�(h�!{base_url}/intervals/list{format}�h�+Returns a list of geological time intervals�h ]�h"]�(�scale��min_ma��max_ma�h;h<eh=h>u�intervals_single�}�(h�#{base_url}/intervals/single{format}�h�)Returns a single geological time interval�h ]�h&ah"]�(h;h<eh=h>u�timescales_list�}�(h�"{base_url}/timescales/list{format}�h�'Returns a list of geological timescales�h ]�h"]�(h;h<eh=h>u�timescales_single�}�(h�${base_url}/timescales/single{format}�h�%Returns a single geological timescale�h ]�h&ah"]�(h;h<eh=h>u�bounds_list�}�(h�{base_url}/bounds/list{format}�h�!Returns a list of interval bounds�h ]�h"]�(h�h�h�h;h<eh=h>u�places_list�}�(h�{base_url}/places/list{format}�h�#Returns a list of geographic places�h ]�h"]�(hvh5h6h7h8h9h:h;h<eh=h>u�
places_single�}�(h� {base_url}/places/single{format}�h�!Returns a single geographic place�h ]�h&ah"]�(h;h<eh=h>u�strata_list�}�(h�{base_url}/strata/list{format}�h�#Returns a list of geological strata�h ]�h"]�(hv�type�hwh;h<eh=h>u�
strata_single�}�(h� {base_url}/strata/single{format}�h�#Returns a single geological stratum�h ]�h&ah"]�(h;h<eh=h>u�	refs_list�}�(h�{base_url}/refs/list{format}�h�*Returns a list of bibliographic references�h ]�h"]�(�author��year��title�h&h��pubid�h;h<eh=h>u�refs_single�}�(h�{base_url}/refs/single{format}�h�(Returns a single bibliographic reference�h ]�h&ah"]�(h;h<eh=h>u�	pubs_list�}�(h�{base_url}/pubs/list{format}�h�'Returns a list of official publications�h ]�h"]�(h;h<eh=h>u�pubs_single�}�(h�{base_url}/pubs/single{format}�h�%Returns a single official publication�h ]�h&ah"]�(h;h<eh=h>u�
archives_list�}�(h� {base_url}/archives/list{format}�h�Returns a list of data archives�h ]�h"]�(h;h<eh=h>u�archives_single�}�(h�"{base_url}/archives/single{format}�h�Returns a single data archive�h ]�h&ah"]�(h;h<eh=h>u�config�}�(h�{base_url}/config{format}�h�9Returns configuration information for client applications�h ]�h"]�h<ah=h>u�combined_records�}�(h�#{base_url}/combined/records{format}�h�&Returns multiple types of data records�h ]�h"]�(h$h%h&h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<eh=h>u�
combined_auto�}�(h� {base_url}/combined/auto{format}�h�8Auto-completes a partial name across multiple data types�h ]�hvah"]�(h;h<eh=h>u�people_list�}�(h�{base_url}/people/list{format}�h�'Returns a list of database contributors�h ]�h"]�(h;h<eh=h>u�
people_single�}�(h� {base_url}/people/single{format}�h�7Returns information about a single database contributor�h ]�h&ah"]�(h;h<eh=h>u�
stats_summary�}�(h� {base_url}/stats/summary{format}�h�-Returns summary statistics about the database�h ]�h"]�h<ah=h>u�
stats_taxa�}�(h�{base_url}/stats/taxa{format}�h�+Returns statistics about taxonomic coverage�h ]�h"]�(hwh<eh=h>uu�parameter_details�}�(h$}�(h�Base taxonomic name to match��format��String�uh%}�(h�Taxonomic name to match�j'  j(  uhv}�(h�$Name to match (depending on context)�j'  j(  uh&}�(h�$Record identifier (context-specific)�j'  �Number or String�uh'}�(h�Collection identifier�j'  �Number�uhc}�(h�Specimen identifier�j'  j2  uh�}�(h�Reference identifier�j'  j2  uh�}�(h�Publication identifier�j'  j(  uh(}�(h�Geological time interval name�j'  j(  uh)}�(h� Maximum geological time interval�j'  j(  uh*}�(h� Minimum geological time interval�j'  j(  uh+}�(h�Taxonomic level to return�j'  j(  uh,}�(h�Country name�j'  j(  uh-}�(h�Continent name�j'  j(  uh.}�(h�State/province name�j'  j(  uh/}�(h�County name�j'  j(  uh0}�(h�Formation name�j'  j(  uh1}�(h�Member name�j'  j(  uh2}�(h�
Group name�j'  j(  uh3}�(h�Lithology description�j'  j(  uh4}�(h�Environmental setting�j'  j(  uh5}�(h�,Bounding box in format 'lng1,lat1,lng2,lat2'�j'  j(  uh6}�(h�"Location point in format 'lng,lat'�j'  j(  uh7}�(h�Minimum longitude�j'  j2  uh8}�(h�Maximum longitude�j'  j2  uh9}�(h�Minimum latitude�j'  j2  uh:}�(h�Maximum latitude�j'  j2  uhw}�(h�Taxonomic rank�j'  j(  uh�}�(h�Time scale identifier�j'  j(  uh�}�(h� Minimum age in millions of years�j'  j2  uh�}�(h� Maximum age in millions of years�j'  j2  uh�}�(h� Author name for reference search�j'  j(  uh�}�(h�%Publication year for reference search�j'  j2  uh�}�(h�Title for reference search�j'  j(  uh�}�(h�3Stratum type (e.g., 'formation', 'member', 'group')�j'  j(  uh;}�(h�4Comma-separated list of additional fields to include�j'  j(  uh<}�(h�(Response vocabulary (pbdb, com, bibjson)�j'  j(  uuu.