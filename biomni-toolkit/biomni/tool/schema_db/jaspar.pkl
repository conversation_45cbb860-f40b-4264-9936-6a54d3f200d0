��'
      }�(�metadata�}�(�name��JASPAR REST API��version��v1��base_url��https://jaspar.elixir.no/api/v1��description��RA RESTful API to programmatically access the latest version of the JASPAR database�u�
categories�}�(�collections�}�(h	�JASPAR collections��	endpoints�]�(}�(�path��
/collections/��method��GET�h	�,List all the collections available in JASPAR�u}�(h�/collections/{collection}/�hhh	�>Returns a list of all matrix profiles based on collection name��
parameters�]�}�(h�
collection��in�hh	�Name of the collection��required��uaueu�infer�}�(h	�,Infer matrix profiles from protein sequences�h]�}�(h�/infer/{sequence}/�hhh	�/Infer matrix profiles, given a protein sequence�h]�}�(h�sequence�hhh	�Protein sequence�h!�uauau�matrix�}�(h	�Matrix profiles in JASPAR�h]�(}�(h�/matrix/�hhh	�%Returns a list of all matrix profiles�u}�(h�/matrix/{base_id}/versions/�hhh	�-List matrix profile versions based on base_id�h]�}�(h�base_id�hhh	�Base ID of the matrix�h!�uau}�(h�/matrix/{matrix_id}/�hhh	�Gets profile detail information�h]�}�(h�	matrix_id�hhh	� ID of the matrix (e.g. MA0001.1)�h!�uaueu�releases�}�(h	�JASPAR database releases�h]�(}�(h�
/releases/�hhh	�'Returns all releases of JASPAR database�u}�(h�/releases/{release_number}/�hhh	�7Gets JASPAR release information based on release number�h]�}�(h�release_number�hhh	�Release number�h!�uaueu�sites�}�(h	�Matrix profile sites�h]�}�(h�/sites/{matrix_id}/�hhh	�,List matrix profile sites based on matrix_id�h]�}�(hh@hhh	hAh!�uauau�species�}�(h	�Species information�h]�(}�(h�	/species/�hhh	�Returns a list of all species�u}�(h�/species/{tax_id}/�hhh	�6Returns a list of all matrix profiles based on species�h]�}�(h�tax_id�hhh	�Taxonomy ID�h!�uaueu�taxon�}�(h	�Taxonomic groups�h]�(}�(h�/taxon/�hhh	�:List all the taxonomic groups that are available in JASPAR�u}�(h�/taxon/{tax_group}/�hhh	�>Returns a list of all matrix profiles based on taxonomic group�h]�}�(h�	tax_group�hhh	�3Taxonomic group (e.g. vertebrates, plants, insects)�h!�uaueu�tffm�}�(h	�+Transcription Factor Flexible Models (TFFM)�h]�(}�(h�/tffm/�hhh	�#Returns a list of all TFFM profiles�u}�(h�/tffm/{tffm_id}/�hhh	�Gets TFFM detail information�h]�}�(h�tffm_id�hhh	�ID of the TFFM (e.g. TF0001.1)�h!�uaueuu�examples�}�(�get_all_matrices�}�(h	�!Get a list of all matrix profiles��url��'https://jaspar.elixir.no/api/v1/matrix/�u�get_matrix_by_id�}�(h	�!Get details for a specific matrix�h��0https://jaspar.elixir.no/api/v1/matrix/MA0002.2/�u�get_matrices_by_species�}�(h	�)Get matrices for human (taxonomy ID 9606)�h��-https://jaspar.elixir.no/api/v1/species/9606/�u�get_matrices_by_collection�}�(h	�%Get matrices from the CORE collection�h��1https://jaspar.elixir.no/api/v1/collections/CORE/�u�get_matrices_by_taxon�}�(h	�Get matrices for vertebrates�h��2https://jaspar.elixir.no/api/v1/taxon/vertebrates/�u�get_matrix_sites�}�(h	�Get sites for a specific matrix�h��/https://jaspar.elixir.no/api/v1/sites/MA0002.2/�u�get_matrix_versions�}�(h	�%Get all versions of a specific matrix�h��7https://jaspar.elixir.no/api/v1/matrix/MA0002/versions/�u�infer_from_sequence�}�(h	�&Infer matrices from a protein sequence�h��[https://jaspar.elixir.no/api/v1/infer/MAASVLCHDDICEDPSVLPCNMTCEHITQPWPVVTGQYRLTQDAYWKQPMDL/�uuu.