���      }�(�base_url��#https://www.ebi.ac.uk/gwas/rest/api��	endpoints�}�(�studies�}�(�description��Information about GWAS studies��search_endpoints�}�(�@findByPublicationIdPubmedId{?pubmedId,page,size,sort,projection}��+Search for a study using pubmedId parameter��*findByAccessionId{?accessionId,projection}��.Search for a study using accessionId parameter��;findByDiseaseTrait{?diseaseTrait,page,size,sort,projection}��ASearch for a study via disease trait using diseaseTrait parameter��3findByEfoTrait{?efoTrait,page,size,sort,projection}��9Search for a study via EFO trait using efoTrait parameter��=findByFullPvalueSet{?fullPvalueSet,page,size,sort,projection}��GSearch for studies with full p-value sets using fullPvalueSet parameter��=findByUserRequested{?userRequested,page,size,sort,projection}��CSearch for studies requested by users using userRequested parameter�u�
parameters�}�(�page��Page number (default: 0)��size��%Number of records per page (max: 500)��sort��Sort field and direction��
projection��Detail level (optional)�uu�associations�}�(h�&Genetic associations from GWAS studies�h	}�(�/findByStudyAccessionId{?accessionId,projection}��,Search for associations by study accessionId��$findByPubmedId{?pubmedId,projection}��*Search for associations via study pubmedId��$findByEfoTrait{?efoTrait,projection}��-Search for associations by efoTrait parameter��findByRsId{?rsId,projection}��#Search for associations by SNP rsId��EfindByRsIdAndAccessionId{?rsId,accessionId,page,size,sort,projection}��9Search for associations by SNP rsId and study accessionId�uh}�(hhhhhhhh uu�singleNucleotidePolymorphisms�}�(h�SNP information�h	}�(h+�$Search for SNPs using rsId parameter��(findByBpLocation{?bpLocation,projection}��%Search for SNPs by base pair location��JfindByChromBpLocationRange{?chrom,bpStart,bpEnd,page,size,sort,projection}��1Search for SNPs on a chromosome within a bp range�h)�,Search for SNPs associated with an EFO trait��3findByPubmedId{?pubmedId,page,size,sort,projection}��3Search for SNPs associated with studies by pubmedId��/findByGene{?geneName,page,size,sort,projection}��Search for SNPs by gene name��ufindIdsByLocationsChromosomeNameAndLocationsChromosomePositionBetween{?chrom,bpStart,bpEnd,page,size,sort,projection}��ASearch for SNPs by chromosome, base pair start, and base pair end��,findByDiseaseTrait{?diseaseTrait,projection}��/Search for SNPs associated with a disease trait�uh}�(hhhhhhhh uu�	efoTraits�}�(h�EFO trait information�h	}�(�,findByEfoUri{?uri,page,size,sort,projection}��Search for EFO traits by URI��&findByShortForm{?shortForm,projection}��9Search for EFO traits by shortForm ID (e.g., EFO_0001060)�h:�9Search for EFO traits associated with studies by pubmedId��0findByEfoTrait{?trait,page,size,sort,projection}��Search for EFO traits by name�uh}�(hhhhhhhh uuu�example_queries�]�(}�(�question��'Find studies related to Type 2 Diabetes��endpoint��studies/search/findByEfoTrait��params�}��efoTrait��type 2 diabetes�su}�(hR�#Get information about SNP rs7329174�hT�'singleNucleotidePolymorphisms/rs7329174�hV}�u}�(hR�#Find associations for the APOE gene�hT�/singleNucleotidePolymorphisms/search/findByGene�hV}��geneName��APOE�su}�(hR�$Get information about Celiac Disease�hT�efoTraits/EFO_0001060�hV}�ueu.