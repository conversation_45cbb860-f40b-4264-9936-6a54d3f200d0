��k
      }�(�	base_urls�}�(�default��https://string-db.org/api��stable��&https://version-12-0.string-db.org/api�u�output_formats�}�(�text�]�(�tsv��
tsv-no-header��json��xml��psi-mi��
psi-mi-tab�e�image�]�(h�
highres_image��svg�eu�	endpoints�}�(�get_string_ids�}�(�url��){base_url}/{output_format}/get_string_ids��description��SMaps common protein names, synonyms and UniProt identifiers into STRING identifiers��required�]��identifiers�a�optional�]�(�species��
echo_query��limit��caller_identity�eh]�(hhh
heu�
network_image�}�(h�"{base_url}/{output_format}/network�h�DRetrieves the network image with input proteins highlighted in color�h]�hah ]�(h"�add_color_nodes��add_white_nodes��required_score��network_type��network_flavor��hide_node_labels��hide_disconnected_nodes��show_query_node_labels��block_structure_pics_in_bubbles��flat_node_design��center_node_labels��custom_label_font_size�h%eh]�(hhheu�get_link�}�(h�#{base_url}/{output_format}/get_link�h�2Get the link to STRING webpage showing the network�h]�hah ]�(h"h-h.h/h1h0h2h3h4h5h%eh]�(hhh
heu�network_interactions�}�(hh)h�ERetrieves the network interactions for input proteins in text formats�h]�hah ]�(h"h/h0�	add_nodes�h4h%eh]�(hhh
hhheu�interaction_partners�}�(h�/{base_url}/{output_format}/interaction_partners�h�4Gets all the STRING interaction partners of proteins�h]�hah ]�(h"h$h/h0h%eh]�(hhh
hhheu�homology�}�(h�#{base_url}/{output_format}/homology�h�=Retrieve the protein similarity scores between input proteins�h]�hah ]�(h"h%eh]�(hhh
heu�
homology_best�}�(h�({base_url}/{output_format}/homology_best�h�LRetrieve the similarity to the best similar protein from each STRING species�h]�hah ]�(h"�	species_b�h%eh]�(hhh
heu�
enrichment�}�(h�%{base_url}/{output_format}/enrichment�h�GPerforms the enrichment analysis of protein set for various annotations�h]�hah ]�(h"�background_string_identifiers�h%eh]�(hhh
heu�functional_annotation�}�(h�0{base_url}/{output_format}/functional_annotation�h�.Gets the functional annotation of protein list�h]�hah ]�(h"�allow_pubmed��only_pubmed�h%eh]�(hhh
heu�enrichmentfigure�}�(h�+{base_url}/{output_format}/enrichmentfigure�h�7Generates the enrichment figure for a specific category�h]�(hh"eh ]�(�category��group_by_similarity��
color_palette��number_of_term_shown��x_axis�h%eh]�(hhheu�ppi_enrichment�}�(h�){base_url}/{output_format}/ppi_enrichment�h�4Tests if network has more interactions than expected�h]�hah ]�(h"h/hdh%eh]�(hhh
heu�version�}�(h�"{base_url}/{output_format}/version�h�8Prints the current STRING version and its stable address�h]�h ]�h%ah]�(hhh
heu�search_results�}�(h�!https://string-db.org/cgi/network�h�*Create the link to the search results page�h]�hah ]�(h"h-h.h/h1h0h2h3h4h5h%eh]��html�auu�parameter_details�}�(h}�(h�0Protein identifiers separated by carriage return��format��.String with identifiers separated by \r or %0d��example��,DRD1_HUMAN\rDRD2_HUMAN or PTCH1%0dSHH%0dGLI1�uh"}�(h�NCBI/STRING taxon ID�h��Integer�h��,9606 (human), 4932 (yeast), 7227 (fruit fly)�uh/}�(h�*Threshold of significance for interactions�h��Integer between 0 and 1000�h�Depends on network�uh0}�(h�Type of network to retrieve�h��String��values�]�(�
functional��physical�ehh�uh1}�(h�Style of edges in the network�h�h�h�]�(�evidence��
confidence��actions�ehh�uuu.