���      }�(�base_url��https://www.ebi.ac.uk��	endpoints�}�(�entry�}�(�url��{base_url}/emdb/api/entry/{id}��description��EMDB entry information��required�]��id�a�optional�]��method��GET�u�entry_admin�}�(h�${base_url}/emdb/api/entry/admin/{id}�h	�EMDB entry admin information�h]�h
ah]�hhu�entry_publications�}�(h�+{base_url}/emdb/api/entry/publications/{id}�h	�#EMDB entry publications information�h]�h
ah]�hhu�	entry_map�}�(h�"{base_url}/emdb/api/entry/map/{id}�h	�EMDB entry map information�h]�h
ah]�hhu�entry_supplement�}�(h�){base_url}/emdb/api/entry/supplement/{id}�h	�!EMDB entry supplement information�h]�h
ah]�hhu�entry_sample�}�(h�%{base_url}/emdb/api/entry/sample/{id}�h	�EMDB entry sample information�h]�h
ah]�hhu�entry_vitrification�}�(h�,{base_url}/emdb/api/entry/vitrification/{id}�h	�$EMDB entry vitrification information�h]�h
ah]�hhu�
entry_imaging�}�(h�&{base_url}/emdb/api/entry/imaging/{id}�h	�EMDB entry imaging information�h]�h
ah]�hhu�entry_image_acquisition�}�(h�0{base_url}/emdb/api/entry/image_acquisition/{id}�h	�(EMDB entry image acquisition information�h]�h
ah]�hhu�entry_processing�}�(h�){base_url}/emdb/api/entry/processing/{id}�h	�!EMDB entry processing information�h]�h
ah]�hhu�entry_experiment�}�(h�){base_url}/emdb/api/entry/experiment/{id}�h	�!EMDB entry experiment information�h]�h
ah]�hhu�entry_fitted�}�(h�%{base_url}/emdb/api/entry/fitted/{id}�h	�#EMDB entry fitted model information�h]�h
ah]�hhu�analysis�}�(h�!{base_url}/emdb/api/analysis/{id}�h	�EMDB validation analysis�h]�h
ah]�hhu�search�}�(h�"{base_url}/emdb/api/search/{query}�h	�EMDB search�h]��query�ah]�hhu�facet�}�(h�!{base_url}/emdb/api/facet/{query}�h	�
EMDB facet�h]�h_ah]�hhu�yearly�}�(h�"{base_url}/emdb/api/yearly/{query}�h	�EMDB Yearly facet�h]�h_ah]�hhu�
empiar_search�}�(h�){base_url}/emdb/api/empiar/search/{query}�h	�
EMPIAR search�h]�h_ah]�hhu�empiar_facet�}�(h�({base_url}/emdb/api/empiar/facet/{query}�h	�EMPIAR facet�h]�h_ah]�hhu�
empiar_yearly�}�(h�){base_url}/emdb/api/empiar/yearly/{query}�h	�EMPIAR Yearly facet�h]�h_ah]�hhu�annotations�}�(h�${base_url}/emdb/api/annotations/{id}�h	�EMDB annotations�h]�h
ah]�hhuuu.