���      }�(�base_url��"https://www.marinespecies.org/rest��	endpoints�}�(�attribute_keys_by_id�}�(�url��&{base_url}/AphiaAttributeKeysByID/{ID}��description��Get attribute definitions��required�]��ID�a�optional�]��method��GET�u�attributes_by_aphia_id�}�(h�({base_url}/AphiaAttributesByAphiaID/{ID}�h	�,Get a list of attributes for a given AphiaID�h]�h
ah]�hhu�attribute_values_by_category_id�}�(h�0{base_url}/AphiaAttributeValuesByCategoryID/{ID}�h	�1Get list values that are grouped by an CateogryID�h]�h
ah]�hhu�aphia_ids_by_attribute_key_id�}�(h�({base_url}/AphiaIDsByAttributeKeyID/{ID}�h	�XGet a list of AphiaID's (max 50) with attribute tree for a given attribute definition ID�h]�h
ah]�hhu�distributions_by_aphia_id�}�(h�+{base_url}/AphiaDistributionsByAphiaID/{ID}�h	�)Get all distributions for a given AphiaID�h]�h
ah]�hhu�external_id_by_aphia_id�}�(h�({base_url}/AphiaExternalIDByAphiaID/{ID}�h	�2Get any external identifier(s) for a given AphiaID�h]�h
ah]�hhu�aphia_record_by_external_id�}�(h�'{base_url}/AphiaRecordByExternalID/{ID}�h	�4Get the Aphia Record for a given external identifier�h]�h
ah]�hhu�sources_by_aphia_id�}�(h�%{base_url}/AphiaSourcesByAphiaID/{ID}�h	�CGet one or more sources/references including links, for one AphiaID�h]�h
ah]�hhu�children_by_aphia_id�}�(h�&{base_url}/AphiaChildrenByAphiaID/{ID}�h	�5Get the direct children (max. 50) for a given AphiaID�h]�h
ah]�hhu�classification_by_aphia_id�}�(h�,{base_url}/AphiaClassificationByAphiaID/{ID}�h	�YGet the complete classification for one taxon. This also includes any sub or super ranks.�h]�h
ah]�hhu�aphia_id_by_name�}�(h�){base_url}/AphiaIDByName/{ScientificName}�h	�!Get the AphiaID for a given name.�h]��ScientificName�ah]�hhu�name_by_aphia_id�}�(h�"{base_url}/AphiaNameByAphiaID/{ID}�h	� Get the name for a given AphiaID�h]�h
ah]�hhu�record_by_aphia_id�}�(h�${base_url}/AphiaRecordByAphiaID/{ID}�h	�0Get the complete AphiaRecord for a given AphiaID�h]�h
ah]�hhu�record_full_by_aphia_id�}�(h�({base_url}/AphiaRecordFullByAphiaID/{ID}�h	�4Get the complete AphiaFullRecord for a given AphiaID�h]�h
ah]�hhu�records_by_aphia_ids�}�(h�!{base_url}/AphiaRecordsByAphiaIDs�h	�<Get an AphiaRecord for multiple AphiaIDs in one go (max: 50)�h]�h]��aphiaids�ahhu�records_by_date�}�(h�{base_url}/AphiaRecordsByDate�h	�nLists all AphiaRecords (taxa) that have their last edit action (modified or added) during the specified period�h]�h]�(�	startdate��enddate��marine_only��offset�ehhu�records_by_match_names�}�(h�#{base_url}/AphiaRecordsByMatchNames�h	�RTry to find AphiaRecords using the TAXAMATCH fuzzy matching algorithm by Tony Rees�h]�h]�(�scientificnames�hpehhu�records_by_name�}�(h�.{base_url}/AphiaRecordsByName/{ScientificName}�h	�@Get one or more matching (max. 50) AphiaRecords for a given name�h]�hMah]�(�like��fuzzy�hphqehhu�records_by_names�}�(h�{base_url}/AphiaRecordsByNames�h	�DFor each given scientific name, try to find one or more AphiaRecords�h]�h]�(hxhh�hpehhu�records_by_taxon_rank_id�}�(h�){base_url}/AphiaRecordsByTaxonRankID/{ID}�h	�5Get the AphiaRecords for a given taxonRankID (max 50)�h]�h
ah]�hqahhu�synonyms_by_aphia_id�}�(h�&{base_url}/AphiaSynonymsByAphiaID/{ID}�h	�$Get all synonyms for a given AphiaID�h]�h
ah]�hqahhu�taxon_ranks_by_id�}�(h�#{base_url}/AphiaTaxonRanksByID/{ID}�h	�'Get taxonomic ranks by their identifier�h]�h
ah]�hhu�taxon_ranks_by_name�}�(h�,{base_url}/AphiaTaxonRanksByName/{taxonRank}�h	�!Get taxonomic ranks by their name�h]��	taxonRank�ah]�hhu�records_by_vernacular�}�(h�0{base_url}/AphiaRecordsByVernacular/{Vernacular}�h	�>Get one or more Aphia Records (max. 50) for a given vernacular�h]��
Vernacular�ah]�(hhqehhu�vernaculars_by_aphia_id�}�(h�){base_url}/AphiaVernacularsByAphiaID/{ID}�h	�'Get all vernaculars for a given AphiaID�h]�h
ah]�hhuu�parameter_details�}�(h
}�(h	�-Numeric identifier for various WoRMS entities��format��Integer�uhM}�(h	�Scientific name of a taxon�h��String�uh�}�(h	�Taxonomic rank name�h�h�uh�}�(h	�!Common/vernacular name of a taxon�h�h�uhg}�(h	�$Multiple AphiaIDs separated by comma�h��!String (comma-separated integers)�uhx}�(h	�/Multiple scientific names separated by pipe (|)�h��String (pipe-separated names)�uhn}�(h	�4Start date for record filtering (format: yyyy-mm-dd)�h��
String (date)�uho}�(h	�2End date for record filtering (format: yyyy-mm-dd)�h�h�uhp}�(h	�,Flag to restrict results to marine taxa only�h��Boolean (true/false)�uhq}�(h	�%Starting record number for pagination�h�h�uh}�(h	�7Flag to use SQL LIKE matching instead of exact matching�h�h�uh�}�(h	�%Flag to use fuzzy matching algorithms�h�h�uuu.