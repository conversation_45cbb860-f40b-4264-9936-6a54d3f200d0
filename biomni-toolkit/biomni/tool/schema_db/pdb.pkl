���
      }�(�search_services�}�(�text��pPerforms attribute searches against specific textual fields. Requires attribute, operator, and value parameters.��	full_text��PPerforms searches across multiple textual fields. Only requires value parameter.��sequence��<Searches for protein or nucleic acid sequences (BLAST-like).��	structure��CSearches for 3D structural similarity using BioZernike descriptors.��seqmotif��ESearches for sequence motifs using simple, prosite or regex patterns.��chemical��ASearches for chemical structures using SMILES, InChI, or formula.�u�return_types�}�(�entry��Returns PDB IDs (e.g., 4HHB)��assembly��,Returns biological assemblies (e.g., 4HHB-1)��polymer_entity��'Returns polymer entities (e.g., 4HHB_1)��non_polymer_entity��3Returns non-polymer entities/ligands (e.g., 4HHB_1)��polymer_instance��Returns chains (e.g., 4HHB.A)��mol_definition��,Returns molecular definition IDs (e.g., ATP)�u�common_attributes�}�(�#rcsb_entry_info.resolution_combined��Resolution in Angstroms��rcsb_primary_citation.title��Title of the primary citation��#rcsb_entry_info.experimental_method��Experimental method used��0rcsb_entity_source_organism.rcsb_gene_name.value��	Gene name��+rcsb_entity_source_organism.scientific_name��"Scientific name of source organism��&rcsb_polymer_entity.rcsb_ec_lineage.id��Enzyme classification ID��[rcsb_polymer_entity_container_identifiers.reference_sequence_identifiers.database_accession��
UniProt ID��;rcsb_polymer_entity.rcsb_macromolecular_names_combined.name��Macromolecule name��)rcsb_entry_container_identifiers.entry_id��PDB/Entry ID��(rcsb_entry_container_identifiers.rcsb_id��RCSB ID�u�	operators�}�(�exact_match��(Exact match, case-insensitive by default��in��'Match any value in list, case-sensitive��contains_words��%Full-text search for any of the words��contains_phrase��%Full-text search for the exact phrase��greater��Greater than (numeric/date)��less��Less than (numeric/date)��greater_or_equal��'Greater than or equal to (numeric/date)��
less_or_equal��$Less than or equal to (numeric/date)��range��)Between min and max values (numeric/date)��exists��Field exists and is not null�u�example_queries�}�(�
basic_keyword�}�(�query�}�(�type��terminal��service�h�
parameters�}��value��thymidine kinase�su�return_type�hu�attribute_search�}�(hM}�(hOhPhQhhR}�(�	attribute�h%�operator�h5hT�BRCA1�uuhVhu�resolution_filter�}�(hM}�(hOhPhQhhR}�(h[hh\hChTG@       uuhVhu�combined_search�}�(hM}�(hO�group��logical_operator��and��nodes�]�(}�(hOhPhQhhR}�(h[�?rcsb_entity_source_organism.rcsb_organism_scientific_name.value�h\h5hT�Homo sapiens�uu}�(hOhPhQhhR}�(h[hh\hChTG@       uueuhVhuuu.