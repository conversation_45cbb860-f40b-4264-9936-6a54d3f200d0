���W      X�W  "This element represents the tag variant with its associated statistics"
    type CredSetTagElement {
    "Tag Variant in the credibleset table"
    tagVariant: Variant!

    "p-val"
    pval: Float!

    "SE"
    se: Float!

    "beta"
    beta: Float!

    "Posterior Probability"
    postProb: Float!

    "Multisignal Method"
    MultisignalMethod: String!

    "Log ABF"
    logABF: Float!

    "Is over 95 percentile"
    is95: Boolean!

    "Is over 99 percentile"
    is99: Boolean!
    }

    "A list of rows with each link"
    type DistanceElement {
    typeId: String!
    sourceId: String!
    aggregatedScore: Float!
    tissues: [DistanceTissue!]!
    }

    type DistanceTissue {
    tissue: Tissue!
    "Distance to the canonical TSS"
    distance: Long

    "Score 1 / Distance"
    score: Float

    "Quantile of the score"
    quantile: Float
    }

    type FPredTissue {
    tissue: Tissue!
    maxEffectLabel: String
    maxEffectScore: Float
    }

    "A list of rows with each link"
    type FunctionalPredictionElement {
    typeId: String!
    sourceId: String!
    aggregatedScore: Float!
    tissues: [FPredTissue!]!
    }

    "A list of rows with each link"
    type G2VSchema {
    "qtl structure definition"
    qtls: [G2VSchemaElement!]!

    "qtl structure definition"
    intervals: [G2VSchemaElement!]!

    "qtl structure definition"
    functionalPredictions: [G2VSchemaElement!]!

    "Distance structure definition"
    distances: [G2VSchemaElement!]!
    }

    "A list of rows with each link"
    type G2VSchemaElement {
    id: String!
    sourceId: String!
    sourceLabel: String
    sourceDescriptionOverview: String
    sourceDescriptionBreakdown: String

    "PubmedID"
    pmid: String
    tissues: [Tissue!]!
    }

    type GWASColocalisation {
    "Tag variant ID as ex. 1_12345_A_T"
    indexVariant: Variant!

    "study ID"
    study: Study!

    "Beta"
    beta: Float

    "H3"
    h3: Float!

    "H4"
    h4: Float!

    "Log2 H4/H3"
    log2h4h3: Float!
    }

    type GWASColocalisationForQTLWithGene {
    "Tag variant ID as ex. 1_12345_A_T"
    leftVariant: Variant!

    "GWAS Study"
    study: Study!

    "QTL study ID"
    qtlStudyId: String!

    "Phenotype ID"
    phenotypeId: String!

    "QTL bio-feature"
    tissue: Tissue!

    "H3"
    h3: Float!

    "H4"
    h4: Float!

    "Log2 H4/H3"
    log2h4h3: Float!
    }

    type GWASLRColocalisation {
    "Tag variant ID as ex. 1_12345_A_T"
    leftVariant: Variant!

    "study ID"
    leftStudy: Study!

    "Tag variant ID as ex. 1_12345_A_T"
    rightVariant: Variant!

    "study ID"
    rightStudy: Study!

    "H3"
    h3: Float!

    "H4"
    h4: Float!

    "Log2 H4/H3"
    log2h4h3: Float!
    }

    type Gecko {
    genes: [Gene!]!
    tagVariants: [Variant!]!
    indexVariants: [Variant!]!
    studies: [Study!]!
    geneTagVariants: [GeneTagVariant!]!
    tagVariantIndexVariantStudies: [TagVariantIndexVariantStudy!]!
    }

    type Gene {
    id: String!
    symbol: String
    bioType: String
    description: String
    chromosome: String
    tss: Long
    start: Long
    end: Long
    fwdStrand: Boolean
    exons: [Long!]!
    }

    "A list of rows with each link"
    type GeneForVariant {
    "Associated scored gene"
    gene: Gene!

    "Associated scored variant"
    variant: String!
    overallScore: Float!
    qtls: [QTLElement!]!
    intervals: [IntervalElement!]!
    functionalPredictions: [FunctionalPredictionElement!]!
    distances: [DistanceElement!]!
    }

    type GeneTagVariant {
    geneId: String!
    tagVariantId: String!
    overallScore: Float!
    }

    "This object represent a link between a triple (study, trait, index_variant) and a tag variant via an expansion method (either ldExpansion or FineMapping)"
    type IndexVariantAssociation {
    "Tag variant ID as ex. 1_12345_A_T"
    tagVariant: Variant!

    "study ID"
    study: Study!

    "p-val between a study and an the provided index variant"
    pval: Float!

    "p-val between a study and an the provided index variant"
    pvalMantissa: Float!

    "p-val between a study and an the provided index variant"
    pvalExponent: Long!

    "n total cases (n initial + n replication)"
    nTotal: Long!

    "n cases"
    nCases: Long!

    "study ID"
    overallR2: Float
    afr1000GProp: Float
    amr1000GProp: Float
    eas1000GProp: Float
    eur1000GProp: Float
    sas1000GProp: Float
    log10Abf: Float
    posteriorProbability: Float
    oddsRatio: Float
    oddsRatioCILower: Float
    oddsRatioCIUpper: Float
    beta: Float
    betaCILower: Float
    betaCIUpper: Float
    direction: String
    }

    "A list of rows with each link"
    type IndexVariantsAndStudiesForTagVariant {
    "A list of associations connected to a Index variant and a Study through some expansion methods"
    associations: [TagVariantAssociation!]!
    }

    "A list of rows with each link"
    type IntervalElement {
    typeId: String!
    sourceId: String!
    aggregatedScore: Float!
    tissues: [IntervalTissue!]!
    }

    type IntervalTissue {
    tissue: Tissue!
    quantile: Float!
    score: Float
    }

    "This element represents a Manhattan like plot"
    type Manhattan {
    "A list of associations"
    associations: [ManhattanAssociation!]!

    "A list of overlapped studies"
    topOverlappedStudies(
        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): TopOverlappedStudies
    }

    type ManhattanAssociation {
    pvalMantissa: Float!
    pvalExponent: Long!

    "The cardinal of the set defined as tag variants for an index variant coming from crediblesets"
    credibleSetSize: Long

    "The cardinal of the set defined as tag variants for an index variant coming from ld expansion"
    ldSetSize: Long

    "The cardinal of the set defined as tag variants for an index variant coming from any expansion"
    totalSetSize: Long!

    "Index variant"
    variant: Variant!

    "Computed p-Value"
    pval: Float!
    oddsRatio: Float
    oddsRatioCILower: Float
    oddsRatioCIUpper: Float
    beta: Float
    betaCILower: Float
    betaCIUpper: Float
    direction: String

    "A list of best genes associated"
    bestGenes: [ScoredGene!]!

    "A list of best genes associated"
    bestColocGenes: [ScoredGene!]!

    "A list of best L2G scored genes associated"
    bestLocus2Genes: [ScoredGene!]!
    }

    type Metadata {
    name: String!
    apiVersion: Version!
    dataVersion: Version!
    }

    "This element represents an overlap between two variants for two studies"
    type Overlap {
    variantIdA: String!
    variantIdB: String!
    overlapAB: Long!
    distinctA: Long!
    distinctB: Long!
    }

    type OverlappedInfoForStudy {
    "A study object"
    study: Study
    overlappedVariantsForStudies: [OverlappedVariantsStudies!]!
    variantIntersectionSet: [String!]!
    }

    "This element represent a overlap between two stduies"
    type OverlappedStudy {
    "A study object"
    studyId: String!

    "A study object"
    study: Study

    "Orig variant id which is been used for computing the overlap with the referenced study"
    numOverlapLoci: Int!
    }

    "This element represents an overlap between two studies"
    type OverlappedVariantsStudies {
    "Orig variant id which is been used for computing the overlap with the referenced study"
    overlaps: [Overlap!]!

    "A study object"
    study: Study
    }

    input Pagination {
    index: Int!
    size: Int!
    }

    "This element represents a PheWAS like plot"
    type PheWAS {
    "A total number of unique GWAS studies in the summary stats table"
    totalGWASStudies: Long!

    "A list of associations"
    associations: [PheWASAssociation!]!
    }

    "This element represents an association between a variant and a reported trait through a study"
    type PheWASAssociation {
    studyId: String!

    "Effect Allele Frequency"
    eaf: Float
    beta: Float

    "Standard error"
    se: Float

    "Computed p-Value"
    pval: Float!

    "Total sample size (variant level)"
    nTotal: Long
    nCases: Long

    "Study Object"
    study: Study

    "Odds ratio (if case control)"
    oddsRatio: Float
    }

    type QTLColocalisation {
    "Tag variant ID as ex. 1_12345_A_T"
    indexVariant: Variant!

    "Gene"
    gene: Gene!

    "QTL Phenotype ID"
    phenotypeId: String!

    "QTL bio-feature"
    tissue: Tissue!

    "QTL study ID"
    qtlStudyName: String!

    "Beta"
    beta: Float

    "H3"
    h3: Float!

    "H4"
    h4: Float!

    "Log2 H4/H3"
    log2h4h3: Float!
    }

    "A list of rows with each link"
    type QTLElement {
    typeId: String!
    sourceId: String!
    aggregatedScore: Float!
    tissues: [QTLTissue!]!
    }

    type QTLTissue {
    tissue: Tissue!
    quantile: Float!
    beta: Float
    pval: Float
    }

    type Query {
    "Return Open Targets Genetics API metadata"
    meta: Metadata!
    search(
        "Query text to search for"
        queryString: String!, page: Pagination): SearchResult!
    genes(
        "Chromosome as String between 1..22 or X, Y, MT"
        chromosome: String!,

        "Start position in a specified chromosome"
        start: Long!,

        "End position in a specified chromosome"
        end: Long!): [Gene!]!
    geneInfo(
        "Gene ID using Ensembl identifier"
        geneId: String!): Gene
    studyInfo(
        "Study ID which links a top loci with a trait"
        studyId: String!): Study
    variantInfo(
        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!): Variant
    studiesForGene(
        "Gene ID using Ensembl identifier"
        geneId: String!): [StudyForGene!]!
    studyLocus2GeneTable(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!,

        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): SLGTable!
    manhattan(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): Manhattan!
    topOverlappedStudies(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): TopOverlappedStudies!
    overlapInfoForStudy(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "List of study IDs"
        studyIds: [String!]!): OverlappedInfoForStudy!
    tagVariantsAndStudiesForIndexVariant(
        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!,

        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): TagVariantsAndStudiesForIndexVariant!
    indexVariantsAndStudiesForTagVariant(
        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!,

        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): IndexVariantsAndStudiesForTagVariant!
    pheWAS(
        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!,

        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): PheWAS!
    gecko(
        "Chromosome as String between 1..22 or X, Y, MT"
        chromosome: String!,

        "Start position in a specified chromosome"
        start: Long!,

        "End position in a specified chromosome"
        end: Long!): Gecko
    regionPlot(
        "Study ID which links a top loci with a trait"
        optionalStudyId: String,

        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        optionalVariantId: String,

        "Gene ID using Ensembl identifier"
        optionalGeneId: String): Gecko
    genesForVariantSchema: G2VSchema!
    genesForVariant(
        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!): [GeneForVariant!]!
    gwasRegional(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "Chromosome as String between 1..22 or X, Y, MT"
        chromosome: String!,

        "Start position in a specified chromosome"
        start: Long!,

        "End position in a specified chromosome"
        end: Long!): [RegionalAssociation!]!
    qtlRegional(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "BioFeature represents either a tissue, cell type, aggregation type, ..."
        bioFeature: String!,

        "Gene ID using Ensembl identifier"
        geneId: String!,

        "Chromosome as String between 1..22 or X, Y, MT"
        chromosome: String!,

        "Start position in a specified chromosome"
        start: Long!,

        "End position in a specified chromosome"
        end: Long!): [RegionalAssociation!]!
    studyAndLeadVariantInfo(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!): StudiesAndLeadVariantsForGene
    gwasCredibleSet(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!): [CredSetTagElement!]!
    qtlCredibleSet(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!,

        "Gene ID using Ensembl identifier"
        geneId: String!,

        "BioFeature represents either a tissue, cell type, aggregation type, ..."
        bioFeature: String!): [CredSetTagElement!]!
    colocalisationsForGene(
        "Gene ID using Ensembl identifier"
        geneId: String!): [GWASColocalisationForQTLWithGene!]!
    gwasColocalisationForRegion(
        "Chromosome as String between 1..22 or X, Y, MT"
        chromosome: String!,

        "Start position in a specified chromosome"
        start: Long!,

        "End position in a specified chromosome"
        end: Long!): [GWASLRColocalisation!]!
    gwasColocalisation(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!): [GWASColocalisation!]!
    qtlColocalisation(
        "Study ID which links a top loci with a trait"
        studyId: String!,

        "Variant ID formated as CHR_POSITION_REFALLELE_ALT_ALLELE"
        variantId: String!): [QTLColocalisation!]!
    studiesAndLeadVariantsForGene(
        "Gene ID using Ensembl identifier"
        geneId: String!): [StudiesAndLeadVariantsForGene!]!
    studiesAndLeadVariantsForGeneByL2G(
        "Gene ID using Ensembl identifier"
        geneId: String!,

        "pagination index >= 0"
        pageIndex: Int,

        "pagination size > 0"
        pageSize: Int): [V2DL2GRowByGene!]!
    }

    "Variant with a p-val"
    type RegionalAssociation {
    "Summary Stats simple variant information"
    variant: Variant!

    "p-val"
    pval: Float!
    }

    type SLGRow {
    yProbaDistance: Float!
    yProbaInteraction: Float!
    yProbaMolecularQTL: Float!
    yProbaPathogenicity: Float!
    yProbaModel: Float!
    hasColoc: Boolean!
    distanceToLocus: Long!

    "Gene"
    gene: Gene!
    }

    type SLGTable {
    rows: [SLGRow!]!

    "Study"
    study: Study

    "Variant"
    variant: Variant
    }

    "This object link a Gene with a score"
    type ScoredGene {
    "Gene Info"
    gene: Gene!

    "Score a Float number between [0. .. 1.]"
    score: Float!
    }

    "Search data by a query string"
    type SearchResult {
    "Total number of genes found"
    totalGenes: Long!

    "Total number of variants found"
    totalVariants: Long!

    "Total number of studies found"
    totalStudies: Long!

    "Gene search result list"
    genes: [Gene!]!

    "Variant search result list"
    variants: [Variant!]!

    "Study search result list"
    studies: [Study!]!
    }

    "A list of Studies and Lead Variants for a Gene"
    type StudiesAndLeadVariantsForGene {
    "Tag variant ID as ex. 1_12345_A_T"
    indexVariant: Variant!

    "study ID"
    study: Study!

    "p-val between a study and an the provided index variant"
    pval: Float!

    "p-val between a study and an the provided index variant"
    pvalMantissa: Float!

    "p-val between a study and an the provided index variant"
    pvalExponent: Long!
    oddsRatio: Float
    oddsRatioCILower: Float
    oddsRatioCIUpper: Float
    beta: Float
    betaCILower: Float
    betaCIUpper: Float
    direction: String
    }

    type Study {
    studyId: String!

    "Trait Label as reported on the publication"
    traitReported: String!

    "Database or BioBank providing the study"
    source: String

    "A list of curated efo codes"
    traitEfos: [String!]!

    "PubMed ID for the corresponding publication"
    pmid: String

    "Publication Date as YYYY-MM-DD"
    pubDate: String

    "Publication Journal name"
    pubJournal: String
    pubTitle: String
    pubAuthor: String

    "Contains summary statistical information"
    hasSumstats: Boolean
    ancestryInitial: [String!]!
    ancestryReplication: [String!]!
    nInitial: Long
    nReplication: Long
    nCases: Long
    traitCategory: String
    numAssocLoci: Long

    "n total cases (n initial + n replication)"
    nTotal: Long!
    }

    type StudyForGene {
    "A study object"
    study: Study!
    }

    "This object represent a link between a triple (study, trait, index_variant) and a tag variant via an expansion method (either ldExpansion or FineMapping)"
    type TagVariantAssociation {
    "Tag variant ID as ex. 1_12345_A_T"
    indexVariant: Variant!

    "study ID"
    study: Study!

    "p-val between a study and an the provided index variant"
    pval: Float!

    "p-val between a study and an the provided index variant"
    pvalMantissa: Float!

    "p-val between a study and an the provided index variant"
    pvalExponent: Long!

    "n total cases (n initial + n replication)"
    nTotal: Long!

    "n cases"
    nCases: Long!

    "study ID"
    overallR2: Float
    afr1000GProp: Float
    amr1000GProp: Float
    eas1000GProp: Float
    eur1000GProp: Float
    sas1000GProp: Float
    log10Abf: Float
    posteriorProbability: Float
    oddsRatio: Float
    oddsRatioCILower: Float
    oddsRatioCIUpper: Float
    beta: Float
    betaCILower: Float
    betaCIUpper: Float
    direction: String
    }

    type TagVariantIndexVariantStudy {
    tagVariantId: String!
    indexVariantId: String!
    studyId: String!
    r2: Float
    posteriorProbability: Float
    pval: Float!

    "p-val between a study and an the provided index variant"
    pvalMantissa: Float!

    "p-val between a study and an the provided index variant"
    pvalExponent: Long!
    oddsRatio: Float
    oddsRatioCILower: Float
    oddsRatioCIUpper: Float
    beta: Float
    betaCILower: Float
    betaCIUpper: Float
    direction: String
    }

    "A list of rows with each link"
    type TagVariantsAndStudiesForIndexVariant {
    "A list of associations connected to a Index variant and a Study through some expansion methods"
    associations: [IndexVariantAssociation!]!
    }

    type Tissue {
    id: String!
    name: String!
    }

    "This element represent a overlap between two stduies"
    type TopOverlappedStudies {
    "A study object"
    study: Study

    "Top N studies ordered by loci overlap"
    topStudiesByLociOverlap: [OverlappedStudy!]!
    }

    type V2DBeta {
    direction: String
    betaCI: Float
    betaCILower: Float
    betaCIUpper: Float
    }

    type V2DL2GRowByGene {
    odds: V2DOdds!
    beta: V2DBeta!
    pval: Float!
    pvalExponent: Long!
    pvalMantissa: Float!
    yProbaDistance: Float!
    yProbaInteraction: Float!
    yProbaMolecularQTL: Float!
    yProbaPathogenicity: Float!
    yProbaModel: Float!

    "Study"
    study: Study!

    "Variant"
    variant: Variant!
    }

    type V2DOdds {
    oddsCI: Float
    oddsCILower: Float
    oddsCIUpper: Float
    }

    type Variant {
    "Ensembl Gene ID of a gene"
    chromosome: String!

    "Approved symbol name of a gene"
    position: Long!
    refAllele: String!
    altAllele: String!

    "Approved symbol name of a gene"
    rsId: String

    "chrom ID GRCH37"
    chromosomeB37: String

    "Approved symbol name of a gene"
    positionB37: Long

    "Variant ID"
    id: String!

    "Nearest gene"
    nearestGene: Gene

    "Distance to the nearest gene (any biotype)"
    nearestGeneDistance: Long

    "Nearest protein-coding gene"
    nearestCodingGene: Gene

    "Distance to the nearest gene (protein-coding biotype)"
    nearestCodingGeneDistance: Long

    "Most severe consequence"
    mostSevereConsequence: String

    "Combined Annotation Dependent Depletion - Raw score"
    caddRaw: Float

    "Combined Annotation Dependent Depletion - Scaled score"
    caddPhred: Float

    "gnomAD Allele frequency (African/African-American population)"
    gnomadAFR: Float

    "gnomAD Allele frequency (Latino/Admixed American population)"
    gnomadAMR: Float

    "gnomAD Allele frequency (Ashkenazi Jewish population)"
    gnomadASJ: Float

    "gnomAD Allele frequency (East Asian population)"
    gnomadEAS: Float

    "gnomAD Allele frequency (Finnish population)"
    gnomadFIN: Float

    "gnomAD Allele frequency (Non-Finnish European population)"
    gnomadNFE: Float

    "gnomAD Allele frequency (Non-Finnish Eurpoean Estonian sub-population)"
    gnomadNFEEST: Float

    "gnomAD Allele frequency (Non-Finnish Eurpoean North-Western European sub-population)"
    gnomadNFENWE: Float

    "gnomAD Allele frequency (Non-Finnish Eurpoean Southern European sub-population)"
    gnomadNFESEU: Float

    "gnomAD Allele frequency (Non-Finnish Eurpoean Other non-Finnish European sub-population)"
    gnomadNFEONF: Float

    "gnomAD Allele frequency (Other (population not assigned) population)"
    gnomadOTH: Float
    }

    type Version {
    major: Int!
    minor: Int!
    patch: Int!
    }�.