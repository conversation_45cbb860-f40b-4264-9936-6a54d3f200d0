���      }�(�base_url��$https://remap2022.univ-amu.fr/api/v1��	endpoints�}�(�list_targets�}�(�url��%{base_url}/list/targets/taxid={taxid}��description��*Retrieve all targets from a specific taxid��required�]��taxid�a�optional�]��method��GET�u�
list_biotypes�}�(h�&{base_url}/list/biotypes/taxid={taxid}�h	�+Retrieve all biotypes from a specific taxid�h]�h
ah]�hhu�list_experiments�}�(h�){base_url}/list/experiments/taxid={taxid}�h	�.Retrieve all experiments from a specific taxid�h]�h
ah]�hhu�list_species�}�(h�{base_url}/list/species�h	�'Retrieve all species available in ReMap�h]�h]�hhu�find_datasets_by_target�}�(h�>{base_url}/datasets/findByTarget/target={target}&taxid={taxid}�h	�9Retrieve all datasets from a target with a specific taxid�h]�(�target�h
eh]�hhu�find_datasets_by_biotype�}�(h�A{base_url}/datasets/findByBiotype/biotype={biotype}&taxid={taxid}�h	�:Retrieve all datasets from a biotype with a specific taxid�h]�(�biotype�h
eh]�hhu�find_datasets_by_experiment�}�(h�J{base_url}/datasets/findByExperiment/experiment={experiment}&taxid={taxid}�h	�>Retrieve all datasets from an experiment with a specific taxid�h]�(�
experiment�h
eh]�hhu�find_datasets_by_taxid�}�(h�-{base_url}/datasets/findByTaxid/taxid={taxid}�h	�+Retrieve all datasets from a specific taxid�h]�h
ah]�hhu�advanced_query�}�(h�H{base_url}/advancedQuery/target={target}&biotype={biotype}&taxid={taxid}�h	�4Retrieve all datasets from multiple query parameters�h]�(h)h0h
eh]�hhu�info_by_target�}�(h�6{base_url}/info/byTarget/target={target}&taxid={taxid}�h	�<Retrieve all information from a target with a specific taxid�h]�(h)h
eh]�hhu�info_by_biotype�}�(h�9{base_url}/info/byBiotype/biotype={biotype}&taxid={taxid}�h	�=Retrieve all information from a biotype with a specific taxid�h]�(h0h
eh]�hhu�info_get_all_biotypes�}�(h�,{base_url}/info/getAllBiotypes/taxid={taxid}�h	�@Retrieve all information from all biotypes with a specific taxid�h]�h
ah]�hhu�info_by_experiment�}�(h�B{base_url}/info/byExperiment/experiment={experiment}&taxid={taxid}�h	�ARetrieve all information from an experiment with a specific taxid�h]�(h7h
eh]�hhuuu.