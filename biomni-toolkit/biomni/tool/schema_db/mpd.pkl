���      }�(�base_url��https://phenome.jax.org��	endpoints�}�(�
list_projects�}�(�url��{base_url}/api/projects��description��(Fetch a list of MPD projects / data sets��required�]��optional�]�(�investigator��projsym��projid��	mpdsector��largecollab��panelsym��csv�e�method��GET�u�project_filters�}�(h�+{base_url}/api/project_filters/{filtername}�h	�@Provides allowed filtering values for the /api/projects endpoint�h]��
filtername�ah
]�hhu�project_dataset�}�(h�){base_url}/api/projects/{projsym}/dataset�h	�GReturn the project's entire dataset of measured phenotype animal values�h]�hah
]��json�ahhu�project_strains�}�(h�){base_url}/api/projects/{projsym}/strains�h	�@List the strains that were tested in the project with attributes�h]�hah
]�hhu�project_publications�}�(h�.{base_url}/api/projects/{projsym}/publications�h	�2Fetch the publications associated with the project�h]�hah
]�hhu�project_markers�}�(h�){base_url}/api/projects/{projsym}/markers�h	�<Fetch the markers that have been associated with the project�h]�hah
]�hhu�list_investigators�}�(h�{base_url}/api/investigators�h	�*Fetch a list of contributing investigators�h]�h
]�(�name�hehhu�pheno_animal_vals�}�(h�){base_url}/api/pheno/animalvals/{measnum}�h	�OFetch numeric individual animal data for MPD strain survey phenotype measure(s)�h]��measnum�ah
]�(�	covariate�h�gxl_format1�ehhu�pheno_animal_vals_series�}�(h�0{base_url}/api/pheno/animalvals/series/{measnum}�h	�;Fetch numeric individual animal data for the measure series�h]�hDah
]�hahhu�pheno_shared_animals�}�(h�.{base_url}/api/pheno/shared_animals/{measlist}�h	�pVerify if all measure IDs in measlist are for animal-granularity measures that could have some animals in common�h]��measlist�ah
]�hhu�
pheno_lsmeans�}�(h�'{base_url}/api/pheno/lsmeans/{selector}�h	�EGet model-adjusted least-square strain means for phenotype measure(s)�h]��selector�ah
]�hahhu�pheno_strain_means�}�(h�+{base_url}/api/pheno/strainmeans/{selector}�h	�BGet unadjusted strain means for one or more MPD phenotype measures�h]�hZah
]�hahhu�pheno_measure_info�}�(h�+{base_url}/api/pheno/measureinfo/{selector}�h	�HGet descriptions, units, and other metadata for one or more MPD measures�h]�hZah
]�hhu�pheno_series_info�}�(h�){base_url}/api/pheno/seriesinfo/{measnum}�h	�BGet descriptions, units, and other metadata for the measure series�h]�hDah
]�hhu�pheno_measures_by_ontology�}�(h�4{base_url}/api/pheno/measures_by_ontology/{ont_term}�h	�^Get measure IDs and metadata for all measures annotated to an ontology term or its descendants�h]��ont_term�ah
]�(�this_term_only��
omit_baseline��collapse_series�hehhu�limsdata�}�(h�{base_url}/api/limsdata�h	�?Get a list of all JaxLIMS procedures for which data is captured�h]�h
]�hhu�limsdata_procedure�}�(h�#{base_url}/api/limsdata/{procedure}�h	�LGet names and data types of all fields captured from JaxLIMS for a procedure�h]��	procedure�ah
]�hhu�limsdata_measures�}�(h�,{base_url}/api/limsdata/measures/{procedure}�h	�@Get a list of accessioned MPD measures available for a procedure�h]�h�ah
]�hhu�limsdata_procedure_parameters�}�(h�0{base_url}/api/limsdata/{procedure}/{parameters}�h	�OGet the JaxLIMS individual animal data for the given procedure and parameter(s)�h]�(h��
parameters�eh
]�(�alltime��genotype_id��	startdate��enddate��jrnum��gene��maxrows��newest_first��wkomp_animal_attrs��
trait_colname�hehhu�limsdata_any_measnum�}�(h�%{base_url}/api/limsdata/any/{measnum}�h	�CGet the JaxLIMS individual animal data for the given measure number�h]�hDah
]�(h�h�h�h�h�h�h�h�h�h�hehhu�limsdata_komp_genotypes�}�(h�&{base_url}/api/limsdata/komp_genotypes�h	�,Get info on strains/genotypes tested in KOMP�h]�h
]�(�id�h��jr��desclike�ehhu�limsdata_kompeff�}�(h�*{base_url}/api/limsdata/kompeff/{measnums}�h	�LGet the precomputed effect sizes for one or more KOMP measures by measure ID�h]��measnums�ah
]�hahhu�limsdata_kompeff_by_genotype�}�(h�:{base_url}/api/limsdata/kompeff_by_genotype/{genotype_ids}�h	�XGet the precomputed effect sizes for all loaded KOMP measures for requested genotype_ids�h]��genotype_ids�ah
]�hhu�snpdata�}�(h�{base_url}/api/snpdata�h	�%Retrieve rows from an MPD SNP dataset�h]�(�dataset��region��strains�eh
]��indels�ahhu�geneinfo�}�(h�{base_url}/api/geneinfo/{sym}�h	�EGet basepair coordinates and other attributes for a mouse gene/marker�h]��sym�ah
]�hhu�
straininfo�}�(h�{base_url}/api/straininfo�h	�3See what info is available for a given mouse strain�h]�h
]�(h>�stocknum��mginum�ehhu�generate_uuids�}�(h�{base_url}/api/generate_uuids�h	�@Generate one or more TCI JMUS 36-char unique identifiers (uuids)�h]�h
]��n_ids�ahhuuu.