��C;      }�(�base_url��https://reactome.org��web_url�h�version��2023��services�}�(�AnalysisService�}�(h�2.0��url��/AnalysisService��description��kProvides an API for pathway over-representation and expression analysis as well as species comparison tool.��
categories�}�(�database�}�(h
�Database info queries��	endpoints�}�(�GET /database/name�}�(h
�The name of current database��required_params�]��optional_params�]��example��/database/name�u�GET /database/version�}�(h
�&The version number of current database�h]�h]�h�/database/version�uuu�download�}�(h
�/Methods to download different views of a result�h}�(�>GET /download/{token}/entities/found/{resource}/{filename}.csv�}�(h
�=Downloads found identifiers for a given analysis and resource�h]�(�token��resource��filename�eh]�h�@/download/MjAxODAyMTQxMTI5MzRfMQ/entities/found/TOTAL/result.csv�u�6GET /download/{token}/entities/notfound/{filename}.csv�}�(h
�-Downloads a list of the not found identifiers�h]�(h-h/eh]�h�=/download/MjAxODAyMTQxMTI5MzRfMQ/entities/notfound/result.csv�u�8GET /download/{token}/pathways/{resource}/{filename}.csv�}�(h
�/Downloads all hit pathways for a given analysis�h]�(h-h.h/eh]�h�:/download/MjAxODAyMTQxMTI5MzRfMQ/pathways/TOTAL/result.csv�u�!GET /download/{token}/result.json�}�(h
�*Returns the complete result in json format�h]�h-ah]�h�,/download/MjAxODAyMTQxMTI5MzRfMQ/result.json�u�$GET /download/{token}/result.json.gz�}�(h
�4Returns the complete result in json format (gzipped)�h]�h-ah]�h�//download/MjAxODAyMTQxMTI5MzRfMQ/result.json.gz�uuu�
identifier�}�(h
�Queries for only one identifier�h}�(�GET /identifier/{id}�}�(h
�AAnalyse the identifier over the different species in the database�h]��id�ah]�h�/identifier/P53�u�GET /identifier/{id}/projection�}�(h
�iAnalyse the identifier over the different species in the database and projects the result to Homo Sapiens�h]�hRah]�h�/identifier/P53/projection�uuu�report�}�(h
�$Retrieves report files in PDF format�h}��,GET /report/{token}/{species}/{filename}.pdf�}�(h
�6Downloads a report for a given pathway analysis result�h]�(h-�species�h/eh]�h�6/report/MjAxODAyMTQxMTI5MzRfMQ/Homo sapiens/report.pdf�usuhc}�(h
�Species comparison�h}��"GET /species/homoSapiens/{species}�}�(h
�.Compares Homo sapiens to the specified species�h]�hcah]�h�!/species/homoSapiens/Mus musculus�usuh-}�(h
�Previous queries filter�h}�(�GET /token/{token}�}�(h
�,Returns the result associated with the token�h]�h-ah]�h�/token/MjAxODAyMTQxMTI5MzRfMQ�u�+GET /token/{token}/filter/species/{species}�}�(h
�Filters the result by species�h]�(h-hceh]�h�9/token/MjAxODAyMTQxMTI5MzRfMQ/filter/species/Homo sapiens�u�&GET /token/{token}/found/all/{pathway}�}�(h
�\Returns a summary of the contained identifiers and interactors for a given pathway and token�h]�(h-�pathway�eh]�h�3/token/MjAxODAyMTQxMTI5MzRfMQ/found/all/R-HSA-73843�u�+GET /token/{token}/found/entities/{pathway}�}�(h
�PReturns a summary of the found curated identifiers for a given pathway and token�h]�(h-h�eh]�h�8/token/MjAxODAyMTQxMTI5MzRfMQ/found/entities/R-HSA-73843�u�.GET /token/{token}/found/interactors/{pathway}�}�(h
�HReturns a summary of the found interactors for a given pathway and token�h]�(h-h�eh]�h�;/token/MjAxODAyMTQxMTI5MzRfMQ/found/interactors/R-HSA-73843�u�GET /token/{token}/notFound�}�(h
�=Returns a list of the identifiers not found for a given token�h]�h-ah]�h�&/token/MjAxODAyMTQxMTI5MzRfMQ/notFound�u�!GET /token/{token}/page/{pathway}�}�(h
�]Returns the page where the corresponding pathway is taking into account the passed parameters�h]�(h-h�eh]�h�./token/MjAxODAyMTQxMTI5MzRfMQ/page/R-HSA-73843�u�"GET /token/{token}/pathways/binned�}�(h
�DReturns a list of binned hit pathway sizes associated with the token�h]�h-ah]�h�-/token/MjAxODAyMTQxMTI5MzRfMQ/pathways/binned�u�&GET /token/{token}/reactions/{pathway}�}�(h
�[Returns the reaction ids of the provided pathway id that are present in the original result�h]�(h-h�eh]�h�3/token/MjAxODAyMTQxMTI5MzRfMQ/reactions/R-HSA-73843�u�GET /token/{token}/resources�}�(h
�7Returns the resources summary associated with the token�h]�h-ah]�h�'/token/MjAxODAyMTQxMTI5MzRfMQ/resources�uuuuu�ContentService�}�(h�1.2�h�/ContentService�h
�REST API for Reactome content�h}�(h}�(h
�$Reactome Data: Database info queries�h}�(�GET /data/database/name�}�(h
hh]�h]�h�/data/database/name�u�GET /data/database/version�}�(h
h!h]�h]�h�/data/database/version�uuu�discover�}�(h
�.Reactome Data: Search engines discovery schema�h}��GET /data/discover/{identifier}�}�(h
�5The schema.org for an Event in Reactome knowledgebase�h]�hJah]�h�/data/discover/R-HSA-5673001�usu�diseases�}�(h
�&Reactome Data: Disease related queries�h}�(�GET /data/diseases�}�(h
�The list of disease objects�h]�h]�h�/data/diseases�u�GET /data/diseases/doid�}�(h
�The list of diseases DOID�h]�h]�h�/data/diseases/doid�uuu�entities�}�(h
�%Reactome Data: PhysicalEntity queries�h}�(�GET /data/complex/{id}/subunits�}�(h
�5A list with the entities contained in a given complex�h]�hRah]�h�$/data/complex/R-HSA-5674003/subunits�u�+GET /data/complexes/{resource}/{identifier}�}�(h
�>A list of complexes containing the pair (identifier, resource)�h]�(h.hJeh]�h�/data/complexes/UniProt/P00533�u�!GET /data/entity/{id}/componentOf�}�(h
�1A list of larger structures containing the entity�h]�hRah]�h�%/data/entity/R-HSA-199420/componentOf�u�GET /data/entity/{id}/in-depth�}�(h
�$In-depth information about an entity�h]�hRah]�h�"/data/entity/R-HSA-199420/in-depth�u� GET /data/entity/{id}/otherForms�}�(h
�#All other forms of a PhysicalEntity�h]�hRah]�h�$/data/entity/R-HSA-199420/otherForms�uuu�events�}�(h
�(Reactome Data: Queries related to events�h}�(�GET /data/event/{id}/ancestors�}�(h
�The ancestors of a given event�h]�hRah]�h�#/data/event/R-HSA-5673001/ancestors�u�#GET /data/eventsHierarchy/{species}�}�(h
�,The full event hierarchy for a given species�h]�hcah]�h�"/data/eventsHierarchy/homo sapiens�uuu�exporter�}�(h
�Reactome Data: Format Exporter�h}�(�(GET /exporter/diagram/{identifier}.{ext}�}�(h
�XExports a given pathway diagram to the specified image format (png, jpg, jpeg, svg, gif)�h]�(hJ�ext�eh]�h�#/exporter/diagram/R-HSA-5673001.png�u�-GET /exporter/document/event/{identifier}.pdf�}�(h
�LExports the content of a given event (pathway or reaction) to a PDF document�h]�hJah]�h�*/exporter/document/event/R-HSA-5673001.pdf�u�%GET /exporter/event/{identifier}.sbgn�}�(h
�+Exports a given pathway or reaction to SBGN�h]�hJah]�h�"/exporter/event/R-HSA-5673001.sbgn�u�%GET /exporter/event/{identifier}.sbml�}�(h
�+Exports a given pathway or reaction to SBML�h]�hJah]�h�"/exporter/event/R-HSA-5673001.sbml�u�'GET /exporter/fireworks/{species}.{ext}�}�(h
�YExports a given pathway overview to the specified image format (png, jpg, jpeg, svg, gif)�h]�(hcj  eh]�h�$/exporter/fireworks/Homo sapiens.svg�u�)GET /exporter/reaction/{identifier}.{ext}�}�(h
�QExports a given reaction to the specified image format (png, jpg, jpeg, svg, gif)�h]�(hJj  eh]�h�$/exporter/reaction/R-HSA-5673001.png�uuu�interactors�}�(h
�Molecule interactors�h}�(�;GET /interactors/psicquic/molecule/{resource}/{acc}/details�}�(h
�RRetrieve clustered interaction, sorted by score, of a given accession by resource.�h]�(h.�acc�eh]�h�4/interactors/psicquic/molecule/intact/P00533/details�u�;GET /interactors/psicquic/molecule/{resource}/{acc}/summary�}�(h
�3Retrieve a summary of a given accession by resource�h]�(h.j?  eh]�h�4/interactors/psicquic/molecule/intact/P00533/summary�u�#GET /interactors/psicquic/resources�}�(h
�3Retrieve a list of all Psicquic Registries services�h]�h]�h�/interactors/psicquic/resources�u�7GET /interactors/static/molecule/enhanced/{acc}/details�}�(h
��Retrieve a custom interaction information of a given accession in Reactome knowledgebase for UI to use, including Reactome entity number�h]�j?  ah]�h�4/interactors/static/molecule/enhanced/P00533/details�u�.GET /interactors/static/molecule/{acc}/details�}�(h
�@Retrieve a detailed interaction information of a given accession�h]�j?  ah]�h�+/interactors/static/molecule/P00533/details�u�/GET /interactors/static/molecule/{acc}/pathways�}�(h
�TRetrieve a list of lower level pathways where the interacting molecules can be found�h]�j?  ah]�h�,/interactors/static/molecule/P00533/pathways�u�.GET /interactors/static/molecule/{acc}/summary�}�(h
�'Retrieve a summary of a given accession�h]�j?  ah]�h�+/interactors/static/molecule/P00533/summary�uuu�mapping�}�(h
�&Reactome Data: Mapping related queries�h}�(�2GET /data/mapping/{resource}/{identifier}/pathways�}�(h
�=The lower level pathways where an identifier can be mapped to�h]�(h.hJeh]�h�%/data/mapping/UniProt/P00533/pathways�u�3GET /data/mapping/{resource}/{identifier}/reactions�}�(h
�2The reactions where an identifier can be mapped to�h]�(h.hJeh]�h�&/data/mapping/UniProt/P00533/reactions�uuu�participants�}�(h
�.Reactome Data: Queries related to participants�h}�(�GET /data/participants/{id}�}�(h
�(A list of participants for a given event�h]�hRah]�h� /data/participants/R-HSA-5673001�u�9GET /data/participants/{id}/participatingPhysicalEntities�}�(h
�:A list of participating PhysicalEntities for a given event�h]�hRah]�h�>/data/participants/R-HSA-5673001/participatingPhysicalEntities�u�-GET /data/participants/{id}/referenceEntities�}�(h
�;A list of participating ReferenceEntities for a given event�h]�hRah]�h�2/data/participants/R-HSA-5673001/referenceEntities�uuu�pathways�}�(h
�&Reactome Data: Pathway related queries�h}�(�&GET /data/pathway/{id}/containedEvents�}�(h
�+All the events contained in the given event�h]�hRah]�h�+/data/pathway/R-HSA-5673001/containedEvents�u�6GET /data/pathway/{id}/containedEvents/{attributeName}�}�(h
�=A single property for each event contained in the given event�h]�(hR�
attributeName�eh]�h�7/data/pathway/R-HSA-5673001/containedEvents/displayName�u�*GET /data/pathways/low/diagram/entity/{id}�}�(h
�NA list of lower level pathways with diagram containing a given entity or event�h]�hRah]�h�./data/pathways/low/diagram/entity/R-HSA-199420�u�3GET /data/pathways/low/diagram/entity/{id}/allForms�}�(h
�QA list of lower level pathways with diagram containing any form of a given entity�h]�hRah]�h�7/data/pathways/low/diagram/entity/R-HSA-199420/allForms�u�"GET /data/pathways/low/entity/{id}�}�(h
�AA list of lower level pathways containing a given entity or event�h]�hRah]�h�&/data/pathways/low/entity/R-HSA-199420�u�+GET /data/pathways/low/entity/{id}/allForms�}�(h
�DA list of lower level pathways containing any form of a given entity�h]�hRah]�h�//data/pathways/low/entity/R-HSA-199420/allForms�u� GET /data/pathways/top/{species}�}�(h
�All Reactome top level pathways�h]�hcah]�h�/data/pathways/top/homo sapiens�uuu�person�}�(h
�Reactome Data: Person queries�h}�(�GET /data/people/name/{name}�}�(h
�GA list of people with first or last name partly matching a given string�h]��name�ah]�h�/data/people/name/steve�u�"GET /data/people/name/{name}/exact�}�(h
�IA list of people with first AND last name exactly matching a given string�h]�j�  ah]�h�"/data/people/name/steve jupe/exact�u�GET /data/person/{id}�}�(h
�A person by his/her identifier�h]�hRah]�h�/data/person/75380�u�&GET /data/person/{id}/authoredPathways�}�(h
�-A list of pathways authored by a given person�h]�hRah]�h�#/data/person/75380/authoredPathways�u�"GET /data/person/{id}/publications�}�(h
�1A list of publications authored by a given person�h]�hRah]�h�/data/person/75380/publications�u�%GET /data/person/{id}/{attributeName}�}�(h
�)A person's property by his/her identifier�h]�(hRj�  eh]�h�/data/person/75380/displayName�uuu�query�}�(h
�$Reactome Data: Common data retrieval�h}�(�GET /data/query/enhanced/{id}�}�(h
�6More information on an entry in Reactome knowledgebase�h]�hRah]�h�"/data/query/enhanced/R-HSA-5673001�u�GET /data/query/{id}�}�(h
�"An entry in Reactome knowledgebase�h]�hRah]�h�/data/query/R-HSA-5673001�u�$GET /data/query/{id}/{attributeName}�}�(h
�7A single property of an entry in Reactome knowledgebase�h]�(hRj�  eh]�h�%/data/query/R-HSA-5673001/displayName�uuu�
references�}�(h
�'Reactome xRefs: ReferenceEntity queries�h}�(�$GET /references/mapping/{identifier}�}�(h
�,All ReferenceEntities for a given identifier�h]�hJah]�h�/references/mapping/P00533�u�*GET /references/mapping/{identifier}/xrefs�}�(h
�MAll cross references and physical entities associated with a given identifier�h]�hJah]�h� /references/mapping/P00533/xrefs�uuu�schema�}�(h
�#Reactome Data: Schema class queries�h}�(�GET /data/schema/model�}�(h
�A list of Reactome data model�h]�h]�h�/data/schema/model�u�GET /data/schema/{className}�}�(h
�7A list of entries corresponding to a given schema class�h]��	className�ah]�h�/data/schema/Pathway�u�"GET /data/schema/{className}/count�}�(h
�9Number of entries belonging to the specified schema class�h]�j  ah]�h�/data/schema/Pathway/count�u� GET /data/schema/{className}/min�}�(h
�BA list of simplified entries corresponding to a given schema class�h]�j  ah]�h�/data/schema/Pathway/min�u�&GET /data/schema/{className}/reference�}�(h
�LA list of simplified reference objects corresponding to a given schema class�h]�j  ah]�h�/data/schema/Pathway/reference�uuu�search�}�(h
�Reactome Search�h}�(�GET /search/diagram/{diagram}�}�(h
�EPerforms a Solr query (diagram widget scoped) for a given QueryObject�h]��diagram�ah]�h�/search/diagram/R-HSA-5673001�u�4GET /search/diagram/{diagram}/occurrences/{instance}�}�(h
j3  h]�(j5  �instance�eh]�h�6/search/diagram/R-HSA-5673001/occurrences/R-HSA-199420�u�$GET /search/diagram/{pathwayId}/flag�}�(h
�cA list of diagram entities plus pathways from the provided list containing the specified identifier�h]��	pathwayId�ah]�h�"/search/diagram/R-HSA-5673001/flag�u�GET /search/facet�}�(h
�@A list of facets corresponding to the whole Reactome search data�h]�h]�h�
/search/facet�u�GET /search/facet_query�}�(h
�2A list of facets corresponding to a specific query�h]�h]�h�/search/facet_query�u�GET /search/fireworks�}�(h
�GPerforms a Solr query (fireworks widget scoped) for a given QueryObject�h]�h]�h�/search/fireworks�u�GET /search/fireworks/flag�}�(h
jS  h]�h]�h�/search/fireworks/flag�u�GET /search/query�}�(h
�/Queries Solr against the Reactome knowledgebase�h]�h]�h�/search/query?query=EGFR�u�GET /search/query/paginated�}�(h
j^  h]�h]�h�1/search/query/paginated?query=EGFR&page=1&rows=10�u�GET /search/spellcheck�}�(h
�)Spell-check suggestions for a given query�h]�h]�h�/search/spellcheck?query=kinas�u�GET /search/suggest�}�(h
�"Auto-suggestions for a given query�h]�h]�h�/search/suggest?query=egf�uuuhc}�(h
�&Reactome Data: Species related queries�h}�(�GET /data/species/all�}�(h
�#The list of all species in Reactome�h]�h]�h�/data/species/all�u�GET /data/species/main�}�(h
�$The list of main species in Reactome�h]�h]�h�/data/species/main�uuuuuuu.