#%% md
## Biomni 101
#%% md
Welcome to Biomni! Here is a simple tutorial on the basics of Biomni package. After you installed the environment, you can then simply try this to initialize the agent, which will automatically download the raw data lake files:
#%%
import sys

sys.path.append("../")

from biomni.agent import A1

agent = A1(path="/dfs/project/bioagentos/biomni_data_test", llm="claude-sonnet-4-20250514")
#%% md
Then, you can simply start prompting the agent with the desired biomedical research task!
#%%
log = agent.go("""Plan a CRISPR screen to identify genes that regulate T cell exhaustion,
        measured by the change in T cell receptor (TCR) signaling between acute
        (interleukin-2 [IL-2] only) and chronic (anti-CD3 and IL-2) stimulation conditions.
        Generate 32 genes that maximize the perturbation effect.""")