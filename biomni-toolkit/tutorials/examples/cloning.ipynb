#%%
import sys

sys.path.append("/dfs/user/kexinh/BioAgentOS/biomni_release")

from biomni.agent import A1

agent = A1(path="/dfs/project/bioagentos/biomni_data", llm="claude-sonnet-4-20250514")

agent.go(
    "I have a plasmid plentiCRISPR v2 (sequence is at ./data/plentiCRISPR.txt). "
    "I hope to clone a CRISPR sgRNA targeting human B2M into this plasmid. "
    "Could you save the final assembied plasmid map into *.fasta and show step-by-step guidance"
    " on how should I perform the cloning?"
)