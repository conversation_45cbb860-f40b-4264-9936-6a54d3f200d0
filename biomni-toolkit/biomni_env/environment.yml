name: biomni_e1
channels:
  - conda-forge
  - defaults
  - bioconda
dependencies:
  - python=3.11
  - pip
  
  # Core Scientific Computing
  - pandas
  - numpy
  - scipy
  - matplotlib
  - seaborn
  - statsmodels
  - scikit-learn
  - jupyter
  - notebook
  - ipykernel
  - pytest
  - requests
  - tqdm
  - pyyaml
  - networkx
  - git
  - curl
  - wget
  - numba
  - h5py
  - tables
  - openpyxl
  - xlrd
  - joblib
  - opencv
  - scikit-image
  
  # Core Bioinformatics Libraries
  - biopython
  - biom-format
  - scanpy
  - anndata
  - pysam
  - pyfaidx
  - pybedtools
  - pyranges
  - gget
  - gseapy
  - scrublet
  - scvelo
  - lifelines
  - umap-learn
  - hyperopt
  - igraph
  - cooler
  - trackpy
  - cellpose
  - viennarna
  - hmmlearn
  - msprime
  - tskit
  - cyvcf2
  - loompy
  - pyBigWig
  - pymzml
  - optlang
  - arboreto
  - pdbfixer
  
  # Structural Biology & Drug Discovery
  - rdkit
  - openbabel
  - openmm
  - pytdc
  - descriptastorus
  
  # Statistical Analysis & Machine Learning
  - pymc3
  - faiss-cpu
  - harmony-pytorch
  
  # Data Storage & Utilities
  - tiledb
  - tiledbsoma
  - reportlab
  - flowkit
  - PyPDF2
  - googlesearch-python
  - pymed
  - arxiv
  - scholarly
  - cryosparc-tools
  - mageck
  - pyscenic
  - python-libsbml
  - cobra
  - fanc
  - FlowIO
  - FlowUtils
  
  # R Environment
  - r-base
  - r-essentials
  - r-ggplot2
  - r-dplyr
  - r-tidyr
  - r-readr
  - r-stringr
  - r-matrix
  - r-deseq2
  - r-clusterprofiler
  - r-edger
  - r-limma
  - r-harmony
  - r-wgcna
  
  # CLI Tools
  - samtools
  - bowtie2
  - bwa
  - bedtools
  - fastqc
  - trimmomatic
  - mafft
  - muscle
  - plink
  - plink2
  - gcta
  - iqtree
  - diamond
  - vina
  
  # Additional pip packages
  - pip:
    - pydantic
    - langchain
    - scikit-bio
    - mudata
    - pyliftover
    - biopandas
    - biotite
    - deeppurpose
    - pyscreener
    - cellxgene-census
    - PyMassSpec
    - pykalman
    - FlowCytometryTools
    - plannotate
