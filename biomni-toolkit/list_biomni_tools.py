import sys
import os

# Add the parent directory to the Python path to allow for relative imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from biomni.utils import read_module2api, textify_api_dict

if __name__ == "__main__":
    # This is a workaround to avoid issues with the tool descriptions trying to import
    # modules that are not in the current environment. We are temporarily adding the
    # biomni directory to the path to allow the imports to work correctly.
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

    module_apis = read_module2api()

    # Remove the temporary path addition
    sys.path.pop(0)

    report = textify_api_dict(module_apis)
    print(report)
