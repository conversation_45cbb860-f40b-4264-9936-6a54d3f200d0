{"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "transport": "stdio"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}, "transport": "stdio"}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander@latest"], "transport": "stdio"}, "todo-md-mcp": {"command": "npx", "args": ["-y", "@danjdewhurst/todo-md-mcp"], "transport": "stdio", "env": {"TODO_FILE_PATH": "/app/workspace/todo.md"}}}