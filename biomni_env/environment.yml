name: biomni_e1
channels:
  - conda-forge
  - defaults
  - bioconda
dependencies:
  - python=3.11
  - pip
  
  # Core Python scientific packages
  - numpy=1.24.3
  - pandas=2.0.3
  - matplotlib=3.7.1
  - scipy=1.10.1
  - statsmodels=0.14.0
  - scikit-learn=1.3.0
  - <PERSON>born=0.12.2
  - networkx=3.1
  - requests=2.31.0
  - pyyaml=6.0
  - tqdm=4.65.0
  - h5py=3.9.0
  - joblib=1.3.1
  
  # Core bioinformatics packages
  - biopython=1.81
  - pysam=0.21.0
  - pybedtools=0.9.0
  - scanpy=1.9.3
  - anndata=0.9.1
  - mudata=0.2.3
  - umap-learn=0.5.3
  - igraph=0.10.6
  - pytables=3.8.0
  
  # Cheminformatics
  - rdkit=2023.3.2
  - openbabel=3.1.1
  
  # Molecular simulation
  - openmm=8.0.0
  
  # Image processing
  - opencv=4.8.0
  - scikit-image=0.21.0
  - imageio=2.31.1
  
  # Data formats
  - xlrd=2.0.1
  - openpyxl=3.1.2
  - pytables=3.8.0
  
  # Development tools
  - jupyter=1.0.0
  - notebook=7.0.0
  - ipykernel=6.25.0
  - jupyterlab=4.0.5
  - pytest=7.4.0
  - pytest-cov=4.1.0
  - ipython=8.14.0
  
  # R environment
  - r-base=4.3.1
  - r-essentials=4.3.1
  - r-ggplot2=3.4.2
  - r-dplyr=1.1.2
  - r-tidyr=1.3.0
  - r-readr=2.1.4
  - r-stringr=1.5.0
  - r-matrix=1.6_0
  - r-devtools=2.4.5
  - r-remotes=2.4.2
  - r-seurat=4.3.0
  - r-limma=3.56.2
  - r-edger=3.42.4
  - r-deseq2=1.40.2
  - r-clusterprofiler=4.8.1
  - r-wgcna=1.72_1
  - r-complexheatmap=2.16.0
  - r-harmony=0.1.1
  
  # CLI bioinformatics tools
  - samtools=1.18
  - bowtie2=2.5.1
  - bwa=0.7.17
  - bedtools=2.30.0
  - fastqc=0.12.1
  - trimmomatic=0.39
  - mafft=7.520
  - plink=1.90b6.21
  - plink2=2.00a3.7
  - iqtree=*******
  - muscle=5.1.0
  - fasttree=2.1.11
  - multiqc=1.15
  - vcftools=0.1.16
  - bcftools=1.18
  - htslib=1.18
  - salmon=1.10.1
  - kallisto=0.48.0
  - hisat2=2.2.1
  - star=2.7.10b
  - diamond=2.1.8
  - macs2=*******
  - homer=4.11
  - snakemake=7.32.4
  - nextflow=23.04.2
  
  # Additional bioinformatics packages from pip
  - pip:
      # Core packages that need specific versions
      - gradio==3.50.0
      - langchain==0.1.20
      - langgraph==0.3.18
      - langchain_openai==0.1.8
      - langchain_anthropic==0.1.8
      - langchain_community==0.1.20
      - langchain-google-genai==1.0.6
      - langchain_ollama==0.1.0
      - openai==1.30.0
      - beautifulsoup4==4.12.2
      - lxml==4.9.3
      - transformers==4.31.0
      - sentencepiece==0.1.99
      - hyperimpute==0.1.22
      
      # Specialized bioinformatics packages
      - gget==0.28.6
      - lifelines==0.27.7
      - gseapy==1.0.4
      - scrublet==0.2.3
      - cellxgene-census==1.8.0
      - hyperopt==0.2.7
      - scvelo==0.3.0
      - pyfaidx==*******
      - pyranges==0.0.129
      - deeppurpose==0.1.4
      - pyscreener==1.3.0
      - descriptastorus==2.6.1
      - pytdc==0.4.1
      - pymc3==3.11.5
      - faiss-cpu==1.7.4
      - harmony-pytorch==0.1.8
      - tiledb==0.21.4
      - tiledbsoma==1.3.0
      - PyPDF2==3.0.1
      - googlesearch-python==1.2.3
      - pymed==0.8.9
      - arxiv==1.4.8
      - scholarly==1.7.11
      - cryosparc-tools==4.2.1
      - mageck==*******
      - pyscenic==0.12.1
      - cooler==0.9.3
      - trackpy==0.6.1
      - cellpose==2.2.2
      - viennarna==2.6.4
      - PyMassSpec==2.4.0
      - python-libsbml==5.20.2
      - cobra==0.29.0
      - reportlab==4.0.4
      - flowkit==0.7.8
      - hmmlearn==0.3.0
      - msprime==1.2.0
      - tskit==0.5.4
      - cyvcf2==0.30.22
      - pykalman==0.9.5
      - fanc==0.9.25
      - loompy==3.0.7
      - pyBigWig==0.3.22
      - pymzml==2.5.6
      - optlang==1.7.0
      - FlowIO==0.9.4
      - FlowUtils==0.1.2
      - arboreto==0.1.6
      - pdbfixer==1.9
      - biotite==0.39.0
      - biopandas==0.4.1
      - pyliftover==0.4
      - biom-format==2.1.12
      - scikit-bio==0.5.8
      - polars==0.19.3
      - dask==2023.8.0
      - plotly==5.15.0
      - bokeh==3.2.1
      - altair==5.0.1
      - holoviews==1.17.1
      - tensorflow==2.13.0
      - torch==2.0.1
      - sentence-transformers==2.2.2
      - ete3==3.1.3
      - pydeseq2==0.4.0
      - sphinx==7.1.2
      - mkdocs==1.5.2
      - pytest-xdist==3.3.1
