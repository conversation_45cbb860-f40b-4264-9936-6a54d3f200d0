import json
import requests
import time
import uuid
import os
import argparse
import re
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel

# --- Configuration ---
BASE_URL = os.environ.get("AWC_BASE_URL", "http://localhost:8000")
TODO_FILE_PATH = "todo.md"
console = Console()


# --- State Management ---
class CliState:
    """Encapsulates the state of the CLI during a workflow run."""

    def __init__(self):
        self.current_assistant_message = ""
        self.last_message_sender = None
        self.current_agent = "Assistant"

    def append_assistant_message(self, delta):
        self.current_assistant_message += delta

    def finalize_assistant_message(self):
        if self.current_assistant_message:
            display_message(
                self.current_assistant_message,
                sender=self.current_agent,
                msg_type="user_facing",
            )
            self.current_assistant_message = ""
            self.last_message_sender = "assistant"


# --- File and Display Functions ---
def reset_todo_file():
    """Clears the todo.md file for a new session."""
    with open(TODO_FILE_PATH, "w") as f:
        f.write("# Todo List\n\n<!-- Generated by MCP Todo Server -->\n")


def read_and_parse_todos():
    """Reads and parses the todo.md file."""
    todos = []
    if not os.path.exists(TODO_FILE_PATH):
        return todos

    try:
        with open(TODO_FILE_PATH, "r") as f:
            content = f.read()

        todo_items = re.findall(r"- \[(x| )\] (.*)", content)

        for i, item in enumerate(todo_items):
            status = "completed" if item[0] == "x" else "pending"
            title = item[1].split("<!--")[0].strip()
            todos.append({"step": i + 1, "title": title, "status": status})

    except Exception as e:
        console.print(f"[red]Error reading todo.md: {e}[/red]")

    return todos


def generate_todos_table(todos):
    """Generate a Rich table to display the to-do list from todo.md."""
    table = Table(
        title="✅ To-Do List Progress",
        show_header=True,
        header_style="bold blue",
        show_lines=True,
        expand=True,
    )
    table.add_column("Step", style="cyan", width=4)
    table.add_column("Task", style="cyan", width=80)
    table.add_column("Status", style="magenta", width=15, justify="center")

    for todo in todos:
        status = todo.get("status", "pending")
        status_styles = {
            "pending": "⏳",
            "in_progress": "🔄",
            "completed": "✅",
            "failed": "❌",
        }
        icon = status_styles.get(status, "❓")

        table.add_row(
            str(todo.get("step")),
            todo.get("title", "N/A"),
            f"{icon} {status.title()}",
        )
    return table


def display_message(content, sender, msg_type):
    """Displays a message with appropriate formatting."""
    sender_name = sender.title() if sender else "Assistant"
    if msg_type == "user_facing":
        console.print(
            Panel(
                content,
                title=f"[bold green]🤖 {sender_name}[/bold green]",
                border_style="green",
            )
        )
    elif msg_type == "tool_usage":
        console.print(f"[dim]🔧 {content}[/dim]")
    else:
        console.print(f"[dim cyan]ℹ️ {content}[/dim cyan]")


# --- Event Handlers ---
def handle_step_start(event, state):
    agent_name = event.get("agentName")
    if agent_name:
        state.current_agent = agent_name


EVENT_HANDLERS = {
    "STEP_STARTED": handle_step_start,
    "tool_output_message": lambda event, state: display_message(
        f"Tool Used: {event.get('message_data', {}).get('tool_name')}",
        sender="System",
        msg_type="tool_usage",
    ),
    "agent_step_message": lambda event, state: display_message(
        f"{event.get('message_data', {}).get('agent_name')} agent processing completed.",
        sender="System",
        msg_type="progress",
    ),
    "final_answer_message": lambda event, state: display_message(
        event.get("message_data", {}).get("content"),
        sender=event.get("message_data", {}).get("agent_name", "Assistant"),
        msg_type="user_facing",
    ),
    "TEXT_MESSAGE_CONTENT": lambda event, state: state.append_assistant_message(
        event.get("delta", "")
    ),
    "TEXT_MESSAGE_END": lambda event, state: state.finalize_assistant_message(),
    "RUN_FINISHED": lambda event, state: display_message(
        "Workflow Completed Successfully!", sender="System", msg_type="progress"
    ),
    "RUN_ERROR": lambda event, state: display_message(
        f"Workflow Error: {event.get('message', 'Unknown error')}",
        sender="System",
        msg_type="progress",
    ),
}


# --- Main Processing Logic ---
def process_streaming_response(response, state, detailed_view, debug_mode):
    """Process the streaming HTTP response."""
    with Live(generate_todos_table([]), refresh_per_second=4, console=console) as live:
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode("utf-8")
                if decoded_line.startswith("data:"):
                    try:
                        event = json.loads(decoded_line[5:])
                        event_type = event.get("type")

                        if debug_mode:
                            console.print(
                                f"[dim yellow]🔍 DEBUG: {event_type} - {event}[/dim yellow]"
                            )

                        if event_type in EVENT_HANDLERS:
                            EVENT_HANDLERS[event_type](event, state)

                        todos = read_and_parse_todos()
                        live.update(generate_todos_table(todos))
                    except Exception as e:
                        console.print(f"[red]❌ Error processing event: {e}[/red]")


def start_workflow(thread_id, detailed_view, debug_mode):
    """Starts or continues a workflow chat session."""
    console.print("\n" + "=" * 80)
    console.print(
        "[bold blue]🔬 Multi-Agent Bioinformatics Workflow[/bold blue]".center(80)
    )
    console.print("=" * 80)
    console.print(f"[cyan]Thread ID:[/cyan] {thread_id}")
    console.print(
        f"[cyan]Mode:[/cyan] {'🔍 Detailed' if detailed_view else '📋 Summary'}"
    )
    console.print(
        f"[cyan]Debug:[/cyan] {'✅ Enabled' if debug_mode else '❌ Disabled'}"
    )
    console.print("=" * 80)
    console.print("\n[dim]💡 Tip: Type 'exit' or 'quit' to end the conversation.[/dim]")

    messages = []
    state = CliState()
    while True:
        try:
            console.print("[bold blue]You: [/bold blue]", end="")
            message_content = input()

            if message_content.lower() in ["exit", "quit"]:
                break

            messages.append(
                {"id": str(uuid.uuid4()), "role": "user", "content": message_content}
            )
            data = {"thread_id": thread_id, "messages": messages}

            with requests.post(
                f"{BASE_URL}/awp", json=data, stream=True, timeout=300
            ) as response:
                response.raise_for_status()
                process_streaming_response(response, state, detailed_view, debug_mode)

        except requests.exceptions.RequestException as e:
            console.print(f"[red]❌ Connection Error: {e}[/red]")
        except Exception as e:
            console.print(f"[red]❌ Unexpected Error: {e}[/red]")


# --- Main Menu and CLI Entrypoint ---
def main():
    """Main function to parse arguments and show the main menu."""
    parser = argparse.ArgumentParser(
        description="CLI for the Multi-Agent Workflow System."
    )
    parser.add_argument(
        "--thread-id", help="Specify a thread ID to jump directly into a workflow."
    )
    parser.add_argument(
        "--detailed", action="store_true", help="Enable detailed view for tool calls."
    )
    parser.add_argument(
        "--debug", action="store_true", help="Enable debug mode for verbose logging."
    )
    args = parser.parse_args()

    if args.thread_id:
        start_workflow(args.thread_id, args.detailed, args.debug)
        return

    while True:
        console.print("\n[bold green]Multi-Agent System CLI[/bold green]")
        table = Table(show_header=False, box=None)
        table.add_row("[cyan]1[/cyan]", "Start new workflow")
        table.add_row("[cyan]2[/cyan]", "Continue workflow")
        table.add_row("[cyan]3[/cyan]", "Exit")
        console.print(table)

        choice = input("Enter your choice: ")

        if choice == "1":
            new_thread_id = str(uuid.uuid4())
            console.print(
                f"[yellow]Starting new workflow with Thread ID: {new_thread_id}[/yellow]"
            )
            reset_todo_file()
            start_workflow(new_thread_id, args.detailed, args.debug)
        elif choice == "2":
            thread_id = input("Enter the Thread ID to continue: ")
            if thread_id:
                start_workflow(thread_id, args.detailed, args.debug)
            else:
                console.print("[red]Thread ID cannot be empty.[/red]")
        elif choice == "3" or choice.lower() in ["exit", "quit"]:
            break
        else:
            console.print("[red]Invalid choice. Please try again.[/red]")


if __name__ == "__main__":
    main()
