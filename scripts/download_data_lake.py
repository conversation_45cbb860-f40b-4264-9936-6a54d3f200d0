import os
import zipfile
import tempfile
from urllib.parse import urljoin
import requests
import tqdm

# Data lake dictionary with detailed descriptions
data_lake_dict = {
    "Cosmic_Fusion_v101_GRCh38.csv": "Gene fusion events from COSMIC.",
    "Cosmic_Genes_v101_GRCh38.parquet": (
        "List of genes associated with cancer from COSMIC."
    ),
    "txgnn_name_mapping.pkl": "Name mapping for TXGNN.",
}


def check_and_download_s3_files(
    s3_bucket_url: str,
    local_data_lake_path: str,
    expected_files: list[str],
    folder: str = "data_lake",
) -> dict[str, bool]:
    """Check for missing files in the local data lake and download them from S3 bucket.

    Args:
        s3_bucket_url: Base URL of the S3 bucket (e.g., "https://biomni-release.s3.amazonaws.com")
        local_data_lake_path: Local path to the data lake directory
        expected_files: List of expected file names in the data lake
        folder: S3 folder name ("data_lake" or "benchmark")

    Returns:
        Dictionary mapping file names to download success status
    """

    os.makedirs(local_data_lake_path, exist_ok=True)
    download_results = {}

    def download_with_progress(url: str, file_path: str, desc: str) -> bool:
        """Download file with progress bar."""
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get("content-length", 0))

            with open(file_path, "wb") as f:
                if total_size > 0:
                    with tqdm.tqdm(
                        total=total_size, unit="B", unit_scale=True, desc=desc, ncols=80
                    ) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            return True
        except Exception as e:
            print(f"✗ Failed to download {desc}: {e}")
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except OSError:
                    pass
            return False

    def cleanup_file(file_path: str):
        """Clean up file if it exists."""
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except OSError:
                pass

    # Handle benchmark folder (download as zip)
    if folder == "benchmark":
        print(f"Downloading entire {folder} folder structure...")
        s3_zip_url = urljoin(s3_bucket_url + "/", folder + ".zip")

        with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as tmp_zip:
            tmp_zip_path = tmp_zip.name

            if download_with_progress(s3_zip_url, tmp_zip_path, f"{folder}.zip"):
                print(f"Extracting {folder}.zip...")
                try:
                    with zipfile.ZipFile(tmp_zip_path, "r") as zip_ref:
                        zip_ref.extractall(local_data_lake_path)
                    print(f"✓ Successfully downloaded and extracted {folder} folder")
                    download_results = dict.fromkeys(expected_files, True)
                except Exception as e:
                    print(f"✗ Error extracting {folder}.zip: {e}")
                    download_results = dict.fromkeys(expected_files, False)
                finally:
                    cleanup_file(tmp_zip_path)
            else:
                download_results = dict.fromkeys(expected_files, False)

        return download_results

    # Handle data_lake folder (download individual files)
    for filename in expected_files:
        local_file_path = os.path.join(local_data_lake_path, filename)

        if os.path.exists(local_file_path):
            download_results[filename] = True
            continue

        s3_file_url = urljoin(s3_bucket_url + "/" + folder + "/", filename)
        print(f"Downloading {filename} from {folder}...")

        if download_with_progress(s3_file_url, local_file_path, filename):
            print(f"✓ Successfully downloaded: {filename}")
            download_results[filename] = True
        else:
            download_results[filename] = False

    return download_results


def main() -> None:
    """Main function to download data lake files."""
    S3_BUCKET_URL = "https://biomni-release.s3.amazonaws.com"
    # Get the project root directory (the parent of the 'scripts' directory)
    PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    LOCAL_DATA_LAKE_PATH = os.path.join(PROJECT_ROOT, "data")
    EXPECTED_FILES = list(data_lake_dict.keys())

    print(f"Project root detected: {PROJECT_ROOT}")
    print(f"Data lake path set to: {LOCAL_DATA_LAKE_PATH}")

    results = check_and_download_s3_files(
        S3_BUCKET_URL, LOCAL_DATA_LAKE_PATH, EXPECTED_FILES
    )

    # Print summary
    total_files = len(results)
    successful_downloads = sum(results.values())
    print(
        f"\nDownload Summary: {successful_downloads}/{total_files} files downloaded successfully"
    )

    if successful_downloads < total_files:
        failed_files = [f for f, success in results.items() if not success]
        print(f"Failed downloads: {', '.join(failed_files)}")


if __name__ == "__main__":
    main()
