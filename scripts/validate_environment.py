#!/usr/bin/env python3
"""
Biomni E1 Environment Validation Script

This script validates that the Biomni E1 environment is properly set up
by importing key bioinformatics packages and verifying their functionality.
"""

import sys
import os
import importlib
import subprocess
import logging
from typing import Dict, List, Tuple, Optional, Callable, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)

# Define packages to validate with their expected functionality
VALIDATION_PACKAGES: Dict[str, Dict[str, Any]] = {
    # Core bioinformatics packages
    "Bio": {
        "description": "Biopython - Bioinformatics tools",
        "test_import": "Bio.Seq",
        "test_function": lambda: str(
            __import__("Bio.Seq", fromlist=["Seq"]).Seq("ATCG").reverse_complement()
        ),
    },
    "scanpy": {
        "description": "Single-cell analysis in Python",
        "test_import": "scanpy",
        "test_function": lambda: importlib.import_module("scanpy").__version__,
    },
    "rdkit": {
        "description": "RDKit - Cheminformatics and machine learning",
        "test_import": "rdkit.Chem",
        "test_function": (
            lambda: __import__("rdkit.Chem", fromlist=["Chem"]).Chem.MolFromSmiles(
                "CCO"
            )
            is not None
        ),
    },
    # Core scientific packages
    "pandas": {
        "description": "Data manipulation and analysis",
        "test_import": "pandas",
        "test_function": (
            lambda: len(importlib.import_module("pandas").DataFrame({"A": [1, 2, 3]}))
            == 3
        ),
    },
    "numpy": {
        "description": "Numerical computing",
        "test_import": "numpy",
        "test_function": (
            lambda: importlib.import_module("numpy").array([1, 2, 3]).mean() == 2.0
        ),
    },
    "scipy": {
        "description": "Scientific computing",
        "test_import": "scipy",
        "test_function": (
            lambda: importlib.import_module("scipy").__version__ is not None
        ),
    },
    "matplotlib": {
        "description": "Plotting library",
        "test_import": "matplotlib.pyplot",
        "test_function": (
            lambda: importlib.import_module("matplotlib").__version__ is not None
        ),
    },
    "seaborn": {
        "description": "Statistical data visualization",
        "test_import": "seaborn",
        "test_function": (
            lambda: importlib.import_module("seaborn").__version__ is not None
        ),
    },
    "sklearn": {
        "description": "Machine learning library",
        "test_import": "sklearn",
        "test_function": (
            lambda: importlib.import_module("sklearn").__version__ is not None
        ),
    },
    "statsmodels": {
        "description": "Statistical modeling",
        "test_import": "statsmodels",
        "test_function": (
            lambda: importlib.import_module("statsmodels").__version__ is not None
        ),
    },
    # Development and testing tools
    "jupyter": {
        "description": "Jupyter notebook",
        "test_import": "jupyter",
        "test_function": (
            lambda: importlib.import_module("jupyter").__version__ is not None
        ),
    },
    "pytest": {
        "description": "Testing framework",
        "test_import": "pytest",
        "test_function": (
            lambda: importlib.import_module("pytest").__version__ is not None
        ),
    },
}


def validate_python_environment() -> Dict[str, bool]:
    """Validate Python packages in the current environment.

    Returns:
        Dict mapping package names to their validation status.
    """
    results: Dict[str, bool] = {}

    print("🔍 Validating Python Environment...")
    print("=" * 60)

    for package_name, config in VALIDATION_PACKAGES.items():
        print(f"Testing {package_name}: {config['description']}")

        try:
            # Test import
            importlib.import_module(config["test_import"])

            # Test basic functionality
            test_function: Optional[Callable] = config.get("test_function")
            if test_function:
                test_function()

            print(f"✅ {package_name} - OK")
            results[package_name] = True

        except ImportError as e:
            print(f"❌ {package_name} - Import Error: {e}")
            logger.debug(f"Import error details for {package_name}: {e}")
            results[package_name] = False
        except Exception as e:
            print(f"⚠️  {package_name} - Function Error: {e}")
            logger.debug(f"Function error details for {package_name}: {e}")
            results[package_name] = False

    return results


def validate_r_environment() -> bool:
    """Validate R environment and key packages."""
    print("\n🔍 Validating R Environment...")
    print("=" * 60)

    try:
        # Check if R is available
        result = subprocess.run(
            ["R", "--version"], capture_output=True, text=True, timeout=10
        )

        if result.returncode == 0:
            print("✅ R - Available")
            version_line = result.stdout.split("\n")[0]
            print(f"   Version: {version_line}")

            # Test basic R functionality
            r_test = subprocess.run(
                ["R", "--slave", "-e", "print('R is working')"],
                capture_output=True,
                text=True,
                timeout=10,
            )

            if r_test.returncode == 0:
                print("✅ R - Basic functionality OK")
                return True
            else:
                print(f"❌ R - Function test failed: {r_test.stderr}")
                return False
        else:
            print(f"❌ R - Not available: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ R - Timeout during validation")
        return False
    except FileNotFoundError:
        print("❌ R - R command not found in PATH")
        return False
    except Exception as e:
        print(f"❌ R - Unexpected error: {e}")
        return False


def validate_cli_tools() -> Dict[str, bool]:
    """Validate command-line bioinformatics tools.

    Returns:
        Dict mapping tool names to their validation status.
    """
    print("\n🔍 Validating CLI Tools...")
    print("=" * 60)

    cli_tools: Dict[str, str] = {
        "plink2": "PLINK 2.0 - Whole genome association analysis",
        "gcta64": "GCTA - Genome-wide complex trait analysis",
        "iqtree2": "IQ-TREE - Phylogenetic inference",
        "muscle": "MUSCLE - Multiple sequence alignment",
        "FastTree": "FastTree - Phylogenetic inference",
        "bwa": "BWA - Burrows-Wheeler Aligner",
    }

    results: Dict[str, bool] = {}

    # Check if setup_path.sh exists and source it for tool testing
    setup_path_script = os.path.join(
        os.path.dirname(__file__),
        "../biomni-toolkit/biomni_env/biomni_tools/setup_path.sh",
    )

    env = os.environ.copy()
    if os.path.exists(setup_path_script):
        print("📁 Found setup_path.sh - updating PATH for CLI tools")
        try:
            # Source the setup_path.sh script to get the correct PATH
            result = subprocess.run(
                f"source {setup_path_script} && echo $PATH",
                shell=True,
                capture_output=True,
                text=True,
                timeout=5,
            )
            if result.returncode == 0:
                new_path = result.stdout.strip()
                env["PATH"] = new_path
                print(f"✅ Updated PATH for CLI tool testing")
        except Exception as e:
            print(f"⚠️  Could not source setup_path.sh: {e}")

    for tool, description in cli_tools.items():
        print(f"Testing {tool}: {description}")

        try:
            # Test if tool is available with updated PATH
            result = subprocess.run(
                [tool, "--help"], capture_output=True, text=True, timeout=5, env=env
            )

            # Many tools return non-zero exit codes for --help
            if result.returncode in [0, 1]:
                print(f"✅ {tool} - Available")
                results[tool] = True
            else:
                print(
                    f"❌ {tool} - Not responding correctly (exit code: {result.returncode})"
                )
                logger.debug(f"Tool {tool} stderr: {result.stderr}")
                results[tool] = False

        except subprocess.TimeoutExpired:
            print(f"❌ {tool} - Timeout")
            results[tool] = False
        except FileNotFoundError:
            print(f"❌ {tool} - Not found in PATH")
            results[tool] = False
        except Exception as e:
            print(f"❌ {tool} - Error: {e}")
            logger.debug(f"Unexpected error for {tool}: {e}")
            results[tool] = False

    return results


def generate_validation_report(
    python_results: Dict[str, bool], r_result: bool, cli_results: Dict[str, bool]
) -> None:
    """Generate a comprehensive validation report."""
    print("\n📊 Validation Report")
    print("=" * 60)

    # Python packages summary
    python_passed = sum(python_results.values())
    python_total = len(python_results)
    print(f"Python Packages: {python_passed}/{python_total} passed")

    # R summary
    r_status = "✅ PASSED" if r_result else "❌ FAILED"
    print(f"R Environment: {r_status}")

    # CLI tools summary
    cli_passed = sum(cli_results.values())
    cli_total = len(cli_results)
    print(f"CLI Tools: {cli_passed}/{cli_total} passed")

    # Overall status
    overall_success = python_passed == python_total and r_result and cli_passed > 0

    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 VALIDATION SUCCESSFUL!")
        print("The Biomni E1 environment is properly configured.")
    else:
        print("⚠️  VALIDATION ISSUES DETECTED!")
        print("Some components may need attention.")

        # Show failed components
        failed_python = [k for k, v in python_results.items() if not v]
        failed_cli = [k for k, v in cli_results.items() if not v]

        if failed_python:
            print(f"\nFailed Python packages: {', '.join(failed_python)}")
        if not r_result:
            print("R environment validation failed")
        if failed_cli:
            print(f"Failed CLI tools: {', '.join(failed_cli)}")

    print("=" * 60)

    # Return appropriate exit code
    sys.exit(0 if overall_success else 1)


def main():
    """Main validation function."""
    print("🧬 Biomni E1 Environment Validation")
    print("=" * 60)
    print("This script validates the Biomni E1 bioinformatics environment")
    print("by testing key packages and tools.\n")

    # Validate Python environment
    python_results = validate_python_environment()

    # Validate R environment
    r_result = validate_r_environment()

    # Validate CLI tools
    cli_results = validate_cli_tools()

    # Generate final report
    generate_validation_report(python_results, r_result, cli_results)


if __name__ == "__main__":
    main()
