
# Title: WGCNA Network Construction for Breast Cancer and Normal Tissues
# Description: Performs WGCNA to construct gene co-expression networks for breast cancer and normal breast tissues.
# Created: 2025-08-04

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries
required_packages <- c("WGCNA", "flashClust", "dplyr")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

# Enable WGCNA threads for faster computation
enableWGCNAThreads()

cat("Starting WGCNA network construction...\n")

# Load processed data
expression_matrix <- readRDS("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/expression_matrix.rds")
sample_metadata <- readRDS("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/sample_metadata.rds")

cat("Data loaded for WGCNA. \n")

# Ensure expression matrix is numeric and free of NA/Inf values
expression_matrix[is.na(expression_matrix)] <- 0 # Simple NA handling, consider more sophisticated methods if NAs are prevalent
expression_matrix[!is.finite(expression_matrix)] <- 0

# Transpose the expression matrix for WGCNA (samples as rows, genes as columns)
dataExpr <- t(expression_matrix)

# Separate data by tissue type
normal_samples <- sample_metadata %>% filter(TissueType == "normal_breast_tissue") %>% pull(SampleID)
cancer_samples <- sample_metadata %>% filter(TissueType == "breast_cancer_tissue") %>% pull(SampleID)

dataExpr_normal <- dataExpr[normal_samples, ]
dataExpr_cancer <- dataExpr[cancer_samples, ]

cat("Data separated into normal (", nrow(dataExpr_normal), " samples) and cancer (", nrow(dataExpr_cancer), " samples) groups.\n")

# --- WGCNA for Normal Breast Tissue ---
cat("Performing WGCNA for Normal Breast Tissue...\n")

# For soft-thresholding, use a subset of genes to speed up computation
nGenes <- ncol(dataExpr_normal)
set.seed(12345) # for reproducibility
if (nGenes > 5000) {
  gene_subset_normal <- sample(nGenes, size = 5000)
  dataExpr_normal_subset <- dataExpr_normal[, gene_subset_normal]
} else {
  dataExpr_normal_subset <- dataExpr_normal
}

# Choose a set of soft-thresholding powers
powers_normal <- c(c(1:10), seq(from = 12, to = 20, by = 2))

# Call the network topology analysis function on the subset
sft_normal <- pickSoftThreshold(dataExpr_normal_subset, powerVector = powers_normal, verbose = 5, 
                                corFnc = "bicor")

# Plot the results: Scale-free topology fit index as a function of the soft-thresholding power
png("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/sft_plot_normal.png", width = 900, height = 500)
par(mfrow = c(1,2));
cex1 = 0.9;
# Scale-free topology fit index as a function of the soft-thresholding power
plot(sft_normal$fitIndices[,1], -sign(sft_normal$fitIndices[,3])*sft_normal$fitIndices[,2],
     xlab="Soft Threshold (power)",ylab="Scale Free Topology Model Fit,signed R^2",type="n",
     main = paste("Scale independence (Normal Tissue)"));
text(sft_normal$fitIndices[,1], -sign(sft_normal$fitIndices[,3])*sft_normal$fitIndices[,2],
     labels=powers_normal,cex=cex1,col="red");
# this line corresponds to using an R^2 cut-off of 0.90
abline(h=0.90,col="red")
# Mean connectivity as a function of the soft-thresholding power
plot(sft_normal$fitIndices[,1], sft_normal$fitIndices[,5],
     xlab="Soft Threshold (power)",ylab="Mean Connectivity", type="n",
     main = paste("Mean connectivity (Normal Tissue)"))
text(sft_normal$fitIndices[,1], sft_normal$fitIndices[,5], labels=powers_normal, cex=cex1,col="red")
dev.off()

# Select the soft-thresholding power (e.g., first power for which R^2 > 0.9)
softPower_normal <- sft_normal$powerEstimate
if (is.null(softPower_normal)) {
  # If no power satisfies R^2 > 0.9, try for R^2 > 0.8
  suitable_powers <- sft_normal$fitIndices[sft_normal$fitIndices[,2] > 0.8, 1]
  if (length(suitable_powers) > 0) {
    softPower_normal <- min(suitable_powers)
    cat("Warning: No power reached R^2 > 0.9 for normal tissue. Using smallest power = ", softPower_normal, " with R^2 > 0.8.\n")
  } else {
    # If still no suitable power, choose the one with the highest R^2
    softPower_normal <- sft_normal$fitIndices[which.max(sft_normal$fitIndices[,2]), 1]
    cat("Warning: No power reached R^2 > 0.8 for normal tissue. Using power = ", softPower_normal, " (max R^2).\n")
  }
}
cat("Soft-thresholding power for Normal Tissue: ", softPower_normal, "\n")

# Construct the adjacency matrix using the full dataset
adjacency_normal <- adjacency(dataExpr_normal, power = softPower_normal, 
                              corFnc = "bicor")

# Turn adjacency into a Topological Overlap Matrix (TOM)
TOM_normal <- TOMsimilarity(adjacency_normal, TOMType = "signed")
colnames(TOM_normal) <- rownames(TOM_normal) <- colnames(dataExpr_normal)

# Hierarchical clustering of the genes based on the TOM
dissTOM_normal <- 1 - TOM_normal
geneTree_normal <- flashClust(as.dist(dissTOM_normal), method = "average")

# Module detection using dynamic tree cut
minModuleSize <- 30
dynamicMods_normal <- cutreeDynamic(dendro = geneTree_normal, distM = dissTOM_normal,
                                    deepSplit = 2, pamRespectsDendro = FALSE,
                                    minClusterSize = minModuleSize)

# Assign colors to modules
dynamicColors_normal <- labels2colors(dynamicMods_normal)

cat("Normal Tissue WGCNA completed. Detected ", length(unique(dynamicColors_normal)) - 1, " modules (excluding grey).\n")

# Save WGCNA results for normal tissue
saveRDS(TOM_normal, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/TOM_normal.rds")
saveRDS(dynamicColors_normal, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/dynamicColors_normal.rds")
saveRDS(geneTree_normal, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/geneTree_normal.rds")

# --- WGCNA for Breast Cancer Tissue ---
cat("Performing WGCNA for Breast Cancer Tissue...\n")

# For soft-thresholding, use a subset of genes to speed up computation
nGenes <- ncol(dataExpr_cancer)
set.seed(12345) # for reproducibility
if (nGenes > 5000) {
  gene_subset_cancer <- sample(nGenes, size = 5000)
  dataExpr_cancer_subset <- dataExpr_cancer[, gene_subset_cancer]
} else {
  dataExpr_cancer_subset <- dataExpr_cancer
}

# Choose a set of soft-thresholding powers
powers_cancer <- c(c(1:10), seq(from = 12, to = 20, by = 2))

# Call the network topology analysis function on the subset
sft_cancer <- pickSoftThreshold(dataExpr_cancer_subset, powerVector = powers_cancer, verbose = 5,
                                corFnc = "bicor")

# Plot the results: Scale-free topology fit index as a function of the soft-thresholding power
png("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/sft_plot_cancer.png", width = 900, height = 500)
par(mfrow = c(1,2));
cex1 = 0.9;
# Scale-free topology fit index as a function of the soft-thresholding power
plot(sft_cancer$fitIndices[,1], -sign(sft_cancer$fitIndices[,3])*sft_cancer$fitIndices[,2],
     xlab="Soft Threshold (power)",ylab="Scale Free Topology Model Fit,signed R^2",type="n",
     main = paste("Scale independence (Cancer Tissue)"));
text(sft_cancer$fitIndices[,1], -sign(sft_cancer$fitIndices[,3])*sft_cancer$fitIndices[,2],
     labels=powers_cancer,cex=cex1,col="red");
# this line corresponds to using an R^2 cut-off of 0.90
abline(h=0.90,col="red")
# Mean connectivity as a function of the soft-thresholding power
plot(sft_cancer$fitIndices[,1], sft_cancer$fitIndices[,5],
     xlab="Soft Threshold (power)",ylab="Mean Connectivity", type="n",
     main = paste("Mean connectivity (Cancer Tissue)"))
text(sft_cancer$fitIndices[,1], sft_cancer$fitIndices[,5], labels=powers_cancer, cex=cex1,col="red")
dev.off()

# Select the soft-thresholding power
softPower_cancer <- sft_cancer$powerEstimate
if (is.null(softPower_cancer)) {
  suitable_powers <- sft_cancer$fitIndices[sft_cancer$fitIndices[,2] > 0.8, 1]
  if (length(suitable_powers) > 0) {
    softPower_cancer <- min(suitable_powers)
    cat("Warning: No power reached R^2 > 0.9 for cancer tissue. Using smallest power = ", softPower_cancer, " with R^2 > 0.8.\n")
  } else {
    softPower_cancer <- sft_cancer$fitIndices[which.max(sft_cancer$fitIndices[,2]), 1]
    cat("Warning: No power reached R^2 > 0.8 for cancer tissue. Using power = ", softPower_cancer, " (max R^2).\n")
  }
}
cat("Soft-thresholding power for Cancer Tissue: ", softPower_cancer, "\n")

# Construct the adjacency matrix using the full dataset
adjacency_cancer <- adjacency(dataExpr_cancer, power = softPower_cancer,
                              corFnc = "bicor")

# Turn adjacency into a Topological Overlap Matrix (TOM)
TOM_cancer <- TOMsimilarity(adjacency_cancer, TOMType = "signed")
colnames(TOM_cancer) <- rownames(TOM_cancer) <- colnames(dataExpr_cancer)

# Hierarchical clustering of the genes based on the TOM
dissTOM_cancer <- 1 - TOM_cancer
geneTree_cancer <- flashClust(as.dist(dissTOM_cancer), method = "average")

# Module detection using dynamic tree cut
dynamicMods_cancer <- cutreeDynamic(dendro = geneTree_cancer, distM = dissTOM_cancer,
                                    deepSplit = 2, pamRespectsDendro = FALSE,
                                    minClusterSize = minModuleSize)

# Assign colors to modules
dynamicColors_cancer <- labels2colors(dynamicMods_cancer)

cat("Cancer Tissue WGCNA completed. Detected ", length(unique(dynamicColors_cancer)) - 1, " modules (excluding grey).\n")

# Save WGCNA results for cancer tissue
saveRDS(TOM_cancer, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/TOM_cancer.rds")
saveRDS(dynamicColors_cancer, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/dynamicColors_cancer.rds")
saveRDS(geneTree_cancer, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/geneTree_cancer.rds")

cat("WGCNA network construction completed successfully for both normal and cancer tissues. Results saved to outputs directory.\n")
