
# Title: Quality Control and Normalization of Gene Expression Data
# Description: Performs initial quality control checks and normalization (if necessary) on the expression data.
# Created: 2025-08-04

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries
required_packages <- c("ggplot2", "reshape2", "limma", "RColorBrewer")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

cat("Starting Quality Control and Normalization...\n")

# Load processed data
expression_matrix <- readRDS("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/expression_matrix.rds")
sample_metadata <- readRDS("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/sample_metadata.rds")

cat("Data loaded for QC. \n")

# --- 1. Expression Distribution Visualization ---

# Convert matrix to data frame for ggplot2 and melt for box/density plots
expression_df <- as.data.frame(expression_matrix)
expression_df$Gene <- rownames(expression_df)
melted_expression_df <- melt(expression_df, id.vars = "Gene", variable.name = "SampleID", value.name = "Expression")

# Merge with sample metadata to get TissueType
melted_expression_df <- merge(melted_expression_df, sample_metadata, by = "SampleID")

cat("Generating box plots...\n")
# Box plots of expression values per sample
box_plot <- ggplot(melted_expression_df, aes(x = SampleID, y = Expression, fill = TissueType)) +
  geom_boxplot() +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 90, hjust = 1)) +
  labs(title = "Expression Distribution per Sample", y = "Expression Value", x = "Sample")
ggsave("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/expression_boxplot.png", plot = box_plot, width = 10, height = 7)

cat("Generating density plots...\n")
# Density plots of expression values
density_plot <- ggplot(melted_expression_df, aes(x = Expression, color = SampleID)) +
  geom_density(alpha = 0.7) +
  facet_wrap(~TissueType, scales = "free_y") +
  theme_minimal() +
  labs(title = "Expression Density per Sample", y = "Density", x = "Expression Value")
ggsave("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/expression_densityplot.png", plot = density_plot, width = 10, height = 7)

# --- 2. Principal Component Analysis (PCA) ---
cat("Performing PCA...\n")
# Transpose the matrix for PCA (samples as rows, genes as columns)
pca_data <- t(expression_matrix)

# Perform PCA
pca_result <- prcomp(pca_data, scale. = TRUE)

# Prepare data for PCA plot
pca_df <- as.data.frame(pca_result$x)
pca_df$SampleID <- rownames(pca_df)
pca_df <- merge(pca_df, sample_metadata, by = "SampleID")

# Calculate explained variance
variance_explained <- (pca_result$sdev^2) / sum(pca_result$sdev^2)

pca_plot <- ggplot(pca_df, aes(x = PC1, y = PC2, color = TissueType)) +
  geom_point(size = 3) +
  geom_text(aes(label = SampleID), vjust = -1, hjust = 0.5, size = 3) + # Add sample labels
  theme_minimal() +
  labs(title = "PCA of Gene Expression Data",
       x = paste0("PC1 (", round(variance_explained[1] * 100, 2), "% explained)"),
       y = paste0("PC2 (", round(variance_explained[2] * 100, 2), "% explained)")) +
  theme(legend.position = "bottom")
ggsave("/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/pca_plot.png", plot = pca_plot, width = 10, height = 8)

cat("Quality Control completed. Plots saved to outputs directory.\n")

# --- 3. Normalization (Placeholder) ---
# The initial inspection of the data (values like 12.0713) suggests it's already log-transformed and likely normalized.
# If QC plots indicate issues (e.g., widely varying distributions), normalization steps would be added here.
# For now, we assume the data is suitable for direct use in co-expression analysis.

# Example of quantile normalization if needed:
# normalized_expression_matrix <- normalizeBetweenArrays(expression_matrix, method = "quantile")
# saveRDS(normalized_expression_matrix, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/normalized_expression_matrix.rds")

cat("QC and potential normalization steps completed successfully.\n")
