
# Title: Load and Prepare Gene Expression Data
# Description: Loads the expression matrix, extracts sample metadata, and prepares data for differential network analysis.
# Created: 2025-08-04

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries
required_packages <- c("data.table", "dplyr")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

cat("Starting data loading and preparation...\n")

# Define the path to the expression data file
data_file_path <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/44620_GSE33447_expmat.data.txt"

# Read the data, skipping initial comment lines
# Determine the number of comment lines by reading the file once
header_lines <- readLines(data_file_path, n = 10)
skip_lines <- sum(grepl("^#", header_lines))

expression_data <- fread(data_file_path, sep = "\t", header = TRUE, skip = skip_lines)

cat("Data loaded successfully. Dimensions: ", dim(expression_data)[1], " rows, ", dim(expression_data)[2], " columns.\n")

# Extract sample names and tissue types from column headers
# Sample columns start from the 7th column (index 6 in 0-based, 7 in 1-based)
sample_columns_raw_names <- names(expression_data)[7:ncol(expression_data)]

# Extract tissue type from the sample names
# Example: GSE33447_Biomat_14___BioAssayId=1088953Name=Breastnon.cancertissueReplicate8
# We want "Breastnon.cancertissue" or "Breastcancertissue"
extracted_names <- sub(".*Name=(.*?)Replicate.*", "\\1", sample_columns_raw_names)
sample_tissue_types <- gsub("Breastnon.cancertissue", "normal_breast_tissue", extracted_names)
sample_tissue_types <- gsub("Breastcancertissue", "breast_cancer_tissue", sample_tissue_types)

# Create a sample metadata data frame
sample_metadata <- data.frame(
  SampleID = sample_columns_raw_names,
  TissueType = sample_tissue_types,
  stringsAsFactors = FALSE
)

cat("Sample metadata extracted:\n")
print(sample_metadata)

# Prepare the expression matrix
# Use GeneSymbol as row names and remove annotation columns
# Handle potential duplicate GeneSymbols by making them unique
expression_matrix <- as.data.frame(expression_data)
rownames(expression_matrix) <- make.unique(expression_matrix$GeneSymbol)

# Select only the expression columns
expression_matrix <- expression_matrix[, sample_columns_raw_names]

# Convert to numeric matrix
expression_matrix <- as.matrix(expression_matrix)

cat("Expression matrix prepared. Dimensions: ", dim(expression_matrix)[1], " rows, ", dim(expression_matrix)[2], " columns.\n")

# Save processed data for next steps
saveRDS(expression_matrix, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/expression_matrix.rds")
saveRDS(sample_metadata, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/sample_metadata.rds")

cat("Data loading and preparation completed successfully. Processed data saved to outputs directory.\n")
