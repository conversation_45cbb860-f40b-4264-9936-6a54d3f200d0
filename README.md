# Omiy

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)


Omiy is a sophisticated multi-agent framework built on LangGraph that orchestrates specialized AI agents to tackle complex tasks through collaborative problem-solving.

### Recent Major Updates

🚀 **Complete System Rework**: 
- **Enhanced State Management**: Improved checkpoint persistence and conversation threading
- **Streamlined Agent Architecture**: Refined agent roles and communication patterns
- **Docker Support**: Full containerization with docker-compose deployment
- **Robust Error Handling**: Better handling of state conflicts and edge cases
- **Improved API Design**: Updated endpoints with comprehensive request/response schemas
- **Unique Identifier System**: Critical implementation to prevent state conflicts

## Table of Contents
- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [Features](#features)
- [Why Omiy?](#why-Omiy)
- [Setup](#setup)
    - [Prerequisites](#prerequisites)
    - [Installation](#installation)
    - [Configuration](#configuration)
- [Usage](#usage)
- [Web UI](#web-ui)
- [Development](#development)
- [Contributing](#contributing)
- [License](#license)
- [Acknowledgments](#acknowledgments)

## Quick Start

```bash
# Clone the repository
git clone https://github.com/omiydev/multiagent_langgraph
cd multiagent_langgraph

# Create and activate virtual environment through uv
uv python install 3.12
uv venv --python 3.12

source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
uv sync

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Run the project
uv run main.py
```

## Architecture

Omiy implements a streamlined multi-agent system with the following key components:

![Omiy Architecture](./assets/agent_graph.png)

### Core Agents

- **Supervisor**: The central orchestrator that coordinates the team and delegates tasks by analyzing requests and determining which specialist should handle them. Makes decisions about task completion and workflow transitions.
- **Researcher**: Specializes in information gathering through web searches and data collection using Tavily search and web crawling capabilities.
- **Coder**: Professional software engineer focused on Python and bash scripting, handling code execution, analysis, and technical problem-solving.
- **File Manager**: Handles all file system operations with a focus on properly formatting and saving content in markdown format.
- **Browser**: Web interaction specialist that handles website navigation, page interaction (clicking, typing, scrolling), and content extraction.
- **Reporter**: Compiles results and generates comprehensive reports from the team's work.

### Key Features

- **LLM Integration**: Support for multiple language models (OpenAI, Anthropic, Google, etc.)
- **Search & Retrieval**: Integrated Tavily search and Jina reader for web content
- **Python Integration**: Secure code execution environment with comprehensive tooling
- **Workflow Management**: LangGraph-based state management with persistent checkpointing
- **Docker Support**: Full containerization for consistent deployment
- **State Persistence**: SQLite-based checkpoint system for conversation continuity

## Features

- **Multi-Agent Coordination**: Streamlined agent system with specialized roles and clear responsibilities
- **Persistent State Management**: SQLite-based checkpointing for conversation continuity across sessions
- **Unique Identifier System**: Robust handling of thread and run IDs to prevent state conflicts
- **Docker Deployment**: Full containerization with docker-compose for easy deployment
- **Enhanced Error Handling**: Improved resilience and debugging capabilities
- **Streaming API**: Real-time response streaming with Server-Sent Events (SSE)
- **Flexible Configuration**: Easy customization of agents, tools, and workflows
- **Time-Travel Debugging**: Advanced checkpoint management for exploring alternative conversation paths

## Time-Travel Functionality

Omiy includes powerful time-travel capabilities that allow you to explore alternative conversation paths, modify states at specific checkpoints, and resume execution from any point in the conversation history. This feature is built on LangGraph's checkpoint system and provides advanced debugging and experimentation capabilities.

### Key Time-Travel Features

- **Checkpoint Management**: Automatic creation and storage of conversation checkpoints
- **State Modification**: Ability to modify conversation state at any checkpoint
- **Alternative Path Exploration**: Create and explore different conversation branches
- **Timeline Visualization**: View the complete conversation timeline with all checkpoints
- **Resume from Any Point**: Continue execution from any previous checkpoint
- **Conversation History**: Access detailed history of all conversation states

### Time-Travel API

The time-travel functionality is exposed through the `TimeTravel` class in `src/service/time_travel_service.py`:

```python
from src.service.time_travel_service import create_time_travel_session

# Create a time-travel session for a conversation thread
time_travel = await create_time_travel_session(thread_id)

# List all checkpoints in the conversation
checkpoints = await time_travel.list_checkpoints()

# Get detailed information about a specific checkpoint
checkpoint_details = await time_travel.get_checkpoint(checkpoint_id)

# Modify state at a specific checkpoint
new_checkpoint = await time_travel.modify_state(checkpoint_id, new_state)

# Resume execution from a checkpoint
async for event in time_travel.resume_from(checkpoint_id):
    # Handle streaming events
    pass

# Explore alternative paths
async for event in time_travel.explore_alternative(checkpoint_id, modifications):
    # Handle alternative execution events
    pass

# Get conversation timeline
timeline = await time_travel.get_conversation_timeline()
```

### Example Usage

Omiy includes a comprehensive test demonstrating time-travel functionality:

```bash
# Run the time-travel demonstration
uv run tests/test_time_travel.py

# Run interactive time-travel session
uv run tests/test_time_travel.py --interactive
```

The example demonstrates:
- Running a workflow and capturing checkpoints
- Exploring conversation timeline
- Modifying state at specific points
- Resuming execution from alternative states
- Comparing different execution paths

### Persistence and Storage

Time-travel functionality leverages the same SQLite-based persistence system used for conversation checkpoints:

- **Database**: `data/checkpoints.db`
- **Checkpointer**: `AsyncSqliteSaver` for persistent storage
- **Fallback**: `MemorySaver` for development/testing
- **Thread-based**: Each conversation thread maintains its own checkpoint history

### Use Cases

1. **Debugging**: Examine conversation state at any point to understand agent behavior
2. **Experimentation**: Try different approaches by modifying state and resuming execution
3. **A/B Testing**: Compare different conversation paths and outcomes
4. **Recovery**: Resume conversations from specific points after interruptions
5. **Analysis**: Study agent decision-making patterns across conversation history

### Technical Implementation

The time-travel system is built on several key components:

- **Persistence Service** (`src/service/persistence_service.py`): Core checkpoint management
- **Time Travel Service** (`src/service/time_travel_service.py`): High-level time-travel API
- **LangGraph Integration**: Built on LangGraph's native checkpoint system
- **Async Support**: Full async/await support for streaming operations

## Why Omiy?

We believe in the power of open source collaboration. This project wouldn't be possible without the amazing work of projects like:
- [Qwen](https://github.com/QwenLM/Qwen) for their open source LLMs
- [Tavily](https://tavily.com/) for search capabilities
- [Jina](https://jina.ai/) for neural search technology
- And many other open source contributors

We're committed to giving back to the community and welcome contributions of all kinds - whether it's code, documentation, bug reports, or feature suggestions.

## Setup

### Prerequisites

- [uv](https://github.com/astral-sh/uv) package manager

### Installation

Omiy leverages [uv](https://github.com/astral-sh/uv) as its package manager to streamline dependency management.
Follow the steps below to set up a virtual environment and install the necessary dependencies:

```bash
# Step 1: Create and activate a virtual environment through uv
uv python install 3.12
uv venv --python 3.12

source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Step 2: Install project dependencies
uv sync
```

By completing these steps, you'll ensure your environment is properly configured and ready for development.

### Configuration

Omiy uses a three-tier LLM system with separate configurations for reasoning, basic tasks, and vision-language tasks. Create a `.env` file in the project root and configure the following environment variables:

```ini
# Reasoning LLM Configuration (for complex reasoning tasks)
REASONING_MODEL=your_reasoning_model
REASONING_API_KEY=your_reasoning_api_key
REASONING_BASE_URL=your_custom_base_url  # Optional

# Basic LLM Configuration (for simpler tasks)
BASIC_MODEL=your_basic_model
BASIC_API_KEY=your_basic_api_key
BASIC_BASE_URL=your_custom_base_url  # Optional

# Vision-Language LLM Configuration (for tasks involving images)
VL_MODEL=your_vl_model
VL_API_KEY=your_vl_api_key
VL_BASE_URL=your_custom_base_url  # Optional

# Tool API Keys
TAVILY_API_KEY=your_tavily_api_key
JINA_API_KEY=your_jina_api_key  # Optional

# Browser Configuration
CHROME_INSTANCE_PATH=/Applications/Google Chrome.app/Contents/MacOS/Google Chrome  # Optional, path to Chrome executable
```

> **Note:**
>
> - The system uses different models for different types of tasks:
>     - Reasoning LLM for complex decision-making and analysis
>     - Basic LLM for simpler text-based tasks
>     - Vision-Language LLM for tasks involving image understanding
> - You can customize the base URLs for all LLMs independently
> - Each LLM can use different API keys if needed
> - Jina API key is optional. Provide your own key to access a higher rate limit (get your API key at [jina.ai](https://jina.ai/))
> - Tavily search is configured to return a maximum of 5 results by default (get your API key at [app.tavily.com](https://app.tavily.com/))

You can copy the `.env.example` file as a template to get started:

```bash
cp .env.example .env
```

### R Environment Integration

Omiy is configured to seamlessly integrate with an R environment for bioinformatics tasks. The system automatically detects whether it's running in a Docker container or locally and adjusts the R environment path accordingly.

**For Dockerized Deployment:**

The Docker image (`Dockerfile`) now includes a pre-installed R environment with essential BiocManager packages. This ensures that every time a new container is spawned, the R environment is ready for use without any additional setup.

*   **R Environment Path (inside Docker):** `/opt/conda/envs/r-env/`
*   **Included Packages:** `GEOquery`, `limma`, `clusterProfiler`, `org.Hs.eg.db`, `enrichplot`, `pheatmap`

Your colleagues do not need to perform any manual R environment setup when deploying the Dockerized version. The `Dockerfile` handles everything.

**For Local Development:**

If you are running Omiy locally (e.g., using `uv run server.py`), you will need to have an R environment set up on your machine. The system will look for the `R_ENV_PATH` environment variable in your `.env` file.

1.  **Install Miniconda/Mamba:** If you don't have it, install Miniconda or Mamba.
2.  **Create R Environment:** Create a conda environment with R and the necessary packages.
    ```bash
    conda create -n r-env -c conda-forge r-base r-essentials r-biocmanager -y
    conda activate r-env
    R -e "BiocManager::install(c('GEOquery', 'limma', 'clusterProfiler', 'org.Hs.eg.db', 'enrichplot', 'pheatmap'))"
    ```
3.  **Set `R_ENV_PATH` in `.env`:** In your local `.env` file, set `R_ENV_PATH` to the absolute path of your R environment.
    ```ini
    R_ENV_PATH=/path/to/your/conda/envs/r-env/
    ```
    (Replace `/path/to/your/conda/envs/r-env/` with the actual path on your system, e.g., `/Users/<USER>/opt/anaconda3/envs/r-env/`)

This setup ensures that the R environment is consistently managed across both development and deployment environments.

### Configure Pre-commit Hook
Omiy includes a pre-commit hook that runs linting and formatting checks before each commit. To set it up:

1. Make the pre-commit script executable:
```bash
chmod +x pre-commit
```

2. Install the pre-commit hook:
```bash
ln -s ../../pre-commit .git/hooks/pre-commit
```

The pre-commit hook will automatically:
- Run linting checks (`make lint`)
- Run code formatting (`make format`)
- Add any reformatted files back to staging
- Prevent commits if there are any linting or formatting errors

## Usage

### Basic Execution

To run Omiy with default settings:

```bash
uv run main.py
```

### API Server

Omiy provides a FastAPI-based API server with streaming support and persistent conversation management:

```bash
# Start the API server
make serve

# Or run directly
uv run server.py

# For Docker deployment
docker-compose up --build
```

The API server exposes the following endpoints:

- `POST /`: Main workflow endpoint with streaming support
    - Request body:
    ```json
    {
      "threadId": "unique_thread_id",
      "runId": "unique_run_id", 
      "state": {
        "TEAM_MEMBERS": ["researcher", "coder", "browser", "reporter"],
        "deep_thinking_mode": true,
        "search_before_planning": true
      },
      "messages": [{"id": "1", "role": "user", "content": "Your query here"}],
      "tools": [],
      "context": [],
      "forwardedProps": {}
    }
    ```
    - Returns a Server-Sent Events (SSE) stream with the agent's responses

- `POST /awp`: Alternative workflow endpoint with simplified request format
    - Request body:
    ```json
    {
      "thread_id": "unique_thread_id",
      "run_id": "unique_run_id",
      "messages": [{"id": "1", "role": "user", "content": "Your query here"}],
      "state": {}
    }
    ```
    - Returns a Server-Sent Events (SSE) stream with the agent's responses
    - Simplified alternative to the main endpoint with snake_case parameter names

#### Important: Using Unique Identifiers

**Critical for proper operation**: Always use unique `threadId` and `runId` values for each request to avoid checkpoint state conflicts. Reusing the same identifiers can cause the "Expected string or list of content parts, got: null" error due to persisted state conflicts.

**Recommended testing approach**:

**Using the main endpoint (`POST /`)**:
```bash
# Use timestamp-based unique identifiers
curl -X POST http://localhost:8000/ \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "threadId": "test_thread_'$(date +%s)'",
    "runId": "test_run_'$(date +%s)'",
    "state": {
      "TEAM_MEMBERS": ["researcher", "coder", "browser", "reporter"],
      "deep_thinking_mode": true,
      "search_before_planning": true
    },
    "messages": [{"id": "1", "role": "user", "content": "create a file called happy, use the bash_tool"}],
    "tools": [],
    "context": [],
    "forwardedProps": {}
  }'
```

**Using the simplified endpoint (`POST /awp`)**:
```bash
# Simplified request format with snake_case parameters
curl -X POST http://localhost:8000/awp \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "thread_id": "test_thread_'$(date +%s)'",
    "run_id": "test_run_'$(date +%s)'",
    "messages": [{
      "id": "1",
      "role": "user",
      "content": "create a file called happy.txt with ASCII art"
    }]
  }'
```

**Alternative for development**: Clear checkpoint database between tests:
```bash
# Remove checkpoint files to reset state
rm -f data/checkpoints.db*
```

## Troubleshooting

### Common Issues

#### "Expected string or list of content parts, got: null" Error

This error typically occurs when reusing the same `threadId` and `runId` values, causing checkpoint state conflicts.

**Solutions**:
1. **Use unique identifiers** (recommended):
   ```bash
   # Generate timestamp-based unique IDs
   THREAD_ID="thread_$(date +%s)_$$"
   RUN_ID="run_$(date +%s)_$$"
   ```

2. **Clear checkpoint database**:
   ```bash
   # Stop the server first
   rm -f data/checkpoints.db*
   # Restart the server
   ```

3. **Use memory checkpointer for testing**:
   Set environment variable `SQLITE_DB_PATH=""` to disable SQLite persistence.

#### Docker Issues

- **Permission errors**: Ensure the `workspace` and `data` directories have proper permissions:
  ```bash
  chmod 755 workspace data
  ```

- **Environment variables**: Verify all required API keys are set in your `.env` file or docker-compose environment.

### Advanced Configuration

Omiy can be customized through various configuration files in the `src/config` directory:
- `env.py`: Configure LLM models, API keys, and base URLs
- `tools.py`: Adjust tool-specific settings (e.g., Tavily search results limit)
- `agents.py`: Modify team composition and agent system prompts

### Agent Prompts System

Omiy uses a sophisticated prompting system in the `src/prompts` directory to define agent behaviors and responsibilities:

#### Core Agent Roles

- **Supervisor ([`src/prompts/supervisor.md`](src/prompts/supervisor.md))**: Coordinates the team and delegates tasks by analyzing requests and determining which specialist should handle them. Makes decisions about task completion and workflow transitions.

- **Researcher ([`src/prompts/researcher.md`](src/prompts/researcher.md))**: Specializes in information gathering through web searches and data collection. Uses Tavily search and web crawling capabilities while avoiding mathematical computations or file operations.

- **Coder ([`src/prompts/coder.md`](src/prompts/coder.md))**: Professional software engineer role focused on Python and bash scripting. Handles:
    - Python code execution and analysis
    - Shell command execution
    - Technical problem-solving and implementation

- **File Manager ([`src/prompts/file_manager.md`](src/prompts/file_manager.md))**: Handles all file system operations with a focus on properly formatting and saving content in markdown format.

- **Browser ([`src/prompts/browser.md`](src/prompts/browser.md))**: Web interaction specialist that handles:
    - Website navigation
    - Page interaction (clicking, typing, scrolling)
    - Content extraction from web pages

#### Prompt System Architecture

The prompts system uses a template engine ([`src/prompts/template.py`](src/prompts/template.py)) that:
- Loads role-specific markdown templates
- Handles variable substitution (e.g., current time, team member information)
- Formats system prompts for each agent

Each agent's prompt is defined in a separate markdown file, making it easy to modify behavior and responsibilities without changing the underlying code.


## Development

### Testing

Run the test suite:

```bash
# Run all tests
make test

# Run specific test file
pytest tests/integration/test_workflow.py

# Run with coverage
make coverage
```

### Code Quality

```bash
# Run linting
make lint

# Format code
make format
```

