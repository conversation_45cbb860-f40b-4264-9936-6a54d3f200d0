# R Environment Setup for Multi-Agent Bioinformatics System

## Overview

Your multi-agent system now has a dedicated R execution environment that runs independently from the main Python application. This setup ensures clean separation between the system runtime and the code execution environment.

## Environment Configuration

### Current Setup
- **Conda Environment**: `r-env` (located at `/Users/<USER>/opt/anaconda3/envs/r-env/`)
- **Environment Variable**: `R_CONDA_ENV=r-env` in `.env` file
- **R Version**: 4.3.3 (confirmed working)

### Environment Variables in `.env`
```bash
# R Environment Configuration
R_CONDA_ENV=r-env
R_ENV_PATH=/Users/<USER>/opt/anaconda3/envs/r-env/
```

## R Executor Integration

The system automatically uses your conda environment through:

1. **Environment Variable Detection**: The `RExecutor` class reads `R_CONDA_ENV` from your `.env` file
2. **Conda Integration**: All R scripts are executed using `conda run -n r-env Rscript`
3. **Automatic Package Management**: R packages are installed and managed within the isolated environment

### Key Features
- ✅ **Isolated Environment**: R execution doesn't interfere with your main system
- ✅ **Automatic Configuration**: Uses environment variables for seamless integration
- ✅ **Package Management**: Comprehensive bioinformatics package installation
- ✅ **Error Handling**: Robust error recovery and alternative approaches
- ✅ **Production Ready**: Suitable for real bioinformatics analyses

## Installation & Setup

### 1. Install Essential R Packages
Run the setup script to install core bioinformatics packages:

```bash
# From the project root directory
python scripts/setup_r_environment.py
```

This will install:
- **CRAN packages**: ggplot2, dplyr, tidyr, pheatmap, RColorBrewer
- **Bioconductor packages**: BiocManager, limma, Biobase, BiocGenerics

### 2. Verify Installation
The setup script automatically tests the installation. You should see:
```
🎉 Environment setup successful! All key packages available.
R version: R version 4.3.3 (2024-02-29)
✅ ggplot2
✅ dplyr
✅ limma
... etc
```

### 3. Test R Executor
You can test the R executor directly:

```python
from src.tools.r_executor import get_r_executor

r_executor = get_r_executor()
success, output = r_executor.execute_script('print("Hello from R!")', "Test")
print(f"Success: {success}")
print(f"Output: {output}")
```

## Usage in Multi-Agent System

### For Coder Agent
The coder agent now has access to the R executor for bioinformatics analyses:

```python
from src.tools.r_executor import get_r_executor

# Get R executor configured for conda environment
r_executor = get_r_executor()

# Execute R scripts for analysis
success, output = r_executor.execute_script(r_script_content, "Analysis Script")
```

### For Bioinformatics Workflows
The system includes pre-built workflows for common analyses:

1. **Microarray Analysis**: `r_executor.execute_microarray_analysis()`
2. **Package Installation**: `r_executor.install_packages()`
3. **Environment Setup**: `r_executor.create_bioinformatics_environment()`

## Adding New R Packages

### Method 1: Via Setup Script
Add packages to `scripts/setup_r_environment.py` and re-run it.

### Method 2: Direct Installation in Conda Environment
```bash
# Activate the environment
conda activate r-env

# Start R and install packages
R
> install.packages("new_package")
> BiocManager::install("bioc_package")
```

### Method 3: Programmatically via R Executor
```python
r_executor = get_r_executor()
r_executor.install_packages(["new_package"], bioconductor=False)  # CRAN
r_executor.install_packages(["bioc_package"], bioconductor=True)  # Bioconductor
```

## Architecture Benefits

### Separation of Concerns
- **Main System**: Runs in your default Python environment
- **Code Execution**: Runs in dedicated `r-env` conda environment
- **No Conflicts**: R dependencies don't interfere with system dependencies

### Flexibility
- **Any R Version**: Can upgrade R without affecting the main system
- **Package Isolation**: Bioinformatics packages contained in their own space
- **Easy Reset**: Can recreate the environment if needed

### Production Ready
- **Reproducible**: Environment can be recreated on any system
- **Scalable**: Multiple environments for different project types
- **Maintainable**: Clear separation makes debugging easier

## Testing the Complete System

The enhanced multi-agent system was successfully tested with:
- ✅ Research-driven approach (no hardcoded templates)
- ✅ Dynamic human-in-the-loop confirmation
- ✅ Enhanced CLI with confirmation detection
- ✅ Adaptive workflow generation for bioinformatics
- ✅ Error recovery with alternative solutions
- ✅ R integration via conda environments

Your system is now ready for production bioinformatics analyses!