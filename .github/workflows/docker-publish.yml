name: Docker Build and Publish

on:
  push:
    branches: [ dev, prod ]

jobs:
  build_and_push:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write  # Includes read access for pulling private base images

    steps:
      - uses: actions/checkout@v4

      # Free up disk space on GitHub Actions runner
      - name: Free disk space
        run: |
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /usr/local/lib/android
          sudo rm -rf /opt/ghc
          sudo rm -rf /opt/hostedtoolcache/CodeQL
          sudo docker system prune -af
          df -h

      # 1️⃣ Enable CPU emulation so we can build arm64 on the x86 runner  
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Log in to GHCR
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata (tags & labels)
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository_owner }}/multiagent-langgraph
          tags: type=ref,event=branch

      # 2️⃣ Build **both** platforms: linux/amd64 (default) **and** linux/arm64/v8
      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.server
          push: true
          platforms: linux/amd64,linux/arm64/v8
          build-args: |
            BUILD_MODE=${{ github.ref_name }}
            GIT_SHA=${{ github.sha }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
