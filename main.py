"""
Entry point script for the LangGraph Demo.
"""

import asyncio
import uuid
from src.workflow import run_agent_workflow


async def main():
    import sys

    if len(sys.argv) > 1:
        user_query = " ".join(sys.argv[1:])
    else:
        user_query = input("Enter your query: ")

    # Generate unique thread_id for this conversation
    thread_id = str(uuid.uuid4())
    print(f"🧵 Thread ID: {thread_id}")
    print(f"📝 Query: {user_query}")
    print("🚀 Starting workflow...\n")

    # Consume the event stream
    final_state = None
    async for event in run_agent_workflow(
        user_input=user_query, thread_id=thread_id, debug=True
    ):
        # Print events as they come
        if event.get("type") == "agent" and event.get("data"):
            data = event["data"]
            if "messages" in data:
                print(
                    f"🤖 {event.get('agent', 'Unknown')}: {data['messages'][-1].content[:100]}..."
                )
        elif event.get("type") == "final":
            final_state = event.get("data")
            print("\n✅ Workflow completed!")

    return final_state


if __name__ == "__main__":
    result = asyncio.run(main())

    # Print the conversation history
    print("\n=== Conversation History ===")
    for message in result["messages"]:
        role = message.type
        print(f"\n[{role.upper()}]: {message.content}")
