import logging
import os
from langgraph.prebuilt import create_react_agent

from src.prompts import apply_prompt_template
from src.tools import (
    browser_tool,
    crawl_tool,
    python_repl_tool,
    tavily_tool,
    r_tools,
)
from src.tools.mcp_tools import def_mcp_tools
from src.config.mcp_mapping import AGENT_MCP_MAPPING
from src.tools.file_management import native_file_tools

from .llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP
from langchain_community.tools import ShellTool


def _prioritize_tools_by_supervisor_context(
    tools: list, supervisor_context: str, agent_name: str
) -> list:
    """Prioritize tools based on supervisor-provided filtered tools context.

    This function reorders the tools list to prioritize tools mentioned in the supervisor context,
    while keeping all available tools accessible to the agent.

    Args:
        tools: List of available tools for the agent
        supervisor_context: String containing supervisor-selected tools information
        agent_name: Name of the agent (for logging)

    Returns:
        Reordered list of tools with supervisor-mentioned tools prioritized
    """
    logger = logging.getLogger(__name__)

    if not supervisor_context or not tools:
        return tools

    # Extract tool names mentioned in supervisor context (case-insensitive)
    context_lower = supervisor_context.lower()
    prioritized_tools = []
    remaining_tools = []

    for tool in tools:
        tool_name_lower = tool.name.lower()
        # Check if tool name is mentioned in supervisor context
        if tool_name_lower in context_lower:
            prioritized_tools.append(tool)
            logger.debug(
                f"Prioritizing tool '{tool.name}' for {agent_name} (found in supervisor context)"
            )
        else:
            remaining_tools.append(tool)

    # Return prioritized tools first, then remaining tools
    final_tools = prioritized_tools + remaining_tools

    if prioritized_tools:
        logger.info(
            f"Prioritized {len(prioritized_tools)} supervisor-selected tools for {agent_name}"
        )

    return final_tools


def get_mcp_tools_for_agent(agent_name: str, supervisor_filtered_tools: str = None):
    """Get MCP tools filtered for a specific agent with graceful degradation.

    Uses essential tools filtering with fallback to reliable servers when external servers fail.
    If supervisor_filtered_tools is provided, prioritizes tools mentioned in that context.

    Args:
        agent_name: Name of the agent to get tools for
        supervisor_filtered_tools: Optional string containing supervisor-selected tools context
    """
    logger = logging.getLogger(__name__)

    try:
        # Import server reliability functions
        from ..config.mcp_mapping import get_agent_servers, should_use_external_servers

        # Get servers for this agent, with potential fallback to reliable servers
        use_fallback = not should_use_external_servers()
        allowed_servers = get_agent_servers(agent_name, use_fallback=use_fallback)

        if not allowed_servers:
            logger.warning(f"No servers configured for agent: {agent_name}")
            return []

        # Try essential tools filtering first for maximum efficiency
        from ..tools.mcp_tools import _tool_manager

        essential_tools = _tool_manager.get_essential_tools_for_agent(agent_name)

        if essential_tools:
            # Filter essential tools to only include those from available servers
            filtered_essential_tools = []
            for tool in essential_tools:
                # This is a heuristic - in a real implementation you'd have better server-tool mapping
                tool_name = tool.name.lower()
                tool_allowed = False

                for server in allowed_servers:
                    if server == "desktop-commander" and any(
                        pattern in tool_name
                        for pattern in [
                            "read_",
                            "write_",
                            "create_",
                            "list_",
                            "execute_",
                            "search_",
                        ]
                    ):
                        tool_allowed = True
                        break
                    elif server == "context7" and (
                        "resolve" in tool_name or "library" in tool_name
                    ):
                        tool_allowed = True
                        break
                    elif server == "fetch" and tool_name == "fetch":
                        tool_allowed = True
                        break

                if tool_allowed:
                    filtered_essential_tools.append(tool)

            logger.info(
                f"Agent '{agent_name}' loaded {len(filtered_essential_tools)} essential tools from servers: {allowed_servers}"
            )
            logger.debug(
                f"Essential tools: {[tool.name for tool in filtered_essential_tools]}"
            )

            # Apply supervisor filtering if provided
            if supervisor_filtered_tools:
                prioritized_tools = _prioritize_tools_by_supervisor_context(
                    filtered_essential_tools, supervisor_filtered_tools, agent_name
                )
                return prioritized_tools

            return filtered_essential_tools
        else:
            # Fallback to server-based filtering
            filtered_tools = def_mcp_tools(server_names=allowed_servers)
            logger.info(
                f"Agent '{agent_name}' loaded {len(filtered_tools)} fallback tools from servers: {allowed_servers}"
            )

            # Apply supervisor filtering if provided
            if supervisor_filtered_tools:
                prioritized_tools = _prioritize_tools_by_supervisor_context(
                    filtered_tools, supervisor_filtered_tools, agent_name
                )
                return prioritized_tools

            return filtered_tools

    except Exception as e:
        logger.error(f"Failed to load MCP tools for {agent_name}: {e}")
        # Try to return minimal tools for basic functionality
        try:
            from ..config.mcp_mapping import get_agent_servers

            fallback_servers = get_agent_servers(agent_name, use_fallback=True)
            if fallback_servers:
                minimal_tools = def_mcp_tools(server_names=fallback_servers)
                logger.warning(
                    f"Using minimal fallback tools for {agent_name}: {len(minimal_tools)} tools"
                )
                return minimal_tools
        except Exception:
            pass
        return []


def configure_desktop_commander_if_available():
    """Configure Desktop Commander with restricted directory access - optimized batch configuration."""
    logger = logging.getLogger(__name__)

    # Import here to avoid circular imports
    from ..config.env import WORKSPACE_DIR, IS_DOCKER
    from ..tools.mcp_tools import _tool_manager

    try:
        # Use existing cached tools to avoid creating new connections
        config_tool = None
        if _tool_manager.all_tools_cache:
            # Find config tool from already loaded tools
            config_tool = next(
                (
                    tool
                    for tool in _tool_manager.all_tools_cache
                    if tool.name == "set_config_value"
                ),
                None,
            )

        if not config_tool:
            # Fallback: get desktop-commander tools if not cached yet
            desktop_tools = def_mcp_tools(server_names=["desktop-commander"])
            config_tool = next(
                (tool for tool in desktop_tools if tool.name == "set_config_value"),
                None,
            )

        if config_tool:
            try:
                workspace_path = os.path.abspath(WORKSPACE_DIR)

                # Batch configuration: both allowedDirectories and workingDirectory in sequence
                # This reuses the same MCP connection instead of creating new ones
                logger.debug("Configuring Desktop Commander with batched settings...")

                config_tool.invoke(
                    {"key": "allowedDirectories", "value": [workspace_path]}
                )

                config_tool.invoke({"key": "workingDirectory", "value": workspace_path})

                logger.info(f"Desktop Commander configured: workspace={workspace_path}")

                # Additional Docker-specific configuration
                if IS_DOCKER:
                    logger.debug("Desktop Commander configured for Docker environment")

            except Exception as e:
                logger.warning(f"Failed to configure Desktop Commander: {e}")
        else:
            logger.debug("Desktop Commander set_config_value tool not found")

    except Exception as e:
        logger.error(f"Error during Desktop Commander configuration: {e}")


shell_tool = ShellTool()

import asyncio
import concurrent.futures


def _create_agent_tools(
    agent_name: str,
    additional_tools: list = None,
    supervisor_filtered_tools: str = None,
):
    """Helper function to create tools for an agent.

    Args:
        agent_name: Name of the agent
        additional_tools: Additional tools specific to the agent
        supervisor_filtered_tools: Optional supervisor-provided tool filtering context
    """
    mcp_tools = get_mcp_tools_for_agent(agent_name, supervisor_filtered_tools)

    # Add biomni tools for agents that can benefit from them
    biomni_tools = []
    try:
        from ..tools.biomni_tools import get_biomni_tools_for_agent

        biomni_tools = get_biomni_tools_for_agent(agent_name)
        if biomni_tools:
            logger.info(f"Added {len(biomni_tools)} biomni tools to agent {agent_name}")
            logger.debug(
                f"Biomni tools for {agent_name}: {[t.name for t in biomni_tools]}"
            )
    except Exception as e:
        logger.warning(f"Could not load biomni tools for agent {agent_name}: {e}")
        biomni_tools = []

    # Combine all tools
    all_tools = []
    if additional_tools:
        all_tools.extend(additional_tools)
    all_tools.extend(mcp_tools)
    all_tools.extend(biomni_tools)

    return all_tools


def _create_single_agent(agent_config):
    """Helper function to create a single agent."""
    agent_name, llm_type, additional_tools = agent_config

    tools = _create_agent_tools(agent_name, additional_tools)

    agent = create_react_agent(
        get_llm_by_type(llm_type),
        tools=tools,
        prompt=lambda state: apply_prompt_template(agent_name, state),
    )

    return agent_name, agent, tools


# Configure Desktop Commander first (before agent creation)
configure_desktop_commander_if_available()

# Define agent configurations for parallel initialization
agent_configs = [
    ("researcher", AGENT_LLM_MAP["researcher"], [tavily_tool, crawl_tool]),
    ("coder", AGENT_LLM_MAP["coder"], r_tools + [python_repl_tool]),
    ("browser", AGENT_LLM_MAP["browser"], [browser_tool]),
    ("reporter", AGENT_LLM_MAP["reporter"], []),
    ("execution_evaluator", AGENT_LLM_MAP["execution_evaluator"], []),
]

logger = logging.getLogger(__name__)
logger.info("Initializing agents in parallel...")

# Initialize agents in parallel using ThreadPoolExecutor
with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
    # Submit all agent creation tasks
    future_to_agent = {
        executor.submit(_create_single_agent, config): config[0]
        for config in agent_configs
    }

    # Collect results
    agents = {}
    agent_tools = {}

    for future in concurrent.futures.as_completed(future_to_agent):
        agent_name = future_to_agent[future]
        try:
            name, agent, tools = future.result()
            agents[name] = agent
            agent_tools[name] = tools
            logger.debug(f"Agent '{name}' created with {len(tools)} tools")
        except Exception as e:
            logger.error(f"Failed to create agent '{agent_name}': {e}")

# Extract individual agents
research_agent = agents.get("researcher")
coder_agent = agents.get("coder")
browser_agent = agents.get("browser")
reporter_agent = agents.get("reporter")
execution_evaluator_agent = agents.get("execution_evaluator")


# Extract tool lists for logging
researcher_tools = agent_tools.get("researcher", [])
coder_tools = agent_tools.get("coder", [])
browser_tools = agent_tools.get("browser", [])
reporter_tools = agent_tools.get("reporter", [])
execution_evaluator_tools = agent_tools.get("execution_evaluator", [])

# Log completion (reduced verbosity)
total_tools = (
    len(researcher_tools)
    + len(coder_tools)
    + len(browser_tools)
    + len(reporter_tools)
    + len(execution_evaluator_tools)
)
logger.info(
    f"✅ All agents initialized ({total_tools} total tools) - Researcher: {len(researcher_tools)}, Coder: {len(coder_tools)}, Browser: {len(browser_tools)}, Reporter: {len(reporter_tools)}, Execution Evaluator: {len(execution_evaluator_tools)}"
)
logger.debug(f"Researcher tools: {[tool.name for tool in researcher_tools]}")
logger.debug(f"Coder tools: {[tool.name for tool in coder_tools]}")
logger.debug(f"Browser tools: {[tool.name for tool in browser_tools]}")
logger.debug(f"Reporter tools: {[tool.name for tool in reporter_tools]}")
logger.debug(
    f"Execution Evaluator tools: {[tool.name for tool in execution_evaluator_tools]}"
)
