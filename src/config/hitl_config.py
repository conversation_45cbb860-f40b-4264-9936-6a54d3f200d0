"""
Human-in-the-loop (HITL) configuration for multi-agent workflow management.

This module provides configurable HITL modes, risk assessment settings, and interrupt
strategies for balancing automation with human oversight in bioinformatics workflows.
"""

import os
from typing import Dict, Any, Optional, List, Set
from enum import Enum
from dataclasses import dataclass, field


class HITLMode(Enum):
    """Available human-in-the-loop operational modes."""
    
    FULL_MANUAL = "full_manual"        # Human approval required for every action
    SEMI_AUTONOMOUS = "semi_autonomous"  # Risk-based intervention
    FULLY_AUTONOMOUS = "fully_autonomous"  # No interruptions, full automation


class RiskLevel(Enum):
    """Risk assessment levels for semi-autonomous decision making."""
    
    LOW = "low"           # Safe operations, proceed automatically
    MEDIUM = "medium"     # Moderate risk, may require human review
    HIGH = "high"         # High-stakes operations, require human approval
    CRITICAL = "critical" # Critical operations, always require approval


class InterruptTrigger(Enum):
    """Types of events that can trigger human intervention."""
    
    TOOL_EXECUTION = "tool_execution"        # Before executing tools
    LLM_GENERATION = "llm_generation"        # Before LLM content generation
    STATE_TRANSITION = "state_transition"    # Before moving to next step
    ERROR_RECOVERY = "error_recovery"        # When errors occur
    DATA_MODIFICATION = "data_modification"  # Before modifying data/files
    EXTERNAL_API = "external_api"            # Before external API calls
    PLAN_CHANGES = "plan_changes"            # When modifying execution plan


@dataclass
class HITLTriggerConfig:
    """Configuration for when to trigger human intervention."""
    
    # Triggers that always require approval in manual mode
    manual_mode_triggers: Set[InterruptTrigger] = field(default_factory=lambda: {
        InterruptTrigger.TOOL_EXECUTION,
        InterruptTrigger.LLM_GENERATION,
        InterruptTrigger.STATE_TRANSITION,
        InterruptTrigger.DATA_MODIFICATION,
        InterruptTrigger.EXTERNAL_API,
        InterruptTrigger.PLAN_CHANGES
    })
    
    # Triggers that require approval for high-risk operations in semi-autonomous mode
    high_risk_triggers: Set[InterruptTrigger] = field(default_factory=lambda: {
        InterruptTrigger.DATA_MODIFICATION,
        InterruptTrigger.EXTERNAL_API,
        InterruptTrigger.PLAN_CHANGES
    })
    
    # Triggers that require approval for critical operations
    critical_triggers: Set[InterruptTrigger] = field(default_factory=lambda: {
        InterruptTrigger.DATA_MODIFICATION,
        InterruptTrigger.ERROR_RECOVERY
    })


@dataclass
class RiskAssessmentConfig:
    """Configuration for risk assessment in semi-autonomous mode."""
    
    # Tool risk mappings based on tool name patterns
    tool_risk_patterns: Dict[str, RiskLevel] = field(default_factory=lambda: {
        # Low risk operations
        "query_": RiskLevel.LOW,
        "get_": RiskLevel.LOW,
        "read_": RiskLevel.LOW,
        "list_": RiskLevel.LOW,
        "search_": RiskLevel.LOW,
        "fetch_": RiskLevel.LOW,
        
        # Medium risk operations  
        "analyze": RiskLevel.MEDIUM,
        "process": RiskLevel.MEDIUM,
        "compute": RiskLevel.MEDIUM,
        "execute_command": RiskLevel.MEDIUM,
        
        # High risk operations
        "write_": RiskLevel.HIGH,
        "create_": RiskLevel.HIGH,
        "delete_": RiskLevel.HIGH,
        "modify_": RiskLevel.HIGH,
        "upload_": RiskLevel.HIGH,
        
        # Critical operations
        "system_": RiskLevel.CRITICAL,
        "sudo_": RiskLevel.CRITICAL,
        "admin_": RiskLevel.CRITICAL,
    })
    
    # Agent risk levels for different operations
    agent_risk_levels: Dict[str, RiskLevel] = field(default_factory=lambda: {
        "researcher": RiskLevel.LOW,      # Information gathering
        "browser": RiskLevel.LOW,         # Web browsing
        "coder": RiskLevel.MEDIUM,        # Code execution
        "reporter": RiskLevel.LOW,        # Report generation
        "supervisor": RiskLevel.MEDIUM,   # Plan coordination
    })
    
    # Step-specific risk factors
    step_risk_factors: List[str] = field(default_factory=lambda: [
        "large_dataset",        # Operations on large datasets
        "external_api",         # External API interactions
        "file_modification",    # File system modifications
        "database_update",      # Database modifications
        "model_training",       # ML model training
        "system_command",       # System-level commands
    ])


@dataclass
class InterruptConfig:
    """Configuration for interrupt behavior and timeouts."""
    
    # Timeout settings for human responses
    response_timeout_seconds: int = 300  # 5 minutes default
    reminder_interval_seconds: int = 60   # Remind every minute
    max_reminders: int = 3               # Maximum number of reminders
    
    # Default actions when timeout occurs
    timeout_action: str = "pause"        # "pause", "abort", or "continue"
    
    # Interrupt message templates
    approval_message_template: str = "Please review and approve: {action_description}"
    modification_message_template: str = "Please review and modify if needed: {details}"
    
    # Context to include in interrupt payloads
    include_step_context: bool = True
    include_plan_context: bool = True
    include_risk_assessment: bool = True
    include_tool_details: bool = True


class HITLConfig:
    """Main configuration manager for human-in-the-loop workflows."""
    
    def __init__(self, mode: Optional[HITLMode] = None):
        """Initialize HITL configuration with optional mode override."""
        self.mode = mode or self._load_mode_from_env()
        self.trigger_config = HITLTriggerConfig()
        self.risk_config = RiskAssessmentConfig()
        self.interrupt_config = InterruptConfig()
        self._load_env_overrides()
    
    def _load_mode_from_env(self) -> HITLMode:
        """Load HITL mode from environment variable."""
        mode_str = os.getenv("HITL_MODE", "semi_autonomous").lower()
        try:
            return HITLMode(mode_str)
        except ValueError:
            return HITLMode.SEMI_AUTONOMOUS
    
    def _load_env_overrides(self):
        """Load configuration overrides from environment variables."""
        # Response timeout override
        timeout = os.getenv("HITL_RESPONSE_TIMEOUT")
        if timeout:
            try:
                self.interrupt_config.response_timeout_seconds = int(timeout)
            except ValueError:
                pass
        
        # Timeout action override
        timeout_action = os.getenv("HITL_TIMEOUT_ACTION")
        if timeout_action in ["pause", "abort", "continue"]:
            self.interrupt_config.timeout_action = timeout_action
        
        # Risk assessment overrides
        self._load_risk_overrides()
    
    def _load_risk_overrides(self):
        """Load risk assessment overrides from environment."""
        # Allow environment-based risk level overrides
        for agent in ["researcher", "coder", "browser", "reporter", "supervisor"]:
            env_key = f"HITL_RISK_{agent.upper()}"
            risk_value = os.getenv(env_key)
            if risk_value:
                try:
                    self.risk_config.agent_risk_levels[agent] = RiskLevel(risk_value.lower())
                except ValueError:
                    pass
    
    def should_interrupt(
        self, 
        trigger: InterruptTrigger, 
        agent_name: str = None,
        tool_name: str = None,
        step_context: Dict[str, Any] = None,
        mode: HITLMode = None
    ) -> bool:
        """
        Determine if human intervention should be triggered.
        
        Args:
            trigger: Type of event triggering potential intervention
            agent_name: Name of the agent performing the action
            tool_name: Name of the tool being used (if applicable)
            step_context: Additional context about the current step
            mode: Override the default mode for this decision
            
        Returns:
            Boolean indicating whether to interrupt for human input
        """
        effective_mode = mode or self.mode
        if effective_mode == HITLMode.FULLY_AUTONOMOUS:
            return False
        
        if effective_mode == HITLMode.FULL_MANUAL:
            return trigger in self.trigger_config.manual_mode_triggers
        
        # Semi-autonomous mode: risk-based decision making
        if effective_mode == HITLMode.SEMI_AUTONOMOUS:
            risk_level = self.assess_risk(agent_name, tool_name, step_context)
            
            # Always interrupt for critical operations
            if risk_level == RiskLevel.CRITICAL:
                return True
            
            # Interrupt for high-risk operations if trigger is configured
            if risk_level == RiskLevel.HIGH and trigger in self.trigger_config.high_risk_triggers:
                return True
            
            # Interrupt for specific critical triggers regardless of risk
            if trigger in self.trigger_config.critical_triggers:
                return True
        
        return False
    
    def assess_risk(
        self,
        agent_name: str = None,
        tool_name: str = None,
        step_context: Dict[str, Any] = None
    ) -> RiskLevel:
        """
        Assess the risk level of a proposed action.
        
        Args:
            agent_name: Name of the agent performing the action
            tool_name: Name of the tool being used
            step_context: Additional context about the step
            
        Returns:
            RiskLevel enum indicating the risk assessment
        """
        risk_factors = []
        
        # Assess tool-based risk
        if tool_name:
            tool_risk = self._assess_tool_risk(tool_name)
            risk_factors.append(tool_risk)
        
        # Assess agent-based risk
        if agent_name and agent_name in self.risk_config.agent_risk_levels:
            agent_risk = self.risk_config.agent_risk_levels[agent_name]
            risk_factors.append(agent_risk)
        
        # Assess context-based risk
        if step_context:
            context_risk = self._assess_context_risk(step_context)
            risk_factors.append(context_risk)
        
        # Return highest risk level found
        if not risk_factors:
            return RiskLevel.LOW
        
        risk_hierarchy = [RiskLevel.LOW, RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL]
        return max(risk_factors, key=lambda r: risk_hierarchy.index(r))
    
    def _assess_tool_risk(self, tool_name: str) -> RiskLevel:
        """Assess risk based on tool name patterns."""
        tool_lower = tool_name.lower()
        
        # Check specific patterns
        for pattern, risk_level in self.risk_config.tool_risk_patterns.items():
            if pattern in tool_lower:
                return risk_level
        
        # Default to medium risk for unknown tools
        return RiskLevel.MEDIUM
    
    def _assess_context_risk(self, context: Dict[str, Any]) -> RiskLevel:
        """Assess risk based on step context."""
        if not context:
            return RiskLevel.LOW
        
        # Check for high-risk context indicators
        context_str = str(context).lower()
        
        critical_indicators = ["system", "sudo", "admin", "root", "delete_all"]
        if any(indicator in context_str for indicator in critical_indicators):
            return RiskLevel.CRITICAL
        
        high_risk_indicators = ["delete", "remove", "modify", "write", "create"]
        if any(indicator in context_str for indicator in high_risk_indicators):
            return RiskLevel.HIGH
        
        medium_risk_indicators = ["execute", "run", "process", "analyze"]
        if any(indicator in context_str for indicator in medium_risk_indicators):
            return RiskLevel.MEDIUM
        
        return RiskLevel.LOW
    
    def create_interrupt_payload(
        self,
        trigger: InterruptTrigger,
        action_description: str,
        agent_name: str = None,
        tool_name: str = None,
        step_context: Dict[str, Any] = None,
        risk_assessment: Optional[RiskLevel] = None
    ) -> Dict[str, Any]:
        """
        Create a structured payload for human interrupt.
        
        Args:
            trigger: Type of event triggering intervention
            action_description: Description of the action requiring approval
            agent_name: Name of the agent performing the action
            tool_name: Name of the tool being used
            step_context: Additional context about the step
            risk_assessment: Pre-computed risk level
            
        Returns:
            Dictionary containing all interrupt information
        """
        payload = {
            "trigger_type": trigger.value,
            "action_description": action_description,
            "timestamp": None,  # Will be set when interrupt is triggered
            "requires_approval": True,
            "options": ["approve", "modify", "reject", "request_info"]
        }
        
        # Include optional context based on configuration
        if self.interrupt_config.include_step_context and step_context:
            payload["step_context"] = step_context
        
        if agent_name:
            payload["agent_name"] = agent_name
        
        if tool_name:
            payload["tool_name"] = tool_name
        
        if self.interrupt_config.include_risk_assessment:
            if risk_assessment is None:
                risk_assessment = self.assess_risk(agent_name, tool_name, step_context)
            payload["risk_level"] = risk_assessment.value
            payload["risk_explanation"] = self._get_risk_explanation(risk_assessment)
        
        return payload
    
    def _get_risk_explanation(self, risk_level: RiskLevel) -> str:
        """Get human-readable explanation for risk level."""
        explanations = {
            RiskLevel.LOW: "Low risk operation - minimal impact expected",
            RiskLevel.MEDIUM: "Medium risk operation - review recommended",
            RiskLevel.HIGH: "High risk operation - careful review required",
            RiskLevel.CRITICAL: "Critical operation - requires explicit approval"
        }
        return explanations.get(risk_level, "Unknown risk level")


# Global configuration instance
hitl_config = HITLConfig()


def get_hitl_config() -> HITLConfig:
    """Get the global HITL configuration instance."""
    return hitl_config


def set_hitl_mode(mode: HITLMode) -> None:
    """Set the global HITL mode."""
    global hitl_config
    hitl_config.mode = mode


def is_hitl_enabled() -> bool:
    """Check if any form of HITL is enabled."""
    return hitl_config.mode != HITLMode.FULLY_AUTONOMOUS