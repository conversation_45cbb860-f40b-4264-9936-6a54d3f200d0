"""
Execution strategy configuration for cross-environment tool execution.

This module provides configurable timeout values, retry policies, and execution
strategies for biomni tools and MCP-based cross-environment operations.
"""

import os
from typing import Dict, Any, Optional
from enum import Enum


class ToolComplexity(Enum):
    """Tool complexity levels for timeout configuration."""

    SIMPLE = "simple"  # File operations, quick database queries
    MODERATE = "moderate"  # Data processing, API calls
    COMPLEX = "complex"  # Large data analysis, model training
    INTENSIVE = "intensive"  # Heavy computational tasks


class ExecutionStrategy(Enum):
    """Available execution strategies."""

    DIRECT_SUBPROCESS = "direct_subprocess"  # Direct subprocess execution
    DESKTOP_COMMANDER = "desktop_commander"  # Via desktop-commander MCP
    HYBRID = "hybrid"  # Try direct first, fallback to MCP
    AUTO = "auto"  # Automatically select best strategy


# Tool complexity mapping based on tool name patterns
TOOL_COMPLEXITY_MAPPING = {
    # Simple operations (5-15 seconds)
    "query_uniprot": ToolComplexity.SIMPLE,
    "query_alphafold": ToolComplexity.SIMPLE,
    "query_pdb": ToolComplexity.SIMPLE,
    "get_protein_info": ToolComplexity.SIMPLE,
    "get_gene_info": ToolComplexity.SIMPLE,
    "read_file": ToolComplexity.SIMPLE,
    "write_file": ToolComplexity.SIMPLE,
    "list_directory": ToolComplexity.SIMPLE,
    # Moderate operations (30-60 seconds)
    "search_pubmed": ToolComplexity.MODERATE,
    "literature_search": ToolComplexity.MODERATE,
    "database_search": ToolComplexity.MODERATE,
    "process_sequence": ToolComplexity.MODERATE,
    "execute_command": ToolComplexity.MODERATE,
    "search_files": ToolComplexity.MODERATE,
    # Complex operations (2-5 minutes)
    "analyze_structure": ToolComplexity.COMPLEX,
    "compute_features": ToolComplexity.COMPLEX,
    "run_analysis": ToolComplexity.COMPLEX,
    "differential_expression": ToolComplexity.COMPLEX,
    "functional_enrichment": ToolComplexity.COMPLEX,
    # Intensive operations (5+ minutes)
    "large_dataset_analysis": ToolComplexity.INTENSIVE,
    "genome_analysis": ToolComplexity.INTENSIVE,
    "phylogenetic_analysis": ToolComplexity.INTENSIVE,
}

# Default timeout configurations by complexity (in seconds)
DEFAULT_TIMEOUTS = {
    ToolComplexity.SIMPLE: {
        "connection_timeout": 5,
        "execution_timeout": 15,
        "total_timeout": 20,
    },
    ToolComplexity.MODERATE: {
        "connection_timeout": 10,
        "execution_timeout": 60,
        "total_timeout": 75,
    },
    ToolComplexity.COMPLEX: {
        "connection_timeout": 15,
        "execution_timeout": 300,  # 5 minutes
        "total_timeout": 320,
    },
    ToolComplexity.INTENSIVE: {
        "connection_timeout": 20,
        "execution_timeout": 900,  # 15 minutes
        "total_timeout": 930,
    },
}

# Retry configuration by complexity
DEFAULT_RETRY_CONFIG = {
    ToolComplexity.SIMPLE: {
        "max_retries": 2,
        "backoff_factor": 1.5,
        "initial_delay": 1.0,
    },
    ToolComplexity.MODERATE: {
        "max_retries": 3,
        "backoff_factor": 2.0,
        "initial_delay": 2.0,
    },
    ToolComplexity.COMPLEX: {
        "max_retries": 2,
        "backoff_factor": 2.5,
        "initial_delay": 5.0,
    },
    ToolComplexity.INTENSIVE: {
        "max_retries": 1,
        "backoff_factor": 3.0,
        "initial_delay": 10.0,
    },
}

# Execution strategy preferences by tool type
DEFAULT_EXECUTION_STRATEGIES = {
    # Biomni tools prefer direct subprocess for better reliability
    "biomni": ExecutionStrategy.HYBRID,
    # File operations work well through desktop-commander
    "filesystem": ExecutionStrategy.DESKTOP_COMMANDER,
    # General tools use auto-selection
    "general": ExecutionStrategy.AUTO,
}


class ExecutionConfig:
    """Configuration manager for cross-environment execution."""

    def __init__(self):
        """Initialize configuration with environment variable overrides."""
        self._load_env_overrides()

    def _load_env_overrides(self):
        """Load configuration overrides from environment variables."""
        # Allow environment variable overrides
        self.global_timeout_multiplier = float(
            os.getenv("EXECUTION_TIMEOUT_MULTIPLIER", "1.0")
        )
        self.force_strategy = os.getenv("FORCE_EXECUTION_STRATEGY")
        self.disable_retries = (
            os.getenv("DISABLE_EXECUTION_RETRIES", "false").lower() == "true"
        )
        self.debug_mode = os.getenv("EXECUTION_DEBUG", "false").lower() == "true"

    def get_tool_complexity(
        self, tool_name: str, command: Optional[str] = None
    ) -> ToolComplexity:
        """
        Determine tool complexity based on name and optional command context.

        Args:
            tool_name: Name of the tool
            command: Optional command string for context

        Returns:
            ToolComplexity enum value
        """
        # Direct mapping
        if tool_name in TOOL_COMPLEXITY_MAPPING:
            return TOOL_COMPLEXITY_MAPPING[tool_name]

        # Pattern-based detection
        tool_lower = tool_name.lower()

        # Simple patterns
        if any(
            pattern in tool_lower
            for pattern in ["get_", "query_", "read_", "list_", "fetch_", "simple_"]
        ):
            return ToolComplexity.SIMPLE

        # Complex patterns
        if any(
            pattern in tool_lower
            for pattern in [
                "analyze",
                "compute",
                "process_large",
                "differential",
                "enrichment",
                "complex_",
                "_analysis",
            ]
        ):
            return ToolComplexity.COMPLEX

        # Command-based detection
        if command:
            command_lower = command.lower()

            # R script analysis patterns
            if any(
                pattern in command_lower
                for pattern in ["rscript", "biocmanager", "limma", "deseq2"]
            ):
                if any(
                    intensive in command_lower
                    for intensive in [
                        "genome",
                        "large_dataset",
                        "differential_expression",
                    ]
                ):
                    return ToolComplexity.INTENSIVE
                else:
                    return ToolComplexity.COMPLEX

            # Python analysis patterns
            if "python" in command_lower and any(
                pattern in command_lower
                for pattern in ["analysis", "processing", "computation"]
            ):
                return ToolComplexity.MODERATE

        # Default to moderate
        return ToolComplexity.MODERATE

    def get_timeout_config(
        self, tool_name: str, command: Optional[str] = None
    ) -> Dict[str, int]:
        """
        Get timeout configuration for a specific tool.

        Args:
            tool_name: Name of the tool
            command: Optional command string for context

        Returns:
            Dictionary with timeout values in seconds
        """
        complexity = self.get_tool_complexity(tool_name, command)
        base_config = DEFAULT_TIMEOUTS[complexity].copy()

        # Apply global multiplier
        for key, value in base_config.items():
            base_config[key] = int(value * self.global_timeout_multiplier)

        # Environment-specific overrides
        if tool_name in ["execute_command", "execute_bash"] and command:
            # Increase timeouts for R scripts
            if "rscript" in command.lower() or command.endswith(".R"):
                base_config["execution_timeout"] *= 2
                base_config["total_timeout"] = base_config["execution_timeout"] + 30

        return base_config

    def get_retry_config(
        self, tool_name: str, command: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get retry configuration for a specific tool.

        Args:
            tool_name: Name of the tool
            command: Optional command string for context

        Returns:
            Dictionary with retry configuration
        """
        if self.disable_retries:
            return {"max_retries": 0, "backoff_factor": 1.0, "initial_delay": 0.0}

        complexity = self.get_tool_complexity(tool_name, command)
        return DEFAULT_RETRY_CONFIG[complexity].copy()

    def get_execution_strategy(
        self, tool_name: str, tool_type: str = "general"
    ) -> ExecutionStrategy:
        """
        Get preferred execution strategy for a tool.

        Args:
            tool_name: Name of the tool
            tool_type: Type category (biomni, filesystem, general)

        Returns:
            ExecutionStrategy enum value
        """
        if self.force_strategy:
            try:
                return ExecutionStrategy(self.force_strategy)
            except ValueError:
                pass

        # Tool-specific overrides
        tool_lower = tool_name.lower()
        if "biomni" in tool_lower or tool_type == "biomni":
            return DEFAULT_EXECUTION_STRATEGIES["biomni"]
        elif any(
            pattern in tool_lower for pattern in ["read_", "write_", "list_", "create_"]
        ):
            return DEFAULT_EXECUTION_STRATEGIES["filesystem"]
        else:
            return DEFAULT_EXECUTION_STRATEGIES["general"]

    def should_use_connection_pooling(self, tool_name: str) -> bool:
        """
        Determine if connection pooling should be used for a tool.

        Args:
            tool_name: Name of the tool

        Returns:
            Boolean indicating if connection pooling is recommended
        """
        # Use connection pooling for tools that are likely to be called repeatedly
        return any(
            pattern in tool_name.lower()
            for pattern in ["query_", "search_", "get_", "fetch_"]
        )

    def get_performance_monitoring_config(self) -> Dict[str, Any]:
        """
        Get performance monitoring configuration.

        Returns:
            Dictionary with monitoring settings
        """
        return {
            "enable_metrics": True,
            "track_execution_time": True,
            "track_success_rate": True,
            "track_failure_patterns": True,
            "alert_on_timeout": True,
            "alert_on_failure_rate": 0.3,  # Alert if failure rate > 30%
            "metrics_retention_hours": 24,
        }


# Global configuration instance
execution_config = ExecutionConfig()


def get_execution_config() -> ExecutionConfig:
    """Get the global execution configuration instance."""
    return execution_config
