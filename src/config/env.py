import os
from pathlib import Path
from dotenv import load_dotenv

# Define workspace directory
# Use Docker-aware path detection
if os.path.exists("/.dockerenv"):
    # In Docker container, use the mounted volume path
    WORKSPACE_DIR = "/app/workspace"
else:
    # Local development, use project-relative path
    project_root = Path(__file__).parent.parent.parent
    WORKSPACE_DIR = os.path.join(project_root, "workspace")

# Load environment variables
load_dotenv()

# Workspace validation logging
import logging

logger = logging.getLogger(__name__)
logger.info(f"Workspace directory configured: {WORKSPACE_DIR}")
logger.info(f"Docker environment detected: {os.path.exists('/.dockerenv')}")
if os.path.exists(WORKSPACE_DIR):
    logger.info(f"Workspace directory exists and is accessible")
else:
    logger.warning(f"Workspace directory does not exist: {WORKSPACE_DIR}")

# Reasoning LLM configuration (for complex reasoning tasks)
REASONING_PROVIDER = os.getenv("REASONING_PROVIDER", "gemini")
REASONING_MODEL = os.getenv("REASONING_MODEL", "gemini-2.5-pro")
REASONING_BASE_URL = os.getenv("REASONING_BASE_URL")
REASONING_API_KEY = os.getenv("REASONING_API_KEY")

# Non-reasoning LLM configuration (for straightforward tasks)
BASIC_PROVIDER = os.getenv("BASIC_PROVIDER", "gemini")
BASIC_MODEL = os.getenv("BASIC_MODEL", "gemini-2.5-flash")
BASIC_BASE_URL = os.getenv("BASIC_BASE_URL")
BASIC_API_KEY = os.getenv("BASIC_API_KEY")

# Vision-language LLM configuration (for tasks requiring visual understanding)
VL_PROVIDER = os.getenv("VL_PROVIDER", "gemini")
VL_MODEL = os.getenv("VL_MODEL", "gemini-2.5-flash")
VL_BASE_URL = os.getenv("VL_BASE_URL")
VL_API_KEY = os.getenv("VL_API_KEY")

# Chrome Instance configuration
CHROME_INSTANCE_PATH = os.getenv("CHROME_INSTANCE_PATH")

# Docker environment detection
IS_DOCKER = os.path.exists("/.dockerenv")

# R Environment Path
# Use user's R environment (mounted in Docker or local)
if IS_DOCKER:
    # In Docker, use the mounted user conda environment
    R_ENV_PATH = "/host-conda"
    PYTHON_ENV_PATH = "/host-python"
else:
    # In local development, use the user's environments directly
    R_ENV_PATH = os.getenv("R_ENV_PATH")
    PYTHON_ENV_PATH = os.getenv("PYTHON_ENV_PATH", R_ENV_PATH)

# MCP Server Configuration
# Set working directory context for MCP tools in Docker
if IS_DOCKER:
    MCP_WORKING_DIR = "/app"
else:
    MCP_WORKING_DIR = str(Path(__file__).parent.parent.parent)
