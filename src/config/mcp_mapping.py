# External server resilience configuration
import os

# Control external server usage - can be disabled when servers are unreliable
EXTERNAL_SERVERS_ENABLED = (
    os.getenv("EXTERNAL_SERVERS_ENABLED", "true").lower() == "true"
)
DEVELOPMENT_MODE = os.getenv("DEV_MODE", "false").lower() == "true"

# Server reliability tiers
SERVER_RELIABILITY = {
    # Tier 1: Local/Reliable servers (always available)
    "local": ["context7", "desktop-commander", "fetch", "todo-md-mcp"],
    # Tier 2: External servers (may be unreliable, optional)
    "external_stable": [],  # Stable external servers (none currently)
    "external_unstable": [
        "exa",
        "pubmed-mcp-server",
    ],  # Known unreliable external servers
}

# Agent-specific MCP server mappings - ultra-optimized for minimal tool loading
AGENT_MCP_MAPPING = {
    "researcher": [
        "fetch",
        "exa",
        "pubmed-mcp-server",
        "desktop-commander",
        "todo-md-mcp",
    ],  # Research: web + literature + data discovery + task management
    "coder": [
        "context7",
        "desktop-commander",
        "todo-md-mcp",
    ],  # Coder: documentation + file system + task management
    "browser": [
        "desktop-commander",
        "todo-md-mcp",
    ],  # Browser: minimal file system access + task management
    "execution_evaluator": [
        "desktop-commander",
        "brave-search",
    ],  # Evaluator: read-only file access + web search
    "reporter": [
        "desktop-commander",
        "todo-md-mcp",
    ],  # Reporter: file system access for cleanup + task verification
    "supervisor": ["todo-md-mcp", "desktop-commander"],
    "planner": [
        "desktop-commander",
        "context7",
        "todo-md-mcp",
    ],  # Planner: file system + docs + task creation
}

# Fallback mappings when external servers are unavailable
AGENT_MCP_MAPPING_FALLBACK = {
    "researcher": [
        "fetch",
        "desktop-commander",
        "todo-md-mcp",
    ],  # Research with local tools only + task management
    "coder": [
        "context7",
        "desktop-commander",
        "todo-md-mcp",
    ],  # Coder remains unchanged (already local) + task management
    "browser": [
        "desktop-commander",
        "todo-md-mcp",
    ],  # Browser remains unchanged (already local) + task management
    "reporter": [
        "desktop-commander",
        "todo-md-mcp",
    ],  # Reporter with local tools + task management
    "supervisor": [
        "todo-md-mcp",
        "desktop-commander",
    ],  # Supervisor with local tools + task management
    "planner": [
        "desktop-commander",
        "context7",
        "todo-md-mcp",
    ],  # Planner with local tools + task management
}

# Essential tools only - refined mapping for each agent's core needs
ESSENTIAL_TOOLS_BY_AGENT = {
    "researcher": {
        # Core research tools only (8-10 tools max)
        "fetch": ["fetch"],
        "exa": [
            "web_search_exa",
            "research_paper_search_exa",
            "crawling_exa",
        ],  # Only 3 most important exa tools
        "pubmed-mcp-server": [
            "search_pubmed_key_words",
            "get_pubmed_article_metadata",
        ],  # Only 2 core pubmed tools
        "desktop-commander": [
            "read_file",
            "list_directory",
            "get_file_info",
            "search_files",
        ],  # Essential file access for data discovery
        "todo-md-mcp": [
            "list_todos",
            "update_todo",
        ],  # Read tasks context and mark work completed
        "biomni": [
            "query_uniprot_direct",
            "query_alphafold_direct",
        ],  # Essential biomni database tools
    },
    "coder": {
        # Documentation + core file operations (8-10 tools max)
        "context7": ["resolve-library-id", "get-library-docs"],
        "desktop-commander": [
            "read_file",
            "write_file",
            "execute_command",
            "list_directory",
            "create_directory",
            "search_code",
        ],  # Only essential file/exec tools
        "todo-md-mcp": [
            "list_todos",
            "update_todo",
        ],  # Read tasks context and mark work completed
        "biomni": [
            "query_uniprot_direct",
            "query_alphafold_direct",
        ],  # Essential biomni database tools for bioinformatics coding
    },
    "browser": {
        # Minimal file system tools (5-6 tools max)
        "desktop-commander": [
            "read_file",
            "write_file",
            "list_directory",
            "get_file_info",
            "search_files",
        ],  # Basic file operations only
        "todo-md-mcp": [
            "list_todos",
            "update_todo",
        ],  # Read tasks context and mark work completed
    },
    "execution_evaluator": {
        "desktop-commander": [
            "read_file",
            "list_directory",
            "get_file_info",
            "write_file",
            "create_directory",
            "delete_file",
        ],
        "brave-search": ["brave_web_search"],
    },
    "reporter": {
        "desktop-commander": ["list_directory", "delete_file"],
        "todo-md-mcp": [
            "list_todos",
            "clear_completed",
        ],  # Review final state and cleanup completed tasks
    },
    "supervisor": {
        "todo-md-mcp": [
            "list_todos",
            "add_todo",
            "update_todo",
            "delete_todo",
        ],  # Full orchestration capabilities
    },
    "planner": {
        "todo-md-mcp": [
            "add_todo",
            "list_todos",
        ],  # Create initial tasks and review existing ones
    },
}


# Helper functions for server reliability management
def get_reliable_servers() -> list:
    """Get list of servers that are considered reliable/local."""
    return SERVER_RELIABILITY["local"]


def get_unreliable_servers() -> list:
    """Get list of servers that are known to be unreliable."""
    return SERVER_RELIABILITY["external_unstable"]


def is_server_reliable(server_name: str) -> bool:
    """Check if a server is considered reliable."""
    return server_name in SERVER_RELIABILITY["local"]


def should_use_external_servers() -> bool:
    """Determine if external servers should be used based on configuration."""
    if DEVELOPMENT_MODE:
        return False
    return EXTERNAL_SERVERS_ENABLED


def get_agent_servers(agent_name: str, use_fallback: bool = False) -> list:
    """
    Get servers for an agent, with optional fallback to reliable servers only.

    Args:
        agent_name: Name of the agent
        use_fallback: If True, use fallback configuration (local servers only)

    Returns:
        List of server names for the agent
    """
    if use_fallback or not should_use_external_servers():
        return AGENT_MCP_MAPPING_FALLBACK.get(agent_name, [])

    servers = AGENT_MCP_MAPPING.get(agent_name, [])

    # Filter out unreliable servers if external servers are disabled
    if not EXTERNAL_SERVERS_ENABLED:
        unreliable = get_unreliable_servers()
        servers = [s for s in servers if s not in unreliable]

    return servers


def filter_failed_servers(servers: list, failed_servers: set) -> list:
    """
    Filter out servers that have failed from a server list.

    Args:
        servers: List of server names
        failed_servers: Set of server names that have failed

    Returns:
        Filtered list of servers excluding failed ones
    """
    return [s for s in servers if s not in failed_servers]
