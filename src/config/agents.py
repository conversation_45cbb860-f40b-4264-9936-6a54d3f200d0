from typing import Literal

# Define available LLM types
LLMType = Literal["basic", "reasoning", "vision"]

# Define agent-LLM mapping
AGENT_LLM_MAP: dict[str, LLMType] = {
    "planner": "reasoning",  # basic llm
    "supervisor": "basic",  # basic llm
    "researcher": "basic",  # basic llm
    "coder": "basic",  # basic llm
    "browser": "vision",  # basic llm
    "reporter": "basic",  # basic llm
    "execution_evaluator": "vision",  # vision llm
}
