"""
Network connectivity fix for IPv6/gRPC issues with langchain-google-genai.

This module MUST be imported before any langchain imports to fix IPv6 connectivity issues.
"""

import os
import socket
import logging

logger = logging.getLogger(__name__)


def apply_ipv4_fix():
    """
    Apply IPv4-only fix for gRPC connections to resolve langchain timeouts.

    This fixes the issue where langchain-google-genai tries to use IPv6 gRPC
    connections that fail on systems with broken IPv6 connectivity.
    """
    logger.info("🔧 Applying IPv4-only network fix for langchain gRPC connections...")

    # Force IPv4 for gRPC connections
    os.environ["GRPC_DNS_RESOLVER"] = "native"
    os.environ["GRPC_POLL_STRATEGY"] = "poll"
    os.environ["GRPC_ENABLE_FORK_SUPPORT"] = "0"

    # Configure gRPC channel arguments for better connectivity
    grpc_args = [
        "grpc.so_reuseaddr:1",
        "grpc.keepalive_time_ms:30000",
        "grpc.keepalive_timeout_ms:5000",
        "grpc.keepalive_permit_without_calls:1",
        "grpc.http2.max_pings_without_data:0",
    ]
    os.environ["GRPC_CHANNEL_ARGS"] = ",".join(grpc_args)

    # Monkey patch socket.getaddrinfo to force IPv4 only
    original_getaddrinfo = socket.getaddrinfo

    def ipv4_only_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
        """Force IPv4 only for all network connections."""
        try:
            return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)
        except socket.gaierror:
            # Fallback to original if IPv4 fails
            return original_getaddrinfo(host, port, family, type, proto, flags)

    socket.getaddrinfo = ipv4_only_getaddrinfo

    logger.info("✅ IPv4-only network fix applied successfully")
    logger.info("📋 gRPC environment variables configured")
    logger.info("📋 Socket.getaddrinfo patched for IPv4-only")


# Apply fix immediately when module is imported
apply_ipv4_fix()
