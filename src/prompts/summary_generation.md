# Executive Summary Generation Prompts

This file contains prompts for generating executive summaries for living documents in the bioinformatics analysis system.

## Core Summary Generation Prompt

You are an expert bioinformatics analyst tasked with generating concise, professional executive summaries for research documents. Your role is to synthesize findings across multiple analysis sessions into clear, actionable overviews.

### Context Variables
- **Document Type:** {analysis_type}
- **Subject Focus:** {subject}
- **Total Sessions:** {total_sessions}
- **Latest Update:** {latest_timestamp}
- **Content Volume:** {total_words} words

### Key Findings to Synthesize
{key_findings}

### Instructions

1. **Generate a Professional Executive Summary** (Maximum 150 words)
   - Focus on the most critical insights and current analysis status
   - Use clear, scientific language appropriate for bioinformatics research
   - Emphasize actionable findings and key discoveries

2. **Apply BMAD-Style Formatting**
   - Use **bold markers** for key points and important findings
   - Structure content with bullet points for clarity
   - Ensure scannable, well-organized presentation
   - Include status indicators where relevant

3. **Content Organization**
   - Lead with the most significant finding or current status
   - Group related insights logically
   - Maintain focus on research progression and outcomes
   - Include brief context for ongoing work

4. **Quality Standards**
   - Maintain scientific accuracy and precision
   - Use domain-appropriate terminology
   - Ensure content is self-contained and informative
   - Balance detail with conciseness

### Output Format
Generate only the executive summary content using markdown formatting. Do not include section headers, metadata, or additional commentary.

---

## Specialized Prompts by Analysis Type

### Genomic Analysis Summary
Generate an executive summary for genomic analysis research:

**Analysis Focus:** {subject} genomic analysis across {total_sessions} sessions
**Key Areas:** {key_findings}

Emphasize:
- Genomic variants and their significance
- Functional implications of findings
- Data quality and coverage metrics
- Biological relevance and clinical impact

### Protein Analysis Summary  
Generate an executive summary for protein analysis research:

**Analysis Focus:** {subject} protein analysis across {total_sessions} sessions
**Key Areas:** {key_findings}

Emphasize:
- Structural and functional insights
- Interaction networks and pathways
- Experimental validation status
- Therapeutic or research implications

### Pathway Analysis Summary
Generate an executive summary for pathway analysis research:

**Analysis Focus:** {subject} pathway analysis across {total_sessions} sessions  
**Key Areas:** {key_findings}

Emphasize:
- Pathway enrichment results
- Regulatory mechanisms identified
- Cross-pathway interactions
- Biological process implications

### Differential Expression Summary
Generate an executive summary for differential expression analysis:

**Analysis Focus:** {subject} differential expression across {total_sessions} sessions
**Key Areas:** {key_findings}

Emphasize:
- Significantly altered genes/transcripts
- Functional category enrichments
- Experimental conditions and comparisons
- Validation status and next steps

---

## Quality Validation Prompts

### Summary Content Validation
Review this executive summary for quality and accuracy:

{summary_content}

**Validation Criteria:**
- Scientific accuracy and precision
- Appropriate terminology usage
- Logical organization and flow
- Completeness of key information
- BMAD-style formatting compliance

Provide feedback on:
1. Content accuracy and relevance
2. Clarity and readability
3. Formatting and organization
4. Missing critical information
5. Suggested improvements

### BMAD-Style Formatting Check
Evaluate this summary for BMAD-style formatting compliance:

{summary_content}

**Required Elements:**
- Bold markers for key points (**Key Point:** format)
- Bullet point organization where appropriate
- Clear, scannable structure
- Professional scientific tone
- Proper markdown formatting

Rate compliance and suggest specific formatting improvements.

---

## Iterative Refinement Prompts

### Summary Enhancement
Improve this executive summary while maintaining its core message:

**Current Summary:** {current_summary}
**Session Context:** {session_context}
**New Findings:** {new_findings}

**Enhancement Goals:**
- Incorporate new findings seamlessly
- Maintain clarity and conciseness
- Preserve existing valid insights
- Update status information appropriately
- Ensure continued BMAD-style formatting

### Multi-Session Synthesis
Synthesize findings from multiple analysis sessions:

**Previous Sessions:** {previous_sessions}
**Current Session:** {current_session}
**Key Themes:** {recurring_themes}

**Synthesis Approach:**
- Identify consistent findings across sessions
- Highlight progression and evolution of insights
- Resolve any apparent contradictions
- Emphasize cumulative knowledge gained
- Maintain chronological awareness

---

## Fallback and Error Prompts

### Minimal Context Summary
Generate a basic executive summary with limited context:

**Available Information:**
- Document has {session_count} analysis sessions
- Most recent update: {timestamp}
- Analysis type: {analysis_type}

**Fallback Requirements:**
- Create professional placeholder summary
- Indicate ongoing analysis status
- Use standard BMAD formatting
- Include promise of future updates
- Maintain scientific tone

### Recovery Summary
Generate a recovery summary when primary generation fails:

**Recovery Context:**
- Document exists with content
- Primary summarization encountered errors
- Basic document metadata available

**Recovery Approach:**
- Focus on document structure and session count
- Indicate active analysis status
- Use conservative, professional language
- Promise detailed summary in future updates
- Maintain user confidence in system