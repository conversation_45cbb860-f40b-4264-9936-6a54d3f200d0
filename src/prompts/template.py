import os
import re
from datetime import datetime

from langchain_core.prompts import PromptTemplate
from langchain_core.messages import SystemMessage
from langgraph.prebuilt.chat_agent_executor import AgentState


def get_prompt_template(prompt_name: str) -> str:
    template = open(os.path.join(os.path.dirname(__file__), f"{prompt_name}.md")).read()
    # Escape curly braces using backslash
    template = template.replace("{", "{{").replace("}", "}}")
    # Replace `<<VAR>>` with `{VAR}`
    template = re.sub(r"<<([^>>]+)>>", r"{\1}", template)
    return template


from src.config.env import WORKSPACE_DIR


def apply_prompt_template(prompt_name: str, state: AgentState) -> list:
    template_str = get_prompt_template(prompt_name)
    prompt = PromptTemplate.from_template(template_str)

    # Extract filtered tools from current PlanStep if available
    filtered_tools_context = ""
    current_step_info = ""

    structured_plan = state.get("structured_plan")
    if structured_plan:
        current_step = structured_plan.get_current_step()
        if current_step:
            # Add current step context
            current_step_info = f"""
## Current Task
**Step {current_step.step_index + 1}**: {current_step.title}
**Description**: {current_step.description}
**Status**: {current_step.status}
"""

            # Add filtered tools context if available
            if current_step.filtered_tools:
                filtered_tools_context = f"""
## 🎯 Supervisor-Recommended Tools for This Task
The supervisor has intelligently analyzed your current task and recommended the following tools as most relevant:

{current_step.filtered_tools}

### 📋 Tool Usage Guidance:
- **PRIORITY**: Start with the tools listed above - they've been specifically selected for this task
- **CONTEXT-AWARE**: These tools were chosen based on the task description: "{current_step.description}"
- **FALLBACK**: You have access to your full tool set if the recommended tools are insufficient
- **EFFICIENCY**: Using the recommended tools first will likely lead to better and faster results

### 🔧 How to Leverage These Tools:
1. Review the recommended tools and their descriptions
2. Consider how each tool relates to your current task
3. Use the most relevant tools first before exploring alternatives
4. If you need additional capabilities, you can access your full tool set
"""

    # Format the prompt with enhanced context
    system_prompt_content = prompt.format(
        CURRENT_TIME=datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
        WORKSPACE_DIR=WORKSPACE_DIR,
        FILTERED_TOOLS_CONTEXT=filtered_tools_context,
        CURRENT_STEP_INFO=current_step_info,
        **state,
    )

    return [SystemMessage(content=system_prompt_content)] + state["messages"]
