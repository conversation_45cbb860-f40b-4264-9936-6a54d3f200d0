import logging
from src.config import TEAM_MEMBERS
from src.graph import build_graph
from src.graph.builder import create_checkpointer

# Removed OpenAI-specific import to support multiple model providers
import uuid
import asyncio
from src.tools.mcp_tools import get_progress_events

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Default level is INFO
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

# Suppress specific warnings
logging.getLogger("langchain_google_genai._function_utils").setLevel(logging.ERROR)


def enable_debug_logging():
    """Enable debug level logging for more detailed execution information."""
    logging.getLogger("src").setLevel(logging.DEBUG)


logger = logging.getLogger(__name__)

# Global variables for graph and checkpointer
graph = None
checkpointer = None


async def initialize_graph():
    """Initialize the graph with persistent checkpointer."""
    global graph, checkpointer
    if graph is None:
        logger.info("Initializing graph with persistent checkpointer...")
        checkpointer = await create_checkpointer()
        graph = build_graph(checkpointer)
        logger.info("Graph initialization complete")
    return graph


def get_graph():
    """Get the initialized graph, creating it if necessary."""
    global graph
    if graph is None:
        # For synchronous access, create a simple graph without persistence
        logger.warning(
            "Graph not initialized with persistence, creating non-persistent version"
        )
        graph = build_graph()
    return graph


async def handle_hitl_resumption(
    thread_id: str, 
    human_response: dict,
    interrupt_id: str = None
):
    """
    Handle HITL resumption using LangGraph Command pattern.
    
    Args:
        thread_id: Thread ID of the conversation
        human_response: Structured human response data
        interrupt_id: Optional interrupt ID for tracking
    """
    logger.info(f"Processing HITL resumption for thread {thread_id}: {human_response.get('action', 'unknown')}")
    
    # Initialize graph with persistence
    current_graph = await initialize_graph()
    config = {"configurable": {"thread_id": thread_id}}
    
    # Get current state to verify interrupt context
    current_state = await current_graph.aget_state(config)
    
    if not current_state:
        raise ValueError(f"No state found for thread {thread_id}")
    
    if not current_state.values.get("awaiting_human_input"):
        logger.warning(f"Thread {thread_id} is not waiting for human input")
    
    # Update state with human response
    updated_values = current_state.values.copy()
    updated_values["awaiting_human_input"] = False
    updated_values["human_feedback"] = human_response
    
    # Add to interrupt history
    interrupt_history = updated_values.get("interrupt_history", [])
    interrupt_history.append({
        "timestamp": human_response.get("timestamp"),
        "interrupt_id": interrupt_id,
        "action": human_response.get("action"),
        "human_response": human_response
    })
    updated_values["interrupt_history"] = interrupt_history
    
    # Clear interrupt context since it's been resolved
    updated_values["interrupt_context"] = None
    
    await current_graph.aupdate_state(config, updated_values, as_node="__start__")
    
    # Create Command for resumption
    from langgraph.types import Command
    resume_command = Command(resume=human_response)
    
    # Stream the resumed workflow
    async for event in current_graph.astream(resume_command, config):
        if event:
            yield event


async def handle_user_confirmation(thread_id: str, user_response: str):
    """
    Handle user confirmation response and continue workflow.

    Args:
        thread_id: Thread ID of the conversation
        user_response: User's response to the confirmation request
    """
    logger.info(f"Processing user confirmation for thread {thread_id}: {user_response}")

    # Initialize graph with persistence
    current_graph = await initialize_graph()
    config = {"configurable": {"thread_id": thread_id}}

    # Get current state
    current_state = await current_graph.aget_state(config)

    if not current_state or not current_state.values.get("awaiting_confirmation"):
        logger.warning(f"No pending confirmation found for thread {thread_id}")
        return

    # Use LLM to semantically process user response
    from src.agents.llm import get_llm_by_type
    from src.config.agents import AGENT_LLM_MAP
    from langchain_core.messages import HumanMessage

    response_processor = get_llm_by_type(AGENT_LLM_MAP["planner"])

    processing_prompt = f"""You are processing a user's response to a bioinformatics analysis confirmation request.

User's response: "{user_response}"

Researcher findings were: {current_state.values.get("researcher_findings", "")}

Determine the user's intent and respond with ONE of these actions:
- PROCEED: User wants to proceed with the proposed analysis
- MODIFY: User wants changes to the approach (include specific modifications)
- ALTERNATIVE: User wants different methodology entirely  
- QUESTION: User has questions that need answering
- CLARIFY: User's intent is unclear

Format your response as:
ACTION: [your choice]
DETAILS: [specific details about what the user wants, if any]

Your response:"""

    processing_response = response_processor.invoke(
        [HumanMessage(content=processing_prompt)]
    )
    response_lines = processing_response.content.strip().split("\n")

    action = "CLARIFY"
    details = ""

    for line in response_lines:
        if line.startswith("ACTION:"):
            action = line.replace("ACTION:", "").strip()
        elif line.startswith("DETAILS:"):
            details = line.replace("DETAILS:", "").strip()

    logger.info(f"Processed user intent: {action} - {details}")

    # Create continuation message based on user intent
    if action == "PROCEED":
        continuation_message = f"""User has approved the analysis approach: "{user_response}"

The user wants to proceed with the recommended methodology. Please coordinate with the implementation team to execute the analysis."""

        # Update state and continue to supervisor
        updated_values = current_state.values.copy()
        updated_values["awaiting_confirmation"] = False
        updated_values["user_approved"] = True
        updated_values["messages"] = current_state.values["messages"] + [
            HumanMessage(content=f"User: {user_response}", name="user"),
            HumanMessage(content=continuation_message, name="planner"),
        ]

        await current_graph.aupdate_state(config, updated_values)

        # Continue workflow from supervisor
        async for event in current_graph.astream(None, config):
            if event:
                yield event

    elif action in ["MODIFY", "ALTERNATIVE"]:
        modification_message = f"""User has requested changes to the analysis approach: "{user_response}"

User intent: {action}
Specific details: {details}

Please research and provide updated recommendations that address the user's requirements."""

        # Update state and return to researcher
        updated_values = current_state.values.copy()
        updated_values["awaiting_confirmation"] = False
        updated_values["modification_request"] = user_response
        updated_values["modification_details"] = details
        updated_values["messages"] = current_state.values["messages"] + [
            HumanMessage(content=f"User: {user_response}", name="user"),
            HumanMessage(content=modification_message, name="planner"),
        ]

        await current_graph.aupdate_state(config, updated_values)

        # Continue workflow from researcher
        async for event in current_graph.astream({"next": "researcher"}, config):
            if event:
                yield event

    elif action == "QUESTION":
        question_message = f"""User has questions about the analysis approach: "{user_response}"

Please provide clear answers to address the user's questions and then ask for their decision on proceeding."""

        # Update state but keep awaiting confirmation
        updated_values = current_state.values.copy()
        updated_values["user_question"] = user_response
        updated_values["messages"] = current_state.values["messages"] + [
            HumanMessage(content=f"User: {user_response}", name="user"),
            HumanMessage(content=question_message, name="planner"),
        ]

        await current_graph.aupdate_state(config, updated_values)

        # Continue workflow from planner to answer questions
        async for event in current_graph.astream({"next": "planner"}, config):
            if event:
                yield event

    else:  # CLARIFY or unclear
        clarification_message = f"""User response needs clarification: "{user_response}"

Please ask the user to clarify their intent regarding the proposed analysis approach."""

        # Update state and ask for clarification
        updated_values = current_state.values.copy()
        updated_values["needs_clarification"] = True
        updated_values["messages"] = current_state.values["messages"] + [
            HumanMessage(content=f"User: {user_response}", name="user"),
            HumanMessage(content=clarification_message, name="planner"),
        ]

        await current_graph.aupdate_state(config, updated_values)

        # Continue workflow from planner
        async for event in current_graph.astream({"next": "planner"}, config):
            if event:
                yield event


async def run_agent_workflow(
    user_input_messages: list,
    thread_id: str = None,
    debug: bool = True,
    deep_thinking_mode: bool = False,
    search_before_planning: bool = False,
):
    """Run the agent workflow with the given user input."""
    if not user_input_messages:
        raise ValueError("Input could not be empty")

    if debug:
        enable_debug_logging()

    # Initialize graph with persistence if not already done
    current_graph = await initialize_graph()

    # Generate workflow and thread IDs
    workflow_id = str(uuid.uuid4())
    if thread_id is None:
        thread_id = workflow_id

    logger.info(
        f"Starting workflow with user input: {user_input_messages}, thread_id: {thread_id}"
    )

    # Create config for thread-based persistence
    config = {"configurable": {"thread_id": thread_id}}

    streaming_llm_agents = [*TEAM_MEMBERS, "planner"]

    global is_handoff_case
    is_handoff_case = False

    # Initialize HITL configuration
    from ..config.hitl_config import get_hitl_config
    hitl_config = get_hitl_config()
    
    # Initial state with minimal defaults to prevent KeyErrors
    initial_state = {
        # Constants
        "TEAM_MEMBERS": TEAM_MEMBERS,
        # Runtime Variables
        "messages": user_input_messages,
        "deep_thinking_mode": deep_thinking_mode,
        "search_before_planning": search_before_planning,
        # HITL Configuration
        "hitl_mode": hitl_config.mode.value,
        "awaiting_human_input": False,
        "interrupt_context": None,
        "human_feedback": None,
        "interrupt_history": [],
    }

    async for event in current_graph.astream_events(
        initial_state,
        config={**config, "recursion_limit": 50},
        version="v2",
    ):
        # Check for progress events from MCP tools
        progress_events = get_progress_events()
        for progress_event in progress_events:
            if progress_event["event_type"] in ["tool_progress", "tool_heartbeat"]:
                yield {
                    "event": progress_event["event_type"],
                    "data": {
                        "tool_name": progress_event["tool_name"],
                        "message": progress_event["message"],
                        "timestamp": progress_event["timestamp"],
                    },
                }
        kind = event.get("event")
        data = event.get("data")
        name = event.get("name")
        metadata = event.get("metadata")
        node = (
            ""
            if (metadata.get("checkpoint_ns") is None)
            else metadata.get("checkpoint_ns").split(":")[0]
        )
        langgraph_step = (
            ""
            if (metadata.get("langgraph_step") is None)
            else str(metadata["langgraph_step"])
        )
        run_id = "" if (event.get("run_id") is None) else str(event["run_id"])

        if kind == "on_chain_start" and name in streaming_llm_agents:
            if name == "planner":
                yield {
                    "event": "start_of_workflow",
                    "data": {"workflow_id": workflow_id, "input": user_input_messages},
                }
            ydata = {
                "event": "start_of_agent",
                "data": {
                    "agent_name": name,
                    "agent_id": f"{workflow_id}_{name}_{langgraph_step}",
                },
            }
        elif kind == "on_chain_end" and name in streaming_llm_agents:
            ydata = {
                "event": "end_of_agent",
                "data": {
                    "agent_name": name,
                    "agent_id": f"{workflow_id}_{name}_{langgraph_step}",
                },
            }
        elif kind == "on_chat_model_start" and node in streaming_llm_agents:
            ydata = {
                "event": "start_of_llm",
                "data": {"agent_name": node},
            }
        elif kind == "on_chat_model_end" and node in streaming_llm_agents:
            ydata = {
                "event": "end_of_llm",
                "data": {"agent_name": node},
            }
        elif kind == "on_chat_model_stream" and node in streaming_llm_agents:
            content = data["chunk"].content
            if content is None or content == "":
                if not data["chunk"].additional_kwargs.get("reasoning_content"):
                    # Skip empty messages
                    continue
                ydata = {
                    "event": "message",
                    "data": {
                        "message_id": data["chunk"].id,
                        "delta": {
                            "reasoning_content": (
                                data["chunk"].additional_kwargs["reasoning_content"]
                            )
                        },
                    },
                }
            else:
                # For other agents, send the message directly
                ydata = {
                    "event": "message",
                    "data": {
                        "message_id": data["chunk"].id,
                        "delta": {"content": content},
                    },
                }
        elif kind == "on_tool_start" and node in TEAM_MEMBERS:
            ydata = {
                "event": "tool_call",
                "data": {
                    "tool_call_id": f"{workflow_id}_{node}_{name}_{run_id}",
                    "tool_name": name,
                    "tool_input": data.get("input"),
                },
            }
        elif kind == "on_tool_end" and node in TEAM_MEMBERS:
            # Handle both string outputs and objects with content attribute
            output = data.get("output", "")
            if hasattr(output, "content"):
                tool_result = output.content
            elif isinstance(output, str):
                tool_result = output
            else:
                tool_result = str(output) if output else ""

            ydata = {
                "event": "tool_call_result",
                "data": {
                    "tool_call_id": f"{workflow_id}_{node}_{name}_{run_id}",
                    "tool_name": name,
                    "tool_result": tool_result,
                },
            }
        else:
            continue
        yield ydata

    if is_handoff_case:
        yield {
            "event": "end_of_workflow",
            "data": {
                "workflow_id": workflow_id,
                "messages": [
                    msg.dict() if hasattr(msg, "dict") else msg.model_dump()
                    for msg in data["output"].get("messages", [])
                ],
            },
        }
