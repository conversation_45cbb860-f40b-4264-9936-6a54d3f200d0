"""
Executive Summary Generation Service for Living Documents System

This service generates dynamic executive summaries that synthesize findings across
all sessions, maintaining BMAD-style clarity and organization.

Key Features:
- Multi-session content analysis and synthesis
- Key finding extraction with prioritization
- BMAD-style formatting and organization
- Incremental summary updates with session awareness
- LLM-powered content consolidation
"""

import os
import re
import json
import logging
import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from src.agents.llm import get_llm_by_type
from langchain_core.messages import HumanMessage

logger = logging.getLogger(__name__)

# Summary generation constants
SUMMARY_MAX_LENGTH = 500  # Maximum words for executive summary
KEY_FINDINGS_LIMIT = 10  # Maximum key findings to include
BMAD_STYLE_ELEMENTS = {
    "clarity_markers": ["**Key Point:**", "**Important:**", "**Note:**"],
    "organization_headers": [
        "## Executive Summary",
        "### Key Findings",
        "### Current Status",
    ],
    "bullet_format": "- **{category}:** {content}",
    "section_separator": "\n\n---\n\n",
}


class SummaryService:
    """
    Generates and manages executive summaries for living documents with multi-session synthesis.

    This service analyzes document content across sessions to extract key findings,
    synthesize insights, and generate BMAD-style executive summaries that provide
    quick overviews of research state.
    """

    def __init__(self, workspace_dir: str):
        """
        Initialize the summary service.

        Args:
            workspace_dir: Directory where documents are stored
        """
        self.workspace_dir = workspace_dir
        self.llm = get_llm_by_type("basic")  # Use basic LLM for summary generation

        # Key finding categories for bioinformatics analysis
        self.finding_categories = {
            "methodology": ["method", "approach", "technique", "protocol", "workflow"],
            "results": ["result", "finding", "outcome", "discovery", "observation"],
            "biological_significance": [
                "biological",
                "pathway",
                "function",
                "mechanism",
                "interaction",
            ],
            "technical_insights": [
                "performance",
                "optimization",
                "implementation",
                "validation",
            ],
            "limitations": [
                "limitation",
                "constraint",
                "issue",
                "challenge",
                "problem",
            ],
            "future_directions": [
                "future",
                "next",
                "recommendation",
                "improvement",
                "extension",
            ],
        }

        # Session content patterns for parsing
        self.session_patterns = {
            "session_header": r"## Session \d+ - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})",
            "session_id": r"\*\*Session ID:\*\* (.+)",
            "section_header": r"### (.+)",
            "key_finding": r"- \*\*(.+?):\*\* (.+)",
            "important_content": r"\*\*(.+?)\*\*",
        }

    def extract_session_content(self, document_path: str) -> List[Dict[str, Any]]:
        """
        Extract content from all sessions in a document.

        Args:
            document_path: Path to the document

        Returns:
            List of session data with extracted content
        """
        if not os.path.exists(document_path):
            logger.warning(f"Document not found: {document_path}")
            return []

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()

            sessions = []

            # Split content by session headers
            session_splits = re.split(r"---\n\n## Session \d+", content)

            if len(session_splits) <= 1:
                # No session splits found, treat entire document as single session
                sessions.append(
                    {
                        "session_number": 1,
                        "timestamp": (
                            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        ),
                        "session_id": "legacy",
                        "content": content,
                        "word_count": len(content.split()),
                        "key_sections": self._extract_key_sections(content),
                    }
                )
            else:
                for i, session_content in enumerate(session_splits[1:], 1):
                    # Add back the session header for parsing
                    session_content = f"## Session {i}" + session_content

                    # Extract session metadata
                    timestamp_match = re.search(
                        self.session_patterns["session_header"], session_content
                    )
                    session_id_match = re.search(
                        self.session_patterns["session_id"], session_content
                    )

                    session_data = {
                        "session_number": i,
                        "timestamp": (
                            timestamp_match.group(1) if timestamp_match else "unknown"
                        ),
                        "session_id": (
                            session_id_match.group(1)
                            if session_id_match
                            else f"session_{i}"
                        ),
                        "content": session_content,
                        "word_count": len(session_content.split()),
                        "key_sections": self._extract_key_sections(session_content),
                    }

                    sessions.append(session_data)

            logger.info(f"Extracted {len(sessions)} sessions from {document_path}")
            return sessions

        except Exception as e:
            logger.error(f"Failed to extract session content from {document_path}: {e}")
            return []

    def _extract_key_sections(self, content: str) -> Dict[str, str]:
        """
        Extract key sections from session content.

        Args:
            content: Session content to analyze

        Returns:
            Dictionary of section names and their content
        """
        sections = {}

        # Find all section headers and their content
        section_pattern = r"### (.+?)\n(.*?)(?=\n### |\n## |\Z)"
        matches = re.findall(section_pattern, content, re.DOTALL)

        for section_name, section_content in matches:
            sections[section_name.strip()] = section_content.strip()

        # Also extract content before first section header
        first_section = content.find("### ")
        if first_section > 0:
            intro_content = content[:first_section].strip()
            if intro_content:
                sections["introduction"] = intro_content

        return sections

    def extract_key_findings(
        self, sessions: List[Dict[str, Any]]
    ) -> List[Dict[str, str]]:
        """
        Extract and categorize key findings from all sessions.

        Args:
            sessions: List of session data

        Returns:
            List of categorized key findings
        """
        all_findings = []

        for session in sessions:
            session_findings = self._extract_session_findings(session)
            for finding in session_findings:
                finding["session_number"] = session["session_number"]
                finding["timestamp"] = session["timestamp"]
                all_findings.append(finding)

        # Deduplicate and prioritize findings
        deduplicated_findings = self._deduplicate_findings(all_findings)

        # Sort by importance and recency
        prioritized_findings = self._prioritize_findings(deduplicated_findings)

        return prioritized_findings[:KEY_FINDINGS_LIMIT]

    def _extract_session_findings(
        self, session: Dict[str, Any]
    ) -> List[Dict[str, str]]:
        """
        Extract findings from a single session.

        Args:
            session: Session data

        Returns:
            List of findings from the session
        """
        findings = []
        content = session["content"]

        # Extract explicit findings (marked with bold formatting)
        finding_matches = re.findall(self.session_patterns["key_finding"], content)
        for category, finding_text in finding_matches:
            findings.append(
                {
                    "category": self._categorize_finding(category, finding_text),
                    "content": finding_text.strip(),
                    "original_category": category.strip(),
                    "extraction_method": "explicit_markup",
                }
            )

        # Extract implicit findings from important content
        important_matches = re.findall(
            self.session_patterns["important_content"], content
        )
        for important_text in important_matches:
            if len(important_text) > 20 and not any(
                important_text in f["content"] for f in findings
            ):
                findings.append(
                    {
                        "category": self._categorize_finding("", important_text),
                        "content": important_text.strip(),
                        "original_category": "implicit",
                        "extraction_method": "important_markup",
                    }
                )

        # Extract findings from section summaries
        for section_name, section_content in session.get("key_sections", {}).items():
            section_findings = self._extract_section_findings(
                section_name, section_content
            )
            findings.extend(section_findings)

        return findings

    def _extract_section_findings(
        self, section_name: str, section_content: str
    ) -> List[Dict[str, str]]:
        """
        Extract findings from a specific section.

        Args:
            section_name: Name of the section
            section_content: Content of the section

        Returns:
            List of findings from the section
        """
        findings = []

        # Extract bullet points as potential findings
        bullet_pattern = r"- (.+?)(?=\n- |\n\n|\Z)"
        bullet_matches = re.findall(bullet_pattern, section_content, re.DOTALL)

        for bullet_text in bullet_matches:
            bullet_text = bullet_text.strip()
            if len(bullet_text) > 15:  # Filter out short bullets
                findings.append(
                    {
                        "category": self._categorize_finding(section_name, bullet_text),
                        "content": bullet_text,
                        "original_category": section_name,
                        "extraction_method": "section_analysis",
                    }
                )

        return findings

    def _categorize_finding(self, context: str, content: str) -> str:
        """
        Categorize a finding based on context and content.

        Args:
            context: Context information (section name, original category)
            content: Finding content

        Returns:
            Categorized finding type
        """
        combined_text = f"{context} {content}".lower()

        # Score against each category
        category_scores = {}
        for category, keywords in self.finding_categories.items():
            score = sum(1 for keyword in keywords if keyword in combined_text)
            category_scores[category] = score

        # Return highest scoring category
        if category_scores and max(category_scores.values()) > 0:
            return max(category_scores.items(), key=lambda x: x[1])[0]

        return "general"

    def _deduplicate_findings(
        self, findings: List[Dict[str, str]]
    ) -> List[Dict[str, str]]:
        """
        Remove duplicate findings based on content similarity.

        Args:
            findings: List of findings to deduplicate

        Returns:
            Deduplicated list of findings
        """
        unique_findings = []

        for finding in findings:
            is_duplicate = False
            finding_content = finding["content"].lower()

            for unique_finding in unique_findings:
                unique_content = unique_finding["content"].lower()

                # Simple similarity check - if 80% of words overlap, consider duplicate
                finding_words = set(finding_content.split())
                unique_words = set(unique_content.split())

                if finding_words and unique_words:
                    overlap = len(finding_words.intersection(unique_words))
                    similarity = overlap / min(len(finding_words), len(unique_words))

                    if similarity > 0.8:
                        is_duplicate = True
                        break

            if not is_duplicate:
                unique_findings.append(finding)

        return unique_findings

    def _prioritize_findings(
        self, findings: List[Dict[str, str]]
    ) -> List[Dict[str, str]]:
        """
        Prioritize findings by importance and recency.

        Args:
            findings: List of findings to prioritize

        Returns:
            Prioritized list of findings
        """
        # Assign priority scores based on category importance
        category_priorities = {
            "results": 10,
            "biological_significance": 9,
            "methodology": 8,
            "technical_insights": 7,
            "limitations": 6,
            "future_directions": 5,
            "general": 3,
        }

        for finding in findings:
            category = finding.get("category", "general")
            finding["priority_score"] = category_priorities.get(category, 3)

            # Boost score for recent sessions
            session_number = finding.get("session_number", 1)
            finding["priority_score"] += session_number * 0.5

            # Boost score for longer, more detailed findings
            content_length = len(finding["content"])
            if content_length > 100:
                finding["priority_score"] += 2
            elif content_length > 50:
                finding["priority_score"] += 1

        # Sort by priority score (descending)
        return sorted(findings, key=lambda x: x["priority_score"], reverse=True)

    def generate_summary_content(
        self,
        document_path: str,
        sessions: List[Dict[str, Any]],
        key_findings: List[Dict[str, str]],
    ) -> str:
        """
        Generate executive summary content using LLM synthesis.

        Args:
            document_path: Path to the document
            sessions: List of session data
            key_findings: List of key findings

        Returns:
            Generated executive summary content
        """
        try:
            # Prepare context for LLM
            context = self._prepare_summary_context(
                document_path, sessions, key_findings
            )

            # Generate summary using LLM
            prompt = self._create_summary_prompt(context)

            response = self.llm.invoke([HumanMessage(content=prompt)])
            summary_content = response.content.strip()

            # Post-process to ensure BMAD-style formatting
            formatted_summary = self._apply_bmad_formatting(summary_content)

            logger.info(f"Generated executive summary for {document_path}")
            return formatted_summary

        except Exception as e:
            logger.error(f"Failed to generate summary content: {e}")
            # Return fallback summary
            return self._generate_fallback_summary(sessions, key_findings)

    def _prepare_summary_context(
        self,
        document_path: str,
        sessions: List[Dict[str, Any]],
        key_findings: List[Dict[str, str]],
    ) -> Dict[str, Any]:
        """
        Prepare context information for summary generation.

        Args:
            document_path: Path to the document
            sessions: List of session data
            key_findings: List of key findings

        Returns:
            Context dictionary for LLM prompt
        """
        # Extract document metadata
        doc_name = os.path.basename(document_path).replace(".md", "")
        analysis_type = "General"
        subject = "Analysis"

        if "_" in doc_name:
            parts = doc_name.split("_")
            if len(parts) >= 3 and parts[0] == "Analysis":
                analysis_type = parts[1]
                subject = parts[2]

        # Calculate session statistics
        total_sessions = len(sessions)
        total_words = sum(s["word_count"] for s in sessions)
        latest_session = (
            max(sessions, key=lambda s: s["session_number"]) if sessions else None
        )

        # Categorize findings by type
        findings_by_category = {}
        for finding in key_findings:
            category = finding.get("category", "general")
            if category not in findings_by_category:
                findings_by_category[category] = []
            findings_by_category[category].append(finding)

        return {
            "document_name": doc_name,
            "analysis_type": analysis_type,
            "subject": subject,
            "total_sessions": total_sessions,
            "total_words": total_words,
            "latest_timestamp": (
                latest_session["timestamp"] if latest_session else "unknown"
            ),
            "findings_by_category": findings_by_category,
            "key_findings": key_findings[:5],  # Top 5 findings for prompt
            "session_count": total_sessions,
        }

    def _create_summary_prompt(self, context: Dict[str, Any]) -> str:
        """
        Create LLM prompt for summary generation.

        Args:
            context: Context information

        Returns:
            Formatted prompt string
        """
        findings_text = ""
        for i, finding in enumerate(context["key_findings"], 1):
            findings_text += f"{i}. **{finding.get('category', 'general').title()}:** {finding['content']}\n"

        prompt = f"""
Generate a concise, professional executive summary for a bioinformatics analysis document.

DOCUMENT CONTEXT:
- Analysis Type: {context['analysis_type']}
- Subject: {context['subject']}
- Total Sessions: {context['total_sessions']}
- Latest Update: {context['latest_timestamp']}
- Total Content: {context['total_words']} words

TOP KEY FINDINGS:
{findings_text}

REQUIREMENTS:
1. Write a clear, concise executive summary (maximum 150 words)
2. Use BMAD-style formatting with bold markers for key points
3. Organize content with bullet points and clear structure
4. Focus on the most important findings and current status
5. Include analysis progress and key insights
6. Use professional, scientific tone appropriate for bioinformatics

FORMAT:
Use markdown formatting with:
- Bold text for **Key Points**
- Bullet points for organization
- Clear, scannable structure
- Scientific terminology where appropriate

Generate only the executive summary content, no additional text or headers.
"""

        return prompt.strip()

    def _apply_bmad_formatting(self, content: str) -> str:
        """
        Apply BMAD-style formatting to summary content.

        Args:
            content: Raw summary content

        Returns:
            BMAD-formatted summary content
        """
        # Ensure proper markdown formatting
        formatted = content.strip()

        # Add bold formatting to key phrases if not already present
        key_phrases = [
            "key finding",
            "important result",
            "significant",
            "notable",
            "primary outcome",
            "main result",
            "conclusion",
            "discovery",
        ]

        for phrase in key_phrases:
            pattern = f"\\b({phrase})\\b"
            replacement = f"**{phrase}**"
            formatted = re.sub(pattern, replacement, formatted, flags=re.IGNORECASE)

        # Ensure proper bullet point formatting
        lines = formatted.split("\n")
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if (
                line
                and not line.startswith("- ")
                and not line.startswith("**")
                and not line.startswith("#")
            ):
                # Check if line looks like a bullet point
                if any(
                    line.lower().startswith(word)
                    for word in ["the", "this", "analysis", "results", "findings"]
                ):
                    line = f"- {line}"
            formatted_lines.append(line)

        return "\n".join(formatted_lines)

    def _generate_fallback_summary(
        self, sessions: List[Dict[str, Any]], key_findings: List[Dict[str, str]]
    ) -> str:
        """
        Generate fallback summary when LLM generation fails.

        Args:
            sessions: List of session data
            key_findings: List of key findings

        Returns:
            Fallback summary content
        """
        total_sessions = len(sessions)
        latest_session = (
            max(sessions, key=lambda s: s["session_number"]) if sessions else None
        )

        summary = f"""**Analysis Overview:** This document contains {total_sessions} analysis session{'s' if total_sessions != 1 else ''} with ongoing research and findings.

**Current Status:** Analysis is actively being updated with new sessions and insights."""

        if latest_session:
            summary += f" Latest update: {latest_session['timestamp']}."

        if key_findings:
            summary += "\n\n**Key Findings:**\n"
            for i, finding in enumerate(key_findings[:3], 1):
                category = finding.get("category", "general").replace("_", " ").title()
                summary += f"- **{category}:** {finding['content'][:100]}{'...' if len(finding['content']) > 100 else ''}\n"

        summary += "\n\n**Note:** This summary will be updated as analysis progresses with additional sessions and findings."

        return summary

    def update_document_summary(self, document_path: str, new_summary: str) -> bool:
        """
        Update the executive summary section in a living document.

        Args:
            document_path: Path to the document
            new_summary: New summary content

        Returns:
            True if update was successful, False otherwise
        """
        if not os.path.exists(document_path):
            logger.error(
                f"Cannot update summary in non-existent document: {document_path}"
            )
            return False

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Find executive summary section
            summary_start = content.find("## Executive Summary")
            if summary_start == -1:
                logger.warning(f"No Executive Summary section found in {document_path}")
                return False

            # Find the end of the executive summary section
            summary_end = content.find("\n## ", summary_start + 1)
            if summary_end == -1:
                summary_end = len(content)

            # Replace the executive summary content
            before_summary = content[:summary_start]
            after_summary = content[summary_end:]

            new_section = f"## Executive Summary\n\n{new_summary}\n\n"
            updated_content = before_summary + new_section + after_summary

            # Write updated content
            with open(document_path, "w", encoding="utf-8") as f:
                f.write(updated_content)

            logger.info(f"Updated executive summary in {document_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to update document summary: {e}")
            return False

    def generate_executive_summary(self, document_path: str) -> Optional[str]:
        """
        Generate a complete executive summary for a document.

        Args:
            document_path: Path to the document

        Returns:
            Generated executive summary content, or None if failed
        """
        try:
            # Extract session content
            sessions = self.extract_session_content(document_path)
            if not sessions:
                logger.warning(f"No sessions found in {document_path}")
                return None

            # Extract key findings
            key_findings = self.extract_key_findings(sessions)

            # Generate summary content
            summary_content = self.generate_summary_content(
                document_path, sessions, key_findings
            )

            return summary_content

        except Exception as e:
            logger.error(
                f"Failed to generate executive summary for {document_path}: {e}"
            )
            return None

    def get_summary_metadata(self, document_path: str) -> Dict[str, Any]:
        """
        Get metadata about the summary generation process.

        Args:
            document_path: Path to the document

        Returns:
            Metadata dictionary
        """
        try:
            sessions = self.extract_session_content(document_path)
            key_findings = self.extract_key_findings(sessions)

            return {
                "total_sessions": len(sessions),
                "total_findings": len(key_findings),
                "last_updated": datetime.datetime.now().isoformat(),
                "word_count": sum(s["word_count"] for s in sessions),
                "finding_categories": list(
                    set(f.get("category", "general") for f in key_findings)
                ),
                "summary_length": len(
                    self.generate_summary_content(
                        document_path, sessions, key_findings
                    ).split()
                ),
            }

        except Exception as e:
            logger.error(f"Failed to get summary metadata: {e}")
            return {}
