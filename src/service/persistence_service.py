"""Persistence service for managing conversation state and history."""

import logging
from typing import List, Dict, Any, Optional
from src.graph.builder import create_checkpointer
from src.graph import build_graph

logger = logging.getLogger(__name__)

# Global checkpointer instance
_checkpointer = None
_graph = None


async def get_checkpointer():
    """Get or create the global checkpointer instance."""
    global _checkpointer
    if _checkpointer is None:
        _checkpointer = await create_checkpointer()
    return _checkpointer


async def get_persistent_graph():
    """Get or create the persistent graph instance."""
    global _graph
    if _graph is None:
        checkpointer = await get_checkpointer()
        _graph = build_graph(checkpointer)
    return _graph


async def get_conversation_state(
    thread_id: str, checkpoint_id: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """Get the current state of a conversation thread.

    Args:
        thread_id: The thread identifier
        checkpoint_id: Optional specific checkpoint ID, defaults to latest

    Returns:
        The conversation state or None if not found
    """
    try:
        graph = await get_persistent_graph()
        config = {"configurable": {"thread_id": thread_id}}
        if checkpoint_id:
            config["configurable"]["checkpoint_id"] = checkpoint_id

        state = await graph.aget_state(config)
        return state.values if state else None

    except Exception as e:
        logger.error(f"Error retrieving conversation state for thread {thread_id}: {e}")
        return None


async def get_conversation_history(
    thread_id: str, limit: Optional[int] = None
) -> List[Dict[str, Any]]:
    """Get the conversation history for a thread.

    Args:
        thread_id: The thread identifier
        limit: Optional limit on number of history entries

    Returns:
        List of conversation states in chronological order (most recent first)
    """
    try:
        graph = await get_persistent_graph()
        config = {"configurable": {"thread_id": thread_id}}

        history = []
        async for state in graph.aget_state_history(config):
            history.append(state)
            if limit and len(history) >= limit:
                break

        return [
            {
                "checkpoint_id": state.config["configurable"]["checkpoint_id"],
                "values": state.values,
                "metadata": state.metadata,
                "created_at": state.created_at,
                "next": state.next,
            }
            for state in history
        ]

    except Exception as e:
        logger.error(
            f"Error retrieving conversation history for thread {thread_id}: {e}"
        )
        return []


async def list_conversation_threads() -> List[str]:
    """List all available conversation thread IDs.

    Returns:
        List of thread IDs
    """
    try:
        checkpointer = await get_checkpointer()

        # This is a simplified implementation - actual implementation depends on checkpointer type
        # For SQLite checkpointer, you might need to query the database directly
        logger.warning(
            "list_conversation_threads not fully implemented - depends on checkpointer type"
        )
        return []

    except Exception as e:
        logger.error(f"Error listing conversation threads: {e}")
        return []


# Note: Additional utility functions can be added here as needed
# Focus on core time-travel functionality for now


async def update_conversation_state(
    thread_id: str, checkpoint_id: str, new_values: Dict[str, Any]
) -> Optional[str]:
    """Update state at a specific checkpoint and return new checkpoint ID.

    This implements time-travel functionality by allowing modification of state
    at any checkpoint in the conversation history.

    Args:
        thread_id: The thread identifier
        checkpoint_id: The checkpoint ID to modify
        new_values: New values to update in the state

    Returns:
        New checkpoint ID if successful, None otherwise
    """
    try:
        graph = await get_persistent_graph()
        config = {
            "configurable": {"thread_id": thread_id, "checkpoint_id": checkpoint_id}
        }

        # Update state at the specified checkpoint
        new_config = await graph.aupdate_state(config, values=new_values)
        new_checkpoint_id = new_config["configurable"]["checkpoint_id"]

        logger.info(
            f"Updated state for thread {thread_id} at checkpoint {checkpoint_id}, new checkpoint: {new_checkpoint_id}"
        )
        return new_checkpoint_id

    except Exception as e:
        logger.error(
            f"Error updating conversation state for thread {thread_id} at checkpoint {checkpoint_id}: {e}"
        )
        return None


async def resume_from_checkpoint(thread_id: str, checkpoint_id: str):
    """Resume execution from a specific checkpoint.

    This implements time-travel functionality by allowing resumption of workflow
    execution from any previous checkpoint.

    Args:
        thread_id: The thread identifier
        checkpoint_id: The checkpoint ID to resume from

    Yields:
        Events from the resumed workflow execution
    """
    try:
        graph = await get_persistent_graph()
        config = {
            "configurable": {"thread_id": thread_id, "checkpoint_id": checkpoint_id}
        }

        logger.info(
            f"Resuming execution for thread {thread_id} from checkpoint {checkpoint_id}"
        )

        # Resume execution with None input (continue from checkpoint)
        async for event in graph.astream(None, config=config):
            yield event

    except Exception as e:
        logger.error(
            f"Error resuming from checkpoint {checkpoint_id} for thread {thread_id}: {e}"
        )
        yield {"error": f"Failed to resume from checkpoint: {str(e)}"}


async def get_checkpoint_details(
    thread_id: str, checkpoint_id: str
) -> Optional[Dict[str, Any]]:
    """Get detailed information about a specific checkpoint.

    Args:
        thread_id: The thread identifier
        checkpoint_id: The checkpoint ID to examine

    Returns:
        Checkpoint details or None if not found
    """
    try:
        graph = await get_persistent_graph()
        config = {
            "configurable": {"thread_id": thread_id, "checkpoint_id": checkpoint_id}
        }

        state = await graph.aget_state(config)
        if state:
            return {
                "checkpoint_id": state.config["configurable"]["checkpoint_id"],
                "values": state.values,
                "metadata": state.metadata,
                "created_at": state.created_at,
                "next": state.next,
                "parent_config": state.parent_config,
            }
        return None

    except Exception as e:
        logger.error(
            f"Error getting checkpoint details for {checkpoint_id} in thread {thread_id}: {e}"
        )
        return None


async def list_checkpoints_for_thread(
    thread_id: str, limit: Optional[int] = None
) -> List[Dict[str, Any]]:
    """List all checkpoints for a thread with basic information.

    Args:
        thread_id: The thread identifier
        limit: Optional limit on number of checkpoints

    Returns:
        List of checkpoint summaries
    """
    try:
        history = await get_conversation_history(thread_id, limit)

        # Return simplified checkpoint information for easier navigation
        return [
            {
                "checkpoint_id": item["checkpoint_id"],
                "created_at": item["created_at"],
                "next": item["next"],
                "has_messages": bool(item["values"].get("messages", [])),
                "message_count": len(item["values"].get("messages", [])),
            }
            for item in history
        ]

    except Exception as e:
        logger.error(f"Error listing checkpoints for thread {thread_id}: {e}")
        return []
