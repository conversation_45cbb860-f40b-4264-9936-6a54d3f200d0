"""Time Travel Service for LangGraph Workflows.

This module provides high-level time-travel functionality for LangGraph workflows,
allowing users to explore alternative conversation paths, modify agent decisions
at specific points, and resume workflows from any previous state.
"""

import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from .persistence_service import (
    get_conversation_history,
    get_checkpoint_details,
    update_conversation_state,
    resume_from_checkpoint,
    list_checkpoints_for_thread,
)

logger = logging.getLogger(__name__)


class TimeTravel:
    """High-level interface for time-travel functionality."""

    def __init__(self, thread_id: str):
        """Initialize time travel for a specific thread.

        Args:
            thread_id: The conversation thread to work with
        """
        self.thread_id = thread_id

    async def list_checkpoints(
        self, limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """List all available checkpoints for this thread.

        Args:
            limit: Optional limit on number of checkpoints

        Returns:
            List of checkpoint summaries with navigation info
        """
        return await list_checkpoints_for_thread(self.thread_id, limit)

    async def get_checkpoint(self, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific checkpoint.

        Args:
            checkpoint_id: The checkpoint to examine

        Returns:
            Detailed checkpoint information
        """
        return await get_checkpoint_details(self.thread_id, checkpoint_id)

    async def modify_state(
        self, checkpoint_id: str, modifications: Dict[str, Any]
    ) -> Optional[str]:
        """Modify the state at a specific checkpoint.

        This creates a new branch in the conversation timeline.

        Args:
            checkpoint_id: The checkpoint to modify
            modifications: State changes to apply

        Returns:
            New checkpoint ID if successful
        """
        logger.info(
            f"Modifying state at checkpoint {checkpoint_id} for thread {self.thread_id}"
        )
        return await update_conversation_state(
            self.thread_id, checkpoint_id, modifications
        )

    async def resume_from(
        self, checkpoint_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Resume workflow execution from a specific checkpoint.

        Args:
            checkpoint_id: The checkpoint to resume from

        Yields:
            Events from the resumed workflow execution
        """
        logger.info(
            f"Resuming workflow from checkpoint {checkpoint_id} for thread {self.thread_id}"
        )
        async for event in resume_from_checkpoint(self.thread_id, checkpoint_id):
            yield event

    async def explore_alternative(
        self, checkpoint_id: str, new_input: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Explore an alternative path by modifying state and resuming.

        This is a convenience method that combines state modification and resumption.

        Args:
            checkpoint_id: The checkpoint to branch from
            new_input: New state values to explore

        Yields:
            Events from the alternative execution path
        """
        # First, modify the state
        new_checkpoint = await self.modify_state(checkpoint_id, new_input)
        if new_checkpoint:
            # Then resume from the new checkpoint
            async for event in self.resume_from(new_checkpoint):
                yield event
        else:
            yield {"error": "Failed to create alternative branch"}

    async def get_conversation_timeline(self) -> List[Dict[str, Any]]:
        """Get a timeline view of the conversation with checkpoint information.

        Returns:
            Chronological list of conversation states
        """
        history = await get_conversation_history(self.thread_id)

        timeline = []
        for i, item in enumerate(
            reversed(history)
        ):  # Reverse to get chronological order
            timeline_item = {
                "step": i + 1,
                "checkpoint_id": item["checkpoint_id"],
                "created_at": item["created_at"],
                "next_agent": item["next"],
                "message_count": len(item["values"].get("messages", [])),
                "last_message": None,
            }

            # Get the last message for context
            messages = item["values"].get("messages", [])
            if messages:
                last_msg = messages[-1]
                timeline_item["last_message"] = {
                    "type": getattr(last_msg, "type", "unknown"),
                    "content": (
                        getattr(last_msg, "content", str(last_msg))[:100] + "..."
                        if len(str(getattr(last_msg, "content", last_msg))) > 100
                        else getattr(last_msg, "content", str(last_msg))
                    ),
                }

            timeline.append(timeline_item)

        return timeline


async def create_time_travel_session(thread_id: str) -> TimeTravel:
    """Create a new time travel session for a thread.

    Args:
        thread_id: The conversation thread to work with

    Returns:
        TimeTravel instance for the thread
    """
    return TimeTravel(thread_id)


async def quick_explore_alternative(
    thread_id: str, checkpoint_id: str, modifications: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """Quick utility to explore an alternative path and collect all results.

    Args:
        thread_id: The conversation thread
        checkpoint_id: The checkpoint to branch from
        modifications: State changes to apply

    Returns:
        List of all events from the alternative execution
    """
    time_travel = await create_time_travel_session(thread_id)
    events = []

    async for event in time_travel.explore_alternative(checkpoint_id, modifications):
        events.append(event)

    return events
