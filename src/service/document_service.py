"""
Document Management Service for Living Documents System

This service provides semantic context identification, document existence checking,
and intelligent naming for living documents that replace UUID-based reports.

Key Features:
- Semantic naming: Analysis_{AnalysisType}_{Subject}.md
- Multi-domain support (bioinformatics, ML, data science, software engineering)
- Document existence checking with exact match and semantic similarity
- Analysis type detection with 6+ analysis patterns
- Subject extraction with biological term recognition
- Backup system for existing document updates
"""

import os
import re
import json
import logging
import datetime
import tempfile
import uuid
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)

# Session handling constants
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
SESSION_HEADER_SEPARATOR = "\n---\n"
SESSION_TIMESTAMP_REGEX = r"Session \d+ - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})"


class DocumentService:
    """
    Manages living documents with semantic context identification and intelligent naming.

    This service replaces UUID-based report naming with semantic analysis of the research
    context to create meaningful document names and manage document lifecycle.
    """

    def __init__(self, workspace_dir: str):
        """
        Initialize the document service with workspace directory.

        Args:
            workspace_dir: Directory where documents will be stored
        """
        self.workspace_dir = workspace_dir
        self.ensure_workspace_exists()

        # Analysis type patterns for different domains
        self.analysis_patterns = {
            "differential_expression": [
                "differential expression",
                "diff expression",
                "de analysis",
                "deseq",
                "gene expression",
                "rna-seq",
                "rnaseq",
                "transcriptome",
            ],
            "protein_analysis": [
                "protein analysis",
                "proteomics",
                "protein structure",
                "protein function",
                "protein-protein interaction",
                "ppi",
                "mass spectrometry",
            ],
            "genomic_analysis": [
                "genomic analysis",
                "genome analysis",
                "dna analysis",
                "genomics",
                "variant analysis",
                "snp analysis",
                "gwas",
                "whole genome",
            ],
            "pathway_analysis": [
                "pathway analysis",
                "enrichment analysis",
                "gene ontology",
                "go analysis",
                "kegg",
                "reactome",
                "functional enrichment",
            ],
            "machine_learning": [
                "machine learning",
                "ml model",
                "classification",
                "regression",
                "clustering",
                "deep learning",
                "neural network",
                "prediction",
            ],
            "data_analysis": [
                "data analysis",
                "statistical analysis",
                "correlation analysis",
                "pca analysis",
                "dimensionality reduction",
                "data exploration",
            ],
        }

        # Subject extraction patterns for biological and technical contexts
        self.subject_patterns = {
            "biological": [
                r"\b(cancer|tumor|carcinoma|leukemia|lymphoma)\b",
                r"\b(breast|lung|prostate|colon|liver|brain|ovarian)\s+(cancer|tumor)\b",
                r"\b(covid|sars|coronavirus|influenza|hiv|hepatitis)\b",
                r"\b(diabetes|alzheimer|parkinson|huntington)\b",
                r"\b(mouse|human|rat|zebrafish|drosophila|c\.elegans)\b",
                r"\b(cell|tissue|organ|blood|plasma|serum)\b",
                r"\b(gene|protein|enzyme|antibody|receptor)\b",
            ],
            "technical": [
                r"\b(dataset|data|sample|cohort|study)\b",
                r"\b(model|algorithm|method|approach|technique)\b",
                r"\b(analysis|workflow|pipeline|process)\b",
            ],
        }

        # Domain-specific exclusion terms that shouldn't be subjects
        self.exclusion_terms = {
            "analysis",
            "study",
            "research",
            "investigation",
            "examination",
            "workflow",
            "pipeline",
            "process",
            "method",
            "approach",
            "technique",
            "data",
            "dataset",
            "sample",
            "result",
            "finding",
            "conclusion",
        }

    def ensure_workspace_exists(self) -> None:
        """Ensure the workspace directory exists."""
        os.makedirs(self.workspace_dir, exist_ok=True)
        logger.info(f"Workspace directory ensured: {self.workspace_dir}")

    def identify_semantic_context(
        self, goal: str, plan_title: str = "", plan_steps: List[Dict] = None
    ) -> Dict[str, str]:
        """
        Identify semantic context from goal, plan title, and steps.

        Args:
            goal: The research goal or objective
            plan_title: The title of the analysis plan
            plan_steps: List of plan steps with descriptions

        Returns:
            Dictionary with analysis_type, subject, and domain
        """
        combined_text = f"{goal} {plan_title}"
        if plan_steps:
            step_text = " ".join([step.get("description", "") for step in plan_steps])
            combined_text += f" {step_text}"

        combined_text = combined_text.lower()

        # Detect analysis type
        analysis_type = self._detect_analysis_type(combined_text)

        # Extract subject
        subject = self._extract_subject(combined_text)

        # Determine domain
        domain = self._determine_domain(combined_text, analysis_type)

        return {"analysis_type": analysis_type, "subject": subject, "domain": domain}

    def _detect_analysis_type(self, text: str) -> str:
        """
        Detect the type of analysis from text content with entity-aware logic.

        Args:
            text: Combined text from goal, plan title, and steps

        Returns:
            Detected analysis type or 'General' if none found
        """
        text_lower = text.lower()

        # PRIORITY 1: Entity-specific analysis type detection
        entity_analysis_type = self._detect_entity_specific_analysis(text_lower)
        if entity_analysis_type:
            return entity_analysis_type

        # PRIORITY 2: Pattern-based analysis type detection
        type_scores = {}
        for analysis_type, patterns in self.analysis_patterns.items():
            score = 0
            for pattern in patterns:
                if pattern in text_lower:
                    # Give higher scores for exact matches and longer patterns
                    score += len(pattern.split())
            type_scores[analysis_type] = score

        # Return the highest scoring analysis type
        if type_scores and max(type_scores.values()) > 0:
            best_type = max(type_scores.items(), key=lambda x: x[1])[0]
            # Convert to title case for naming
            return best_type.replace("_", " ").title().replace(" ", "")

        return "General"

    def _detect_entity_specific_analysis(self, text_lower: str) -> Optional[str]:
        """
        Detect analysis type based on specific entities and databases queried.
        This ensures that queries about the same entity type get the same analysis type.

        Args:
            text_lower: Lowercased text to analyze

        Returns:
            Specific analysis type or None if not entity-specific
        """
        # Protein-related queries (UniProt, AlphaFold, PDB, etc.)
        protein_indicators = [
            "uniprot",
            "alphafold",
            "pdb",
            "protein",
            "proteomic",
            "peptide",
            "amino acid",
            "structural biology",
        ]

        # Gene-related queries
        gene_indicators = [
            "gene",
            "ensembl",
            "refseq",
            "genetic",
            "genomic",
            "genome",
            "dna",
            "nucleotide",
            "chromosome",
        ]

        # Compound/Drug-related queries
        compound_indicators = [
            "chembl",
            "pubchem",
            "compound",
            "drug",
            "molecule",
            "chemical",
            "pharmacology",
            "ligand",
        ]

        # Pathway-related queries
        pathway_indicators = [
            "kegg",
            "reactome",
            "pathway",
            "network",
            "interaction",
            "signaling",
            "metabolic",
        ]

        # Check for specific entity types
        if any(indicator in text_lower for indicator in protein_indicators):
            return "ProteinAnalysis"
        elif any(indicator in text_lower for indicator in gene_indicators):
            return "GenomicAnalysis"
        elif any(indicator in text_lower for indicator in compound_indicators):
            return "CompoundAnalysis"
        elif any(indicator in text_lower for indicator in pathway_indicators):
            return "PathwayAnalysis"

        # Check for specific database queries that indicate analysis type
        if "query" in text_lower:
            if any(db in text_lower for db in ["uniprot", "alphafold", "pdb"]):
                return "ProteinAnalysis"
            elif any(db in text_lower for db in ["ensembl", "genbank", "refseq"]):
                return "GenomicAnalysis"
            elif any(db in text_lower for db in ["kegg", "reactome"]):
                return "PathwayAnalysis"

        return None

    def _extract_subject(self, text: str) -> str:
        """
        Extract the main subject from text content with enhanced intelligence.
        Prioritizes specific identifiers over generic terms.

        Args:
            text: Combined text from goal, plan title, and steps

        Returns:
            Extracted subject or 'Analysis' if none found
        """
        text_lower = text.lower()

        # PRIORITY 1: Specific biological identifiers (highest priority)
        specific_identifiers = self._extract_specific_identifiers(text)
        if specific_identifiers:
            return specific_identifiers[
                0
            ]  # Return the first (most specific) identifier

        # PRIORITY 2: Biological subjects from patterns
        subjects = []
        for pattern in self.subject_patterns["biological"]:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            subjects.extend(matches)

        # PRIORITY 3: Technical subjects as fallback
        if not subjects:
            for pattern in self.subject_patterns["technical"]:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                subjects.extend(matches)

        if subjects:
            # Filter out common exclusion terms
            filtered_subjects = []
            for subject in subjects:
                if isinstance(subject, tuple):
                    # Handle regex groups - take the most specific match
                    subject = max(subject, key=len) if subject else ""

                subject = subject.strip().lower()
                if subject and subject not in self.exclusion_terms:
                    filtered_subjects.append(subject)

            if filtered_subjects:
                # Choose the most specific (longest) subject
                best_subject = max(filtered_subjects, key=len)
                return best_subject.title().replace(" ", "")

        # PRIORITY 4: Enhanced fallback for meaningful words
        words = re.findall(r"\b[a-zA-Z]{3,}\b", text_lower)
        meaningful_words = []

        for word in words:
            if (
                word not in self.exclusion_terms
                and len(word) >= 4
                and not word.isdigit()
                and word
                not in ["with", "from", "that", "this", "have", "been", "were", "will"]
            ):
                meaningful_words.append(word)

        if meaningful_words:
            # Prefer biological/domain-specific terms
            bio_terms = [
                "protein",
                "gene",
                "cell",
                "cancer",
                "tumor",
                "disease",
                "treatment",
            ]
            for word in meaningful_words:
                if any(bio_term in word for bio_term in bio_terms):
                    return word.title()

            # Otherwise return the first meaningful word
            return meaningful_words[0].title()

        return "Analysis"

    def _extract_specific_identifiers(self, text: str) -> List[str]:
        """
        Extract specific biological identifiers with highest priority.

        Args:
            text: Text to search for identifiers

        Returns:
            List of found identifiers, ordered by specificity
        """
        identifiers = []

        # UniProt accession numbers (e.g., Q86VK4, P12345)
        uniprot_pattern = r"\b[A-NR-Z][0-9][A-Z][A-Z0-9][A-Z0-9][0-9]\b|\b[OPQ][0-9][A-Z0-9][A-Z0-9][A-Z0-9][0-9]\b"
        uniprot_matches = re.findall(uniprot_pattern, text, re.IGNORECASE)
        identifiers.extend([match.upper() for match in uniprot_matches])

        # Gene symbols (e.g., BRCA1, TP53, EGFR) - 3-10 characters, all caps
        gene_pattern = r"\b[A-Z]{3,10}[0-9]*\b"
        gene_matches = re.findall(gene_pattern, text)
        # Filter out common false positives
        gene_exclusions = {
            "DNA",
            "RNA",
            "ATP",
            "GDP",
            "GTP",
            "ADP",
            "NAD",
            "NADH",
            "FAD",
            "HTTP",
            "HTTPS",
            "URL",
            "API",
        }
        gene_matches = [
            match
            for match in gene_matches
            if match not in gene_exclusions and len(match) >= 3
        ]
        identifiers.extend(gene_matches)

        # Ensembl IDs (e.g., ENSG00000139618)
        ensembl_pattern = r"\bENS[GT][0-9]{11}\b"
        ensembl_matches = re.findall(ensembl_pattern, text, re.IGNORECASE)
        identifiers.extend([match.upper() for match in ensembl_matches])

        # RefSeq IDs (e.g., NM_000546, NP_000537)
        refseq_pattern = r"\b(NM_|NP_|XM_|XP_)[0-9]{6,}\b"
        refseq_matches = re.findall(refseq_pattern, text, re.IGNORECASE)
        identifiers.extend([match.upper() for match in refseq_matches])

        # PDB IDs (e.g., 1ABC, 2XYZ)
        pdb_pattern = r"\b[0-9][A-Z0-9]{3}\b"
        pdb_matches = re.findall(pdb_pattern, text, re.IGNORECASE)
        identifiers.extend([match.upper() for match in pdb_matches])

        # Chemical compound IDs (e.g., CHEMBL123456, PUBCHEM:123456)
        compound_pattern = r"\b(CHEMBL|PUBCHEM:|CID:)\d+\b"
        compound_matches = re.findall(compound_pattern, text, re.IGNORECASE)
        identifiers.extend([match.upper() for match in compound_matches])

        # Remove duplicates while preserving order
        seen = set()
        unique_identifiers = []
        for identifier in identifiers:
            if identifier not in seen:
                seen.add(identifier)
                unique_identifiers.append(identifier)

        return unique_identifiers

    def _determine_domain(self, text: str, analysis_type: str) -> str:
        """
        Determine the research domain based on text and analysis type.

        Args:
            text: Combined text content
            analysis_type: Detected analysis type

        Returns:
            Domain classification
        """
        domain_indicators = {
            "bioinformatics": [
                "gene",
                "protein",
                "dna",
                "rna",
                "genome",
                "genomic",
                "transcriptome",
                "proteome",
                "bioinformatics",
                "computational biology",
                "sequence",
                "phylogenetic",
                "molecular",
                "biological",
            ],
            "machine_learning": [
                "machine learning",
                "deep learning",
                "neural network",
                "classification",
                "regression",
                "clustering",
                "prediction",
                "model training",
                "algorithm",
            ],
            "data_science": [
                "data science",
                "data mining",
                "statistical analysis",
                "visualization",
                "big data",
                "analytics",
                "correlation",
                "regression",
            ],
            "software_engineering": [
                "software",
                "application",
                "system",
                "architecture",
                "development",
                "programming",
                "code",
                "framework",
                "api",
            ],
        }

        # Score domains based on indicators
        domain_scores = {}
        for domain, indicators in domain_indicators.items():
            score = sum(1 for indicator in indicators if indicator in text)
            domain_scores[domain] = score

        if domain_scores and max(domain_scores.values()) > 0:
            return max(domain_scores.items(), key=lambda x: x[1])[0]

        # Fallback based on analysis type
        if any(
            term in analysis_type.lower()
            for term in ["differential", "protein", "genomic", "pathway"]
        ):
            return "bioinformatics"
        elif "machine" in analysis_type.lower() or "learning" in analysis_type.lower():
            return "machine_learning"

        return "data_science"

    def generate_semantic_filename(self, context: Dict[str, str]) -> str:
        """
        Generate semantic filename based on context.

        Args:
            context: Dictionary with analysis_type, subject, and domain

        Returns:
            Semantic filename in format: Analysis_{AnalysisType}_{Subject}.md
        """
        analysis_type = context.get("analysis_type", "General")
        subject = context.get("subject", "Analysis")

        # Sanitize components
        analysis_type = self._sanitize_component(analysis_type)
        subject = self._sanitize_component(subject)

        return f"Analysis_{analysis_type}_{subject}.md"

    def _sanitize_component(self, component: str) -> str:
        """
        Sanitize filename component.

        Args:
            component: Component to sanitize

        Returns:
            Sanitized component safe for filenames
        """
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', "", component)
        sanitized = re.sub(r"\s+", "", sanitized)  # Remove spaces
        sanitized = re.sub(
            r"[^\w\-_.]", "", sanitized
        )  # Keep only word chars, hyphens, underscores

        # Ensure it's not empty and has reasonable length
        if not sanitized or len(sanitized) < 2:
            sanitized = "Analysis"
        elif len(sanitized) > 50:
            sanitized = sanitized[:50]

        return sanitized

    def find_existing_document(
        self, semantic_filename: str, context: Dict[str, str], goal: str = ""
    ) -> Optional[str]:
        """
        Find existing document by exact match, identifier match, or semantic similarity.
        Prioritizes specific identifiers over generic terms.

        Args:
            semantic_filename: The generated semantic filename
            context: Semantic context for similarity matching
            goal: Original user goal for identifier extraction

        Returns:
            Path to existing document if found, None otherwise
        """
        if not os.path.exists(self.workspace_dir):
            return None

        # First try exact match
        exact_path = os.path.join(self.workspace_dir, semantic_filename)
        if os.path.exists(exact_path):
            logger.info(f"Found exact match: {exact_path}")
            return exact_path

        existing_files = [
            f
            for f in os.listdir(self.workspace_dir)
            if f.endswith(".md") and f.startswith("Analysis_")
        ]

        if not existing_files:
            return None

        # PRIORITY 1: Try identifier-based matching
        identifier_match = self._find_by_identifier(
            existing_files, goal, semantic_filename
        )
        if identifier_match:
            logger.info(f"Found identifier match: {identifier_match}")
            return identifier_match

        # PRIORITY 2: Try semantic similarity matching
        similarities = []
        target_components = self._parse_filename_components(semantic_filename)

        for existing_file in existing_files:
            existing_components = self._parse_filename_components(existing_file)
            similarity = self._calculate_semantic_similarity(
                target_components, existing_components
            )
            similarities.append((existing_file, similarity))

        # Return highest similarity if above threshold
        if similarities:
            best_match = max(similarities, key=lambda x: x[1])
            if best_match[1] > 0.7:  # 70% similarity threshold
                match_path = os.path.join(self.workspace_dir, best_match[0])
                logger.info(f"Found semantic match ({best_match[1]:.2%}): {match_path}")
                return match_path

        return None

    def _find_by_identifier(
        self, existing_files: List[str], goal: str, target_filename: str
    ) -> Optional[str]:
        """
        Find existing document by specific biological identifiers.
        This ensures queries about the same entity (e.g., Q86VK4) match the same document.

        Args:
            existing_files: List of existing document filenames
            goal: User's original goal/query
            target_filename: The filename being generated

        Returns:
            Path to matching document or None
        """
        # Extract identifiers from the current goal/query
        current_identifiers = self._extract_specific_identifiers(goal)
        if not current_identifiers:
            return None

        # Check each existing file for matching identifiers
        for existing_file in existing_files:
            existing_path = os.path.join(self.workspace_dir, existing_file)

            # Try to extract identifiers from filename
            filename_identifiers = self._extract_identifiers_from_filename(
                existing_file
            )

            # Check for direct identifier match in filename
            if any(
                identifier in filename_identifiers for identifier in current_identifiers
            ):
                return existing_path

            # Check document content for identifiers (more thorough)
            try:
                with open(existing_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    content_identifiers = self._extract_specific_identifiers(content)

                # If any current identifier appears in the existing document
                if any(
                    identifier in content_identifiers
                    for identifier in current_identifiers
                ):
                    return existing_path

            except Exception as e:
                logger.warning(
                    f"Could not read {existing_path} for identifier matching: {e}"
                )
                continue

        return None

    def _extract_identifiers_from_filename(self, filename: str) -> List[str]:
        """
        Extract identifiers from filename components.

        Args:
            filename: Filename to parse

        Returns:
            List of identifiers found in filename
        """
        # Remove .md extension and split by underscores
        name_parts = filename.replace(".md", "").split("_")

        identifiers = []
        for part in name_parts:
            # Check if part looks like an identifier
            part_identifiers = self._extract_specific_identifiers(part)
            identifiers.extend(part_identifiers)

        return identifiers

    def _parse_filename_components(self, filename: str) -> Dict[str, str]:
        """
        Parse filename into components for similarity comparison.

        Args:
            filename: Filename to parse

        Returns:
            Dictionary with parsed components
        """
        # Remove .md extension
        name = filename.replace(".md", "")

        # Split by underscores
        parts = name.split("_")

        if len(parts) >= 3 and parts[0] == "Analysis":
            return {
                "analysis_type": parts[1].lower(),
                "subject": parts[2].lower() if len(parts) > 2 else "",
            }

        return {"analysis_type": "", "subject": ""}

    def _calculate_semantic_similarity(
        self, components1: Dict[str, str], components2: Dict[str, str]
    ) -> float:
        """
        Calculate semantic similarity between filename components.

        Args:
            components1: First set of components
            components2: Second set of components

        Returns:
            Similarity score between 0 and 1
        """
        analysis_sim = SequenceMatcher(
            None,
            components1.get("analysis_type", ""),
            components2.get("analysis_type", ""),
        ).ratio()

        subject_sim = SequenceMatcher(
            None, components1.get("subject", ""), components2.get("subject", "")
        ).ratio()

        # Weighted average (analysis type is more important)
        return (analysis_sim * 0.6) + (subject_sim * 0.4)

    def create_document_backup(self, document_path: str) -> str:
        """
        Create backup of existing document before updating.

        Args:
            document_path: Path to document to backup

        Returns:
            Path to backup file
        """
        if not os.path.exists(document_path):
            raise FileNotFoundError(f"Document not found: {document_path}")

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(document_path))[0]
        backup_name = f"{base_name}_backup_{timestamp}.md"
        backup_path = os.path.join(self.workspace_dir, backup_name)

        # Handle duplicate backups
        counter = 1
        while os.path.exists(backup_path):
            backup_name = f"{base_name}_backup_{timestamp}_{counter}.md"
            backup_path = os.path.join(self.workspace_dir, backup_name)
            counter += 1

        # Create backup
        with open(document_path, "r", encoding="utf-8") as src:
            with open(backup_path, "w", encoding="utf-8") as dst:
                dst.write(src.read())

        logger.info(f"Created backup: {backup_path}")
        return backup_path

    def get_document_metadata(self, document_path: str) -> Dict[str, Any]:
        """
        Extract metadata from existing document.

        Args:
            document_path: Path to document

        Returns:
            Dictionary with document metadata
        """
        if not os.path.exists(document_path):
            return {}

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Extract basic metadata
            metadata = {
                "file_size": os.path.getsize(document_path),
                "last_modified": (
                    datetime.datetime.fromtimestamp(
                        os.path.getmtime(document_path)
                    ).isoformat()
                ),
                "word_count": len(content.split()),
                "line_count": len(content.splitlines()),
            }

            # Try to extract title from content
            lines = content.splitlines()
            for line in lines:
                if line.startswith("# "):
                    metadata["title"] = line[2:].strip()
                    break

            return metadata

        except Exception as e:
            logger.warning(f"Could not extract metadata from {document_path}: {e}")
            return {}

    def validate_document_content(self, content: str) -> bool:
        """
        Validate that document content is meaningful and not just placeholders.

        Args:
            content: Document content to validate

        Returns:
            True if content appears meaningful, False otherwise
        """
        if not content or len(content.strip()) < 100:
            return False

        # Check for placeholder patterns
        placeholder_patterns = [
            r"will be (added|generated|synthesized|compiled)",
            r"to be (determined|completed|analyzed)",
            r"placeholder",
            r"coming soon",
            r"under construction",
        ]

        content_lower = content.lower()
        placeholder_count = sum(
            1 for pattern in placeholder_patterns if re.search(pattern, content_lower)
        )

        # If more than 20% of lines contain placeholders, consider invalid
        lines = content.splitlines()
        if placeholder_count > len(lines) * 0.2:
            return False

        return True

    def cleanup_old_backups(self, max_backups: int = 10) -> None:
        """
        Clean up old backup files to prevent disk space issues.

        Args:
            max_backups: Maximum number of backups to keep per document
        """
        if not os.path.exists(self.workspace_dir):
            return

        backup_files = [
            f
            for f in os.listdir(self.workspace_dir)
            if f.endswith(".md") and "_backup_" in f
        ]

        # Group backups by base document name
        backup_groups = {}
        for backup_file in backup_files:
            # Extract base name (everything before _backup_)
            base_name = backup_file.split("_backup_")[0]
            if base_name not in backup_groups:
                backup_groups[base_name] = []
            backup_groups[base_name].append(backup_file)

        # Clean up each group
        for base_name, backups in backup_groups.items():
            if len(backups) > max_backups:
                # Sort by modification time (oldest first)
                backup_paths = [
                    (f, os.path.getmtime(os.path.join(self.workspace_dir, f)))
                    for f in backups
                ]
                backup_paths.sort(key=lambda x: x[1])

                # Remove oldest backups
                to_remove = backup_paths[:-max_backups]
                for backup_file, _ in to_remove:
                    backup_path = os.path.join(self.workspace_dir, backup_file)
                    try:
                        os.remove(backup_path)
                        logger.info(f"Removed old backup: {backup_path}")
                    except Exception as e:
                        logger.warning(f"Could not remove backup {backup_path}: {e}")

    # Session handling methods for Story 4.2

    def track_session_id(self, document_path: str, session_id: str) -> None:
        """
        Track session ID in document metadata for session awareness.

        Args:
            document_path: Path to the document
            session_id: Unique session identifier
        """
        if not os.path.exists(document_path):
            logger.warning(
                f"Cannot track session for non-existent document: {document_path}"
            )
            return

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Check if session metadata already exists
            if "## Session Overview" not in content:
                # Add session overview section after document metadata
                metadata_end = content.find("## Executive Summary")
                if metadata_end != -1:
                    session_overview = f"""
## Session Overview
- **Current Session:** {session_id}
- **Session Count:** {self.get_session_count(document_path) + 1}
- **Last Updated:** {datetime.datetime.now().strftime(DATETIME_FORMAT)}

"""
                    content = (
                        content[:metadata_end]
                        + session_overview
                        + content[metadata_end:]
                    )

                    with open(document_path, "w", encoding="utf-8") as f:
                        f.write(content)

                    logger.info(f"Added session tracking to document: {document_path}")
                else:
                    logger.warning(
                        f"Could not find metadata section in document: {document_path}"
                    )
            else:
                # Update existing session metadata
                session_pattern = r"- \*\*Current Session:\*\* (.+)"
                content = re.sub(
                    session_pattern, f"- **Current Session:** {session_id}", content
                )

                count_pattern = r"- \*\*Session Count:\*\* (\d+)"
                current_count = self.get_session_count(document_path)
                content = re.sub(
                    count_pattern, f"- **Session Count:** {current_count}", content
                )

                updated_pattern = r"- \*\*Last Updated:\*\* (.+)"
                content = re.sub(
                    updated_pattern,
                    f"- **Last Updated:** {datetime.datetime.now().strftime(DATETIME_FORMAT)}",
                    content,
                )

                with open(document_path, "w", encoding="utf-8") as f:
                    f.write(content)

                logger.info(f"Updated session tracking in document: {document_path}")

        except Exception as e:
            logger.error(f"Failed to track session ID in {document_path}: {e}")

    def get_session_count(self, document_path: str) -> int:
        """
        Get the number of sessions in a document by counting session headers.

        Args:
            document_path: Path to the document

        Returns:
            Number of sessions in the document
        """
        if not os.path.exists(document_path):
            return 0

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Count session headers using regex
            session_headers = re.findall(SESSION_TIMESTAMP_REGEX, content)
            return len(session_headers)

        except Exception as e:
            logger.warning(f"Could not count sessions in {document_path}: {e}")
            return 0

    def generate_session_header(self, session_count: int, session_id: str) -> str:
        """
        Generate a timestamped session header for content separation.

        Args:
            session_count: Current session number
            session_id: Unique session identifier

        Returns:
            Formatted session header with timestamp
        """
        timestamp = datetime.datetime.now().strftime(DATETIME_FORMAT)
        return f"""
{SESSION_HEADER_SEPARATOR}

## Session {session_count} - {timestamp}
**Session ID:** {session_id}

"""

    def append_session_content(
        self, document_path: str, new_content: str, session_id: str
    ) -> bool:
        """
        Append new session content to existing document with proper session demarcation.

        Args:
            document_path: Path to the existing document
            new_content: Content to append
            session_id: Unique session identifier

        Returns:
            True if successful, False otherwise
        """
        if not new_content or not new_content.strip():
            logger.warning("Cannot append empty content to document")
            return False

        if not os.path.exists(document_path):
            logger.error(f"Cannot append to non-existent document: {document_path}")
            return False

        try:
            # Get current session count
            current_session_count = self.get_session_count(document_path) + 1

            # Generate session header
            session_header = self.generate_session_header(
                current_session_count, session_id
            )

            # Prepare content to append
            content_to_append = session_header + new_content.strip() + "\n"

            # Use atomic write operation
            temp_file = None
            try:
                # Create temporary file in same directory
                dir_path = os.path.dirname(document_path)
                temp_file = tempfile.NamedTemporaryFile(
                    mode="w",
                    encoding="utf-8",
                    dir=dir_path,
                    delete=False,
                    suffix=".tmp",
                )

                # Copy existing content and append new content
                with open(document_path, "r", encoding="utf-8") as src:
                    existing_content = src.read()

                temp_file.write(existing_content)
                temp_file.write(content_to_append)
                temp_file.flush()
                temp_file.close()

                # Atomic rename
                os.rename(temp_file.name, document_path)

                # Update session tracking
                self.track_session_id(document_path, session_id)

                logger.info(
                    f"Successfully appended session {current_session_count} content to {document_path}"
                )
                return True

            except Exception as e:
                # Clean up temp file if it exists
                if temp_file and os.path.exists(temp_file.name):
                    try:
                        os.unlink(temp_file.name)
                    except:
                        pass
                raise e

        except Exception as e:
            logger.error(f"Failed to append session content to {document_path}: {e}")
            return False

    def create_or_update_document(
        self, document_path: str, content: str, context: Dict[str, str], session_id: str
    ) -> str:
        """
        Create new document or update existing document with session awareness.

        Args:
            document_path: Path where document should be created/updated
            content: Document content
            context: Semantic context for document creation
            session_id: Unique session identifier

        Returns:
            Path to the created/updated document
        """
        if os.path.exists(document_path):
            # Document exists - append as new session
            success = self.append_session_content(document_path, content, session_id)
            if success:
                return document_path
            else:
                raise Exception(
                    f"Failed to append session content to existing document: {document_path}"
                )
        else:
            # Document doesn't exist - create new document with session tracking
            return self._create_new_document_with_session(
                document_path, content, context, session_id
            )

    def _create_new_document_with_session(
        self, document_path: str, content: str, context: Dict[str, str], session_id: str
    ) -> str:
        """
        Create a new document with session metadata and tracking.

        Args:
            document_path: Path where document should be created
            content: Document content
            context: Semantic context for document creation
            session_id: Unique session identifier

        Returns:
            Path to the created document
        """
        try:
            # Load living document template
            template_path = os.path.join(
                os.path.dirname(__file__), "..", "templates", "living_document.md"
            )

            if os.path.exists(template_path):
                with open(template_path, "r", encoding="utf-8") as f:
                    template_content = f.read()
            else:
                # Fallback template
                template_content = """# {document_title}

## Document Metadata
- **Analysis Type:** {analysis_type}
- **Subject:** {subject}
- **Domain:** {domain}
- **Created:** {creation_date}
- **Last Updated:** {last_updated}
- **Version:** {version}

## Session Overview
- **Current Session:** {session_id}
- **Session Count:** 1
- **Last Updated:** {creation_date}

## Executive Summary

{executive_summary}

## Session 1 - {creation_date}
**Session ID:** {session_id}

{content}
"""

            # Fill template with context and content
            current_time = datetime.datetime.now().strftime(DATETIME_FORMAT)

            # Prepare template variables with comprehensive defaults
            template_vars = {
                "document_title": (
                    f"Analysis: {context.get('analysis_type', 'General')} - {context.get('subject', 'Analysis')}"
                ),
                "analysis_type": context.get("analysis_type", "General"),
                "subject": context.get("subject", "Analysis"),
                "domain": context.get("domain", "data_science"),
                "creation_date": current_time,
                "last_updated": current_time,
                "version": "1.0",
                "session_id": session_id,
                "session_count": "1",
                "executive_summary": (
                    "This document represents ongoing analysis work with session-based updates."
                ),
                "objective": (
                    f"Initial analysis session for {context.get('analysis_type', 'general analysis')}"
                ),
                "research_questions": (
                    "- Research questions will be developed based on analysis objectives"
                ),
                "hypothesis": (
                    "Working hypothesis will be formulated based on initial findings"
                ),
                "key_results": (
                    content.strip()
                    if content.strip()
                    else "Initial session results to be documented"
                ),
                "primary_results": "Primary results from initial analysis session",
                "statistical_analysis": (
                    "Statistical analysis to be performed in subsequent sessions"
                ),
                "visualizations": (
                    "Visualizations will be generated as analysis progresses"
                ),
                "methodology_overview": (
                    "Methodology established for systematic analysis approach"
                ),
                "tools_and_technologies": (
                    "Tools and technologies utilized in analysis workflow"
                ),
                "data_sources": "Data sources identified and accessed for analysis",
                "analysis_pipeline": (
                    "Analysis pipeline established for systematic processing"
                ),
                "technical_details": "Technical implementation details documented",
                "performance_metrics": (
                    "Performance metrics tracked throughout analysis"
                ),
                "validation_methods": (
                    "Validation approaches implemented for result verification"
                ),
                "biological_interpretation": (
                    "Biological interpretation to be developed from findings"
                ),
                "clinical_relevance": (
                    "Clinical relevance will be assessed based on results"
                ),
                "comparative_analysis": "Comparative analysis framework established",
                "key_insights": "Key insights emerging from initial analysis session",
                "unexpected_findings": (
                    "Unexpected findings to be documented as they emerge"
                ),
                "implications": "Implications will be assessed as analysis progresses",
                "supporting_evidence": (
                    "Supporting evidence compiled from analysis results"
                ),
                "quality_control": (
                    "Quality control measures implemented throughout workflow"
                ),
                "reproducibility_info": (
                    "Reproducibility protocols established for analysis"
                ),
                "limitations": "Limitations identified and documented for transparency",
                "data_quality_issues": (
                    "Data quality assessment performed and documented"
                ),
                "methodological_constraints": (
                    "Methodological constraints acknowledged and addressed"
                ),
                "future_directions": (
                    "Future research directions identified for continuation"
                ),
                "recommended_followup": (
                    "Follow-up studies recommended based on findings"
                ),
                "method_improvements": (
                    "Method improvements suggested for enhanced analysis"
                ),
                "literature_references": (
                    "Literature references compiled during analysis"
                ),
                "reference_data_sources": (
                    "Data source references documented for reproducibility"
                ),
                "reference_tools": (
                    "Tool references maintained for methodology transparency"
                ),
                "supplementary_data": (
                    "Supplementary data files generated during analysis"
                ),
                "code_snippets": "Code implementations documented for reproducibility",
                "additional_figures": (
                    "Additional figures and visualizations as generated"
                ),
                "version_history": (
                    f"v1.0 - {current_time} - Initial document creation with session support"
                ),
                "change_log": (
                    f"- {current_time}: Document created with session-aware functionality"
                ),
                "contributors": (
                    "Analysis performed by Multi-Agent Bioinformatics System"
                ),
            }

            formatted_content = template_content.format(**template_vars)

            # Add initial session header after the template formatting for new documents
            if "Session 1 -" not in formatted_content:
                # Insert session header before the document history section
                history_marker = "---\n\n## Document History"
                if history_marker in formatted_content:
                    session_header = f"\n{SESSION_HEADER_SEPARATOR}\n\n## Session 1 - {current_time}\n**Session ID:** {session_id}\n\n### Session Content\n{content.strip()}\n\n"
                    formatted_content = formatted_content.replace(
                        history_marker, session_header + history_marker
                    )
                else:
                    # Fallback: append session header at the end
                    session_header = f"\n{SESSION_HEADER_SEPARATOR}\n\n## Session 1 - {current_time}\n**Session ID:** {session_id}\n\n### Session Content\n{content.strip()}\n"
                    formatted_content += session_header

            # Write document
            os.makedirs(os.path.dirname(document_path), exist_ok=True)
            with open(document_path, "w", encoding="utf-8") as f:
                f.write(formatted_content)

            logger.info(f"Created new document with session tracking: {document_path}")
            return document_path

        except Exception as e:
            logger.error(f"Failed to create new document with session: {e}")
            raise

    # Executive Summary Management Methods for Story 4.3

    def has_executive_summary_section(self, document_path: str) -> bool:
        """
        Check if document has an executive summary section.

        Args:
            document_path: Path to the document

        Returns:
            True if executive summary section exists, False otherwise
        """
        if not os.path.exists(document_path):
            return False

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()
            return "## Executive Summary" in content
        except Exception as e:
            logger.warning(
                f"Could not check executive summary section in {document_path}: {e}"
            )
            return False

    def get_executive_summary_content(self, document_path: str) -> Optional[str]:
        """
        Extract current executive summary content from document.

        Args:
            document_path: Path to the document

        Returns:
            Current executive summary content, or None if not found
        """
        if not os.path.exists(document_path):
            return None

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Find executive summary section
            summary_start = content.find("## Executive Summary")
            if summary_start == -1:
                return None

            # Find the end of the executive summary section
            summary_end = content.find("\n## ", summary_start + 1)
            if summary_end == -1:
                summary_end = len(content)

            # Extract summary content
            summary_section = content[summary_start:summary_end]

            # Remove the header and clean up
            summary_content = summary_section.replace(
                "## Executive Summary", ""
            ).strip()

            return summary_content if summary_content else None

        except Exception as e:
            logger.error(
                f"Failed to get executive summary content from {document_path}: {e}"
            )
            return None

    def update_executive_summary(
        self,
        document_path: str,
        new_summary: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Update or create executive summary section in document.

        Args:
            document_path: Path to the document
            new_summary: New summary content
            metadata: Optional metadata about the summary update

        Returns:
            True if update was successful, False otherwise
        """
        if not os.path.exists(document_path):
            logger.error(
                f"Cannot update summary in non-existent document: {document_path}"
            )
            return False

        try:
            with open(document_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Track summary update metadata
            if metadata:
                self._track_summary_metadata(document_path, metadata)

            # Check if executive summary section exists
            summary_start = content.find("## Executive Summary")

            if summary_start == -1:
                # Create new executive summary section
                return self._add_executive_summary_section(
                    document_path, content, new_summary
                )
            else:
                # Update existing executive summary section
                return self._replace_executive_summary_content(
                    document_path, content, new_summary
                )

        except Exception as e:
            logger.error(f"Failed to update executive summary: {e}")
            return False

    def _add_executive_summary_section(
        self, document_path: str, content: str, new_summary: str
    ) -> bool:
        """
        Add executive summary section to document that doesn't have one.

        Args:
            document_path: Path to the document
            content: Current document content
            new_summary: Summary content to add

        Returns:
            True if successful, False otherwise
        """
        try:
            # Find appropriate insertion point (after document metadata/session overview)
            insertion_points = [
                "## Session Overview",
                "## Document Metadata",
                "## Objective & Context",
            ]

            insertion_index = -1
            for point in insertion_points:
                point_index = content.find(point)
                if point_index != -1:
                    # Find the end of this section
                    next_section = content.find("\n## ", point_index + 1)
                    if next_section != -1:
                        insertion_index = next_section
                        break
                    else:
                        # Insert at end if no next section
                        insertion_index = len(content)
                        break

            if insertion_index == -1:
                # Fallback: insert after first header or at beginning
                first_header = content.find("\n## ")
                insertion_index = first_header + 1 if first_header != -1 else 0

            # Create executive summary section
            summary_section = f"\n## Executive Summary\n\n{new_summary}\n\n"

            # Insert the section
            updated_content = (
                content[:insertion_index] + summary_section + content[insertion_index:]
            )

            with open(document_path, "w", encoding="utf-8") as f:
                f.write(updated_content)

            logger.info(f"Added executive summary section to {document_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to add executive summary section: {e}")
            return False

    def _replace_executive_summary_content(
        self, document_path: str, content: str, new_summary: str
    ) -> bool:
        """
        Replace existing executive summary content.

        Args:
            document_path: Path to the document
            content: Current document content
            new_summary: New summary content

        Returns:
            True if successful, False otherwise
        """
        try:
            # Find executive summary section boundaries
            summary_start = content.find("## Executive Summary")
            summary_end = content.find("\n## ", summary_start + 1)
            if summary_end == -1:
                summary_end = len(content)

            # Replace the executive summary content
            before_summary = content[:summary_start]
            after_summary = content[summary_end:]

            new_section = f"## Executive Summary\n\n{new_summary}\n\n"
            updated_content = before_summary + new_section + after_summary

            # Write updated content atomically
            temp_file = None
            try:
                dir_path = os.path.dirname(document_path)
                temp_file = tempfile.NamedTemporaryFile(
                    mode="w",
                    encoding="utf-8",
                    dir=dir_path,
                    delete=False,
                    suffix=".tmp",
                )
                temp_file.write(updated_content)
                temp_file.flush()
                temp_file.close()

                # Atomic rename
                os.rename(temp_file.name, document_path)

                logger.info(f"Updated executive summary in {document_path}")
                return True

            except Exception as e:
                # Clean up temp file if it exists
                if temp_file and os.path.exists(temp_file.name):
                    try:
                        os.unlink(temp_file.name)
                    except:
                        pass
                raise e

        except Exception as e:
            logger.error(f"Failed to replace executive summary content: {e}")
            return False

    def _track_summary_metadata(
        self, document_path: str, metadata: Dict[str, Any]
    ) -> None:
        """
        Track metadata about summary updates.

        Args:
            document_path: Path to the document
            metadata: Metadata to track
        """
        try:
            # Create metadata directory if it doesn't exist
            metadata_dir = os.path.join(self.workspace_dir, ".summary_metadata")
            os.makedirs(metadata_dir, exist_ok=True)

            # Create metadata file path
            doc_name = os.path.basename(document_path).replace(".md", "")
            metadata_path = os.path.join(
                metadata_dir, f"{doc_name}_summary_metadata.json"
            )

            # Load existing metadata or create new
            existing_metadata = []
            if os.path.exists(metadata_path):
                with open(metadata_path, "r", encoding="utf-8") as f:
                    existing_metadata = json.load(f)

            # Add new metadata entry
            metadata_entry = {
                "timestamp": datetime.datetime.now().isoformat(),
                "document_path": document_path,
                **metadata,
            }
            existing_metadata.append(metadata_entry)

            # Keep only last 50 metadata entries to prevent excessive growth
            if len(existing_metadata) > 50:
                existing_metadata = existing_metadata[-50:]

            # Save updated metadata
            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(existing_metadata, f, indent=2, ensure_ascii=False)

            logger.debug(f"Tracked summary metadata for {document_path}")

        except Exception as e:
            logger.warning(f"Failed to track summary metadata: {e}")

    def get_summary_metadata(self, document_path: str) -> List[Dict[str, Any]]:
        """
        Get summary update metadata for a document.

        Args:
            document_path: Path to the document

        Returns:
            List of metadata entries for summary updates
        """
        try:
            doc_name = os.path.basename(document_path).replace(".md", "")
            metadata_dir = os.path.join(self.workspace_dir, ".summary_metadata")
            metadata_path = os.path.join(
                metadata_dir, f"{doc_name}_summary_metadata.json"
            )

            if os.path.exists(metadata_path):
                with open(metadata_path, "r", encoding="utf-8") as f:
                    return json.load(f)

            return []

        except Exception as e:
            logger.warning(f"Failed to get summary metadata: {e}")
            return []

    def should_update_summary(self, document_path: str, session_id: str) -> bool:
        """
        Determine if executive summary should be updated based on document changes.

        Args:
            document_path: Path to the document
            session_id: Current session ID

        Returns:
            True if summary should be updated, False otherwise
        """
        try:
            # Check if document has new content since last summary update
            metadata = self.get_summary_metadata(document_path)

            if not metadata:
                # No previous summary updates, should update
                return True

            last_update = metadata[-1]
            last_update_timestamp = datetime.datetime.fromisoformat(
                last_update["timestamp"]
            )

            # Check if document was modified after last summary update
            doc_modified_time = datetime.datetime.fromtimestamp(
                os.path.getmtime(document_path)
            )

            if doc_modified_time > last_update_timestamp:
                # Document modified since last summary update
                return True

            # Check if this is a new session
            current_session_count = self.get_session_count(document_path)
            last_session_count = last_update.get("session_count", 0)

            if current_session_count > last_session_count:
                # New session added, should update summary
                return True

            return False

        except Exception as e:
            logger.warning(f"Error determining if summary should be updated: {e}")
            # Default to updating if we can't determine
            return True

    def trigger_summary_update(self, document_path: str, session_id: str) -> bool:
        """
        Trigger executive summary update if needed.

        Args:
            document_path: Path to the document
            session_id: Current session ID

        Returns:
            True if summary was updated, False otherwise
        """
        if not self.should_update_summary(document_path, session_id):
            logger.debug(f"Summary update not needed for {document_path}")
            return False

        try:
            # Import summary service here to avoid circular imports
            from src.service.summary_service import SummaryService

            summary_service = SummaryService(self.workspace_dir)
            new_summary = summary_service.generate_executive_summary(document_path)

            if new_summary:
                # Update the summary with metadata
                metadata = {
                    "session_id": session_id,
                    "session_count": self.get_session_count(document_path),
                    "update_trigger": "automatic",
                    "summary_length": len(new_summary.split()),
                }

                success = self.update_executive_summary(
                    document_path, new_summary, metadata
                )
                if success:
                    logger.info(
                        f"Automatically updated executive summary for {document_path}"
                    )
                    return True

            return False

        except Exception as e:
            logger.error(f"Failed to trigger summary update: {e}")
            return False
