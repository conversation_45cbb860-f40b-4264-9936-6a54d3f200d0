"""
Advanced risk assessment framework for semi-autonomous decision making.

This module provides sophisticated risk analysis capabilities for the HITL system,
including confidence scoring, action impact assessment, and dynamic risk evaluation.
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..config.hitl_config import RiskLevel, get_hitl_config
from ..graph.types import State, PlanStep

logger = logging.getLogger(__name__)


class ActionCategory(Enum):
    """Categories of actions for risk assessment."""
    
    DATA_READ = "data_read"           # Reading data, queries
    DATA_WRITE = "data_write"         # Writing, creating, modifying data
    COMPUTATION = "computation"       # Analysis, processing
    EXTERNAL_API = "external_api"     # External service calls
    SYSTEM_COMMAND = "system_command" # System-level operations
    MODEL_INFERENCE = "model_inference" # AI model calls
    WORKFLOW_CONTROL = "workflow_control" # Workflow state changes


class ConfidenceSource(Enum):
    """Sources of confidence information."""
    
    AGENT_RESPONSE = "agent_response"     # From agent's response confidence
    TOOL_RELIABILITY = "tool_reliability" # Based on tool success history  
    DATA_QUALITY = "data_quality"         # Quality of input data
    CONTEXT_CLARITY = "context_clarity"   # Clarity of user requirements
    HISTORICAL_SUCCESS = "historical_success" # Past success in similar tasks


@dataclass
class RiskFactor:
    """Individual risk factor assessment."""
    
    factor_name: str
    risk_level: RiskLevel
    confidence: float  # 0.0 to 1.0
    description: str
    mitigation_strategies: List[str]


@dataclass
class RiskAssessment:
    """Comprehensive risk assessment result."""
    
    overall_risk: RiskLevel
    confidence_score: float  # 0.0 to 1.0
    risk_factors: List[RiskFactor]
    recommendation: str
    requires_human_approval: bool
    mitigation_suggestions: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert assessment to dictionary format."""
        return {
            "overall_risk": self.overall_risk.value,
            "confidence_score": self.confidence_score,
            "recommendation": self.recommendation,
            "requires_human_approval": self.requires_human_approval,
            "risk_factors": [
                {
                    "name": rf.factor_name,
                    "risk_level": rf.risk_level.value,
                    "confidence": rf.confidence,
                    "description": rf.description,
                    "mitigation_strategies": rf.mitigation_strategies
                }
                for rf in self.risk_factors
            ],
            "mitigation_suggestions": self.mitigation_suggestions
        }


class RiskAssessor:
    """Advanced risk assessment engine."""
    
    def __init__(self):
        self.config = get_hitl_config()
        self._initialize_risk_patterns()
    
    def _initialize_risk_patterns(self):
        """Initialize risk assessment patterns and weights."""
        
        # High-risk command patterns
        self.high_risk_command_patterns = [
            r'rm\s+-rf',
            r'sudo\s+',
            r'chmod\s+777',
            r'delete\s+from\s+',
            r'drop\s+table',
            r'truncate\s+',
            r'format\s+',
            r'fdisk\s+',
        ]
        
        # Medium-risk patterns
        self.medium_risk_command_patterns = [
            r'pip\s+install',
            r'conda\s+install',
            r'git\s+push',
            r'docker\s+run',
            r'wget\s+',
            r'curl\s+.*-X\s+POST',
        ]
        
        # File operation risk patterns
        self.file_risk_patterns = {
            'critical_dirs': ['/etc/', '/usr/', '/var/', '/sys/', '/boot/'],
            'sensitive_files': ['.env', 'config.json', 'secrets.yaml', 'credentials'],
            'system_files': ['.bashrc', '.profile', 'hosts', 'passwd', 'shadow']
        }
        
        # Tool reliability scores (could be updated based on historical data)
        self.tool_reliability_scores = {
            'query_uniprot': 0.95,
            'search_pubmed': 0.90,
            'execute_command': 0.70,
            'write_file': 0.85,
            'analyze_structure': 0.88,
        }
    
    def assess_step_risk(
        self,
        state: State,
        step: PlanStep,
        additional_context: Dict[str, Any] = None
    ) -> RiskAssessment:
        """
        Perform comprehensive risk assessment for a workflow step.
        
        Args:
            state: Current workflow state
            step: The step to assess
            additional_context: Additional context for assessment
            
        Returns:
            Comprehensive risk assessment
        """
        risk_factors = []
        context = additional_context or {}
        
        # Agent-based risk assessment
        agent_risk = self._assess_agent_risk(step.agent_name, step)
        if agent_risk:
            risk_factors.append(agent_risk)
        
        # Step content risk assessment
        content_risk = self._assess_step_content_risk(step)
        if content_risk:
            risk_factors.append(content_risk)
        
        # Context-based risk assessment
        if context:
            context_risk = self._assess_context_risk(context)
            if context_risk:
                risk_factors.append(context_risk)
        
        # Historical risk assessment
        historical_risk = self._assess_historical_risk(step, state)
        if historical_risk:
            risk_factors.append(historical_risk)
        
        # Data sensitivity assessment
        data_risk = self._assess_data_sensitivity_risk(step, context)
        if data_risk:
            risk_factors.append(data_risk)
        
        return self._synthesize_risk_assessment(risk_factors, step)
    
    def assess_tool_risk(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        agent_name: str = None,
        context: Dict[str, Any] = None
    ) -> RiskAssessment:
        """
        Assess risk for a specific tool execution.
        
        Args:
            tool_name: Name of the tool
            tool_args: Tool arguments
            agent_name: Agent using the tool
            context: Additional context
            
        Returns:
            Risk assessment for the tool execution
        """
        risk_factors = []
        
        # Tool-specific risk assessment
        tool_risk = self._assess_tool_specific_risk(tool_name, tool_args)
        if tool_risk:
            risk_factors.append(tool_risk)
        
        # Argument-based risk assessment
        args_risk = self._assess_tool_args_risk(tool_name, tool_args)
        if args_risk:
            risk_factors.append(args_risk)
        
        # Reliability assessment
        reliability_risk = self._assess_tool_reliability_risk(tool_name)
        if reliability_risk:
            risk_factors.append(reliability_risk)
        
        # Agent context risk
        if agent_name:
            agent_context_risk = self._assess_agent_tool_compatibility_risk(agent_name, tool_name)
            if agent_context_risk:
                risk_factors.append(agent_context_risk)
        
        return self._synthesize_risk_assessment(risk_factors, None, tool_name)
    
    def _assess_agent_risk(self, agent_name: str, step: PlanStep) -> Optional[RiskFactor]:
        """Assess risk based on agent type and capabilities."""
        base_risk = self.config.risk_config.agent_risk_levels.get(agent_name, RiskLevel.MEDIUM)
        
        # Adjust risk based on step characteristics
        adjusted_risk = base_risk
        confidence = 0.8
        
        # Increase risk for agents performing out-of-scope actions
        if agent_name == "researcher" and "execute" in step.description.lower():
            adjusted_risk = RiskLevel.MEDIUM
        elif agent_name == "coder" and any(pattern in step.description.lower() 
                                           for pattern in ["system", "admin", "sudo"]):
            adjusted_risk = RiskLevel.HIGH
        
        return RiskFactor(
            factor_name=f"agent_{agent_name}_risk",
            risk_level=adjusted_risk,
            confidence=confidence,
            description=f"Risk assessment for {agent_name} agent executing this step",
            mitigation_strategies=[
                "Review agent capabilities and permissions",
                "Validate step requirements against agent scope",
                "Consider agent specialization boundaries"
            ]
        )
    
    def _assess_step_content_risk(self, step: PlanStep) -> Optional[RiskFactor]:
        """Assess risk based on step content and description."""
        content = f"{step.title} {step.description}".lower()
        risk_level = RiskLevel.LOW
        confidence = 0.9
        mitigation_strategies = []
        
        # Check for high-risk command patterns
        for pattern in self.high_risk_command_patterns:
            if re.search(pattern, content):
                risk_level = RiskLevel.HIGH
                mitigation_strategies.append(f"Review command pattern: {pattern}")
        
        # Check for medium-risk patterns
        if risk_level == RiskLevel.LOW:
            for pattern in self.medium_risk_command_patterns:
                if re.search(pattern, content):
                    risk_level = RiskLevel.MEDIUM
                    mitigation_strategies.append(f"Validate command pattern: {pattern}")
        
        # Check for file operations
        file_ops = ["write", "create", "delete", "modify", "remove"]
        if any(op in content for op in file_ops):
            if risk_level == RiskLevel.LOW:
                risk_level = RiskLevel.MEDIUM
            mitigation_strategies.append("Review file operation safety")
        
        # Check for critical directories
        for critical_dir in self.file_risk_patterns['critical_dirs']:
            if critical_dir in content:
                risk_level = RiskLevel.CRITICAL
                mitigation_strategies.append(f"Critical directory access: {critical_dir}")
        
        if risk_level != RiskLevel.LOW or mitigation_strategies:
            return RiskFactor(
                factor_name="step_content_risk",
                risk_level=risk_level,
                confidence=confidence,
                description="Risk assessment based on step content analysis",
                mitigation_strategies=mitigation_strategies or ["Standard precautions apply"]
            )
        
        return None
    
    def _assess_context_risk(self, context: Dict[str, Any]) -> Optional[RiskFactor]:
        """Assess risk based on additional context."""
        risk_level = RiskLevel.LOW
        confidence = 0.7
        mitigation_strategies = []
        
        # Check for high-risk context indicators
        context_str = str(context).lower()
        
        if any(indicator in context_str for indicator in ["production", "live", "master"]):
            risk_level = RiskLevel.HIGH
            mitigation_strategies.append("Production environment detected")
        
        if any(indicator in context_str for indicator in ["large_dataset", "bulk_operation"]):
            if risk_level == RiskLevel.LOW:
                risk_level = RiskLevel.MEDIUM
            mitigation_strategies.append("Large-scale operation detected")
        
        if mitigation_strategies:
            return RiskFactor(
                factor_name="context_risk",
                risk_level=risk_level,
                confidence=confidence,
                description="Risk assessment based on execution context",
                mitigation_strategies=mitigation_strategies
            )
        
        return None
    
    def _assess_historical_risk(self, step: PlanStep, state: State) -> Optional[RiskFactor]:
        """Assess risk based on historical performance."""
        confidence = 0.6  # Lower confidence due to limited historical data
        
        # Check retry count
        if step.retry_count > 2:
            return RiskFactor(
                factor_name="historical_failure_risk",
                risk_level=RiskLevel.HIGH,
                confidence=confidence,
                description=f"Step has failed {step.retry_count} times previously",
                mitigation_strategies=[
                    "Review previous failure reasons",
                    "Consider alternative approaches",
                    "Validate input requirements"
                ]
            )
        elif step.retry_count > 0:
            return RiskFactor(
                factor_name="historical_retry_risk",
                risk_level=RiskLevel.MEDIUM,
                confidence=confidence,
                description=f"Step has been retried {step.retry_count} times",
                mitigation_strategies=[
                    "Monitor execution carefully",
                    "Prepare fallback strategies"
                ]
            )
        
        return None
    
    def _assess_data_sensitivity_risk(
        self, 
        step: PlanStep, 
        context: Dict[str, Any]
    ) -> Optional[RiskFactor]:
        """Assess risk based on data sensitivity."""
        sensitive_indicators = [
            "patient", "medical", "personal", "confidential", 
            "private", "credentials", "password", "secret", "key"
        ]
        
        content = f"{step.title} {step.description}".lower()
        context_str = str(context).lower() if context else ""
        full_content = f"{content} {context_str}"
        
        if any(indicator in full_content for indicator in sensitive_indicators):
            return RiskFactor(
                factor_name="data_sensitivity_risk",
                risk_level=RiskLevel.HIGH,
                confidence=0.8,
                description="Potentially sensitive data detected",
                mitigation_strategies=[
                    "Verify data handling compliance",
                    "Ensure proper anonymization",
                    "Review data access permissions"
                ]
            )
        
        return None
    
    def _assess_tool_specific_risk(
        self, 
        tool_name: str, 
        tool_args: Dict[str, Any]
    ) -> Optional[RiskFactor]:
        """Assess risk specific to the tool being used."""
        tool_lower = tool_name.lower()
        
        # Use pattern-based risk assessment from config
        risk_level = self.config.risk_config.tool_risk_patterns.get(
            tool_name, 
            self.config._assess_tool_risk(tool_name)
        )
        
        mitigation_strategies = []
        
        # Tool-specific adjustments
        if "execute" in tool_lower or "command" in tool_lower:
            risk_level = max(risk_level, RiskLevel.MEDIUM)
            mitigation_strategies.append("Review command execution safety")
        
        if "write" in tool_lower or "create" in tool_lower:
            risk_level = max(risk_level, RiskLevel.MEDIUM)
            mitigation_strategies.append("Validate write operation targets")
        
        return RiskFactor(
            factor_name=f"tool_{tool_name}_risk",
            risk_level=risk_level,
            confidence=0.85,
            description=f"Risk assessment for {tool_name} tool",
            mitigation_strategies=mitigation_strategies or ["Standard tool precautions"]
        )
    
    def _assess_tool_args_risk(
        self, 
        tool_name: str, 
        tool_args: Dict[str, Any]
    ) -> Optional[RiskFactor]:
        """Assess risk based on tool arguments."""
        args_str = str(tool_args).lower()
        risk_level = RiskLevel.LOW
        mitigation_strategies = []
        
        # Check for risky argument patterns
        if any(pattern in args_str for pattern in ["sudo", "rm -rf", "format", "delete"]):
            risk_level = RiskLevel.HIGH
            mitigation_strategies.append("High-risk command arguments detected")
        
        # Check for file paths in critical directories
        for critical_dir in self.file_risk_patterns['critical_dirs']:
            if critical_dir in args_str:
                risk_level = RiskLevel.CRITICAL
                mitigation_strategies.append(f"Critical directory in arguments: {critical_dir}")
        
        if mitigation_strategies:
            return RiskFactor(
                factor_name="tool_args_risk",
                risk_level=risk_level,
                confidence=0.9,
                description="Risk assessment based on tool arguments",
                mitigation_strategies=mitigation_strategies
            )
        
        return None
    
    def _assess_tool_reliability_risk(self, tool_name: str) -> Optional[RiskFactor]:
        """Assess risk based on tool reliability history."""
        reliability_score = self.tool_reliability_scores.get(tool_name, 0.8)
        
        if reliability_score < 0.7:
            return RiskFactor(
                factor_name="tool_reliability_risk",
                risk_level=RiskLevel.MEDIUM,
                confidence=0.8,
                description=f"Tool has lower reliability score: {reliability_score:.2f}",
                mitigation_strategies=[
                    "Monitor execution closely",
                    "Prepare fallback options",
                    "Validate results carefully"
                ]
            )
        
        return None
    
    def _assess_agent_tool_compatibility_risk(
        self, 
        agent_name: str, 
        tool_name: str
    ) -> Optional[RiskFactor]:
        """Assess risk based on agent-tool compatibility."""
        # Define agent-tool compatibility matrix
        compatibility_issues = {
            "researcher": ["write_file", "execute_command", "system_command"],
            "browser": ["execute_command", "write_file", "system_command"],
            "reporter": ["execute_command", "system_command"]
        }
        
        incompatible_tools = compatibility_issues.get(agent_name, [])
        if any(incompatible in tool_name.lower() for incompatible in incompatible_tools):
            return RiskFactor(
                factor_name="agent_tool_compatibility_risk",
                risk_level=RiskLevel.MEDIUM,
                confidence=0.9,
                description=f"Potential compatibility issue: {agent_name} using {tool_name}",
                mitigation_strategies=[
                    "Verify agent has appropriate permissions",
                    "Consider using specialized agent",
                    "Review tool requirements"
                ]
            )
        
        return None
    
    def _synthesize_risk_assessment(
        self,
        risk_factors: List[RiskFactor],
        step: Optional[PlanStep] = None,
        tool_name: Optional[str] = None
    ) -> RiskAssessment:
        """Synthesize individual risk factors into overall assessment."""
        if not risk_factors:
            # No specific risks identified
            return RiskAssessment(
                overall_risk=RiskLevel.LOW,
                confidence_score=0.9,
                risk_factors=[],
                recommendation="Proceed with standard precautions",
                requires_human_approval=False,
                mitigation_suggestions=["Monitor execution progress"]
            )
        
        # Determine overall risk level (take maximum)
        risk_hierarchy = [RiskLevel.LOW, RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL]
        overall_risk = max(risk_factors, key=lambda rf: risk_hierarchy.index(rf.risk_level)).risk_level
        
        # Calculate confidence score (weighted average)
        total_weight = sum(rf.confidence for rf in risk_factors)
        confidence_score = total_weight / len(risk_factors) if risk_factors else 0.5
        
        # Generate recommendation
        recommendation = self._generate_recommendation(overall_risk, risk_factors)
        
        # Determine if human approval is required
        requires_approval = self._requires_human_approval(overall_risk, confidence_score)
        
        # Collect mitigation suggestions
        mitigation_suggestions = []
        for rf in risk_factors:
            mitigation_suggestions.extend(rf.mitigation_strategies)
        
        # Remove duplicates while preserving order
        mitigation_suggestions = list(dict.fromkeys(mitigation_suggestions))
        
        return RiskAssessment(
            overall_risk=overall_risk,
            confidence_score=confidence_score,
            risk_factors=risk_factors,
            recommendation=recommendation,
            requires_human_approval=requires_approval,
            mitigation_suggestions=mitigation_suggestions
        )
    
    def _generate_recommendation(
        self, 
        overall_risk: RiskLevel, 
        risk_factors: List[RiskFactor]
    ) -> str:
        """Generate human-readable recommendation."""
        risk_descriptions = {
            RiskLevel.LOW: "Proceed with standard monitoring",
            RiskLevel.MEDIUM: "Proceed with enhanced monitoring and validation",
            RiskLevel.HIGH: "Requires careful review and approval before proceeding",
            RiskLevel.CRITICAL: "Requires explicit human approval and additional safeguards"
        }
        
        base_recommendation = risk_descriptions[overall_risk]
        
        # Add specific concerns from risk factors
        concerns = [rf.factor_name.replace("_", " ").title() for rf in risk_factors]
        if concerns:
            concern_text = ", ".join(concerns[:3])  # Limit to top 3 concerns
            if len(concerns) > 3:
                concern_text += f" and {len(concerns) - 3} other factors"
            
            return f"{base_recommendation}. Key concerns: {concern_text}."
        
        return base_recommendation
    
    def _requires_human_approval(
        self, 
        overall_risk: RiskLevel, 
        confidence_score: float
    ) -> bool:
        """Determine if human approval is required based on risk and confidence."""
        if overall_risk == RiskLevel.CRITICAL:
            return True
        elif overall_risk == RiskLevel.HIGH:
            return True
        elif overall_risk == RiskLevel.MEDIUM and confidence_score < 0.8:
            return True
        
        return False


# Global risk assessor instance
risk_assessor = RiskAssessor()


def get_risk_assessor() -> RiskAssessor:
    """Get the global risk assessor instance."""
    return risk_assessor