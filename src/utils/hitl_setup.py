"""
HITL (Human-in-the-Loop) setup utilities for configuring workflows.

This module provides convenient functions for setting up HITL modes
based on different use case scenarios and requirements.
"""

import logging
from typing import Optional, Dict, Any

from ..config.hitl_config import HITLMode, get_hitl_config, set_hitl_mode
from ..service.workflow_service import get_graph

logger = logging.getLogger(__name__)


class HITLWorkflowConfigurator:
    """Utility class for configuring HITL settings in workflows."""
    
    def __init__(self):
        self.config = get_hitl_config()
    
    async def configure_for_production(self, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Configure HITL for production environment - semi-autonomous mode.
        
        Args:
            thread_id: Optional thread ID for thread-specific configuration
            
        Returns:
            Configuration dictionary
        """
        mode = HITLMode.SEMI_AUTONOMOUS
        
        if thread_id:
            await self._set_thread_hitl_mode(thread_id, mode)
            logger.info(f"Set production HITL mode (semi-autonomous) for thread {thread_id}")
        else:
            set_hitl_mode(mode)
            logger.info("Set global production HITL mode (semi-autonomous)")
        
        return {
            "hitl_mode": mode.value,
            "thread_id": thread_id,
            "description": "Production mode: Risk-based human intervention",
            "characteristics": [
                "Automatic execution for low-risk operations",
                "Human approval required for high-risk operations",
                "Smart risk assessment based on action context"
            ]
        }
    
    async def configure_for_development(self, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Configure HITL for development environment - full manual mode.
        
        Args:
            thread_id: Optional thread ID for thread-specific configuration
            
        Returns:
            Configuration dictionary
        """
        mode = HITLMode.FULL_MANUAL
        
        if thread_id:
            await self._set_thread_hitl_mode(thread_id, mode)
            logger.info(f"Set development HITL mode (full manual) for thread {thread_id}")
        else:
            set_hitl_mode(mode)
            logger.info("Set global development HITL mode (full manual)")
        
        return {
            "hitl_mode": mode.value,
            "thread_id": thread_id,
            "description": "Development mode: Manual approval for all actions",
            "characteristics": [
                "Human approval required for every significant action",
                "Maximum control and oversight",
                "Ideal for testing and development"
            ]
        }
    
    async def configure_for_automation(self, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Configure HITL for full automation - autonomous mode.
        
        Args:
            thread_id: Optional thread ID for thread-specific configuration
            
        Returns:
            Configuration dictionary
        """
        mode = HITLMode.FULLY_AUTONOMOUS
        
        if thread_id:
            await self._set_thread_hitl_mode(thread_id, mode)
            logger.info(f"Set automation HITL mode (fully autonomous) for thread {thread_id}")
        else:
            set_hitl_mode(mode)
            logger.info("Set global automation HITL mode (fully autonomous)")
        
        return {
            "hitl_mode": mode.value,
            "thread_id": thread_id,
            "description": "Automation mode: No human intervention",
            "characteristics": [
                "Fully automated execution",
                "No interruptions or approvals",
                "Maximum speed and efficiency"
            ]
        }
    
    async def configure_for_bioinformatics_analysis(
        self, 
        thread_id: Optional[str] = None,
        sensitivity_level: str = "medium"
    ) -> Dict[str, Any]:
        """
        Configure HITL specifically for bioinformatics analysis workflows.
        
        Args:
            thread_id: Optional thread ID for thread-specific configuration
            sensitivity_level: "low", "medium", or "high" sensitivity
            
        Returns:
            Configuration dictionary
        """
        # Choose mode based on data sensitivity
        if sensitivity_level == "high":
            mode = HITLMode.FULL_MANUAL
            description = "High sensitivity: Manual approval for all operations"
        elif sensitivity_level == "low":
            mode = HITLMode.FULLY_AUTONOMOUS
            description = "Low sensitivity: Automated processing"
        else:
            mode = HITLMode.SEMI_AUTONOMOUS
            description = "Medium sensitivity: Risk-based intervention"
        
        if thread_id:
            await self._set_thread_hitl_mode(thread_id, mode)
            logger.info(f"Set bioinformatics HITL mode ({mode.value}) for thread {thread_id}")
        else:
            set_hitl_mode(mode)
            logger.info(f"Set global bioinformatics HITL mode ({mode.value})")
        
        return {
            "hitl_mode": mode.value,
            "thread_id": thread_id,
            "sensitivity_level": sensitivity_level,
            "description": description,
            "use_case": "bioinformatics_analysis",
            "recommendations": self._get_bioinformatics_recommendations(sensitivity_level)
        }
    
    async def configure_custom(
        self,
        mode: HITLMode,
        thread_id: Optional[str] = None,
        custom_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Configure HITL with custom settings.
        
        Args:
            mode: HITL mode to set
            thread_id: Optional thread ID for thread-specific configuration
            custom_settings: Optional custom configuration settings
            
        Returns:
            Configuration dictionary
        """
        if thread_id:
            await self._set_thread_hitl_mode(thread_id, mode)
            logger.info(f"Set custom HITL mode ({mode.value}) for thread {thread_id}")
            
            # Apply custom settings if provided
            if custom_settings:
                await self._apply_custom_thread_settings(thread_id, custom_settings)
        else:
            set_hitl_mode(mode)
            logger.info(f"Set global custom HITL mode ({mode.value})")
        
        return {
            "hitl_mode": mode.value,
            "thread_id": thread_id,
            "custom_settings": custom_settings or {},
            "description": f"Custom configuration: {mode.value}"
        }
    
    async def get_current_configuration(
        self, 
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get the current HITL configuration for a thread or globally.
        
        Args:
            thread_id: Optional thread ID to check
            
        Returns:
            Current configuration dictionary
        """
        if thread_id:
            graph = get_graph()
            config = {"configurable": {"thread_id": thread_id}}
            
            try:
                current_state = await graph.aget_state(config)
                if current_state and current_state.values.get("hitl_mode"):
                    hitl_mode = current_state.values["hitl_mode"]
                    source = "thread"
                else:
                    hitl_mode = self.config.mode.value
                    source = "global"
            except Exception as e:
                logger.warning(f"Error getting thread state: {e}")
                hitl_mode = self.config.mode.value
                source = "global"
        else:
            hitl_mode = self.config.mode.value
            source = "global"
        
        return {
            "hitl_mode": hitl_mode,
            "thread_id": thread_id,
            "source": source,
            "timestamp": None  # Could add timestamp tracking
        }
    
    async def _set_thread_hitl_mode(self, thread_id: str, mode: HITLMode):
        """Set HITL mode for a specific thread."""
        try:
            graph = get_graph()
            config = {"configurable": {"thread_id": thread_id}}
            
            # Get current state
            current_state = await graph.aget_state(config)
            if current_state:
                updated_values = current_state.values.copy()
                updated_values["hitl_mode"] = mode.value
                await graph.aupdate_state(config, updated_values)
            else:
                # Initialize new state with HITL mode
                initial_state = {
                    "hitl_mode": mode.value,
                    "awaiting_human_input": False,
                    "interrupt_context": None,
                    "human_feedback": None,
                    "interrupt_history": []
                }
                await graph.aupdate_state(config, initial_state)
                
        except Exception as e:
            logger.error(f"Error setting thread HITL mode: {e}")
            raise
    
    async def _apply_custom_thread_settings(
        self, 
        thread_id: str, 
        custom_settings: Dict[str, Any]
    ):
        """Apply custom settings to a thread."""
        try:
            graph = get_graph()
            config = {"configurable": {"thread_id": thread_id}}
            
            current_state = await graph.aget_state(config)
            if current_state:
                updated_values = current_state.values.copy()
                
                # Apply custom settings that are valid for HITL
                valid_settings = [
                    "hitl_timeout_seconds", 
                    "hitl_reminder_interval",
                    "hitl_auto_approve_low_risk"
                ]
                
                for setting, value in custom_settings.items():
                    if setting in valid_settings:
                        updated_values[f"hitl_{setting}"] = value
                
                await graph.aupdate_state(config, updated_values)
                
        except Exception as e:
            logger.error(f"Error applying custom thread settings: {e}")
            raise
    
    def _get_bioinformatics_recommendations(self, sensitivity_level: str) -> list:
        """Get recommendations based on bioinformatics data sensitivity."""
        if sensitivity_level == "high":
            return [
                "Review all data access and processing steps",
                "Ensure compliance with data protection regulations",
                "Verify anonymization and privacy measures",
                "Approve all external API calls and data exports"
            ]
        elif sensitivity_level == "medium":
            return [
                "Review high-risk operations automatically",
                "Monitor data processing for quality issues",
                "Approve operations involving external services",
                "Enable automatic approval for standard analysis steps"
            ]
        else:  # low
            return [
                "Enable full automation for standard workflows",
                "Monitor execution for errors and failures",
                "Set up alerts for unusual patterns or errors",
                "Review results periodically for quality assurance"
            ]


# Convenience functions for common use cases

async def setup_development_hitl(thread_id: Optional[str] = None) -> Dict[str, Any]:
    """Quick setup for development environment."""
    configurator = HITLWorkflowConfigurator()
    return await configurator.configure_for_development(thread_id)


async def setup_production_hitl(thread_id: Optional[str] = None) -> Dict[str, Any]:
    """Quick setup for production environment."""
    configurator = HITLWorkflowConfigurator()
    return await configurator.configure_for_production(thread_id)


async def setup_automation_hitl(thread_id: Optional[str] = None) -> Dict[str, Any]:
    """Quick setup for fully automated workflows."""
    configurator = HITLWorkflowConfigurator()
    return await configurator.configure_for_automation(thread_id)


async def setup_bioinformatics_hitl(
    sensitivity_level: str = "medium",
    thread_id: Optional[str] = None
) -> Dict[str, Any]:
    """Quick setup for bioinformatics workflows."""
    configurator = HITLWorkflowConfigurator()
    return await configurator.configure_for_bioinformatics_analysis(
        thread_id, sensitivity_level
    )