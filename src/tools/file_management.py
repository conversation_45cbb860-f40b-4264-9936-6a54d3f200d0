import logging
import os
import shutil
from pathlib import Path
from typing import Optional, Type, List
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from ..config.env import WORKSPACE_DIR
from .decorators import create_logged_tool
from langchain_core.tools.base import ArgsSchema

logger = logging.getLogger(__name__)


class WriteFileInput(BaseModel):
    """Input for WriteFileTool."""

    file_path: str = Field(..., description="name of file")
    text: str = Field(..., description="text to write to file")
    append: bool = Field(
        default=False, description="Whether to append to an existing file."
    )


class WorkspaceRestrictedWriteFileTool(BaseTool):
    """Tool that can write a file with workspace directory restrictions."""

    name: str = "write_file"
    description: str = "Write file to disk. Creates any necessary directories."
    args_schema: ArgsSchema | None = WriteFileInput
    return_direct: bool = False

    def _run(
        self,
        file_path: str,
        text: str,
        append: bool = False,
    ) -> str:
        try:
            # Ensure workspace directory exists
            os.makedirs(WORKSPACE_DIR, exist_ok=True)

            # Convert to absolute path and resolve any relative components
            if os.path.isabs(file_path):
                # If absolute path, check if it's within workspace
                abs_file_path = os.path.abspath(file_path)
            else:
                # If relative path, make it relative to workspace
                abs_file_path = os.path.abspath(os.path.join(WORKSPACE_DIR, file_path))

            # Ensure the resolved path is within the workspace directory
            workspace_abs = os.path.abspath(WORKSPACE_DIR)
            if (
                not abs_file_path.startswith(workspace_abs + os.sep)
                and abs_file_path != workspace_abs
            ):
                raise ValueError(
                    f"Access denied: File path '{file_path}' is outside the allowed workspace directory '{WORKSPACE_DIR}'"
                )

            # Create directory if it doesn't exist
            directory = os.path.dirname(abs_file_path)
            if directory:
                os.makedirs(directory, exist_ok=True)

            # Write the file
            mode = "a" if append else "w"
            with open(abs_file_path, mode, encoding="utf-8") as f:
                f.write(text)

            return f"File written successfully to {abs_file_path}"
        except Exception as e:
            return f"Error writing file: {str(e)}"


class ReadFileInput(BaseModel):
    """Input for ReadFileTool."""

    file_path: str = Field(..., description="path to file to read")


class WorkspaceRestrictedReadFileTool(BaseTool):
    """Tool that can read files with workspace directory restrictions."""

    name: str = "read_file"
    description: str = "Read contents of a file from disk."
    args_schema: ArgsSchema | None = ReadFileInput
    return_direct: bool = False

    def _run(self, file_path: str) -> str:
        try:
            # Convert to absolute path and resolve any relative components
            if os.path.isabs(file_path):
                abs_file_path = os.path.abspath(file_path)
            else:
                abs_file_path = os.path.abspath(os.path.join(WORKSPACE_DIR, file_path))

            # Ensure the resolved path is within the workspace directory
            workspace_abs = os.path.abspath(WORKSPACE_DIR)
            if (
                not abs_file_path.startswith(workspace_abs + os.sep)
                and abs_file_path != workspace_abs
            ):
                raise ValueError(
                    f"Access denied: File path '{file_path}' is outside the allowed workspace directory '{WORKSPACE_DIR}'"
                )

            # Read the file
            with open(abs_file_path, "r", encoding="utf-8") as f:
                content = f.read()

            return f"File content from {abs_file_path}:\n\n{content}"
        except Exception as e:
            return f"Error reading file: {str(e)}"


class ListFilesInput(BaseModel):
    """Input for ListFilesTool."""

    directory_path: str = Field(
        default=".", description="directory path to list (default: current directory)"
    )


class WorkspaceRestrictedListFilesTool(BaseTool):
    """Tool that can list files and directories with workspace restrictions."""

    name: str = "list_files"
    description: str = "List files and directories in a given path within workspace."
    args_schema: ArgsSchema | None = ListFilesInput
    return_direct: bool = False

    def _run(self, directory_path: str = ".") -> str:
        try:
            # Convert to absolute path
            if os.path.isabs(directory_path):
                abs_dir_path = os.path.abspath(directory_path)
            else:
                abs_dir_path = os.path.abspath(
                    os.path.join(WORKSPACE_DIR, directory_path)
                )

            # Ensure the resolved path is within the workspace directory
            workspace_abs = os.path.abspath(WORKSPACE_DIR)
            if (
                not abs_dir_path.startswith(workspace_abs + os.sep)
                and abs_dir_path != workspace_abs
            ):
                raise ValueError(
                    f"Access denied: Directory path '{directory_path}' is outside the allowed workspace directory '{WORKSPACE_DIR}'"
                )

            if not os.path.exists(abs_dir_path):
                return f"Directory does not exist: {abs_dir_path}"

            if not os.path.isdir(abs_dir_path):
                return f"Path is not a directory: {abs_dir_path}"

            # List contents
            items = []
            for item in sorted(os.listdir(abs_dir_path)):
                item_path = os.path.join(abs_dir_path, item)
                if os.path.isdir(item_path):
                    items.append(f"📁 {item}/")
                else:
                    # Get file size
                    size = os.path.getsize(item_path)
                    items.append(f"📄 {item} ({size} bytes)")

            if not items:
                return f"Directory is empty: {abs_dir_path}"

            return f"Contents of {abs_dir_path}:\n" + "\n".join(items)
        except Exception as e:
            return f"Error listing files: {str(e)}"


class DeleteFileInput(BaseModel):
    """Input for DeleteFileTool."""

    file_path: str = Field(..., description="path to file or directory to delete")


class WorkspaceRestrictedDeleteFileTool(BaseTool):
    """Tool that can delete files and directories with workspace restrictions."""

    name: str = "delete_file"
    description: str = "Delete a file or directory within workspace."
    args_schema: ArgsSchema | None = DeleteFileInput
    return_direct: bool = False

    def _run(self, file_path: str) -> str:
        try:
            # Convert to absolute path
            if os.path.isabs(file_path):
                abs_file_path = os.path.abspath(file_path)
            else:
                abs_file_path = os.path.abspath(os.path.join(WORKSPACE_DIR, file_path))

            # Ensure the resolved path is within the workspace directory
            workspace_abs = os.path.abspath(WORKSPACE_DIR)
            if (
                not abs_file_path.startswith(workspace_abs + os.sep)
                and abs_file_path != workspace_abs
            ):
                raise ValueError(
                    f"Access denied: File path '{file_path}' is outside the allowed workspace directory '{WORKSPACE_DIR}'"
                )

            if not os.path.exists(abs_file_path):
                return f"File or directory does not exist: {abs_file_path}"

            # Delete file or directory
            if os.path.isdir(abs_file_path):
                shutil.rmtree(abs_file_path)
                return f"Directory deleted successfully: {abs_file_path}"
            else:
                os.remove(abs_file_path)
                return f"File deleted successfully: {abs_file_path}"
        except Exception as e:
            return f"Error deleting file: {str(e)}"


# Create tool instances
write_file_tool = create_logged_tool(WorkspaceRestrictedWriteFileTool)()
read_file_tool = create_logged_tool(WorkspaceRestrictedReadFileTool)()
list_files_tool = create_logged_tool(WorkspaceRestrictedListFilesTool)()
delete_file_tool = create_logged_tool(WorkspaceRestrictedDeleteFileTool)()

# Export native file tools for fallback use
native_file_tools = [write_file_tool, read_file_tool, list_files_tool, delete_file_tool]
