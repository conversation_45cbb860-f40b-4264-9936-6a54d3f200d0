"""
Enhanced error handling and recovery system for biomni tools.

This module provides comprehensive error classification, user-friendly error messages,
and intelligent recovery strategies for biomni tool execution failures.

Key Features:
- Error taxonomy with detailed categorization
- User-friendly error messages with actionable guidance
- Tool-specific error context and troubleshooting hints
- Environment and dependency validation
- Recovery strategies and fallback mechanisms
"""

import re
import time
import json
import asyncio
import logging
import threading
import os
import subprocess
from typing import Dict, List, Optional, Any, Callable, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict
from pathlib import Path

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Enhanced error categories for biomni tool failures."""

    TIMEOUT = "timeout"
    DEPENDENCY = "dependency"
    PERMISSION = "permission"
    NETWORK = "network"
    ENVIRONMENT = "environment"
    INPUT_VALIDATION = "input_validation"
    TOOL_SPECIFIC = "tool_specific"
    SYSTEM_RESOURCE = "system_resource"
    CONFIGURATION = "configuration"
    AUTHENTICATION = "authentication"
    CONNECTION_ERROR = "connection_error"  # MCP connection issues
    DATA_ERROR = "data_error"  # Data format/availability issues
    UNKNOWN = "unknown"  # Unclassified errors


class ErrorSeverity(Enum):
    """Error severity levels."""

    CRITICAL = "critical"  # Tool completely unavailable
    HIGH = "high"  # Major functionality affected
    MEDIUM = "medium"  # Some functionality affected
    LOW = "low"  # Minor issues, workarounds available
    INFO = "info"  # Informational warnings


class RecoveryStrategy(Enum):
    """Available recovery strategies."""

    RETRY = "retry"  # Simple retry
    FALLBACK_EXECUTION = "fallback_execution"  # Switch execution method
    ENVIRONMENT_RESET = "environment_reset"  # Reset environment
    DEPENDENCY_INSTALL = "dependency_install"  # Install missing packages
    PERMISSION_FIX = "permission_fix"  # Fix file permissions
    CACHE_CLEAR = "cache_clear"  # Clear caches
    CONNECTION_RESET = "connection_reset"  # Reset connections
    NO_RECOVERY = "no_recovery"  # Cannot recover


@dataclass
class ErrorContext:
    """Enhanced context information for an error."""

    category: ErrorCategory
    severity: ErrorSeverity
    tool_name: str
    error_code: str
    original_error: str
    execution_method: str = "unknown"
    environment_info: Dict[str, Any] = field(default_factory=dict)
    attempted_solutions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RecoveryAction:
    """Recovery action suggestion."""

    action_type: str
    description: str
    priority: int  # Lower numbers = higher priority
    auto_executable: bool = False
    command: Optional[str] = None
    expected_outcome: Optional[str] = None


@dataclass
class ErrorReport:
    """Comprehensive error report with recovery suggestions."""

    context: ErrorContext
    user_message: str
    technical_details: str
    recovery_actions: List[RecoveryAction]
    similar_errors: List[str] = field(default_factory=list)
    documentation_links: List[str] = field(default_factory=list)


@dataclass
class ErrorPattern:
    """Error pattern for detection and classification."""

    pattern: str
    category: ErrorCategory
    recovery_strategy: RecoveryStrategy
    description: str
    confidence: float = 1.0
    severity: ErrorSeverity = ErrorSeverity.MEDIUM


@dataclass
class ErrorInstance:
    """Instance of a detected error."""

    error_id: str
    category: ErrorCategory
    recovery_strategy: RecoveryStrategy
    original_error: str
    tool_name: str
    timestamp: float
    recovery_attempts: int = 0
    recovered: bool = False
    severity: ErrorSeverity = ErrorSeverity.MEDIUM


# Enhanced error pattern database with severity levels
ERROR_PATTERNS = [
    # Timeout errors
    ErrorPattern(
        pattern=r"timeout|timed out|TimeoutError|asyncio\.timeout",
        category=ErrorCategory.TIMEOUT,
        recovery_strategy=RecoveryStrategy.RETRY,
        description="Operation timeout",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"Process.*timed out|Subprocess.*timeout|TimeoutExpired",
        category=ErrorCategory.TIMEOUT,
        recovery_strategy=RecoveryStrategy.FALLBACK_EXECUTION,
        description="Subprocess execution timeout",
        severity=ErrorSeverity.HIGH,
    ),
    # Dependency errors
    ErrorPattern(
        pattern=r"ImportError|ModuleNotFoundError|No module named",
        category=ErrorCategory.DEPENDENCY,
        recovery_strategy=RecoveryStrategy.DEPENDENCY_INSTALL,
        description="Python package missing",
        severity=ErrorSeverity.CRITICAL,
    ),
    ErrorPattern(
        pattern=r"command not found|not found in path",
        category=ErrorCategory.DEPENDENCY,
        recovery_strategy=RecoveryStrategy.DEPENDENCY_INSTALL,
        description="System command missing",
        severity=ErrorSeverity.CRITICAL,
    ),
    ErrorPattern(
        pattern=r"package .* not found|library .* not found",
        category=ErrorCategory.DEPENDENCY,
        recovery_strategy=RecoveryStrategy.DEPENDENCY_INSTALL,
        description="Package dependency missing",
        severity=ErrorSeverity.HIGH,
    ),
    # Permission errors
    ErrorPattern(
        pattern=r"Permission denied|Access denied|Operation not permitted",
        category=ErrorCategory.PERMISSION,
        recovery_strategy=RecoveryStrategy.PERMISSION_FIX,
        description="File/directory permission issue",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"insufficient privileges|not permitted",
        category=ErrorCategory.PERMISSION,
        recovery_strategy=RecoveryStrategy.PERMISSION_FIX,
        description="Insufficient system privileges",
        severity=ErrorSeverity.HIGH,
    ),
    # Network errors
    ErrorPattern(
        pattern=r"connection error|network error|dns resolution failed",
        category=ErrorCategory.NETWORK,
        recovery_strategy=RecoveryStrategy.RETRY,
        description="Network connectivity issue",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"http.*error|url.*error|request failed",
        category=ErrorCategory.NETWORK,
        recovery_strategy=RecoveryStrategy.RETRY,
        description="HTTP request failed",
        severity=ErrorSeverity.MEDIUM,
    ),
    ErrorPattern(
        pattern=r"ssl.*error|certificate.*error|tls.*error",
        category=ErrorCategory.NETWORK,
        recovery_strategy=RecoveryStrategy.RETRY,
        description="SSL/TLS connection error",
        severity=ErrorSeverity.MEDIUM,
    ),
    # Environment errors
    ErrorPattern(
        pattern=r"conda.*not found|python.*not found|No such file or directory.*python",
        category=ErrorCategory.ENVIRONMENT,
        recovery_strategy=RecoveryStrategy.ENVIRONMENT_RESET,
        description="Python environment not found",
        severity=ErrorSeverity.CRITICAL,
    ),
    ErrorPattern(
        pattern=r"environment.*not found|environment.*not activated",
        category=ErrorCategory.ENVIRONMENT,
        recovery_strategy=RecoveryStrategy.ENVIRONMENT_RESET,
        description="Conda environment not activated",
        severity=ErrorSeverity.HIGH,
    ),
    # Input validation errors
    ErrorPattern(
        pattern=r"invalid.*argument|invalid.*parameter|bad.*input",
        category=ErrorCategory.INPUT_VALIDATION,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="Invalid input parameters",
        severity=ErrorSeverity.MEDIUM,
    ),
    ErrorPattern(
        pattern=r"missing.*required.*parameter|required.*argument",
        category=ErrorCategory.INPUT_VALIDATION,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="Missing required parameters",
        severity=ErrorSeverity.MEDIUM,
    ),
    # System resource errors
    ErrorPattern(
        pattern=r"out of memory|MemoryError|Cannot allocate memory",
        category=ErrorCategory.SYSTEM_RESOURCE,
        recovery_strategy=RecoveryStrategy.CACHE_CLEAR,
        description="Insufficient memory",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"disk.*full|no space left|insufficient.*space",
        category=ErrorCategory.SYSTEM_RESOURCE,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="Insufficient disk space",
        severity=ErrorSeverity.HIGH,
    ),
    # Authentication errors
    ErrorPattern(
        pattern=r"authentication.*failed|invalid.*credentials|unauthorized",
        category=ErrorCategory.AUTHENTICATION,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="Authentication failed",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"api.*key.*invalid|token.*expired|api.*limit",
        category=ErrorCategory.AUTHENTICATION,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="API authentication error",
        severity=ErrorSeverity.HIGH,
    ),
    # Tool-specific errors (UniProt)
    ErrorPattern(
        pattern=r"uniprot.*not responding|uniprot.*error",
        category=ErrorCategory.TOOL_SPECIFIC,
        recovery_strategy=RecoveryStrategy.RETRY,
        description="UniProt service error",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"invalid.*uniprot.*id|uniprot.*id.*not found",
        category=ErrorCategory.TOOL_SPECIFIC,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="Invalid UniProt ID",
        severity=ErrorSeverity.MEDIUM,
    ),
    # Tool-specific errors (AlphaFold)
    ErrorPattern(
        pattern=r"alphafold.*not responding|alphafold.*error",
        category=ErrorCategory.TOOL_SPECIFIC,
        recovery_strategy=RecoveryStrategy.RETRY,
        description="AlphaFold service error",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"no.*alphafold.*prediction|prediction.*not.*available",
        category=ErrorCategory.TOOL_SPECIFIC,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="No AlphaFold prediction available",
        severity=ErrorSeverity.LOW,
    ),
    # Connection errors (MCP)
    ErrorPattern(
        pattern=r"No active session found|Session not found|Connection closed|Connection reset",
        category=ErrorCategory.CONNECTION_ERROR,
        recovery_strategy=RecoveryStrategy.CONNECTION_RESET,
        description="MCP session/connection lost",
        severity=ErrorSeverity.HIGH,
    ),
    ErrorPattern(
        pattern=r"Failed to connect|Connection refused|Connection timeout",
        category=ErrorCategory.CONNECTION_ERROR,
        recovery_strategy=RecoveryStrategy.RETRY,
        description="Connection establishment failed",
        severity=ErrorSeverity.HIGH,
    ),
    # Data errors
    ErrorPattern(
        pattern=r"File not found|No such file|FileNotFoundError",
        category=ErrorCategory.DATA_ERROR,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="Required file missing",
        confidence=0.8,
        severity=ErrorSeverity.MEDIUM,
    ),
    ErrorPattern(
        pattern=r"Invalid.*format|Parse.*error|JSON.*decode.*error",
        category=ErrorCategory.DATA_ERROR,
        recovery_strategy=RecoveryStrategy.NO_RECOVERY,
        description="Data format error",
        severity=ErrorSeverity.MEDIUM,
    ),
]


class ErrorDetector:
    """
    Advanced error detection and classification system.

    Analyzes error messages, stack traces, and execution context to classify
    errors and recommend appropriate recovery strategies.
    """

    def __init__(self):
        """Initialize error detector."""
        self.patterns = ERROR_PATTERNS.copy()
        self.error_history: Dict[str, List[ErrorInstance]] = defaultdict(list)
        self._lock = threading.RLock()

    def detect_error(
        self, error_message: str, tool_name: str, context: Dict[str, Any] = None
    ) -> Optional[ErrorInstance]:
        """
        Detect and classify an error.

        Args:
            error_message: The error message or stack trace
            tool_name: Name of the tool that generated the error
            context: Additional context information

        Returns:
            ErrorInstance if error is classified, None otherwise
        """
        if not error_message or not error_message.strip():
            return None

        context = context or {}
        error_lower = error_message.lower()

        # Find matching patterns
        best_match = None
        best_confidence = 0.0

        for pattern in self.patterns:
            if re.search(pattern.pattern, error_message, re.IGNORECASE):
                if pattern.confidence > best_confidence:
                    best_match = pattern
                    best_confidence = pattern.confidence

        if best_match:
            error_id = f"{tool_name}_{int(time.time() * 1000)}"

            error_instance = ErrorInstance(
                error_id=error_id,
                category=best_match.category,
                recovery_strategy=best_match.recovery_strategy,
                original_error=error_message,
                tool_name=tool_name,
                timestamp=time.time(),
                severity=best_match.severity,
            )

            # Store in history
            with self._lock:
                self.error_history[tool_name].append(error_instance)

            logger.info(
                f"Detected {best_match.category.value} for {tool_name}: {best_match.description}"
            )
            return error_instance

        # Fallback: create unknown error
        error_id = f"{tool_name}_unknown_{int(time.time() * 1000)}"
        unknown_error = ErrorInstance(
            error_id=error_id,
            category=ErrorCategory.UNKNOWN,
            recovery_strategy=RecoveryStrategy.RETRY,  # Default to retry
            original_error=error_message,
            tool_name=tool_name,
            timestamp=time.time(),
            severity=ErrorSeverity.MEDIUM,
        )

        with self._lock:
            self.error_history[tool_name].append(unknown_error)

        logger.warning(
            f"Unknown error pattern for {tool_name}: {error_message[:100]}..."
        )
        return unknown_error

    def classify_error_to_context(
        self, error_text: str, tool_name: str, execution_method: str = "unknown"
    ) -> ErrorContext:
        """
        Classify an error into ErrorContext for enhanced reporting.

        Args:
            error_text: The error message or traceback
            tool_name: Name of the tool that failed
            execution_method: Method used for execution

        Returns:
            ErrorContext with classification and metadata
        """
        error_lower = error_text.lower()

        # Check for specific error patterns
        for pattern in self.patterns:
            if re.search(pattern.pattern, error_text, re.IGNORECASE):
                return ErrorContext(
                    category=pattern.category,
                    severity=pattern.severity,
                    tool_name=tool_name,
                    error_code=pattern.description.upper().replace(" ", "_"),
                    original_error=error_text,
                    execution_method=execution_method,
                    metadata={
                        "matched_pattern": pattern.pattern,
                        "confidence": pattern.confidence,
                    },
                )

        # Default classification for unrecognized errors
        return ErrorContext(
            category=ErrorCategory.UNKNOWN,
            severity=ErrorSeverity.MEDIUM,
            tool_name=tool_name,
            error_code="UNKNOWN_ERROR",
            original_error=error_text,
            execution_method=execution_method,
        )

    def get_error_frequency(
        self, tool_name: str, category: ErrorCategory = None, time_window: int = 3600
    ) -> int:
        """
        Get error frequency for a tool within a time window.

        Args:
            tool_name: Name of the tool
            category: Optional error category filter
            time_window: Time window in seconds (default: 1 hour)

        Returns:
            Number of errors in the time window
        """
        current_time = time.time()
        cutoff_time = current_time - time_window

        with self._lock:
            errors = self.error_history.get(tool_name, [])

            count = 0
            for error in errors:
                if error.timestamp >= cutoff_time:
                    if category is None or error.category == category:
                        count += 1

            return count

    def get_recovery_success_rate(
        self, tool_name: str, recovery_strategy: RecoveryStrategy
    ) -> float:
        """
        Get success rate for a specific recovery strategy.

        Args:
            tool_name: Name of the tool
            recovery_strategy: Recovery strategy to analyze

        Returns:
            Success rate (0.0 to 1.0)
        """
        with self._lock:
            errors = self.error_history.get(tool_name, [])

            total_attempts = 0
            successful_recoveries = 0

            for error in errors:
                if (
                    error.recovery_strategy == recovery_strategy
                    and error.recovery_attempts > 0
                ):
                    total_attempts += 1
                    if error.recovered:
                        successful_recoveries += 1

            if total_attempts == 0:
                return 0.5  # Neutral assumption

            return successful_recoveries / total_attempts

    def should_attempt_recovery(
        self, error: ErrorInstance, max_attempts: int = 3
    ) -> bool:
        """
        Determine if recovery should be attempted for an error.

        Args:
            error: ErrorInstance to evaluate
            max_attempts: Maximum number of recovery attempts

        Returns:
            Boolean indicating if recovery should be attempted
        """
        if error.recovery_strategy == RecoveryStrategy.NO_RECOVERY:
            return False

        if error.recovery_attempts >= max_attempts:
            return False

        # Check recent error frequency to avoid recovery loops
        recent_frequency = self.get_error_frequency(
            error.tool_name, error.category, 300
        )  # 5 minutes
        if recent_frequency > 5:  # Too many recent errors
            logger.warning(
                f"High error frequency for {error.tool_name}, skipping recovery"
            )
            return False

        return True


class ErrorMessageGenerator:
    """Generates user-friendly error messages with actionable guidance."""

    def __init__(self):
        self.message_templates = self._initialize_message_templates()
        self.recovery_templates = self._initialize_recovery_templates()

    def generate_error_report(self, context: ErrorContext) -> ErrorReport:
        """
        Generate comprehensive error report with recovery suggestions.

        Args:
            context: ErrorContext with error classification

        Returns:
            ErrorReport with user-friendly messages and recovery actions
        """
        # Generate user-friendly message
        user_message = self._generate_user_message(context)

        # Generate technical details
        technical_details = self._generate_technical_details(context)

        # Generate recovery actions
        recovery_actions = self._generate_recovery_actions(context)

        # Get similar errors and documentation
        similar_errors = self._get_similar_errors(context)
        documentation_links = self._get_documentation_links(context)

        return ErrorReport(
            context=context,
            user_message=user_message,
            technical_details=technical_details,
            recovery_actions=recovery_actions,
            similar_errors=similar_errors,
            documentation_links=documentation_links,
        )

    def _generate_user_message(self, context: ErrorContext) -> str:
        """Generate user-friendly error message."""
        template_key = f"{context.category.value}_{context.error_code}"

        if template_key in self.message_templates:
            template = self.message_templates[template_key]
        else:
            # Fallback to category-level template
            template = self.message_templates.get(
                context.category.value, self.message_templates["default"]
            )

        # Replace placeholders in template
        message = template.format(
            tool_name=context.tool_name,
            error_code=context.error_code,
            execution_method=context.execution_method,
            severity=context.severity.value.upper(),
        )

        return message

    def _generate_technical_details(self, context: ErrorContext) -> str:
        """Generate technical error details."""
        details = [
            f"Tool: {context.tool_name}",
            f"Error Category: {context.category.value}",
            f"Error Code: {context.error_code}",
            f"Severity: {context.severity.value}",
            f"Execution Method: {context.execution_method}",
            "",
            "Original Error:",
            context.original_error,
        ]

        if context.environment_info:
            details.extend(
                [
                    "",
                    "Environment Information:",
                    json.dumps(context.environment_info, indent=2),
                ]
            )

        if context.attempted_solutions:
            details.extend(
                [
                    "",
                    "Previously Attempted Solutions:",
                    "\n".join(
                        f"- {solution}" for solution in context.attempted_solutions
                    ),
                ]
            )

        return "\n".join(details)

    def _generate_recovery_actions(self, context: ErrorContext) -> List[RecoveryAction]:
        """Generate list of recovery actions."""
        actions = []

        # Get category-specific recovery actions
        category_key = context.category.value
        if category_key in self.recovery_templates:
            for action_template in self.recovery_templates[category_key]:
                actions.append(
                    RecoveryAction(
                        action_type=action_template["type"],
                        description=action_template["description"].format(
                            tool_name=context.tool_name, error_code=context.error_code
                        ),
                        priority=action_template["priority"],
                        auto_executable=action_template.get("auto_executable", False),
                        command=action_template.get("command"),
                        expected_outcome=action_template.get("expected_outcome"),
                    )
                )

        # Get tool-specific recovery actions
        tool_key = f"tool_{context.tool_name}"
        if tool_key in self.recovery_templates:
            for action_template in self.recovery_templates[tool_key]:
                actions.append(
                    RecoveryAction(
                        action_type=action_template["type"],
                        description=action_template["description"],
                        priority=action_template["priority"],
                        auto_executable=action_template.get("auto_executable", False),
                        command=action_template.get("command"),
                        expected_outcome=action_template.get("expected_outcome"),
                    )
                )

        # Add generic recovery actions if no specific ones found
        if not actions:
            actions.extend(
                [
                    RecoveryAction(
                        action_type="retry",
                        description="Try executing the tool again with the same parameters",
                        priority=1,
                    ),
                    RecoveryAction(
                        action_type="alternative_method",
                        description="Try using a different execution method",
                        priority=2,
                    ),
                    RecoveryAction(
                        action_type="support",
                        description="Contact support with error details if the problem persists",
                        priority=10,
                    ),
                ]
            )

        # Sort by priority
        actions.sort(key=lambda x: x.priority)
        return actions

    def _get_similar_errors(self, context: ErrorContext) -> List[str]:
        """Get similar error patterns for context."""
        similar = []

        if context.category == ErrorCategory.TIMEOUT:
            similar.extend(
                [
                    "Network timeouts when accessing external databases",
                    "Subprocess timeouts during long-running computations",
                    "API rate limiting causing timeout responses",
                ]
            )
        elif context.category == ErrorCategory.DEPENDENCY:
            similar.extend(
                [
                    "Missing Python packages in virtual environment",
                    "Incorrect environment activation",
                    "Version incompatibilities between packages",
                ]
            )
        elif context.category == ErrorCategory.NETWORK:
            similar.extend(
                [
                    "DNS resolution failures",
                    "Firewall blocking external connections",
                    "SSL certificate validation errors",
                ]
            )

        return similar

    def _get_documentation_links(self, context: ErrorContext) -> List[str]:
        """Get relevant documentation links."""
        links = [
            "https://github.com/your-org/biomni-toolkit/wiki/troubleshooting",
            "https://github.com/your-org/biomni-toolkit/wiki/error-reference",
        ]

        if context.category == ErrorCategory.ENVIRONMENT:
            links.append(
                "https://github.com/your-org/biomni-toolkit/wiki/environment-setup"
            )
        elif context.category == ErrorCategory.DEPENDENCY:
            links.append("https://github.com/your-org/biomni-toolkit/wiki/dependencies")

        return links

    def _initialize_message_templates(self) -> Dict[str, str]:
        """Initialize user-friendly message templates."""
        return {
            "timeout": (
                "🕐 The {tool_name} tool timed out during execution. "
                "This usually happens when the operation takes longer than expected "
                "or when there are network connectivity issues."
            ),
            "dependency": (
                "📦 The {tool_name} tool is missing required dependencies. "
                "Some Python packages or system tools may not be installed "
                "in the current environment."
            ),
            "permission": (
                "🔒 The {tool_name} tool encountered permission issues. "
                "The system may be blocking access to required files or resources."
            ),
            "network": (
                "🌐 The {tool_name} tool cannot connect to external services. "
                "This could be due to network connectivity issues or service unavailability."
            ),
            "environment": (
                "🔧 The {tool_name} tool encountered environment configuration issues. "
                "The required conda environment or Python installation may not be properly set up."
            ),
            "input_validation": (
                "⚠️  The {tool_name} tool received invalid input parameters. "
                "Please check the provided arguments and try again."
            ),
            "tool_specific": (
                "🧬 The {tool_name} tool encountered a specific error ({error_code}). "
                "This is related to the tool's specialized functionality."
            ),
            "system_resource": (
                "💾 The {tool_name} tool ran out of system resources. "
                "The system may be low on memory or disk space."
            ),
            "authentication": (
                "🔑 The {tool_name} tool failed authentication. "
                "API keys or credentials may be missing or invalid."
            ),
            "connection_error": (
                "🔌 The {tool_name} tool lost its connection. "
                "MCP server connection may have been interrupted."
            ),
            "data_error": (
                "📄 The {tool_name} tool encountered data issues. "
                "Required files may be missing or corrupted."
            ),
            "default": (
                "❌ The {tool_name} tool encountered an unexpected error ({error_code}). "
                "Please check the error details and try again."
            ),
        }

    def _initialize_recovery_templates(self) -> Dict[str, List[Dict[str, Any]]]:
        """Initialize recovery action templates."""
        return {
            "timeout": [
                {
                    "type": "retry_with_timeout",
                    "description": "Retry with increased timeout (60 seconds)",
                    "priority": 1,
                    "auto_executable": True,
                },
                {
                    "type": "check_network",
                    "description": "Check network connectivity to external services",
                    "priority": 2,
                },
                {
                    "type": "reduce_scope",
                    "description": (
                        "Try reducing the scope of the query (fewer results, smaller dataset)"
                    ),
                    "priority": 3,
                },
            ],
            "dependency": [
                {
                    "type": "install_dependencies",
                    "description": (
                        "Install missing Python packages in biomni_e1 environment"
                    ),
                    "priority": 1,
                    "auto_executable": True,
                    "command": (
                        "conda activate biomni_e1 && pip install -r requirements.txt"
                    ),
                },
                {
                    "type": "check_environment",
                    "description": "Verify conda environment is properly activated",
                    "priority": 2,
                    "auto_executable": True,
                },
                {
                    "type": "recreate_environment",
                    "description": (
                        "Recreate the biomni_e1 conda environment from scratch"
                    ),
                    "priority": 8,
                },
            ],
            "permission": [
                {
                    "type": "check_permissions",
                    "description": "Check file and directory permissions",
                    "priority": 1,
                },
                {
                    "type": "run_as_admin",
                    "description": (
                        "Try running with elevated privileges (if appropriate)"
                    ),
                    "priority": 5,
                },
            ],
            "network": [
                {
                    "type": "check_connectivity",
                    "description": "Test internet connectivity and DNS resolution",
                    "priority": 1,
                },
                {
                    "type": "retry_later",
                    "description": (
                        "Retry in a few minutes - the service may be temporarily unavailable"
                    ),
                    "priority": 2,
                },
                {
                    "type": "use_cached_data",
                    "description": "Use cached data if available",
                    "priority": 3,
                },
            ],
            "environment": [
                {
                    "type": "validate_environment",
                    "description": "Run environment validation checks",
                    "priority": 1,
                    "auto_executable": True,
                },
                {
                    "type": "activate_environment",
                    "description": "Ensure biomni_e1 conda environment is activated",
                    "priority": 2,
                    "auto_executable": True,
                    "command": "conda activate biomni_e1",
                },
                {
                    "type": "check_python_path",
                    "description": "Verify Python executable path is correct",
                    "priority": 3,
                },
            ],
            "tool_query_uniprot": [
                {
                    "type": "validate_uniprot_id",
                    "description": (
                        "Verify the UniProt ID format (e.g., P12345, P12345-1)"
                    ),
                    "priority": 1,
                },
                {
                    "type": "try_alternative_endpoint",
                    "description": "Try using a different UniProt API endpoint",
                    "priority": 2,
                },
                {
                    "type": "check_uniprot_status",
                    "description": (
                        "Check UniProt service status at https://www.uniprot.org/"
                    ),
                    "priority": 3,
                },
            ],
        }


class EnvironmentValidator:
    """Validates environment and dependency health."""

    def __init__(self):
        self.biomni_python_path = (
            "/Users/<USER>/miniforge3/envs/biomni_e1/bin/python3"
        )
        self.conda_env_name = "biomni_e1"
        self.required_packages = [
            "pandas",
            "numpy",
            "requests",
            "biopython",
            "matplotlib",
        ]

    def validate_environment(self) -> Dict[str, Any]:
        """
        Perform comprehensive environment validation.

        Returns:
            Dictionary with validation results and recommendations
        """
        results = {
            "overall_status": "unknown",
            "python_environment": {},
            "conda_environment": {},
            "package_dependencies": {},
            "network_connectivity": {},
            "recommendations": [],
        }

        # Validate Python environment
        results["python_environment"] = self._validate_python_environment()

        # Validate conda environment
        results["conda_environment"] = self._validate_conda_environment()

        # Validate package dependencies
        results["package_dependencies"] = self._validate_package_dependencies()

        # Validate network connectivity
        results["network_connectivity"] = self._validate_network_connectivity()

        # Determine overall status
        results["overall_status"] = self._determine_overall_status(results)

        # Generate recommendations
        results["recommendations"] = self._generate_recommendations(results)

        return results

    def _validate_python_environment(self) -> Dict[str, Any]:
        """Validate Python environment."""
        result = {
            "status": "unknown",
            "python_path": self.biomni_python_path,
            "exists": False,
            "executable": False,
            "version": None,
            "error": None,
        }

        try:
            # Check if Python executable exists
            if os.path.exists(self.biomni_python_path):
                result["exists"] = True

                # Test if executable
                proc = subprocess.run(
                    [self.biomni_python_path, "--version"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                )

                if proc.returncode == 0:
                    result["executable"] = True
                    result["version"] = proc.stdout.strip()
                    result["status"] = "healthy"
                else:
                    result["error"] = proc.stderr
                    result["status"] = "error"
            else:
                result["status"] = "missing"
                result["error"] = (
                    f"Python executable not found at {self.biomni_python_path}"
                )

        except subprocess.TimeoutExpired:
            result["error"] = "Python version check timed out"
            result["status"] = "timeout"
        except Exception as e:
            result["error"] = str(e)
            result["status"] = "error"

        return result

    def _validate_conda_environment(self) -> Dict[str, Any]:
        """Validate conda environment."""
        result = {
            "status": "unknown",
            "environment_name": self.conda_env_name,
            "exists": False,
            "activated": False,
            "packages_count": 0,
            "error": None,
        }

        try:
            # Check if conda environment exists
            proc = subprocess.run(
                ["conda", "env", "list"], capture_output=True, text=True, timeout=15
            )

            if proc.returncode == 0:
                env_list = proc.stdout
                if self.conda_env_name in env_list:
                    result["exists"] = True

                    # Try to get package list from environment
                    try:
                        pkg_proc = subprocess.run(
                            ["conda", "list", "-n", self.conda_env_name],
                            capture_output=True,
                            text=True,
                            timeout=15,
                        )

                        if pkg_proc.returncode == 0:
                            # Count packages (excluding header lines)
                            pkg_lines = [
                                line
                                for line in pkg_proc.stdout.split("\n")
                                if line.strip() and not line.startswith("#")
                            ]
                            result["packages_count"] = len(pkg_lines)
                            result["status"] = "healthy"
                        else:
                            result["error"] = (
                                f"Could not list packages: {pkg_proc.stderr}"
                            )
                            result["status"] = "warning"

                    except subprocess.TimeoutExpired:
                        result["error"] = "Package listing timed out"
                        result["status"] = "timeout"
                else:
                    result["status"] = "missing"
                    result["error"] = (
                        f"Conda environment '{self.conda_env_name}' not found"
                    )
            else:
                result["error"] = f"Conda command failed: {proc.stderr}"
                result["status"] = "error"

        except subprocess.TimeoutExpired:
            result["error"] = "Conda environment check timed out"
            result["status"] = "timeout"
        except FileNotFoundError:
            result["error"] = "Conda command not found - conda may not be installed"
            result["status"] = "missing"
        except Exception as e:
            result["error"] = str(e)
            result["status"] = "error"

        return result

    def _validate_package_dependencies(self) -> Dict[str, Any]:
        """Validate required package dependencies."""
        result = {
            "status": "unknown",
            "required_packages": self.required_packages,
            "installed_packages": {},
            "missing_packages": [],
            "error": None,
        }

        try:
            # Check each required package
            for package in self.required_packages:
                try:
                    proc = subprocess.run(
                        [
                            self.biomni_python_path,
                            "-c",
                            f"import {package}; print({package}.__version__)",
                        ],
                        capture_output=True,
                        text=True,
                        timeout=5,
                    )

                    if proc.returncode == 0:
                        version = proc.stdout.strip()
                        result["installed_packages"][package] = {
                            "status": "installed",
                            "version": version,
                        }
                    else:
                        result["installed_packages"][package] = {
                            "status": "missing",
                            "error": proc.stderr,
                        }
                        result["missing_packages"].append(package)

                except subprocess.TimeoutExpired:
                    result["installed_packages"][package] = {
                        "status": "timeout",
                        "error": "Import check timed out",
                    }

            # Determine overall package status
            if not result["missing_packages"]:
                result["status"] = "healthy"
            elif len(result["missing_packages"]) < len(self.required_packages) / 2:
                result["status"] = "warning"
            else:
                result["status"] = "critical"

        except Exception as e:
            result["error"] = str(e)
            result["status"] = "error"

        return result

    def _validate_network_connectivity(self) -> Dict[str, Any]:
        """Validate network connectivity to common bioinformatics services."""
        result = {
            "status": "unknown",
            "services": {},
            "overall_connectivity": False,
            "error": None,
        }

        test_services = [
            ("uniprot.org", "UniProt"),
            ("alphafold.ebi.ac.uk", "AlphaFold"),
            ("rcsb.org", "Protein Data Bank"),
            ("google.com", "General Internet"),
        ]

        successful_connections = 0

        for host, service_name in test_services:
            try:
                proc = subprocess.run(
                    ["ping", "-c", "1", "-W", "5000", host],
                    capture_output=True,
                    text=True,
                    timeout=10,
                )

                if proc.returncode == 0:
                    result["services"][service_name] = {
                        "status": "reachable",
                        "host": host,
                    }
                    successful_connections += 1
                else:
                    result["services"][service_name] = {
                        "status": "unreachable",
                        "host": host,
                        "error": "Ping failed",
                    }

            except subprocess.TimeoutExpired:
                result["services"][service_name] = {
                    "status": "timeout",
                    "host": host,
                    "error": "Ping timed out",
                }
            except Exception as e:
                result["services"][service_name] = {
                    "status": "error",
                    "host": host,
                    "error": str(e),
                }

        # Determine overall connectivity status
        if successful_connections == len(test_services):
            result["status"] = "healthy"
            result["overall_connectivity"] = True
        elif successful_connections > 0:
            result["status"] = "partial"
            result["overall_connectivity"] = True
        else:
            result["status"] = "failed"
            result["overall_connectivity"] = False

        return result

    def _determine_overall_status(self, results: Dict[str, Any]) -> str:
        """Determine overall environment status."""
        python_status = results["python_environment"]["status"]
        conda_status = results["conda_environment"]["status"]
        package_status = results["package_dependencies"]["status"]
        network_status = results["network_connectivity"]["status"]

        # Critical issues
        if python_status in ["missing", "error"]:
            return "critical"
        if conda_status == "missing":
            return "critical"
        if package_status == "critical":
            return "critical"

        # Warning issues
        if any(
            status in ["warning", "timeout"]
            for status in [python_status, conda_status, package_status, network_status]
        ):
            return "warning"

        # All healthy
        if all(
            status == "healthy"
            for status in [python_status, conda_status, package_status, network_status]
        ):
            return "healthy"

        return "unknown"

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []

        python_result = results["python_environment"]
        conda_result = results["conda_environment"]
        package_result = results["package_dependencies"]
        network_result = results["network_connectivity"]

        # Python environment recommendations
        if python_result["status"] == "missing":
            recommendations.append(
                "Install Python 3.8+ and create biomni_e1 conda environment"
            )
        elif python_result["status"] == "error":
            recommendations.append(
                "Fix Python installation issues in biomni_e1 environment"
            )

        # Conda environment recommendations
        if conda_result["status"] == "missing":
            recommendations.append(
                f"Create conda environment: conda create -n {self.conda_env_name} python=3.9"
            )
        elif conda_result["packages_count"] < 10:
            recommendations.append("Install required packages in conda environment")

        # Package dependency recommendations
        if package_result["missing_packages"]:
            missing = ", ".join(package_result["missing_packages"])
            recommendations.append(f"Install missing packages: pip install {missing}")

        # Network connectivity recommendations
        if network_result["status"] == "failed":
            recommendations.append("Check internet connection and firewall settings")
        elif network_result["status"] == "partial":
            recommendations.append(
                "Some bioinformatics services are unreachable - check service status"
            )

        return recommendations


class ErrorRecovery:
    """
    Error recovery implementation with multiple strategies.

    Provides concrete recovery actions for different error categories,
    with success tracking and adaptive behavior.
    """

    def __init__(self, error_detector: ErrorDetector):
        """
        Initialize error recovery system.

        Args:
            error_detector: ErrorDetector instance for error analysis
        """
        self.error_detector = error_detector
        self.recovery_functions: Dict[RecoveryStrategy, Callable] = {
            RecoveryStrategy.RETRY: self._simple_retry,
            RecoveryStrategy.FALLBACK_EXECUTION: self._fallback_execution,
            RecoveryStrategy.ENVIRONMENT_RESET: self._environment_reset,
            RecoveryStrategy.DEPENDENCY_INSTALL: self._dependency_install,
            RecoveryStrategy.PERMISSION_FIX: self._permission_fix,
            RecoveryStrategy.CACHE_CLEAR: self._cache_clear,
            RecoveryStrategy.CONNECTION_RESET: self._connection_reset,
        }

        # Recovery state tracking
        self.recovery_stats = defaultdict(lambda: {"attempts": 0, "successes": 0})
        self._recovery_lock = threading.RLock()

    async def attempt_recovery(
        self, error: ErrorInstance, original_function: Callable, *args, **kwargs
    ) -> Tuple[bool, Any]:
        """
        Attempt to recover from an error and re-execute the function.

        Args:
            error: ErrorInstance to recover from
            original_function: Function to re-execute after recovery
            *args: Arguments for the original function
            **kwargs: Keyword arguments for the original function

        Returns:
            Tuple of (success, result)
        """
        if not self.error_detector.should_attempt_recovery(error):
            logger.info(f"Skipping recovery for {error.error_id}")
            return False, None

        recovery_function = self.recovery_functions.get(error.recovery_strategy)
        if not recovery_function:
            logger.warning(
                f"No recovery function for strategy {error.recovery_strategy}"
            )
            return False, None

        error.recovery_attempts += 1

        with self._recovery_lock:
            self.recovery_stats[error.recovery_strategy]["attempts"] += 1

        logger.info(
            f"Attempting recovery for {error.error_id} using {error.recovery_strategy.value}"
        )

        try:
            # Execute recovery action
            recovery_success = await recovery_function(error, *args, **kwargs)

            if recovery_success:
                # Recovery action succeeded, try original function
                try:
                    if hasattr(original_function, "__call__"):
                        result = await original_function(*args, **kwargs)
                        error.recovered = True

                        with self._recovery_lock:
                            self.recovery_stats[error.recovery_strategy][
                                "successes"
                            ] += 1

                        logger.info(f"Recovery successful for {error.error_id}")
                        return True, result

                except Exception as retry_error:
                    logger.warning(
                        f"Function re-execution failed after recovery: {retry_error}"
                    )
                    return False, None
            else:
                logger.warning(f"Recovery action failed for {error.error_id}")
                return False, None

        except Exception as recovery_error:
            logger.error(
                f"Recovery attempt failed for {error.error_id}: {recovery_error}"
            )
            return False, None

    async def _simple_retry(self, error: ErrorInstance, *args, **kwargs) -> bool:
        """
        Simple retry with exponential backoff.

        Args:
            error: ErrorInstance being recovered

        Returns:
            Boolean indicating if retry should proceed
        """
        # Calculate backoff delay
        delay = min(2**error.recovery_attempts, 30)  # Max 30 seconds

        logger.debug(f"Retrying {error.tool_name} after {delay}s delay")
        await asyncio.sleep(delay)

        return True  # Always allow retry

    async def _fallback_execution(self, error: ErrorInstance, *args, **kwargs) -> bool:
        """
        Switch to fallback execution method.

        Args:
            error: ErrorInstance being recovered

        Returns:
            Boolean indicating if fallback is available
        """
        # This would typically involve switching from desktop-commander to direct subprocess
        # or vice versa. Implementation depends on the calling context.

        logger.info(f"Switching to fallback execution for {error.tool_name}")

        # Set execution strategy override
        if hasattr(kwargs, "execution_strategy"):
            if kwargs["execution_strategy"] == "desktop_commander":
                kwargs["execution_strategy"] = "direct_subprocess"
            else:
                kwargs["execution_strategy"] = "desktop_commander"

        return True

    async def _environment_reset(self, error: ErrorInstance, *args, **kwargs) -> bool:
        """
        Reset environment paths and configurations.

        Args:
            error: ErrorInstance being recovered

        Returns:
            Boolean indicating if reset was successful
        """
        logger.info(f"Resetting environment for {error.tool_name}")

        # Clear environment-related caches
        try:
            from .connection_pool import get_connection_pool

            pool = get_connection_pool()
            pool.invalidate_server_connections("desktop-commander", "Environment reset")

            return True

        except Exception as e:
            logger.error(f"Environment reset failed: {e}")
            return False

    async def _dependency_install(self, error: ErrorInstance, *args, **kwargs) -> bool:
        """
        Attempt to install missing dependencies.

        Args:
            error: ErrorInstance being recovered

        Returns:
            Boolean indicating if installation was attempted
        """
        logger.info(f"Attempting dependency installation for {error.tool_name}")

        # Extract package name from error message
        package_patterns = [
            r"No module named ['\"]([^'\"]+)['\"]",
            r"ModuleNotFoundError.*['\"]([^'\"]+)['\"]",
            r"there is no package called ['\"]([^'\"]+)['\"]",
        ]

        package_name = None
        for pattern in package_patterns:
            match = re.search(pattern, error.original_error, re.IGNORECASE)
            if match:
                package_name = match.group(1)
                break

        if package_name:
            logger.info(f"Identified missing package: {package_name}")
            # In a real implementation, this would trigger package installation
            # For now, we'll just log and return True to allow retry
            return True

        logger.warning("Could not identify missing package from error message")
        return False

    async def _permission_fix(self, error: ErrorInstance, *args, **kwargs) -> bool:
        """
        Attempt to fix permission issues.

        Args:
            error: ErrorInstance being recovered

        Returns:
            Boolean indicating if permission fix was attempted
        """
        logger.info(f"Attempting permission fix for {error.tool_name}")

        # In a real implementation, this might involve changing file permissions
        # or creating directories. For now, we'll just return True to allow retry.
        return True

    async def _cache_clear(self, error: ErrorInstance, *args, **kwargs) -> bool:
        """
        Clear caches to free up resources.

        Args:
            error: ErrorInstance being recovered

        Returns:
            Boolean indicating if cache clear was successful
        """
        logger.info(f"Clearing caches for {error.tool_name}")

        try:
            # Clear various caches
            from .mcp_tools import _tool_manager

            _tool_manager.clear_cache()

            # Clear biomni tools cache
            from .biomni_tools import _biomni_tools_cache

            _biomni_tools_cache.clear()

            return True

        except Exception as e:
            logger.error(f"Cache clear failed: {e}")
            return False

    async def _connection_reset(self, error: ErrorInstance, *args, **kwargs) -> bool:
        """
        Reset MCP connections.

        Args:
            error: ErrorInstance being recovered

        Returns:
            Boolean indicating if connection reset was successful
        """
        logger.info(f"Resetting connections for {error.tool_name}")

        try:
            from .connection_pool import get_connection_pool

            pool = get_connection_pool()
            pool.clear_all_connections()

            # Also clear MCP tool manager connections
            from .mcp_tools import _tool_manager

            _tool_manager.close_connections()

            return True

        except Exception as e:
            logger.error(f"Connection reset failed: {e}")
            return False

    def get_recovery_stats(self) -> Dict[str, Dict[str, int]]:
        """
        Get recovery statistics.

        Returns:
            Dictionary with recovery statistics by strategy
        """
        with self._recovery_lock:
            return dict(self.recovery_stats)


# Global instances
error_detector = ErrorDetector()
error_recovery = ErrorRecovery(error_detector)
message_generator = ErrorMessageGenerator()
environment_validator = EnvironmentValidator()


def get_error_detector() -> ErrorDetector:
    """Get the global error detector instance."""
    return error_detector


def get_error_recovery() -> ErrorRecovery:
    """Get the global error recovery instance."""
    return error_recovery


def classify_and_report_error(
    error_text: str, tool_name: str, execution_method: str = "unknown"
) -> ErrorReport:
    """
    Classify an error and generate a comprehensive error report.

    Args:
        error_text: The error message or traceback
        tool_name: Name of the tool that failed
        execution_method: Method used for execution

    Returns:
        ErrorReport with classification and recovery suggestions
    """
    context = error_detector.classify_error_to_context(
        error_text, tool_name, execution_method
    )
    return message_generator.generate_error_report(context)


def validate_environment() -> Dict[str, Any]:
    """
    Validate the current environment and return comprehensive status.

    Returns:
        Dictionary with validation results and recommendations
    """
    return environment_validator.validate_environment()


def format_error_for_user(error_report: ErrorReport) -> str:
    """
    Format error report for display to user.

    Args:
        error_report: ErrorReport to format

    Returns:
        Formatted string for user display
    """
    sections = [
        f"## {error_report.context.severity.value.upper()} Error in {error_report.context.tool_name}",
        "",
        error_report.user_message,
        "",
        "### Recommended Actions:",
    ]

    for i, action in enumerate(error_report.recovery_actions[:3], 1):
        sections.append(
            f"{i}. **{action.action_type.replace('_', ' ').title()}:** {action.description}"
        )

    if error_report.similar_errors:
        sections.extend(
            [
                "",
                "### Similar Issues:",
                "\n".join(f"• {error}" for error in error_report.similar_errors[:2]),
            ]
        )

    return "\n".join(sections)


def format_error_for_json(error_report: ErrorReport) -> str:
    """
    Format error report as JSON for API responses.

    Args:
        error_report: ErrorReport to format

    Returns:
        JSON string with error information
    """
    return json.dumps(
        {
            "success": False,
            "error": {
                "category": error_report.context.category.value,
                "severity": error_report.context.severity.value,
                "code": error_report.context.error_code,
                "message": error_report.user_message,
                "tool": error_report.context.tool_name,
                "execution_method": error_report.context.execution_method,
                "recovery_actions": [
                    {
                        "type": action.action_type,
                        "description": action.description,
                        "priority": action.priority,
                        "auto_executable": action.auto_executable,
                    }
                    for action in error_report.recovery_actions[:5]
                ],
                "technical_details": error_report.technical_details,
            },
        },
        indent=2,
    )
