"""
Fallback execution paths and environment resolution for cross-environment execution.

This module provides intelligent fallback strategies and conda environment
activation fixes to improve tool execution reliability.
"""

import os
import asyncio
import logging
import subprocess
import json
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass

from .execution_strategies import (
    get_direct_executor,
    get_mcp_executor,
    get_retry_executor,
)
from ..config.execution_config import get_execution_config, ExecutionStrategy

logger = logging.getLogger(__name__)


@dataclass
class EnvironmentConfig:
    """Configuration for execution environment."""

    python_path: str
    conda_env: str
    environment_vars: Dict[str, str]
    working_directory: str


class EnvironmentResolver:
    """
    Resolves conda environment paths and activation issues.

    Provides intelligent environment detection and path resolution
    to fix common conda activation problems.
    """

    def __init__(self):
        self.config = get_execution_config()
        self._cached_environments = {}

    def resolve_biomni_environment(self) -> EnvironmentConfig:
        """
        Resolve biomni environment configuration with fallbacks.

        Returns:
            EnvironmentConfig with resolved paths
        """
        # Primary biomni environment path
        primary_python = "/Users/<USER>/miniforge3/envs/biomni_e1/bin/python3"
        primary_conda_env = "biomni_e1"

        # Check if primary environment exists and is functional
        if self._validate_python_environment(primary_python):
            logger.debug("Using primary biomni environment")
            return EnvironmentConfig(
                python_path=primary_python,
                conda_env=primary_conda_env,
                environment_vars=self._get_biomni_env_vars(),
                working_directory="/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
            )

        # Fallback environment paths
        fallback_paths = [
            "/Users/<USER>/miniforge3/envs/biomni_e1/bin/python",
            "/Users/<USER>/miniforge3/bin/python3",
            "/Users/<USER>/miniforge3/bin/python",
            "/opt/homebrew/bin/python3",
            "/usr/local/bin/python3",
            "/usr/bin/python3",
        ]

        for python_path in fallback_paths:
            if self._validate_python_environment(python_path):
                logger.warning(f"Using fallback Python environment: {python_path}")
                return EnvironmentConfig(
                    python_path=python_path,
                    conda_env="fallback",
                    environment_vars=self._get_fallback_env_vars(),
                    working_directory="/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
                )

        # Last resort: system python
        logger.error("No suitable Python environment found, using system python")
        return EnvironmentConfig(
            python_path="python3",
            conda_env="system",
            environment_vars=self._get_minimal_env_vars(),
            working_directory="/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph",
        )

    def _validate_python_environment(self, python_path: str) -> bool:
        """
        Validate that a Python environment is functional.

        Args:
            python_path: Path to Python executable

        Returns:
            Boolean indicating if environment is valid
        """
        if python_path in self._cached_environments:
            return self._cached_environments[python_path]

        try:
            # Check if Python executable exists
            if not os.path.exists(python_path):
                self._cached_environments[python_path] = False
                return False

            # Test basic Python functionality
            result = subprocess.run(
                [python_path, "-c", "import sys; print(sys.version_info[:2])"],
                capture_output=True,
                text=True,
                timeout=10,
            )

            if result.returncode == 0:
                version_info = result.stdout.strip()
                logger.debug(
                    f"Python environment {python_path} version: {version_info}"
                )
                self._cached_environments[python_path] = True
                return True
            else:
                logger.debug(
                    f"Python environment {python_path} validation failed: {result.stderr}"
                )
                self._cached_environments[python_path] = False
                return False

        except Exception as e:
            logger.debug(f"Python environment {python_path} validation error: {e}")
            self._cached_environments[python_path] = False
            return False

    def _get_biomni_env_vars(self) -> Dict[str, str]:
        """Get environment variables for biomni environment."""
        base_env = os.environ.copy()

        # Add biomni-specific variables
        biomni_vars = {
            "CONDA_DEFAULT_ENV": "biomni_e1",
            "CONDA_PREFIX": "/Users/<USER>/miniforge3/envs/biomni_e1",
            "PYTHONPATH": (
                "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
            ),
            "REASONING_PROVIDER": os.environ.get("REASONING_PROVIDER", "gemini"),
            "REASONING_API_KEY": os.environ.get("REASONING_API_KEY", ""),
            "REASONING_BASE_URL": os.environ.get("REASONING_BASE_URL", ""),
            "REASONING_MODEL": os.environ.get("REASONING_MODEL", "gemini-2.5-pro"),
        }

        base_env.update(biomni_vars)
        return base_env

    def _get_fallback_env_vars(self) -> Dict[str, str]:
        """Get environment variables for fallback environment."""
        base_env = os.environ.copy()

        fallback_vars = {
            "PYTHONPATH": (
                "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
            ),
            "REASONING_PROVIDER": os.environ.get("REASONING_PROVIDER", "gemini"),
            "REASONING_API_KEY": os.environ.get("REASONING_API_KEY", ""),
            "REASONING_BASE_URL": os.environ.get("REASONING_BASE_URL", ""),
            "REASONING_MODEL": os.environ.get("REASONING_MODEL", "gemini-2.5-pro"),
        }

        base_env.update(fallback_vars)
        return base_env

    def _get_minimal_env_vars(self) -> Dict[str, str]:
        """Get minimal environment variables for system python."""
        return {
            "PYTHONPATH": (
                "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
            ),
            "REASONING_PROVIDER": os.environ.get("REASONING_PROVIDER", "gemini"),
            "REASONING_API_KEY": os.environ.get("REASONING_API_KEY", ""),
            "REASONING_MODEL": os.environ.get("REASONING_MODEL", "gemini-2.5-pro"),
        }


class FallbackExecutionManager:
    """
    Manages fallback execution strategies with intelligent strategy selection.

    Provides hierarchical fallback execution with performance-based strategy
    selection and adaptive behavior based on success rates.
    """

    def __init__(self):
        self.config = get_execution_config()
        self.env_resolver = EnvironmentResolver()
        self.retry_executor = get_retry_executor()
        self.direct_executor = get_direct_executor()
        self.mcp_executor = get_mcp_executor()

        # Strategy performance tracking
        self.strategy_stats = {
            "hybrid": {"attempts": 0, "successes": 0, "avg_time": 0.0},
            "direct_subprocess": {"attempts": 0, "successes": 0, "avg_time": 0.0},
            "desktop_commander": {"attempts": 0, "successes": 0, "avg_time": 0.0},
            "auto": {"attempts": 0, "successes": 0, "avg_time": 0.0},
        }

    async def execute_with_fallback(
        self, tool_name: str, command: Optional[str] = None, **kwargs
    ) -> str:
        """
        Execute tool with intelligent fallback strategy selection.

        Args:
            tool_name: Name of the tool to execute
            command: Optional command string for context
            **kwargs: Tool arguments

        Returns:
            JSON string with execution result
        """
        # Determine optimal execution strategy
        strategy = self._select_execution_strategy(tool_name)

        logger.info(f"Executing {tool_name} with strategy: {strategy.value}")

        # Strategy execution mapping - cleaner than if/elif chain
        strategy_methods = {
            ExecutionStrategy.HYBRID: self._execute_hybrid_strategy,
            ExecutionStrategy.DIRECT_SUBPROCESS: self._execute_direct_strategy,
            ExecutionStrategy.DESKTOP_COMMANDER: self._execute_mcp_strategy,
            ExecutionStrategy.AUTO: self._execute_auto_strategy,
        }

        # Execute with the selected strategy, default to hybrid if unknown
        execute_method = strategy_methods.get(strategy, self._execute_hybrid_strategy)
        return await execute_method(tool_name, command, **kwargs)

    def _select_execution_strategy(self, tool_name: str) -> ExecutionStrategy:
        """
        Select optimal execution strategy based on tool characteristics and performance history.

        Args:
            tool_name: Name of the tool

        Returns:
            ExecutionStrategy enum value
        """
        # Get configured strategy preference
        base_strategy = self.config.get_execution_strategy(tool_name)

        # Override based on performance history
        if base_strategy == ExecutionStrategy.AUTO:
            return self._select_best_performing_strategy(tool_name)

        return base_strategy

    def _select_best_performing_strategy(self, tool_name: str) -> ExecutionStrategy:
        """
        Select strategy based on historical performance.

        Args:
            tool_name: Name of the tool

        Returns:
            Best performing ExecutionStrategy
        """
        # Calculate success rates
        best_strategy = ExecutionStrategy.HYBRID
        best_rate = 0.0

        for strategy_name, stats in self.strategy_stats.items():
            if stats["attempts"] > 0:
                success_rate = stats["successes"] / stats["attempts"]
                if success_rate > best_rate:
                    best_rate = success_rate
                    try:
                        best_strategy = ExecutionStrategy(strategy_name)
                    except ValueError:
                        # Handle case where strategy_name doesn't match enum
                        best_strategy = ExecutionStrategy.HYBRID

        # If no history, default to hybrid
        if best_rate == 0.0:
            best_strategy = ExecutionStrategy.HYBRID

        logger.debug(
            f"Selected strategy {best_strategy.value} for {tool_name} (success rate: {best_rate:.2f})"
        )
        return best_strategy

    async def _execute_hybrid_strategy(
        self, tool_name: str, command: Optional[str] = None, **kwargs
    ) -> str:
        """
        Execute using hybrid strategy (direct first, MCP fallback).

        Args:
            tool_name: Name of the tool
            command: Optional command string
            **kwargs: Tool arguments

        Returns:
            JSON string with execution result
        """
        start_time = asyncio.get_event_loop().time()

        try:
            self.strategy_stats["hybrid"]["attempts"] += 1

            # Try direct subprocess first
            logger.debug(f"Hybrid strategy: trying direct subprocess for {tool_name}")

            try:
                result = await self.direct_executor.execute_tool(
                    tool_name, command, **kwargs
                )

                # Check if result indicates success
                try:
                    result_data = json.loads(result)
                    if result_data.get("success"):
                        execution_time = asyncio.get_event_loop().time() - start_time
                        self._update_strategy_stats("hybrid", True, execution_time)
                        logger.info(
                            f"Hybrid strategy: direct subprocess succeeded for {tool_name}"
                        )
                        return result
                except json.JSONDecodeError:
                    pass

            except Exception as direct_error:
                logger.warning(
                    f"Hybrid strategy: direct subprocess failed for {tool_name}: {direct_error}"
                )

            # Fallback to desktop-commander
            logger.debug(
                f"Hybrid strategy: falling back to desktop-commander for {tool_name}"
            )

            try:
                result = await self.mcp_executor.execute_tool(
                    tool_name, command, **kwargs
                )

                # Check if result indicates success
                try:
                    result_data = json.loads(result)
                    if result_data.get("success"):
                        execution_time = asyncio.get_event_loop().time() - start_time
                        self._update_strategy_stats("hybrid", True, execution_time)
                        logger.info(
                            f"Hybrid strategy: desktop-commander succeeded for {tool_name}"
                        )
                        return result
                except json.JSONDecodeError:
                    pass

            except Exception as mcp_error:
                logger.error(
                    f"Hybrid strategy: desktop-commander also failed for {tool_name}: {mcp_error}"
                )

            # Both strategies failed
            execution_time = asyncio.get_event_loop().time() - start_time
            self._update_strategy_stats("hybrid", False, execution_time)

            return json.dumps(
                {
                    "success": False,
                    "error": (
                        "Both direct subprocess and desktop-commander execution failed"
                    ),
                    "tool": tool_name,
                    "execution_method": "hybrid_failed",
                }
            )

        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self._update_strategy_stats("hybrid", False, execution_time)

            logger.error(f"Hybrid strategy execution error for {tool_name}: {e}")
            return json.dumps(
                {
                    "success": False,
                    "error": f"Hybrid execution error: {str(e)}",
                    "tool": tool_name,
                }
            )

    async def _execute_direct_strategy(
        self, tool_name: str, command: Optional[str] = None, **kwargs
    ) -> str:
        """Execute using direct subprocess strategy."""
        start_time = asyncio.get_event_loop().time()

        try:
            self.strategy_stats["direct_subprocess"]["attempts"] += 1

            result = await self.direct_executor.execute_tool(
                tool_name, command, **kwargs
            )

            # Check success
            try:
                result_data = json.loads(result)
                success = result_data.get("success", False)
            except json.JSONDecodeError:
                success = False

            execution_time = asyncio.get_event_loop().time() - start_time
            self._update_strategy_stats("direct_subprocess", success, execution_time)

            return result

        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self._update_strategy_stats("direct_subprocess", False, execution_time)

            logger.error(f"Direct strategy execution error for {tool_name}: {e}")
            return json.dumps(
                {
                    "success": False,
                    "error": f"Direct execution error: {str(e)}",
                    "tool": tool_name,
                }
            )

    async def _execute_mcp_strategy(
        self, tool_name: str, command: Optional[str] = None, **kwargs
    ) -> str:
        """Execute using desktop-commander MCP strategy."""
        start_time = asyncio.get_event_loop().time()

        try:
            self.strategy_stats["desktop_commander"]["attempts"] += 1

            result = await self.mcp_executor.execute_tool(tool_name, command, **kwargs)

            # Check success
            try:
                result_data = json.loads(result)
                success = result_data.get("success", False)
            except json.JSONDecodeError:
                success = False

            execution_time = asyncio.get_event_loop().time() - start_time
            self._update_strategy_stats("desktop_commander", success, execution_time)

            return result

        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self._update_strategy_stats("desktop_commander", False, execution_time)

            logger.error(f"MCP strategy execution error for {tool_name}: {e}")
            return json.dumps(
                {
                    "success": False,
                    "error": f"MCP execution error: {str(e)}",
                    "tool": tool_name,
                }
            )

    async def _execute_auto_strategy(
        self, tool_name: str, command: Optional[str] = None, **kwargs
    ) -> str:
        """Execute using auto-selected strategy."""
        # Auto strategy just delegates to the best performing strategy
        best_strategy = self._select_best_performing_strategy(tool_name)

        if best_strategy == ExecutionStrategy.DIRECT_SUBPROCESS:
            return await self._execute_direct_strategy(tool_name, command, **kwargs)
        elif best_strategy == ExecutionStrategy.DESKTOP_COMMANDER:
            return await self._execute_mcp_strategy(tool_name, command, **kwargs)
        else:
            return await self._execute_hybrid_strategy(tool_name, command, **kwargs)

    def _update_strategy_stats(
        self, strategy_name: str, success: bool, execution_time: float
    ):
        """
        Update strategy performance statistics.

        Args:
            strategy_name: Name of the strategy
            success: Whether execution was successful
            execution_time: Time taken for execution
        """
        if strategy_name in self.strategy_stats:
            stats = self.strategy_stats[strategy_name]

            if success:
                stats["successes"] += 1

            # Update average execution time
            current_avg = stats["avg_time"]
            total_attempts = stats["attempts"]

            if total_attempts > 0:
                stats["avg_time"] = (
                    (current_avg * (total_attempts - 1)) + execution_time
                ) / total_attempts
            else:
                stats["avg_time"] = execution_time

    def get_strategy_performance(self) -> Dict[str, Dict[str, Any]]:
        """
        Get strategy performance statistics.

        Returns:
            Dictionary with performance stats for each strategy
        """
        performance = {}

        for strategy_name, stats in self.strategy_stats.items():
            if stats["attempts"] > 0:
                success_rate = stats["successes"] / stats["attempts"]
            else:
                success_rate = 0.0

            performance[strategy_name] = {
                "attempts": stats["attempts"],
                "successes": stats["successes"],
                "success_rate": success_rate,
                "average_time": stats["avg_time"],
            }

        return performance

    def reset_strategy_stats(self):
        """Reset strategy performance statistics."""
        for strategy_name in self.strategy_stats:
            self.strategy_stats[strategy_name] = {
                "attempts": 0,
                "successes": 0,
                "avg_time": 0.0,
            }

        logger.info("Strategy performance statistics reset")


# Global fallback manager instance
fallback_manager = FallbackExecutionManager()


def get_fallback_manager() -> FallbackExecutionManager:
    """Get the global fallback execution manager instance."""
    return fallback_manager
