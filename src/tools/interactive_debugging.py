"""
Interactive debugging infrastructure for coder agent.

This module provides step-by-step interactive debugging capabilities
to replace the write-and-fail pattern with intelligent session management.
"""

import logging
import time
import uuid
import hashlib
import json
import os
import asyncio
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

logger = logging.getLogger(__name__)


class SessionStatus(Enum):
    """Status of interactive debugging session."""
    
    STARTING = "starting"
    ACTIVE = "active"
    ERROR = "error"
    COMPLETED = "completed"
    TERMINATED = "terminated"


@dataclass
class CodeCacheEntry:
    """Cache entry for validated code segments."""
    
    code_hash: str
    code: str
    result: str
    timestamp: float
    success: bool
    context_hash: Optional[str] = None  # Hash of session context when cached


@dataclass
class SessionCheckpoint:
    """Checkpoint data for session recovery."""
    
    session_id: str
    process_id: int
    validated_steps: List[str]
    current_step: int
    session_context: Dict[str, Any]
    timestamp: float


@dataclass
class InteractiveSession:
    """Manages state for interactive debugging session."""
    
    session_id: str = field(default_factory=lambda: f"session_{uuid.uuid4().hex[:8]}")
    process_id: Optional[int] = None
    status: SessionStatus = SessionStatus.STARTING
    created_at: float = field(default_factory=time.time)
    last_interaction: float = field(default_factory=time.time)
    
    # Session tracking
    validated_steps: List[str] = field(default_factory=list)
    current_step: int = 0
    errors_encountered: List[Dict[str, Any]] = field(default_factory=list)
    
    # Performance tracking
    api_calls_saved: int = 0
    total_interactions: int = 0
    cache_hits: int = 0
    retry_count: int = 0
    
    # Caching and checkpointing
    session_context: Dict[str, Any] = field(default_factory=dict)
    checkpoint_data: Optional[SessionCheckpoint] = None
    
    def update_last_interaction(self):
        """Update the last interaction timestamp."""
        self.last_interaction = time.time()
    
    def get_session_duration(self) -> float:
        """Get total session duration in seconds."""
        return time.time() - self.created_at
    
    def add_validated_step(self, step_code: str):
        """Add a successfully validated code step."""
        self.validated_steps.append(step_code)
        self.current_step += 1
        self.update_last_interaction()
    
    def add_error(self, error_info: Dict[str, Any]):
        """Record an error encountered during the session."""
        error_info['timestamp'] = time.time()
        error_info['step'] = self.current_step
        self.errors_encountered.append(error_info)
        self.update_last_interaction()


class InteractiveDebugger:
    """
    Interactive debugging manager for coder agent.
    
    Provides step-by-step code validation and execution using
    desktop-commander MCP tools with intelligent session management,
    caching, and performance optimization.
    """
    
    def __init__(self):
        self.active_sessions: Dict[str, InteractiveSession] = {}
        self._desktop_tools = None
        
        # Code caching for performance optimization
        self.code_cache: Dict[str, CodeCacheEntry] = {}
        self.cache_max_size = 1000
        self.cache_ttl = 3600  # 1 hour TTL
        
        # Session checkpointing
        self.checkpoints: Dict[str, SessionCheckpoint] = {}
        self.max_checkpoints = 50
        
        # Performance monitoring
        self.performance_metrics = {
            'total_sessions': 0,
            'successful_sessions': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_api_calls_saved': 0,
            'average_session_duration': 0.0,
            'retry_success_rate': 0.0
        }
        
    def _get_desktop_tools(self):
        """Get desktop-commander MCP tools lazily."""
        if self._desktop_tools is None:
            try:
                from .mcp_tools import def_mcp_tools
                
                all_tools = def_mcp_tools(server_names=['desktop-commander'])
                
                self._desktop_tools = {}
                for tool in all_tools:
                    if tool.name in ['start_process', 'interact_with_process', 'read_process_output', 'force_terminate']:
                        self._desktop_tools[tool.name] = tool
                
                logger.info(f"Loaded {len(self._desktop_tools)} desktop-commander tools for interactive debugging")
                
            except Exception as e:
                logger.error(f"Failed to load desktop-commander tools: {e}")
                self._desktop_tools = {}
        
        return self._desktop_tools
    
    def _compute_code_hash(self, code: str, session_context: Optional[Dict[str, Any]] = None) -> str:
        """Compute hash for code and context for caching."""
        content = code.strip()
        if session_context:
            # Include relevant context in hash for better cache accuracy
            context_str = json.dumps(session_context, sort_keys=True, default=str)
            content += f"|||{context_str}"
        
        return hashlib.sha256(content.encode()).hexdigest()[:16]
    
    def _get_cached_result(self, code: str, session_context: Optional[Dict[str, Any]] = None) -> Optional[CodeCacheEntry]:
        """Get cached result for code if available and valid."""
        code_hash = self._compute_code_hash(code, session_context)
        
        if code_hash in self.code_cache:
            entry = self.code_cache[code_hash]
            
            # Check TTL
            if time.time() - entry.timestamp < self.cache_ttl:
                self.performance_metrics['cache_hits'] += 1
                return entry
            else:
                # Remove expired entry
                del self.code_cache[code_hash]
        
        self.performance_metrics['cache_misses'] += 1
        return None
    
    def _cache_result(self, code: str, result: str, success: bool, session_context: Optional[Dict[str, Any]] = None):
        """Cache code execution result."""
        if len(self.code_cache) >= self.cache_max_size:
            # Remove oldest entries
            oldest_keys = sorted(
                self.code_cache.keys(),
                key=lambda k: self.code_cache[k].timestamp
            )[:self.cache_max_size // 4]  # Remove 25% of oldest entries
            
            for key in oldest_keys:
                del self.code_cache[key]
        
        code_hash = self._compute_code_hash(code, session_context)
        context_hash = self._compute_code_hash("", session_context) if session_context else None
        
        self.code_cache[code_hash] = CodeCacheEntry(
            code_hash=code_hash,
            code=code,
            result=result,
            timestamp=time.time(),
            success=success,
            context_hash=context_hash
        )
    
    def _create_checkpoint(self, session: InteractiveSession) -> SessionCheckpoint:
        """Create a checkpoint for session recovery."""
        checkpoint = SessionCheckpoint(
            session_id=session.session_id,
            process_id=session.process_id or 0,
            validated_steps=session.validated_steps.copy(),
            current_step=session.current_step,
            session_context=session.session_context.copy(),
            timestamp=time.time()
        )
        
        # Manage checkpoint storage
        if len(self.checkpoints) >= self.max_checkpoints:
            # Remove oldest checkpoint
            oldest_key = min(self.checkpoints.keys(), key=lambda k: self.checkpoints[k].timestamp)
            del self.checkpoints[oldest_key]
        
        self.checkpoints[session.session_id] = checkpoint
        session.checkpoint_data = checkpoint
        
        return checkpoint
    
    def _restore_from_checkpoint(self, session_id: str) -> Optional[InteractiveSession]:
        """Restore session from checkpoint if available."""
        if session_id in self.checkpoints:
            checkpoint = self.checkpoints[session_id]
            
            # Create new session from checkpoint data
            session = InteractiveSession(session_id=session_id)
            session.process_id = checkpoint.process_id
            session.validated_steps = checkpoint.validated_steps.copy()
            session.current_step = checkpoint.current_step
            session.session_context = checkpoint.session_context.copy()
            session.checkpoint_data = checkpoint
            session.status = SessionStatus.ACTIVE
            
            return session
        
        return None
    
    async def start_debugging_session(
        self, 
        command: str = "python3 -i",
        working_directory: Optional[str] = None,
        restore_from_checkpoint: bool = False
    ) -> InteractiveSession:
        """
        Start a new interactive debugging session with optional checkpoint restoration.
        
        Args:
            command: Command to start (default: python3 -i for Python REPL)
            working_directory: Working directory for the process
            restore_from_checkpoint: Whether to try restoring from existing checkpoint
            
        Returns:
            InteractiveSession object with session details
        """
        # Try to restore from checkpoint if requested
        if restore_from_checkpoint:
            for session_id in list(self.checkpoints.keys()):
                restored_session = self._restore_from_checkpoint(session_id)
                if restored_session:
                    logger.info(f"Restored session {session_id} from checkpoint")
                    self.active_sessions[session_id] = restored_session
                    return restored_session
        
        session = InteractiveSession()
        tools = self._get_desktop_tools()
        
        if 'start_process' not in tools:
            raise RuntimeError("start_process tool not available")
        
        try:
            logger.info(f"Starting interactive debugging session {session.session_id} with command: {command}")
            
            # Update performance metrics
            self.performance_metrics['total_sessions'] += 1
            
            # Store session context for caching
            session.session_context = {
                'command': command,
                'working_directory': working_directory,
                'start_time': time.time()
            }
            
            # Prepare start_process arguments
            start_args = {
                "command": command,
                "timeout_ms": 10000  # 10 second timeout for process startup
            }
            if working_directory:
                start_args["working_directory"] = working_directory
            
            # Start the process
            start_result = tools['start_process'].invoke(start_args)
            
            # Extract process ID from result
            result_str = str(start_result)
            import re
            process_id_match = re.search(r"Process started with PID (\d+)", result_str)
            
            if process_id_match:
                session.process_id = int(process_id_match.group(1))
                session.status = SessionStatus.ACTIVE
                self.active_sessions[session.session_id] = session
                
                logger.info(f"Session {session.session_id} started successfully with PID {session.process_id}")
                
                # Create initial checkpoint
                self._create_checkpoint(session)
                
                # Give the process a moment to initialize
                import asyncio
                await asyncio.sleep(0.5)
                
                return session
            else:
                session.status = SessionStatus.ERROR
                error_msg = f"Could not extract process ID from start result: {result_str}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
                
        except Exception as e:
            session.status = SessionStatus.ERROR
            logger.error(f"Failed to start debugging session {session.session_id}: {e}")
            raise
    
    async def validate_code_step(
        self, 
        session: InteractiveSession, 
        code: str,
        timeout_ms: int = 8000,
        use_cache: bool = True
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Validate a single code step interactively with caching support.
        
        Args:
            session: Active debugging session
            code: Code to validate/execute
            timeout_ms: Timeout for interaction in milliseconds
            use_cache: Whether to use cached results
            
        Returns:
            Tuple of (success, output, error_info)
        """
        if session.status != SessionStatus.ACTIVE:
            return False, f"Session {session.session_id} not active (status: {session.status})", None
        
        # Check cache first if enabled
        if use_cache:
            cached_entry = self._get_cached_result(code, session.session_context)
            if cached_entry:
                logger.debug(f"Cache hit for code step in session {session.session_id}")
                session.cache_hits += 1
                session.api_calls_saved += 1
                session.update_last_interaction()
                
                if cached_entry.success:
                    session.add_validated_step(code)
                    return True, cached_entry.result, None
                else:
                    error_info = {
                        'code': code,
                        'error': cached_entry.result,
                        'type': 'cached_error'
                    }
                    return False, cached_entry.result, error_info
        
        tools = self._get_desktop_tools()
        if 'interact_with_process' not in tools:
            return False, "interact_with_process tool not available", None
            
        try:
            logger.debug(f"Validating code step in session {session.session_id}: {code[:50]}...")
            
            # Interact with the process
            interact_result = tools['interact_with_process'].invoke({
                'pid': session.process_id,
                'input': code,
                'timeout_ms': timeout_ms,
                'wait_for_prompt': True
            })
            
            session.total_interactions += 1
            
            # Check if the interaction was successful
            result_str = str(interact_result)
            
            # Look for common error patterns
            error_indicators = [
                'SyntaxError:', 'NameError:', 'TypeError:', 'ValueError:', 
                'ImportError:', 'ModuleNotFoundError:', 'AttributeError:',
                'Traceback (most recent call last):'
            ]
            
            has_error = any(indicator in result_str for indicator in error_indicators)
            
            # Cache the result
            if use_cache:
                self._cache_result(code, result_str, not has_error, session.session_context)
            
            if has_error:
                # Code had an error
                error_info = {
                    'code': code,
                    'error': result_str,
                    'type': 'execution_error'
                }
                session.add_error(error_info)
                return False, result_str, error_info
            else:
                # Code executed successfully
                session.add_validated_step(code)
                session.api_calls_saved += 1  # Count this as a saved API call
                
                # Create checkpoint after successful steps
                if session.current_step % 5 == 0:  # Checkpoint every 5 steps
                    self._create_checkpoint(session)
                
                return True, result_str, None
                
        except Exception as e:
            error_info = {
                'code': code,
                'error': str(e),
                'type': 'interaction_error'
            }
            session.add_error(error_info)
            logger.error(f"Error validating code step in session {session.session_id}: {e}")
            
            # Cache the error if it's not a transient issue
            if use_cache and 'timeout' not in str(e).lower():
                self._cache_result(code, str(e), False, session.session_context)
            
            return False, str(e), error_info
    
    async def recover_from_error(
        self, 
        session: InteractiveSession, 
        error_info: Dict[str, Any],
        recovery_code: Optional[str] = None,
        max_retries: int = 3
    ) -> Tuple[bool, str]:
        """
        Attempt to recover from an error during interactive debugging with smart retry logic.
        
        Args:
            session: Active debugging session
            error_info: Error information from failed step
            recovery_code: Optional recovery code to execute
            max_retries: Maximum number of retry attempts
            
        Returns:
            Tuple of (success, message)
        """
        if session.status != SessionStatus.ACTIVE:
            return False, f"Session not active: {session.status}"
        
        session.retry_count += 1
        
        try:
            logger.info(f"Attempting error recovery in session {session.session_id} (retry {session.retry_count})")
            
            if recovery_code:
                # Try executing recovery code with progressively longer timeouts
                timeout_ms = 8000 + (session.retry_count * 2000)  # Increase timeout with retries
                
                for attempt in range(max_retries):
                    success, output, _ = await self.validate_code_step(
                        session, recovery_code, timeout_ms=timeout_ms, use_cache=False
                    )
                    
                    if success:
                        # Update performance metrics
                        if session.retry_count <= max_retries:
                            self.performance_metrics['retry_success_rate'] = (
                                self.performance_metrics.get('retry_success_rate', 0) * 0.9 + 0.1
                            )
                        
                        logger.info(f"Recovery successful after {attempt + 1} attempts in session {session.session_id}")
                        return True, f"Recovery successful: {output}"
                    
                    # Wait before retry with exponential backoff
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5 * (2 ** attempt))
                
                return False, f"Recovery failed after {max_retries} attempts: {output}"
            else:
                # Generic recovery - try to continue
                success, output, _ = await self.validate_code_step(session, "pass", use_cache=False)
                return success, output
                
        except Exception as e:
            logger.error(f"Error recovery failed in session {session.session_id}: {e}")
            return False, str(e)
    
    async def terminate_session(self, session: InteractiveSession):
        """
        Terminate an interactive debugging session and update metrics.
        
        Args:
            session: Session to terminate
        """
        if session.session_id in self.active_sessions:
            tools = self._get_desktop_tools()
            
            try:
                if 'force_terminate' in tools and session.process_id:
                    tools['force_terminate'].invoke({'pid': session.process_id})
                    logger.info(f"Terminated process {session.process_id} for session {session.session_id}")
            except Exception as e:
                logger.warning(f"Error terminating process {session.process_id}: {e}")
            
            # Update performance metrics
            session_duration = session.get_session_duration()
            self.performance_metrics['total_api_calls_saved'] += session.api_calls_saved
            
            # Update average session duration
            current_avg = self.performance_metrics['average_session_duration']
            total_sessions = self.performance_metrics['total_sessions']
            self.performance_metrics['average_session_duration'] = (
                (current_avg * (total_sessions - 1) + session_duration) / total_sessions
            )
            
            # Mark as successful if it had meaningful interaction
            if session.validated_steps or session.total_interactions > 0:
                self.performance_metrics['successful_sessions'] += 1
            
            session.status = SessionStatus.TERMINATED
            del self.active_sessions[session.session_id]
            
            logger.info(f"Session {session.session_id} terminated after {session_duration:.1f}s")
            logger.debug(f"Session metrics - API calls saved: {session.api_calls_saved}, Cache hits: {session.cache_hits}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics for API call reduction tracking.
        
        Returns:
            Dictionary with performance metrics
        """
        total_sessions = max(1, self.performance_metrics['total_sessions'])
        successful_sessions = self.performance_metrics['successful_sessions']
        
        cache_total = self.performance_metrics['cache_hits'] + self.performance_metrics['cache_misses']
        cache_hit_rate = self.performance_metrics['cache_hits'] / max(1, cache_total)
        
        return {
            'session_metrics': {
                'total_sessions': self.performance_metrics['total_sessions'],
                'successful_sessions': successful_sessions,
                'success_rate': successful_sessions / total_sessions,
                'average_duration': self.performance_metrics['average_session_duration']
            },
            'cache_metrics': {
                'cache_size': len(self.code_cache),
                'cache_hit_rate': cache_hit_rate,
                'cache_hits': self.performance_metrics['cache_hits'],
                'cache_misses': self.performance_metrics['cache_misses']
            },
            'efficiency_metrics': {
                'total_api_calls_saved': self.performance_metrics['total_api_calls_saved'],
                'retry_success_rate': self.performance_metrics['retry_success_rate'],
                'average_api_calls_saved_per_session': self.performance_metrics['total_api_calls_saved'] / total_sessions
            },
            'system_metrics': {
                'active_sessions': len(self.active_sessions),
                'checkpoints': len(self.checkpoints),
                'cache_utilization': len(self.code_cache) / self.cache_max_size
            }
        }
    
    def clear_expired_cache(self):
        """Clear expired cache entries to maintain performance."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.code_cache.items()
            if current_time - entry.timestamp > self.cache_ttl
        ]
        
        for key in expired_keys:
            del self.code_cache[key]
        
        if expired_keys:
            logger.debug(f"Cleared {len(expired_keys)} expired cache entries")
    
    def get_session_metrics(self, session: InteractiveSession) -> Dict[str, Any]:
        """
        Get performance metrics for a debugging session.
        
        Args:
            session: Session to get metrics for
            
        Returns:
            Dictionary with session metrics
        """
        return {
            'session_id': session.session_id,
            'duration': session.get_session_duration(),
            'total_interactions': session.total_interactions,
            'validated_steps': len(session.validated_steps),
            'errors_encountered': len(session.errors_encountered),
            'api_calls_saved': session.api_calls_saved,
            'status': session.status.value,
            'efficiency_ratio': session.api_calls_saved / max(1, session.total_interactions)
        }


# Global interactive debugger instance
_interactive_debugger = None

def get_interactive_debugger() -> InteractiveDebugger:
    """Get the global interactive debugger instance."""
    global _interactive_debugger
    if _interactive_debugger is None:
        _interactive_debugger = InteractiveDebugger()
    return _interactive_debugger