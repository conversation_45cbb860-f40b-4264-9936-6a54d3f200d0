"""
Biomni tool wrappers for LangChain integration.

This module provides LangChain-compatible wrappers for biomni toolkit functions,
enabling direct usage of biomni tools by multi-agent system agents.

Key Features:
- Environment separation: Executes biomni tools in biomni_e1 environment
- LangChain compatibility: Converts biomni functions to LangChain tools
- Error handling: Graceful fallback when biomni tools are unavailable
- Desktop commander integration: Uses MCP tools for cross-environment execution
"""

import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional, Union
from langchain_core.tools import BaseTool, StructuredTool, tool
from pydantic import BaseModel, Field
from .decorators import log_io

logger = logging.getLogger(__name__)

# Track if biomni tools are available
_biomni_available = None
_biomni_tools_cache = {}


def _check_biomni_availability() -> bool:
    """Check if biomni tools are available in the current environment."""
    global _biomni_available

    if _biomni_available is not None:
        return _biomni_available

    try:
        # Try to import biomni components
        from src.tools.biomni.tool_modules import load_all_tools
        from src.tools.biomni.tool_registry import ToolRegistry

        # Try to load tools
        all_tools = load_all_tools()
        if not all_tools:
            logger.warning("Biomni tools loaded but registry is empty")
            _biomni_available = False
            return False

        registry = ToolRegistry(all_tools)
        if not registry.tools:
            logger.warning("Biomni tool registry is empty")
            _biomni_available = False
            return False

        _biomni_available = True
        logger.info(f"Biomni tools available: {len(registry.tools)} tools loaded")
        return True

    except ImportError as e:
        logger.debug(f"Biomni tools not available due to import error: {e}")
        _biomni_available = False
        return False
    except Exception as e:
        logger.warning(f"Error checking biomni availability: {e}")
        _biomni_available = False
        return False


def _get_biomni_tools() -> Dict[str, Any]:
    """Get cached biomni tools or load them if not cached."""
    global _biomni_tools_cache

    if _biomni_tools_cache:
        return _biomni_tools_cache

    if not _check_biomni_availability():
        return {}

    try:
        from src.tools.biomni.tool_modules import load_all_tools
        from src.tools.biomni.tool_registry import ToolRegistry

        all_tools = load_all_tools()
        registry = ToolRegistry(all_tools)

        # Cache the tools for future use
        _biomni_tools_cache = {
            "registry": registry,
            "raw_tools": all_tools,
            "tools_by_name": {tool["name"]: tool for tool in registry.tools},
        }

        return _biomni_tools_cache

    except Exception as e:
        logger.error(f"Error loading biomni tools: {e}")
        return {}


def _execute_biomni_tool_direct(tool_name: str, **kwargs) -> str:
    """
    Execute a biomni tool directly using subprocess (fallback method).

    This bypasses desktop-commander entirely and uses direct subprocess execution.

    Args:
        tool_name: Name of the biomni tool to execute
        **kwargs: Arguments to pass to the tool

    Returns:
        JSON string with the tool execution result
    """
    import subprocess
    import json

    try:
        # Build the Python command
        # Create a properly escaped kwargs string
        kwargs_str = repr(kwargs)

        python_code = f"""
import sys
import os
import json
import traceback

# Add only the biomni module directory to avoid project dependency issues
biomni_module_path = "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
if biomni_module_path not in sys.path:
    sys.path.insert(0, biomni_module_path)

try:
    # Import the specific tool function directly from biomni module
    from biomni.database import {tool_name}
    
    # Execute the tool with provided arguments
    tool_kwargs = {kwargs_str}
    result = {tool_name}(**tool_kwargs)
    
    # Return the result as JSON with success marker
    output = {{"success": True, "result": result, "tool": "{tool_name}"}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(output, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except ImportError as e:
    error_result = {{"success": False, "error": f"Import error: {{str(e)}}", "tool": "{tool_name}", "args": {kwargs_str}}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except Exception as e:
    error_result = {{"success": False, "error": str(e), "traceback": traceback.format_exc(), "tool": "{tool_name}", "args": {kwargs_str}}}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
"""

        # Execute using subprocess with direct Python path
        # Use a temporary file to avoid shell escaping issues
        import tempfile

        with tempfile.NamedTemporaryFile(
            mode="w", suffix=".py", delete=False
        ) as temp_file:
            temp_file.write(python_code)
            temp_file_path = temp_file.name

        # Use direct Python executable path to avoid conda activation issues
        biomni_python_path = "/Users/<USER>/miniforge3/envs/biomni_e1/bin/python3"
        cmd = [biomni_python_path, temp_file_path]

        logger.info(f"Executing direct subprocess for {tool_name}")

        try:
            # Create environment with current env plus our specific variables
            env = os.environ.copy()
            env.update(
                {
                    "REASONING_PROVIDER": os.environ.get(
                        "REASONING_PROVIDER", "gemini"
                    ),
                    "REASONING_API_KEY": os.environ.get("REASONING_API_KEY", ""),
                    "REASONING_BASE_URL": os.environ.get("REASONING_BASE_URL", ""),
                    "REASONING_MODEL": os.environ.get(
                        "REASONING_MODEL", "gemini-2.5-pro"
                    ),
                }
            )

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,  # 30 second timeout
                env=env,
            )
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

        if result.returncode == 0:
            # Successfully executed
            output = result.stdout
            logger.info(f"Direct subprocess successful for {tool_name}")

            # Parse the result with enhanced extraction
            if (
                "=== BIOMNI_RESULT_START ===" in output
                and "=== BIOMNI_RESULT_END ===" in output
            ):
                start_marker = output.find("=== BIOMNI_RESULT_START ===")
                end_marker = output.find("=== BIOMNI_RESULT_END ===")

                if start_marker != -1 and end_marker != -1:
                    json_section = output[
                        start_marker + len("=== BIOMNI_RESULT_START ===") : end_marker
                    ].strip()

                    try:
                        parsed_result = json.loads(json_section)
                        return json.dumps(parsed_result, indent=2)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON result: {e}")

            # Fallback: return raw output
            return json.dumps(
                {
                    "success": True,
                    "result": output,
                    "tool": tool_name,
                    "execution_method": "direct_subprocess",
                }
            )
        else:
            # Execution failed
            error_output = result.stderr or result.stdout
            logger.error(f"Direct subprocess failed for {tool_name}: {error_output}")
            return json.dumps(
                {
                    "success": False,
                    "error": f"Subprocess execution failed: {error_output}",
                    "tool": tool_name,
                    "return_code": result.returncode,
                }
            )

    except subprocess.TimeoutExpired:
        logger.error(f"Direct subprocess timed out for {tool_name}")

        # Enhanced error handling for timeout
        try:
            from .error_recovery import classify_and_report_error, format_error_for_json

            error_report = classify_and_report_error(
                error_text="Subprocess execution timed out",
                tool_name=tool_name,
                execution_method="direct_subprocess",
            )

            return format_error_for_json(error_report)

        except Exception:
            # Fallback to simple error response
            return json.dumps(
                {
                    "success": False,
                    "error": "Subprocess execution timed out",
                    "tool": tool_name,
                }
            )

    except Exception as e:
        logger.error(f"Direct subprocess error for {tool_name}: {e}")

        # Enhanced error handling for general errors
        try:
            from .error_recovery import classify_and_report_error, format_error_for_json

            error_report = classify_and_report_error(
                error_text=str(e),
                tool_name=tool_name,
                execution_method="direct_subprocess",
            )

            return format_error_for_json(error_report)

        except Exception:
            # Fallback to simple error response
            return json.dumps(
                {
                    "success": False,
                    "error": f"Direct execution error: {str(e)}",
                    "tool": tool_name,
                }
            )


def _execute_biomni_tool_in_environment(tool_name: str, **kwargs) -> str:
    """
    Execute a biomni tool using optimized execution strategies with enhanced error handling.

    Uses the fallback execution manager with comprehensive error classification
    and user-friendly error reporting.

    Args:
        tool_name: Name of the biomni tool to execute
        **kwargs: Arguments to pass to the tool

    Returns:
        JSON string with the tool execution result
    """
    import asyncio
    import nest_asyncio

    # Apply nest_asyncio to handle nested event loops
    nest_asyncio.apply()

    try:
        # Use the new fallback execution system
        from .execution_fallback import get_fallback_manager

        fallback_manager = get_fallback_manager()

        # Execute with async context
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # We're in an async context, run the coroutine directly
            result = loop.run_until_complete(
                fallback_manager.execute_with_fallback(tool_name, **kwargs)
            )
        else:
            # Start new event loop
            result = asyncio.run(
                fallback_manager.execute_with_fallback(tool_name, **kwargs)
            )

        logger.info(f"Optimized execution completed for {tool_name}")

        # Check if the result indicates a failure and apply enhanced error handling
        try:
            parsed_result = json.loads(result)
            if not parsed_result.get("success", True):
                # Tool failed but didn't raise exception - apply enhanced error handling
                error_message = parsed_result.get("error", "Tool execution failed")

                from .error_recovery import (
                    classify_and_report_error,
                    format_error_for_json,
                )
                from .error_tracking import log_error

                # Classify the error and generate comprehensive report
                error_report = classify_and_report_error(
                    error_text=error_message,
                    tool_name=tool_name,
                    execution_method=parsed_result.get(
                        "execution_method", "fallback_execution"
                    ),
                )

                # Log the error for pattern analysis
                error_log_id = log_error(error_report)
                logger.info(f"Error classified and logged with ID: {error_log_id}")

                # Return enhanced error response
                return format_error_for_json(error_report)
        except (json.JSONDecodeError, AttributeError):
            # If result is not JSON or doesn't have expected structure, return as-is
            pass

        return result

    except Exception as e:
        logger.error(f"Optimized execution failed for {tool_name}: {e}")

        # Enhanced error handling with classification and recovery suggestions
        try:
            from .error_recovery import classify_and_report_error, format_error_for_json
            from .error_tracking import log_error

            # Classify the error and generate comprehensive report
            error_report = classify_and_report_error(
                error_text=str(e),
                tool_name=tool_name,
                execution_method="optimized_fallback",
            )

            # Log the error for pattern analysis
            error_log_id = log_error(error_report)
            logger.info(f"Error logged with ID: {error_log_id}")

            # Return enhanced error response
            return format_error_for_json(error_report)

        except Exception as error_handling_error:
            logger.error(f"Error handling system failed: {error_handling_error}")

            # Fallback to legacy direct execution
            logger.warning(f"Falling back to legacy direct execution for {tool_name}")

            try:
                result = _execute_biomni_tool_direct(tool_name, **kwargs)
                if result:
                    # Parse the result to check if it's successful
                    try:
                        parsed = json.loads(result)
                        if parsed.get("success"):
                            logger.info(f"Legacy execution successful for {tool_name}")
                            return result
                    except json.JSONDecodeError:
                        pass
            except Exception as legacy_error:
                logger.error(
                    f"Legacy execution also failed for {tool_name}: {legacy_error}"
                )

                # Final attempt at error classification for legacy error
                try:
                    from .error_recovery import (
                        classify_and_report_error,
                        format_error_for_json,
                    )

                    error_report = classify_and_report_error(
                        error_text=str(legacy_error),
                        tool_name=tool_name,
                        execution_method="direct_subprocess",
                    )

                    return format_error_for_json(error_report)

                except Exception:
                    # Absolute fallback - simple error response
                    pass

            # Final error response if all else fails
            return json.dumps(
                {
                    "success": False,
                    "error": f"All execution strategies failed: {str(e)}",
                    "tool": tool_name,
                    "note": "Both optimized and legacy execution paths failed",
                }
            )


def _create_biomni_tool_wrapper(tool_info: Dict[str, Any]) -> StructuredTool:
    """
    Create a LangChain StructuredTool wrapper for a biomni tool.

    Args:
        tool_info: Dictionary containing tool information from biomni registry

    Returns:
        LangChain StructuredTool that can be used by agents
    """
    tool_name = tool_info["name"]
    description = tool_info["description"]

    # Create dynamic parameter schema based on tool info
    required_params = tool_info.get("required_parameters", [])
    optional_params = tool_info.get("optional_parameters", [])

    # Build pydantic model for the tool arguments
    fields = {}

    # Add required parameters
    for param in required_params:
        param_name = param["name"]
        param_type = param.get("type", "str")
        param_desc = param.get("description", f"Required parameter: {param_name}")

        # Map string type names to Python types
        if param_type == "str":
            fields[param_name] = (str, Field(description=param_desc))
        elif param_type == "int":
            fields[param_name] = (int, Field(description=param_desc))
        elif param_type == "bool":
            fields[param_name] = (bool, Field(description=param_desc))
        else:
            fields[param_name] = (str, Field(description=param_desc))

    # Add optional parameters
    for param in optional_params:
        param_name = param["name"]
        param_type = param.get("type", "str")
        param_desc = param.get("description", f"Optional parameter: {param_name}")
        param_default = param.get("default", None)

        # Map string type names to Python types
        if param_type == "str":
            fields[param_name] = (
                Optional[str],
                Field(default=param_default, description=param_desc),
            )
        elif param_type == "int":
            fields[param_name] = (
                Optional[int],
                Field(default=param_default, description=param_desc),
            )
        elif param_type == "bool":
            fields[param_name] = (
                Optional[bool],
                Field(default=param_default, description=param_desc),
            )
        else:
            fields[param_name] = (
                Optional[str],
                Field(default=param_default, description=param_desc),
            )

    # Create dynamic pydantic model with proper annotations
    # Handle the annotation issue by creating a proper model class
    annotations = {}
    for field_name, field_info in fields.items():
        annotations[field_name] = field_info[0]

    ArgsModel = type(
        f"{tool_name}Args",
        (BaseModel,),
        {"__annotations__": annotations, **{k: v[1] for k, v in fields.items()}},
    )

    def tool_function(**kwargs) -> str:
        """Execute the biomni tool with provided arguments."""
        logger.debug(f"Executing biomni tool {tool_name} with args: {kwargs}")

        # Filter out None values for optional parameters
        filtered_kwargs = {k: v for k, v in kwargs.items() if v is not None}

        # Execute the tool in the biomni_e1 environment
        result = _execute_biomni_tool_in_environment(tool_name, **filtered_kwargs)

        return result

    # Create the structured tool
    return StructuredTool.from_function(
        func=tool_function,
        name=tool_name,
        description=description,
        args_schema=ArgsModel,
    )


def get_biomni_tools() -> List[BaseTool]:
    """
    Get all available biomni tools as LangChain tools with graceful degradation.

    Returns:
        List of LangChain tools wrapped from biomni functions, with fallback tools if unavailable
    """
    if not _check_biomni_availability():
        logger.warning("Biomni tools not available - returning fallback tools")
        return _create_fallback_tools()

    biomni_data = _get_biomni_tools()
    if not biomni_data:
        logger.warning("No biomni tools loaded - returning fallback tools")
        return _create_fallback_tools()

    registry = biomni_data["registry"]
    tools = []

    for tool_info in registry.tools:
        try:
            wrapped_tool = _create_biomni_tool_wrapper(tool_info)
            tools.append(wrapped_tool)
            logger.debug(f"Created wrapper for biomni tool: {tool_info['name']}")
        except Exception as e:
            logger.warning(
                f"Failed to create wrapper for tool {tool_info['name']}: {e}"
            )
            # Create a degraded version of the tool that provides helpful error messages
            fallback_tool = _create_fallback_tool_wrapper(tool_info["name"], str(e))
            tools.append(fallback_tool)

    if not tools:
        logger.warning("No biomni tools could be created - returning fallback tools")
        return _create_fallback_tools()

    logger.info(f"Created {len(tools)} biomni tool wrappers")
    return tools


def _create_fallback_tools() -> List[BaseTool]:
    """
    Create fallback tools when biomni tools are unavailable.

    Returns:
        List of fallback tools that provide helpful error messages
    """
    fallback_tools = []

    # Essential bioinformatics tools that should always be available
    essential_tools = [
        {
            "name": "query_uniprot",
            "description": (
                "Query UniProt database for protein information (currently unavailable)"
            ),
        },
        {
            "name": "query_alphafold",
            "description": (
                "Query AlphaFold database for protein structures (currently unavailable)"
            ),
        },
        {
            "name": "query_pdb",
            "description": (
                "Query Protein Data Bank for structures (currently unavailable)"
            ),
        },
    ]

    for tool_info in essential_tools:
        fallback_tool = _create_fallback_tool_wrapper(
            tool_info["name"], "Biomni toolkit is not available in this environment"
        )
        fallback_tools.append(fallback_tool)

    return fallback_tools


def _create_fallback_tool_wrapper(tool_name: str, error_reason: str) -> StructuredTool:
    """
    Create a fallback tool wrapper that provides helpful error messages.

    Args:
        tool_name: Name of the unavailable tool
        error_reason: Reason why the tool is unavailable

    Returns:
        StructuredTool that explains the unavailability with recovery suggestions
    """
    from pydantic import BaseModel, Field

    class FallbackArgs(BaseModel):
        query: str = Field(description="Query or parameters for the tool")

    def fallback_function(query: str) -> str:
        """Fallback function that provides helpful error information."""
        try:
            from .error_recovery import classify_and_report_error, format_error_for_user

            # Create a comprehensive error report
            error_report = classify_and_report_error(
                error_text=f"Tool {tool_name} is unavailable: {error_reason}",
                tool_name=tool_name,
                execution_method="fallback",
            )

            # Format for user display
            user_friendly_error = format_error_for_user(error_report)

            return f"""
{user_friendly_error}

### Alternative Approaches:
1. **Manual Research**: You can manually search for information using web resources
2. **Different Tools**: Try using alternative bioinformatics tools if available
3. **Environment Setup**: Check if the biomni environment can be properly configured

### Query Details:
- **Requested Query**: {query}
- **Tool**: {tool_name}
- **Status**: Currently unavailable

The system will continue to work with available tools, but {tool_name} functionality is temporarily limited.
"""

        except Exception:
            # Simple fallback if error handling system fails
            return f"""
## Tool Unavailable: {tool_name}

The {tool_name} tool is currently unavailable due to: {error_reason}

**Your query**: {query}

**Suggested actions**:
1. Check the biomni environment setup
2. Verify required dependencies are installed
3. Try alternative research methods
4. Contact support if the issue persists

The system will continue to operate with available tools.
"""

    return StructuredTool.from_function(
        func=fallback_function,
        name=tool_name,
        description=f"Fallback for {tool_name} - provides error information and alternatives",
        args_schema=FallbackArgs,
    )


def get_biomni_tools_for_agent(agent_name: str) -> List[BaseTool]:
    """
    Get biomni tools filtered for a specific agent.

    Args:
        agent_name: Name of the agent (e.g., "researcher", "coder")

    Returns:
        List of relevant biomni tools for the agent
    """
    all_tools = get_biomni_tools()

    if not all_tools:
        return []

    # Filter tools based on agent specialization
    agent_tool_filters = {
        "researcher": [
            "query_uniprot",
            "query_alphafold",
            "query_pdb",
            "query_kegg",
            "search_pubmed",
            "get_gene_info",
            "get_protein_info",
            "literature_search",
            "database_search",
        ],
        "coder": [
            "query_uniprot",
            "query_alphafold",
            "query_pdb",
            "query_kegg",
            "process_sequence",
            "analyze_structure",
            "compute_features",
            "run_analysis",
        ],
        "browser": ["query_uniprot", "query_pdb", "query_kegg", "search_database"],
    }

    # Get tool names that are relevant for this agent
    relevant_tool_names = agent_tool_filters.get(agent_name, [])

    if not relevant_tool_names:
        # If no specific filter, return essential database tools
        relevant_tool_names = ["query_uniprot", "query_alphafold", "query_pdb"]

    # Filter tools by name
    filtered_tools = []
    for tool in all_tools:
        if any(filter_name in tool.name.lower() for filter_name in relevant_tool_names):
            filtered_tools.append(tool)

    logger.info(f"Filtered {len(filtered_tools)} biomni tools for agent {agent_name}")
    logger.debug(f"Biomni tools for {agent_name}: {[t.name for t in filtered_tools]}")

    return filtered_tools


# Pre-created tool instances for common biomni functions
@tool
@log_io
def query_uniprot_direct(
    prompt: str, endpoint: Optional[str] = None, max_results: int = 5
) -> str:
    """
    Query the UniProt database using natural language or direct endpoint.

    Args:
        prompt: Natural language query about proteins (e.g., "Find information about Q9Y2R9")
        endpoint: Optional direct UniProt API endpoint
        max_results: Maximum number of results to return

    Returns:
        JSON string with UniProt query results
    """
    return _execute_biomni_tool_in_environment(
        "query_uniprot", prompt=prompt, endpoint=endpoint, max_results=max_results
    )


@tool
@log_io
def query_alphafold_direct(
    uniprot_id: str, endpoint: str = "prediction", download: bool = False
) -> str:
    """
    Query AlphaFold database for protein structure predictions.

    Args:
        uniprot_id: UniProt accession ID (e.g., "P53_HUMAN")
        endpoint: AlphaFold endpoint ("prediction", "summary", or "annotations")
        download: Whether to download structure files

    Returns:
        JSON string with AlphaFold query results
    """
    return _execute_biomni_tool_in_environment(
        "query_alphafold", uniprot_id=uniprot_id, endpoint=endpoint, download=download
    )


# Direct tool instances for immediate use
DIRECT_BIOMNI_TOOLS = [query_uniprot_direct, query_alphafold_direct]


def get_direct_biomni_tools() -> List[BaseTool]:
    """
    Get pre-created direct biomni tools.

    Returns:
        List of direct biomni tools that are immediately available
    """
    return DIRECT_BIOMNI_TOOLS


def get_all_biomni_tools() -> List[BaseTool]:
    """
    Get all biomni tools (both dynamically created and direct).

    Returns:
        Combined list of all available biomni tools
    """
    dynamic_tools = get_biomni_tools()
    direct_tools = get_direct_biomni_tools()

    # Combine and deduplicate by name
    all_tools = direct_tools + dynamic_tools
    seen_names = set()
    unique_tools = []

    for tool in all_tools:
        if tool.name not in seen_names:
            unique_tools.append(tool)
            seen_names.add(tool.name)

    return unique_tools
