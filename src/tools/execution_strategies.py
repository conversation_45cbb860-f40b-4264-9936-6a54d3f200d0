"""
Execution strategies for cross-environment tool execution.

This module provides optimized execution strategies with retry logic,
fallback mechanisms, and performance monitoring for biomni tools.
"""

import asyncio
import logging
import subprocess
import tempfile
import time
import os
import json
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from .error_recovery import get_error_detector, get_error_recovery
from .connection_pool import get_connection_pool
from ..config.execution_config import get_execution_config, ExecutionStrategy

logger = logging.getLogger(__name__)


class ExecutionResult:
    """Result of tool execution with metadata."""

    def __init__(
        self,
        success: bool,
        result: Any,
        execution_time: float,
        strategy_used: str,
        retry_count: int = 0,
        error: str = None,
    ):
        self.success = success
        self.result = result
        self.execution_time = execution_time
        self.strategy_used = strategy_used
        self.retry_count = retry_count
        self.error = error
        self.timestamp = time.time()


class ExecutionMetrics:
    """Execution performance metrics tracking."""

    def __init__(self):
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_execution_time = 0.0
        self.retry_counts = {}
        self.strategy_performance = {}
        self.error_frequencies = {}

    def record_execution(self, result: ExecutionResult):
        """Record execution result for metrics."""
        self.total_executions += 1
        self.total_execution_time += result.execution_time

        if result.success:
            self.successful_executions += 1
        else:
            self.failed_executions += 1
            if result.error:
                error_type = (
                    type(result.error).__name__
                    if isinstance(result.error, Exception)
                    else "string_error"
                )
                self.error_frequencies[error_type] = (
                    self.error_frequencies.get(error_type, 0) + 1
                )

        # Track retry counts
        if result.retry_count > 0:
            self.retry_counts[result.retry_count] = (
                self.retry_counts.get(result.retry_count, 0) + 1
            )

        # Track strategy performance
        if result.strategy_used not in self.strategy_performance:
            self.strategy_performance[result.strategy_used] = {
                "total": 0,
                "successful": 0,
                "total_time": 0.0,
            }

        perf = self.strategy_performance[result.strategy_used]
        perf["total"] += 1
        perf["total_time"] += result.execution_time
        if result.success:
            perf["successful"] += 1

    def get_success_rate(self) -> float:
        """Get overall success rate."""
        if self.total_executions == 0:
            return 0.0
        return self.successful_executions / self.total_executions

    def get_average_execution_time(self) -> float:
        """Get average execution time."""
        if self.total_executions == 0:
            return 0.0
        return self.total_execution_time / self.total_executions

    def get_strategy_success_rate(self, strategy: str) -> float:
        """Get success rate for a specific strategy."""
        if strategy not in self.strategy_performance:
            return 0.0

        perf = self.strategy_performance[strategy]
        if perf["total"] == 0:
            return 0.0

        return perf["successful"] / perf["total"]


class RetryExecutor:
    """
    Executor with exponential backoff retry logic.

    Provides configurable retry behavior with exponential backoff,
    jitter, and strategy adaptation based on error patterns.
    """

    def __init__(self):
        self.config = get_execution_config()
        self.error_detector = get_error_detector()
        self.error_recovery = get_error_recovery()
        self.metrics = ExecutionMetrics()

    async def execute_with_retry(
        self,
        execution_func: Callable,
        tool_name: str,
        command: Optional[str] = None,
        **kwargs,
    ) -> ExecutionResult:
        """
        Execute function with retry logic and exponential backoff.

        Args:
            execution_func: Function to execute
            tool_name: Name of the tool being executed
            command: Optional command string for context
            **kwargs: Arguments to pass to execution function

        Returns:
            ExecutionResult with outcome and metadata
        """
        retry_config = self.config.get_retry_config(tool_name, command)
        max_retries = retry_config["max_retries"]
        backoff_factor = retry_config["backoff_factor"]
        initial_delay = retry_config["initial_delay"]

        last_error = None
        start_time = time.time()

        for attempt in range(max_retries + 1):
            try:
                logger.debug(
                    f"Executing {tool_name} (attempt {attempt + 1}/{max_retries + 1})"
                )

                # Execute the function
                result = await self._execute_function(execution_func, **kwargs)

                # Success!
                execution_time = time.time() - start_time
                exec_result = ExecutionResult(
                    success=True,
                    result=result,
                    execution_time=execution_time,
                    strategy_used=kwargs.get("strategy", "unknown"),
                    retry_count=attempt,
                )

                self.metrics.record_execution(exec_result)

                if attempt > 0:
                    logger.info(
                        f"Execution succeeded for {tool_name} after {attempt} retries"
                    )

                return exec_result

            except Exception as e:
                last_error = e
                logger.warning(
                    f"Execution attempt {attempt + 1} failed for {tool_name}: {e}"
                )

                # Detect and classify error
                error_instance = self.error_detector.detect_error(str(e), tool_name)

                # If this is not the last attempt, try recovery
                if attempt < max_retries and error_instance:
                    try:
                        # Attempt error recovery
                        recovery_success, recovery_result = (
                            await self.error_recovery.attempt_recovery(
                                error_instance, execution_func, **kwargs
                            )
                        )

                        if recovery_success:
                            execution_time = time.time() - start_time
                            exec_result = ExecutionResult(
                                success=True,
                                result=recovery_result,
                                execution_time=execution_time,
                                strategy_used=f"{kwargs.get('strategy', 'unknown')}_recovered",
                                retry_count=attempt + 1,
                            )

                            self.metrics.record_execution(exec_result)
                            logger.info(
                                f"Recovery successful for {tool_name} after {attempt + 1} attempts"
                            )
                            return exec_result

                    except Exception as recovery_error:
                        logger.warning(
                            f"Recovery failed for {tool_name}: {recovery_error}"
                        )

                # Calculate backoff delay with jitter
                if attempt < max_retries:
                    delay = initial_delay * (backoff_factor**attempt)
                    # Add jitter to prevent thundering herd
                    jitter = delay * 0.1 * (time.time() % 1)  # 0-10% jitter
                    total_delay = delay + jitter

                    logger.debug(f"Retrying {tool_name} in {total_delay:.2f}s")
                    await asyncio.sleep(total_delay)

        # All retries exhausted
        execution_time = time.time() - start_time
        exec_result = ExecutionResult(
            success=False,
            result=None,
            execution_time=execution_time,
            strategy_used=kwargs.get("strategy", "unknown"),
            retry_count=max_retries,
            error=str(last_error),
        )

        self.metrics.record_execution(exec_result)
        logger.error(f"All retry attempts exhausted for {tool_name}: {last_error}")

        return exec_result

    async def _execute_function(self, func: Callable, **kwargs) -> Any:
        """
        Execute function with proper async/sync handling.

        Args:
            func: Function to execute
            **kwargs: Arguments for the function

        Returns:
            Function result
        """
        if asyncio.iscoroutinefunction(func):
            return await func(**kwargs)
        else:
            return func(**kwargs)

    def get_metrics(self) -> Dict[str, Any]:
        """Get execution metrics."""
        return {
            "total_executions": self.metrics.total_executions,
            "success_rate": self.metrics.get_success_rate(),
            "average_execution_time": self.metrics.get_average_execution_time(),
            "retry_distribution": self.metrics.retry_counts,
            "strategy_performance": self.metrics.strategy_performance,
            "error_frequencies": self.metrics.error_frequencies,
        }


class DirectSubprocessExecutor:
    """
    Direct subprocess execution with improved reliability.

    Provides enhanced subprocess execution with proper environment handling,
    timeout management, and output processing.
    """

    def __init__(self):
        self.config = get_execution_config()

    async def execute_tool(
        self, tool_name: str, command: Optional[str] = None, **kwargs
    ) -> str:
        """
        Execute tool via direct subprocess.

        Args:
            tool_name: Name of the tool to execute
            command: Optional command override
            **kwargs: Tool arguments

        Returns:
            JSON string with execution result
        """
        timeout_config = self.config.get_timeout_config(tool_name, command)
        execution_timeout = timeout_config["execution_timeout"]

        try:
            # Build Python execution script
            python_script = self._build_execution_script(tool_name, kwargs)

            # Create temporary file for script
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".py", delete=False
            ) as temp_file:
                temp_file.write(python_script)
                temp_file_path = temp_file.name

            try:
                # Execute using direct Python path
                biomni_python_path = (
                    "/Users/<USER>/miniforge3/envs/biomni_e1/bin/python3"
                )
                cmd = [biomni_python_path, temp_file_path]

                # Prepare environment
                env = self._prepare_environment()

                logger.info(
                    f"Executing {tool_name} via direct subprocess (timeout: {execution_timeout}s)"
                )

                # Execute with proper timeout and resource management
                result = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env=env,
                )

                try:
                    stdout, stderr = await asyncio.wait_for(
                        result.communicate(), timeout=execution_timeout
                    )
                except asyncio.TimeoutError:
                    result.kill()
                    await result.wait()
                    raise TimeoutError(
                        f"Subprocess execution timed out after {execution_timeout}s"
                    )

                # Process result
                return self._process_execution_result(
                    tool_name, result.returncode, stdout.decode(), stderr.decode()
                )

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except Exception as e:
            logger.error(f"Direct subprocess execution failed for {tool_name}: {e}")
            return json.dumps(
                {
                    "success": False,
                    "error": f"Direct execution error: {str(e)}",
                    "tool": tool_name,
                    "execution_method": "direct_subprocess",
                }
            )

    def _build_execution_script(self, tool_name: str, kwargs: Dict[str, Any]) -> str:
        """
        Build Python execution script for the tool.

        Args:
            tool_name: Name of the tool
            kwargs: Tool arguments

        Returns:
            Python script as string
        """
        kwargs_str = repr(kwargs)

        return f"""
import sys
import os
import json
import traceback

# Add biomni module directory to path
biomni_module_path = "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
if biomni_module_path not in sys.path:
    sys.path.insert(0, biomni_module_path)

try:
    # Import the specific tool function from biomni module
    from biomni.database import {tool_name}
    
    # Execute the tool with provided arguments
    tool_kwargs = {kwargs_str}
    
    # Filter out None values for optional parameters
    filtered_kwargs = {{k: v for k, v in tool_kwargs.items() if v is not None}}
    
    result = {tool_name}(**filtered_kwargs)
    
    # Return the result as JSON with success marker
    output = {{
        "success": True, 
        "result": result, 
        "tool": "{tool_name}",
        "execution_method": "direct_subprocess_optimized"
    }}
    
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(output, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except ImportError as e:
    error_result = {{
        "success": False, 
        "error": f"Import error: {{str(e)}}", 
        "tool": "{tool_name}", 
        "args": {kwargs_str}
    }}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except Exception as e:
    error_result = {{
        "success": False, 
        "error": str(e), 
        "traceback": traceback.format_exc(), 
        "tool": "{tool_name}", 
        "args": {kwargs_str}
    }}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
"""

    def _prepare_environment(self) -> Dict[str, str]:
        """
        Prepare environment variables for subprocess execution.

        Returns:
            Environment dictionary
        """
        env = os.environ.copy()

        # Add required environment variables
        env.update(
            {
                "REASONING_PROVIDER": os.environ.get("REASONING_PROVIDER", "gemini"),
                "REASONING_API_KEY": os.environ.get("REASONING_API_KEY", ""),
                "REASONING_BASE_URL": os.environ.get("REASONING_BASE_URL", ""),
                "REASONING_MODEL": os.environ.get("REASONING_MODEL", "gemini-2.5-pro"),
                "PYTHONPATH": (
                    env.get("PYTHONPATH", "")
                    + ":/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
                ),
            }
        )

        return env

    def _process_execution_result(
        self, tool_name: str, return_code: int, stdout: str, stderr: str
    ) -> str:
        """
        Process subprocess execution result.

        Args:
            tool_name: Name of the executed tool
            return_code: Process return code
            stdout: Standard output
            stderr: Standard error output

        Returns:
            JSON string with processed result
        """
        if return_code == 0:
            # Success - extract result
            if (
                "=== BIOMNI_RESULT_START ===" in stdout
                and "=== BIOMNI_RESULT_END ===" in stdout
            ):
                start_marker = stdout.find("=== BIOMNI_RESULT_START ===")
                end_marker = stdout.find("=== BIOMNI_RESULT_END ===")

                if start_marker != -1 and end_marker != -1:
                    json_section = stdout[
                        start_marker + len("=== BIOMNI_RESULT_START ===") : end_marker
                    ].strip()

                    try:
                        # Validate JSON
                        parsed_result = json.loads(json_section)
                        return json.dumps(parsed_result, indent=2)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON result: {e}")

            # Fallback: return raw output
            return json.dumps(
                {
                    "success": True,
                    "result": stdout,
                    "tool": tool_name,
                    "execution_method": "direct_subprocess_raw",
                }
            )
        else:
            # Execution failed
            error_output = stderr or stdout
            return json.dumps(
                {
                    "success": False,
                    "error": f"Subprocess execution failed: {error_output}",
                    "tool": tool_name,
                    "return_code": return_code,
                    "execution_method": "direct_subprocess",
                }
            )


class DesktopCommanderExecutor:
    """
    Desktop-commander MCP execution with connection pooling.

    Provides enhanced MCP execution with connection reuse,
    session management, and improved error handling.
    """

    def __init__(self):
        self.config = get_execution_config()
        self.connection_pool = get_connection_pool()

    async def execute_tool(
        self, tool_name: str, command: Optional[str] = None, **kwargs
    ) -> str:
        """
        Execute tool via desktop-commander MCP.

        Args:
            tool_name: Name of the tool to execute
            command: Optional command override
            **kwargs: Tool arguments

        Returns:
            JSON string with execution result
        """
        timeout_config = self.config.get_timeout_config(tool_name, command)
        execution_timeout = timeout_config["execution_timeout"]

        # Use connection pooling for improved reliability
        async with self.connection_pool.get_async_connection(
            "desktop-commander", tool_name
        ) as connection:
            if connection is None:
                return json.dumps(
                    {
                        "success": False,
                        "error": "No available desktop-commander connection",
                        "tool": tool_name,
                    }
                )

            try:
                # Get MCP tools
                from .mcp_tools import MCPToolManager

                manager = MCPToolManager()
                tools = manager.get_all_tools()

                # Find process management tools
                start_process_tool = None
                read_process_tool = None

                for tool in tools:
                    if tool.name == "start_process":
                        start_process_tool = tool
                    elif tool.name == "read_process_output":
                        read_process_tool = tool

                if not start_process_tool or not read_process_tool:
                    return json.dumps(
                        {
                            "success": False,
                            "error": "Desktop commander process tools not found",
                            "tool": tool_name,
                        }
                    )

                # Build and execute command
                result = await self._execute_via_mcp(
                    tool_name,
                    kwargs,
                    start_process_tool,
                    read_process_tool,
                    execution_timeout,
                )

                return result

            except Exception as e:
                logger.error(f"Desktop-commander execution failed for {tool_name}: {e}")
                return json.dumps(
                    {
                        "success": False,
                        "error": f"MCP execution error: {str(e)}",
                        "tool": tool_name,
                        "execution_method": "desktop_commander",
                    }
                )

    async def _execute_via_mcp(
        self,
        tool_name: str,
        kwargs: Dict[str, Any],
        start_tool,
        read_tool,
        timeout: int,
    ) -> str:
        """
        Execute tool via MCP process tools.

        Args:
            tool_name: Name of the tool
            kwargs: Tool arguments
            start_tool: MCP start_process tool
            read_tool: MCP read_process_output tool
            timeout: Execution timeout in seconds

        Returns:
            JSON string with execution result
        """
        # Build execution script
        python_script = self._build_mcp_execution_script(tool_name, kwargs)

        # Create temporary file
        with tempfile.NamedTemporaryFile(
            mode="w", suffix=".py", delete=False
        ) as temp_file:
            temp_file.write(python_script)
            temp_file_path = temp_file.name

        try:
            # Start process
            biomni_python_path = "/Users/<USER>/miniforge3/envs/biomni_e1/bin/python3"

            start_result = start_tool.invoke(
                {
                    "command": biomni_python_path,
                    "args": [temp_file_path],
                    "timeout_ms": timeout * 1000,
                }
            )

            # Extract process ID
            import re

            process_id_match = re.search(
                r"Process started with PID (\d+)", str(start_result)
            )
            if not process_id_match:
                raise ValueError(f"Could not extract process ID from: {start_result}")

            process_id = int(process_id_match.group(1))
            logger.debug(f"Started MCP process {process_id} for {tool_name}")

            # Wait for completion with polling
            result = await self._wait_for_process_completion(
                read_tool, process_id, timeout
            )

            return self._process_mcp_result(tool_name, result)

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    def _build_mcp_execution_script(
        self, tool_name: str, kwargs: Dict[str, Any]
    ) -> str:
        """Build execution script for MCP execution."""
        # Similar to DirectSubprocessExecutor but with MCP-specific optimizations
        kwargs_str = repr(kwargs)

        return f"""
import sys
import os
import json
import traceback
import time

# Add biomni module directory to path
biomni_module_path = "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/src/tools"
if biomni_module_path not in sys.path:
    sys.path.insert(0, biomni_module_path)

# Add execution timestamp for MCP tracking
execution_start = time.time()

try:
    # Import the specific tool function from biomni module
    from biomni.database import {tool_name}
    
    # Execute the tool with provided arguments
    tool_kwargs = {kwargs_str}
    filtered_kwargs = {{k: v for k, v in tool_kwargs.items() if v is not None}}
    
    result = {tool_name}(**filtered_kwargs)
    
    execution_time = time.time() - execution_start
    
    # Return the result as JSON with success marker and MCP metadata
    output = {{
        "success": True, 
        "result": result, 
        "tool": "{tool_name}",
        "execution_method": "desktop_commander_optimized",
        "execution_time": execution_time,
        "process_id": os.getpid()
    }}
    
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(output, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except ImportError as e:
    error_result = {{
        "success": False, 
        "error": f"Import error: {{str(e)}}", 
        "tool": "{tool_name}", 
        "args": {kwargs_str},
        "execution_time": time.time() - execution_start
    }}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
    
except Exception as e:
    error_result = {{
        "success": False, 
        "error": str(e), 
        "traceback": traceback.format_exc(), 
        "tool": "{tool_name}", 
        "args": {kwargs_str},
        "execution_time": time.time() - execution_start
    }}
    print("=== BIOMNI_RESULT_START ===")
    print(json.dumps(error_result, indent=2))
    print("=== BIOMNI_RESULT_END ===")
"""

    async def _wait_for_process_completion(
        self, read_tool, process_id: int, timeout: int
    ) -> Optional[str]:
        """
        Wait for MCP process completion with improved polling.

        Args:
            read_tool: MCP read_process_output tool
            process_id: Process ID to monitor
            timeout: Maximum wait time in seconds

        Returns:
            Process output or None if timeout
        """
        start_time = time.time()
        poll_interval = 0.5

        while time.time() - start_time < timeout:
            try:
                output_result = read_tool.invoke({"pid": process_id})

                # Handle different result formats
                if isinstance(output_result, dict):
                    raw_output = output_result.get("output", str(output_result))
                else:
                    raw_output = str(output_result)

                # Check if we got actual output
                if raw_output and raw_output.strip():
                    return raw_output

            except Exception as e:
                # Process might have completed
                if "No active session found" in str(e):
                    logger.debug(f"Process {process_id} session ended")
                    break

                logger.debug(f"Process {process_id} read attempt failed: {e}")

            await asyncio.sleep(poll_interval)

        logger.warning(f"Process {process_id} timed out after {timeout}s")
        return None

    def _process_mcp_result(self, tool_name: str, raw_result: Optional[str]) -> str:
        """
        Process MCP execution result.

        Args:
            tool_name: Name of the executed tool
            raw_result: Raw output from MCP process

        Returns:
            JSON string with processed result
        """
        if raw_result is None:
            return json.dumps(
                {
                    "success": False,
                    "error": "Process timed out or no output received",
                    "tool": tool_name,
                    "execution_method": "desktop_commander",
                }
            )

        # Extract marked result section
        if (
            "=== BIOMNI_RESULT_START ===" in raw_result
            and "=== BIOMNI_RESULT_END ===" in raw_result
        ):
            start_marker = raw_result.find("=== BIOMNI_RESULT_START ===")
            end_marker = raw_result.find("=== BIOMNI_RESULT_END ===")

            if start_marker != -1 and end_marker != -1:
                json_section = raw_result[
                    start_marker + len("=== BIOMNI_RESULT_START ===") : end_marker
                ].strip()

                try:
                    parsed_result = json.loads(json_section)
                    return json.dumps(parsed_result, indent=2)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse MCP JSON result: {e}")

        # Fallback
        return json.dumps(
            {
                "success": False,
                "error": "Failed to parse process output",
                "raw_result": raw_result[:500],  # Truncate for logging
                "tool": tool_name,
                "execution_method": "desktop_commander",
            }
        )


# Global executor instances
retry_executor = RetryExecutor()
direct_executor = DirectSubprocessExecutor()
mcp_executor = DesktopCommanderExecutor()


def get_retry_executor() -> RetryExecutor:
    """Get the global retry executor instance."""
    return retry_executor


def get_direct_executor() -> DirectSubprocessExecutor:
    """Get the global direct subprocess executor instance."""
    return direct_executor


def get_mcp_executor() -> DesktopCommanderExecutor:
    """Get the global desktop-commander executor instance."""
    return mcp_executor
