"""
LangChain tool wrappers for R executor functionality.

Provides efficient R script execution tools for bioinformatics workflows,
leveraging pre-configured R environments and optimized package management.
"""

import logging
from typing import Dict, List, Optional, Union
from langchain_core.tools import tool
from ..tools.r_executor import get_r_executor

logger = logging.getLogger(__name__)

# Create a shared R executor instance for reuse across tool calls
_r_executor = None


def get_shared_r_executor():
    """Get or create a shared R executor instance."""
    global _r_executor
    if _r_executor is None:
        _r_executor = get_r_executor()
    return _r_executor


@tool
def execute_r_workflow(
    script_path: str,
    description: str = "R Script",
    working_dir: Optional[str] = None,
    timeout: int = 300,
) -> str:
    """
    Execute an R script from a file using a pre-configured R environment.

    This tool uses optimized R execution with package caching and environment management,
    significantly faster than generic command execution for R scripts.

    Args:
        script_path: The path to the R script file to execute
        description: Description of the script for logging purposes
        working_dir: Optional working directory for script execution
        timeout: Timeout in seconds (default: 300)

    Returns:
        String containing execution results or error messages
    """
    try:
        executor = get_shared_r_executor()
        with open(script_path, "r") as f:
            script_content = f.read()

        success, output = executor.execute_script(
            script_content=script_content,
            description=description,
            working_dir=working_dir,
            timeout=timeout,
        )

        if success:
            return f"✅ R Script '{description}' executed successfully:\n{output}"
        else:
            return f"❌ R Script '{description}' failed:\n{output}"

    except FileNotFoundError:
        error_msg = f"Error: Script file not found at {script_path}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Error executing R script '{description}': {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def install_r_packages(packages: str, bioconductor: bool = True) -> str:
    """
    Install R packages efficiently with smart caching.

    Checks if packages are already installed before attempting installation,
    reducing execution time for repeated runs.

    Args:
        packages: Comma-separated list of package names to install
        bioconductor: Whether to use Bioconductor for installation (default: True)

    Returns:
        String containing installation results
    """
    try:
        package_list = [pkg.strip() for pkg in packages.split(",")]
        executor = get_shared_r_executor()
        success, output = executor.install_packages(
            package_list, bioconductor=bioconductor
        )

        if success:
            return f"✅ Package installation completed:\n{output}"
        else:
            return f"❌ Package installation failed:\n{output}"

    except Exception as e:
        error_msg = f"Error installing R packages: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def setup_bioinformatics_environment() -> str:
    """
    Set up a minimal bioinformatics R environment by installing BiocManager.

    Installs and configures BiocManager, the package manager for Bioconductor.
    This prepares the environment for on-demand installation of other packages.

    Returns:
        String containing setup results and environment status
    """
    try:
        executor = get_shared_r_executor()
        success, output = executor.create_bioinformatics_environment()

        if success:
            return f"✅ Bioinformatics environment setup completed:\n{output}"
        else:
            return f"❌ Bioinformatics environment setup failed:\n{output}"

    except Exception as e:
        error_msg = f"Error setting up bioinformatics environment: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def validate_r_environment() -> str:
    """
    Validate R installation and environment configuration.

    Checks if R is properly installed and accessible, useful for troubleshooting
    execution issues.

    Returns:
        String containing validation results
    """
    try:
        executor = get_shared_r_executor()
        is_valid = executor.validate_r_installation()

        if is_valid:
            return "✅ R environment validation successful - R is properly configured and accessible"
        else:
            return "❌ R environment validation failed - please check R installation and configuration"

    except Exception as e:
        error_msg = f"Error validating R environment: {str(e)}"
        logger.error(error_msg)
        return error_msg


# List of all R tools for easy import
r_tools = [
    execute_r_workflow,
    install_r_packages,
    setup_bioinformatics_environment,
    validate_r_environment,
]
