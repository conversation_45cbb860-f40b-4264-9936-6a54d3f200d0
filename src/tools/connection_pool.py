"""
Connection pooling and session management for desktop-commander bridge.

This module provides efficient connection reuse and session management
to improve reliability and performance of cross-environment execution.
"""

import asyncio
import logging
import threading
import time
from collections import defaultdict
from contextlib import asynccontextmanager, contextmanager
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    """Connection state tracking."""

    IDLE = "idle"
    ACTIVE = "active"
    FAILED = "failed"
    CLOSED = "closed"


@dataclass
class ConnectionInfo:
    """Information about a pooled connection."""

    connection_id: str
    server_name: str
    created_at: float
    last_used: float
    state: ConnectionState
    usage_count: int
    failure_count: int
    session_data: Dict[str, Any]


class ConnectionPool:
    """
    Connection pool manager for MCP server connections.

    Provides connection reuse, health monitoring, and automatic cleanup
    to improve desktop-commander bridge reliability.
    """

    def __init__(
        self,
        max_connections_per_server: int = 3,
        connection_timeout: int = 30,
        idle_timeout: int = 300,
        health_check_interval: int = 60,
    ):
        """
        Initialize connection pool.

        Args:
            max_connections_per_server: Maximum connections per MCP server
            connection_timeout: Timeout for new connections (seconds)
            idle_timeout: Timeout for idle connections (seconds)
            health_check_interval: Interval between health checks (seconds)
        """
        self.max_connections_per_server = max_connections_per_server
        self.connection_timeout = connection_timeout
        self.idle_timeout = idle_timeout
        self.health_check_interval = health_check_interval

        # Connection tracking
        self._connections: Dict[str, List[ConnectionInfo]] = defaultdict(list)
        self._connection_counter = 0
        self._lock = threading.RLock()

        # Health monitoring
        self._health_check_task = None
        self._pool_metrics = {
            "total_connections": 0,
            "active_connections": 0,
            "failed_connections": 0,
            "connection_reuses": 0,
            "connection_failures": 0,
        }

        # Start health monitoring
        self._start_health_monitoring()

    def _start_health_monitoring(self):
        """Start background health monitoring task."""

        def health_monitor():
            while True:
                try:
                    self._cleanup_expired_connections()
                    self._log_pool_status()
                    time.sleep(self.health_check_interval)
                except Exception as e:
                    logger.error(f"Health monitor error: {e}")
                    time.sleep(30)  # Shorter retry on error

        monitor_thread = threading.Thread(target=health_monitor, daemon=True)
        monitor_thread.start()
        logger.info("Connection pool health monitoring started")

    def _cleanup_expired_connections(self):
        """Clean up expired and failed connections."""
        current_time = time.time()

        with self._lock:
            for server_name, connections in self._connections.items():
                # Filter out expired and failed connections
                active_connections = []
                expired_count = 0

                for conn in connections:
                    if conn.state == ConnectionState.FAILED or (
                        conn.state == ConnectionState.IDLE
                        and current_time - conn.last_used > self.idle_timeout
                    ):
                        expired_count += 1
                        continue
                    active_connections.append(conn)

                if expired_count > 0:
                    logger.debug(
                        f"Cleaned up {expired_count} expired connections for {server_name}"
                    )
                    self._connections[server_name] = active_connections
                    self._pool_metrics["failed_connections"] += expired_count

    def _log_pool_status(self):
        """Log current pool status for monitoring."""
        with self._lock:
            total_connections = sum(len(conns) for conns in self._connections.values())
            active_connections = sum(
                len([c for c in conns if c.state == ConnectionState.ACTIVE])
                for conns in self._connections.values()
            )

            self._pool_metrics["total_connections"] = total_connections
            self._pool_metrics["active_connections"] = active_connections

            if total_connections > 0:
                logger.debug(
                    f"Connection pool: {total_connections} total, "
                    f"{active_connections} active, "
                    f"{self._pool_metrics['connection_reuses']} reuses"
                )

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get current connection pool statistics."""
        with self._lock:
            stats = self._pool_metrics.copy()

            # Add current state
            for server_name, connections in self._connections.items():
                server_stats = {
                    "total": len(connections),
                    "active": len(
                        [c for c in connections if c.state == ConnectionState.ACTIVE]
                    ),
                    "idle": len(
                        [c for c in connections if c.state == ConnectionState.IDLE]
                    ),
                    "failed": len(
                        [c for c in connections if c.state == ConnectionState.FAILED]
                    ),
                }
                stats[f"server_{server_name}"] = server_stats

            return stats

    @contextmanager
    def get_connection(self, server_name: str, tool_name: str = ""):
        """
        Get a pooled connection for synchronous use.

        Args:
            server_name: Name of the MCP server
            tool_name: Name of the tool (for logging)

        Yields:
            ConnectionInfo object or None if no connection available
        """
        connection = None

        try:
            connection = self._acquire_connection(server_name, tool_name)
            yield connection

        except Exception as e:
            logger.error(f"Connection error for {server_name}/{tool_name}: {e}")
            if connection:
                self._mark_connection_failed(connection, str(e))
            raise

        finally:
            if connection:
                self._release_connection(connection)

    @asynccontextmanager
    async def get_async_connection(
        self, server_name: str, tool_name: str = ""
    ) -> AsyncGenerator[Optional[ConnectionInfo], None]:
        """
        Get a pooled connection for asynchronous use.

        Args:
            server_name: Name of the MCP server
            tool_name: Name of the tool (for logging)

        Yields:
            ConnectionInfo object or None if no connection available
        """
        connection = None

        try:
            connection = self._acquire_connection(server_name, tool_name)
            yield connection

        except Exception as e:
            logger.error(f"Async connection error for {server_name}/{tool_name}: {e}")
            if connection:
                self._mark_connection_failed(connection, str(e))
            raise

        finally:
            if connection:
                self._release_connection(connection)

    def _acquire_connection(
        self, server_name: str, tool_name: str
    ) -> Optional[ConnectionInfo]:
        """
        Acquire a connection from the pool.

        Args:
            server_name: Name of the MCP server
            tool_name: Name of the tool (for logging)

        Returns:
            ConnectionInfo object or None if pool is full
        """
        with self._lock:
            connections = self._connections[server_name]

            # Try to find an idle connection
            for conn in connections:
                if conn.state == ConnectionState.IDLE:
                    conn.state = ConnectionState.ACTIVE
                    conn.last_used = time.time()
                    conn.usage_count += 1
                    self._pool_metrics["connection_reuses"] += 1

                    logger.debug(
                        f"Reusing connection {conn.connection_id} for {server_name}/{tool_name}"
                    )
                    return conn

            # Create new connection if under limit
            if len(connections) < self.max_connections_per_server:
                self._connection_counter += 1
                new_conn = ConnectionInfo(
                    connection_id=f"{server_name}_{self._connection_counter}",
                    server_name=server_name,
                    created_at=time.time(),
                    last_used=time.time(),
                    state=ConnectionState.ACTIVE,
                    usage_count=1,
                    failure_count=0,
                    session_data={},
                )

                connections.append(new_conn)
                logger.debug(
                    f"Created new connection {new_conn.connection_id} for {server_name}/{tool_name}"
                )
                return new_conn

            # Pool is full
            logger.warning(
                f"Connection pool full for {server_name} (max {self.max_connections_per_server})"
            )
            return None

    def _release_connection(self, connection: ConnectionInfo):
        """
        Release a connection back to the pool.

        Args:
            connection: ConnectionInfo object to release
        """
        with self._lock:
            if connection.state == ConnectionState.ACTIVE:
                connection.state = ConnectionState.IDLE
                connection.last_used = time.time()
                logger.debug(f"Released connection {connection.connection_id}")

    def _mark_connection_failed(self, connection: ConnectionInfo, error: str):
        """
        Mark a connection as failed.

        Args:
            connection: ConnectionInfo object
            error: Error message
        """
        with self._lock:
            connection.state = ConnectionState.FAILED
            connection.failure_count += 1
            self._pool_metrics["connection_failures"] += 1

            logger.warning(
                f"Connection {connection.connection_id} marked as failed: {error}"
            )

    def invalidate_server_connections(self, server_name: str, reason: str = ""):
        """
        Invalidate all connections for a specific server.

        Args:
            server_name: Name of the MCP server
            reason: Reason for invalidation
        """
        with self._lock:
            connections = self._connections.get(server_name, [])
            failed_count = 0

            for conn in connections:
                if conn.state != ConnectionState.FAILED:
                    conn.state = ConnectionState.FAILED
                    failed_count += 1

            if failed_count > 0:
                logger.warning(
                    f"Invalidated {failed_count} connections for {server_name}: {reason}"
                )

    def clear_all_connections(self):
        """Clear all connections from the pool."""
        with self._lock:
            total_cleared = sum(len(conns) for conns in self._connections.values())
            self._connections.clear()

            logger.info(f"Cleared {total_cleared} connections from pool")

    def get_server_connection_count(self, server_name: str) -> Dict[str, int]:
        """
        Get connection count statistics for a specific server.

        Args:
            server_name: Name of the MCP server

        Returns:
            Dictionary with connection counts by state
        """
        with self._lock:
            connections = self._connections.get(server_name, [])

            return {
                "total": len(connections),
                "active": len(
                    [c for c in connections if c.state == ConnectionState.ACTIVE]
                ),
                "idle": len(
                    [c for c in connections if c.state == ConnectionState.IDLE]
                ),
                "failed": len(
                    [c for c in connections if c.state == ConnectionState.FAILED]
                ),
            }


class SessionManager:
    """
    Session management for persistent tool execution contexts.

    Provides session persistence and recovery for long-running operations
    that may span multiple tool calls.
    """

    def __init__(self, session_timeout: int = 1800):  # 30 minutes default
        """
        Initialize session manager.

        Args:
            session_timeout: Session timeout in seconds
        """
        self.session_timeout = session_timeout
        self._sessions: Dict[str, Dict[str, Any]] = {}
        self._session_lock = threading.RLock()

        # Start session cleanup
        self._start_session_cleanup()

    def _start_session_cleanup(self):
        """Start background session cleanup task."""

        def cleanup_sessions():
            while True:
                try:
                    current_time = time.time()

                    with self._session_lock:
                        expired_sessions = []

                        for session_id, session_data in self._sessions.items():
                            if (
                                current_time - session_data.get("last_used", 0)
                                > self.session_timeout
                            ):
                                expired_sessions.append(session_id)

                        for session_id in expired_sessions:
                            del self._sessions[session_id]

                        if expired_sessions:
                            logger.debug(
                                f"Cleaned up {len(expired_sessions)} expired sessions"
                            )

                    time.sleep(300)  # Check every 5 minutes

                except Exception as e:
                    logger.error(f"Session cleanup error: {e}")
                    time.sleep(60)  # Shorter retry on error

        cleanup_thread = threading.Thread(target=cleanup_sessions, daemon=True)
        cleanup_thread.start()
        logger.info("Session cleanup started")

    def create_session(
        self, session_id: str, initial_data: Dict[str, Any] = None
    ) -> str:
        """
        Create a new session.

        Args:
            session_id: Unique session identifier
            initial_data: Initial session data

        Returns:
            Session ID
        """
        with self._session_lock:
            session_data = {
                "created_at": time.time(),
                "last_used": time.time(),
                "data": initial_data or {},
                "tool_history": [],
            }

            self._sessions[session_id] = session_data
            logger.debug(f"Created session {session_id}")

            return session_id

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session data.

        Args:
            session_id: Session identifier

        Returns:
            Session data dictionary or None if not found
        """
        with self._session_lock:
            session = self._sessions.get(session_id)

            if session:
                session["last_used"] = time.time()
                return session.copy()

            return None

    def update_session(self, session_id: str, data: Dict[str, Any]):
        """
        Update session data.

        Args:
            session_id: Session identifier
            data: Data to update
        """
        with self._session_lock:
            if session_id in self._sessions:
                self._sessions[session_id]["data"].update(data)
                self._sessions[session_id]["last_used"] = time.time()
                logger.debug(f"Updated session {session_id}")

    def add_tool_history(self, session_id: str, tool_name: str, result: str):
        """
        Add tool execution to session history.

        Args:
            session_id: Session identifier
            tool_name: Name of the executed tool
            result: Tool execution result
        """
        with self._session_lock:
            if session_id in self._sessions:
                history_entry = {
                    "timestamp": time.time(),
                    "tool_name": tool_name,
                    "result_length": len(result),
                    "success": "error" not in result.lower(),
                }

                self._sessions[session_id]["tool_history"].append(history_entry)
                self._sessions[session_id]["last_used"] = time.time()

    def invalidate_session(self, session_id: str):
        """
        Invalidate and remove a session.

        Args:
            session_id: Session identifier
        """
        with self._session_lock:
            if session_id in self._sessions:
                del self._sessions[session_id]
                logger.debug(f"Invalidated session {session_id}")

    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get session statistics.

        Returns:
            Dictionary with session statistics
        """
        with self._session_lock:
            current_time = time.time()
            active_sessions = 0

            for session_data in self._sessions.values():
                if (
                    current_time - session_data["last_used"] < 300
                ):  # Active in last 5 minutes
                    active_sessions += 1

            return {
                "total_sessions": len(self._sessions),
                "active_sessions": active_sessions,
                "session_timeout": self.session_timeout,
            }


# Global instances
connection_pool = ConnectionPool()
session_manager = SessionManager()


def get_connection_pool() -> ConnectionPool:
    """Get the global connection pool instance."""
    return connection_pool


def get_session_manager() -> SessionManager:
    """Get the global session manager instance."""
    return session_manager
