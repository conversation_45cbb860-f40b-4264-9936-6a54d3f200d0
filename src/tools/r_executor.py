"""
R Script Executor for Bioinformatics Workflows

This module provides tools for executing R scripts safely and efficiently,
handling package installation, and managing R environments for bioinformatics analyses.
"""

import subprocess
import tempfile
import os
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class RExecutor:
    """
    Execute R scripts and manage R environments for bioinformatics analyses.
    """

    def __init__(
        self,
        r_binary: str = "R",
        rscript_binary: str = "Rscript",
        conda_env: str = None,
    ):
        # Use environment variable if not specified
        self.conda_env = conda_env or os.getenv("R_CONDA_ENV", "r-env")
        self.r_binary = r_binary
        self.rscript_binary = rscript_binary
        self.validate_r_installation()

    def validate_r_installation(self) -> bool:
        """Validate that R is properly installed and accessible."""
        try:
            # Check if running in Docker with mounted R environment
            if os.path.exists("/.dockerenv"):
                # In Docker, use mounted conda environment
                from ..config.env import R_ENV_PATH

                if R_ENV_PATH and os.path.exists(R_ENV_PATH) and self.conda_env:
                    # Try to use conda from mounted environment
                    conda_path = os.path.join(R_ENV_PATH, "bin", "conda")
                    r_binary_path = os.path.join(
                        R_ENV_PATH, "envs", self.conda_env, "bin", "R"
                    )

                    if os.path.exists(conda_path):
                        cmd = [
                            conda_path,
                            "run",
                            "-n",
                            self.conda_env,
                            "R",
                            "--version",
                        ]
                    elif os.path.exists(r_binary_path):
                        # Direct R binary from environment
                        cmd = [r_binary_path, "--version"]
                    else:
                        logger.warning(
                            f"Could not find R in mounted environment {R_ENV_PATH}"
                        )
                        cmd = ["R", "--version"]  # fallback
                else:
                    cmd = ["R", "--version"]
            else:
                # Local development - use conda if specified
                if self.conda_env:
                    cmd = [
                        "conda",
                        "run",
                        "-n",
                        self.conda_env,
                        self.r_binary,
                        "--version",
                    ]
                else:
                    cmd = [self.r_binary, "--version"]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                logger.info(
                    f"R installation validated: {result.stdout.split()[2] if len(result.stdout.split()) > 2 else 'version detected'}"
                )
                return True
            else:
                logger.error(f"R validation failed: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"R validation error: {str(e)}")
            return False

    def install_packages(
        self, packages: List[str], bioconductor: bool = True
    ) -> Tuple[bool, str]:
        """
        Install R packages, including Bioconductor packages.

        Args:
            packages: List of package names to install
            bioconductor: Whether to use Bioconductor for installation

        Returns:
            Tuple of (success, output/error message)
        """
        if bioconductor:
            install_script = f"""
# Install BiocManager if not present
if (!requireNamespace("BiocManager", quietly = TRUE))
    install.packages("BiocManager")

# Install packages
packages <- c({", ".join(f'"{pkg}"' for pkg in packages)})
for (pkg in packages) {{
    if (!requireNamespace(pkg, quietly = TRUE)) {{
        print(paste("Installing package:", pkg))
        BiocManager::install(pkg, update = FALSE, ask = FALSE)
    }} else {{
        print(paste("Package already installed:", pkg))
    }}
}}

print("Package installation completed")
"""
        else:
            install_script = f"""
# Install CRAN packages
packages <- c({", ".join(f'"{pkg}"' for pkg in packages)})
for (pkg in packages) {{
    if (!requireNamespace(pkg, quietly = TRUE)) {{
        print(paste("Installing package:", pkg))
        install.packages(pkg, repos = "https://cran.r-project.org")
    }} else {{
        print(paste("Package already installed:", pkg))
    }}
}}

print("Package installation completed")
"""

        return self.execute_script(install_script, description="Package Installation")

    def execute_script(
        self,
        script_content: str,
        description: str = "R Script",
        working_dir: Optional[str] = None,
        timeout: int = 300,
    ) -> Tuple[bool, str]:
        """
        Execute an R script and return results.

        Args:
            script_content: R script content to execute
            description: Description of the script for logging
            working_dir: Working directory for script execution
            timeout: Timeout in seconds

        Returns:
            Tuple of (success, output/error message)
        """
        logger.info(f"Executing R script: {description}")

        try:
            # Create temporary script file
            with tempfile.NamedTemporaryFile(mode="w", suffix=".R", delete=False) as f:
                f.write(script_content)
                script_path = f.name

            # Execute R script using conda environment if specified
            if os.path.exists("/.dockerenv"):
                # In Docker, use mounted conda environment
                from ..config.env import R_ENV_PATH

                if R_ENV_PATH and os.path.exists(R_ENV_PATH) and self.conda_env:
                    # Try different approaches for mounted environment
                    conda_path = os.path.join(R_ENV_PATH, "bin", "conda")
                    rscript_path = os.path.join(
                        R_ENV_PATH, "envs", self.conda_env, "bin", "Rscript"
                    )

                    if os.path.exists(conda_path):
                        cmd = [
                            conda_path,
                            "run",
                            "-n",
                            self.conda_env,
                            "Rscript",
                            script_path,
                        ]
                    elif os.path.exists(rscript_path):
                        # Direct Rscript binary from environment
                        cmd = [rscript_path, script_path]
                    else:
                        logger.warning(
                            f"Could not find Rscript in mounted environment {R_ENV_PATH}"
                        )
                        cmd = ["Rscript", script_path]  # fallback
                else:
                    cmd = ["Rscript", script_path]
            else:
                # Local development - use conda if specified
                if self.conda_env:
                    cmd = [
                        "conda",
                        "run",
                        "-n",
                        self.conda_env,
                        self.rscript_binary,
                        script_path,
                    ]
                else:
                    cmd = [self.rscript_binary, script_path]

            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=timeout, cwd=working_dir
            )

            # Clean up temporary file
            os.unlink(script_path)

            if result.returncode == 0:
                logger.info(f"R script '{description}' executed successfully")
                return True, result.stdout
            else:
                logger.error(f"R script '{description}' failed: {result.stderr}")
                return False, f"Error: {result.stderr}\nOutput: {result.stdout}"

        except subprocess.TimeoutExpired:
            logger.error(f"R script '{description}' timed out after {timeout} seconds")
            return False, f"Script execution timed out after {timeout} seconds"
        except Exception as e:
            logger.error(f"Error executing R script '{description}': {str(e)}")
            return False, f"Execution error: {str(e)}"

    def create_bioinformatics_environment(self) -> Tuple[bool, str]:
        """
        Set up a minimal bioinformatics R environment by installing BiocManager.
        """
        logger.info("Setting up minimal bioinformatics R environment...")

        # Only install BiocManager. Other packages should be installed on demand.
        # BiocManager is on CRAN, so bioconductor=False
        success, output = self.install_packages(["BiocManager"], bioconductor=False)

        if not success:
            # Check if it failed because it's already there, which is also a success.
            if "already installed" in output:
                logger.info("BiocManager is already installed.")
            else:
                return False, f"Failed to install BiocManager: {output}"

        # Test installation
        test_script = """
if (requireNamespace("BiocManager", quietly = TRUE)) {
    print("Environment setup successful! BiocManager is available.")
    print(paste("R version:", R.version.string))
    print(paste("Bioconductor version:", as.character(BiocManager::version())))
} else {
    print("Warning: BiocManager not found after installation attempt.")
}
"""
        test_success, test_output = self.execute_script(test_script, "Environment Test")

        full_output = (
            f"BiocManager Installation:\n{output}\n\nEnvironment Test:\n{test_output}"
        )

        return test_success, full_output


def get_r_executor() -> RExecutor:
    """Get a configured R executor instance."""
    return RExecutor()
