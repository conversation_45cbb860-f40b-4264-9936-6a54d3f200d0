import asyncio
import json
import logging
import threading
import time
import re
from queue import Queue, Empty
from typing import List, Any, Dict, Optional

import nest_asyncio
from langchain_core.tools import BaseTool, StructuredTool, tool
from langchain_mcp_adapters.client import MultiServerMCPClient

nest_asyncio.apply()

# Global client instance for connection reuse
_global_mcp_client: Optional[MultiServerMCPClient] = None
_global_mcp_config: Optional[Dict] = None

# Global tool cache for maximum efficiency
_tool_cache: Dict[str, List[BaseTool]] = {}  # server_name -> tools
_all_tools_cache: Optional[List[BaseTool]] = None  # all tools cached

# Global progress event system
_progress_event_queue: Queue = Queue()
_active_progress_threads: Dict[str, threading.Thread] = {}


class MCPToolManager:
    """Singleton manager for MCP tools with caching and connection reuse."""

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.logger = logging.getLogger(__name__)
            self.client = None
            self.config = None
            self.tool_cache = {}  # server_name -> List[BaseTool]
            self.all_tools_cache = None
            self.server_to_tools_map = {}  # server_name -> List[tool_names]
            MCPToolManager._initialized = True

    def _load_config(self) -> Optional[Dict]:
        """Load MCP configuration with Docker awareness."""
        from ..config.env import IS_DOCKER, MCP_WORKING_DIR

        config_file = "mcp_servers.json"

        try:
            with open(config_file, "r") as f:
                mcp_config = json.load(f)
        except FileNotFoundError:
            config_file = "mcp_servers_docker.json"
            try:
                with open(config_file, "r") as f:
                    mcp_config = json.load(f)
            except FileNotFoundError:
                self.logger.warning(
                    f"No mcp_servers.json or mcp_servers_docker.json file found, skipping MCP tools."
                )
                return None
        except json.JSONDecodeError:
            self.logger.error(f"Could not parse {config_file}, skipping MCP tools.")
            return None

        # Set working directory for MCP client if in Docker
        if IS_DOCKER:
            self.logger.debug(
                f"Docker environment detected, setting MCP working directory to: {MCP_WORKING_DIR}"
            )
            for server_name, server_config in mcp_config.items():
                if "env" not in server_config:
                    server_config["env"] = {}
                server_config["env"]["PWD"] = MCP_WORKING_DIR
                if server_name == "desktop-commander":
                    server_config["env"]["DESKTOP_COMMANDER_CWD"] = "/app"
                    server_config["env"][
                        "DESKTOP_COMMANDER_WORKSPACE"
                    ] = "/app/workspace"

        return mcp_config

    def _get_or_create_client(self) -> Optional[MultiServerMCPClient]:
        """Get or create MCP client with connection persistence, resilience, and timeout handling."""
        from ..config.mcp_mapping import (
            get_unreliable_servers,
            should_use_external_servers,
        )

        current_config = self._load_config()
        if current_config is None:
            return None

        # Filter out unreliable servers if external servers are disabled
        if not should_use_external_servers():
            unreliable_servers = get_unreliable_servers()
            original_count = len(current_config)
            current_config = {
                name: config
                for name, config in current_config.items()
                if name not in unreliable_servers
            }
            removed_count = original_count - len(current_config)
            if removed_count > 0:
                self.logger.info(
                    f"Filtered out {removed_count} unreliable external servers: {unreliable_servers}"
                )

        # Only create new client if config changed or client doesn't exist
        if self.client is None or self.config != current_config:
            self.logger.info(
                f"Creating persistent MCP client with {len(current_config)} servers: {list(current_config.keys())}"
            )

            # Add timeout and resilience configuration to each server
            resilient_config = self._add_resilience_config(current_config)

            try:
                self.client = MultiServerMCPClient(resilient_config)
                self.config = current_config.copy()
                # Clear cache when config changes
                self.tool_cache.clear()
                self.all_tools_cache = None
                self.server_to_tools_map.clear()
                self.logger.info(
                    "MCP client created successfully with resilience configuration"
                )
            except Exception as e:
                self.logger.error(f"Failed to create MCP client: {e}")
                return None
        else:
            self.logger.debug("Reusing persistent MCP client connection")

        return self.client

    def _add_resilience_config(self, config: Dict) -> Dict:
        """Add timeout and resilience configuration to server configs."""
        from ..config.execution_config import get_execution_config

        resilient_config = {}
        exec_config = get_execution_config()

        for server_name, server_config in config.items():
            # Copy the original config
            new_config = server_config.copy()

            # Add optimized timeout and retry settings
            if new_config.get("transport") != "stdio":
                # Use configurable timeouts based on server reliability
                base_timeout = 10
                if server_name == "desktop-commander":
                    base_timeout = 15  # Desktop-commander needs more time
                elif server_name in ["exa", "pubmed-mcp-server"]:
                    base_timeout = 20  # External servers need more time

                if "timeout" not in new_config:
                    new_config["timeout"] = base_timeout
                if "max_retries" not in new_config:
                    new_config["max_retries"] = (
                        3 if server_name == "desktop-commander" else 2
                    )
                if "connection_timeout" not in new_config:
                    new_config["connection_timeout"] = base_timeout // 2

                # Add buffer and process management settings for desktop-commander
                if server_name == "desktop-commander":
                    new_config["buffer_size"] = 8192  # Larger buffer for data transfer
                    new_config["process_cleanup_timeout"] = 30
                    new_config["session_keepalive"] = True

            # Add communication optimization settings
            new_config["communication_protocol"] = "optimized"
            new_config["data_compression"] = (
                True if server_name == "desktop-commander" else False
            )
            new_config["batch_operations"] = (
                True if server_name in ["desktop-commander", "context7"] else False
            )

            resilient_config[server_name] = new_config

        return resilient_config

    def close_connections(self):
        """Gracefully close MCP client connections (for cleanup)."""
        if self.client:
            try:
                # Note: MultiServerMCPClient might not have explicit close method
                # This is a placeholder for connection cleanup if needed
                self.logger.debug("Closing MCP client connections")
                self.client = None
                self.config = None
            except Exception as e:
                self.logger.warning(f"Error closing MCP client: {e}")

    def keep_connection_alive(self):
        """Ensure MCP connections stay alive during tool operations."""
        if self.client is None:
            self._get_or_create_client()
        # Connections are kept alive through the persistent client instance

    async def _load_all_tools_async(self) -> List[BaseTool]:
        """Load all tools from MCP client with timeout and error handling."""
        client = self._get_or_create_client()
        if client is None:
            self.logger.warning("No MCP client available, returning empty tools list")
            return []

        # Initialize all variables at the start to prevent NameError
        async_tools = []
        sync_tools = []
        failed_tools = []

        try:
            self.logger.debug("Attempting to load tools from MCP client...")
            # Add timeout to tool loading to prevent hanging
            async_tools = await asyncio.wait_for(client.get_tools(), timeout=30.0)
            self.logger.info(
                f"Successfully loaded {len(async_tools)} async tools from MCP servers"
            )
        except asyncio.TimeoutError:
            self.logger.error(
                "Timeout loading tools from MCP servers (30s limit exceeded)"
            )
            return []
        except UnboundLocalError as e:
            # Handle specific bug in langchain-mcp-adapters when external servers fail
            self.logger.error(f"External server failure triggered MCP client bug: {e}")
            self.logger.warning(
                "Falling back to local servers only due to external server failures"
            )

            # Try again with only local servers by recreating client
            try:
                from ..config.mcp_mapping import get_unreliable_servers

                original_config = self.config.copy() if self.config else {}
                unreliable_servers = get_unreliable_servers()
                local_config = {
                    name: config
                    for name, config in original_config.items()
                    if name not in unreliable_servers
                }
                if local_config and len(local_config) > 0:
                    self.logger.info(
                        f"Attempting fallback with {len(local_config)} local servers: {list(local_config.keys())}"
                    )
                    # Create new client with only local servers
                    from langchain_mcp_adapters.client import MultiServerMCPClient

                    resilient_local_config = self._add_resilience_config(local_config)
                    local_client = MultiServerMCPClient(resilient_local_config)
                    async_tools = await asyncio.wait_for(
                        local_client.get_tools(), timeout=15.0
                    )
                    self.logger.info(
                        f"Fallback successful: loaded {len(async_tools)} tools from local servers"
                    )
                    # Update client to the working local-only client
                    self.client = local_client
                    self.config = local_config.copy()
                else:
                    self.logger.error("No local servers available for fallback")
                    return []
            except Exception as fallback_error:
                self.logger.error(
                    f"Fallback to local servers also failed: {fallback_error}"
                )
                return []
        except Exception as e:
            self.logger.error(
                f"Error loading tools from MCP servers: {type(e).__name__}: {e}"
            )
            # Log the full traceback for debugging
            import traceback

            self.logger.debug(f"Full traceback: {traceback.format_exc()}")
            return []

        # Validate that we have tools to convert
        if not async_tools:
            self.logger.warning("No async tools to convert, returning empty list")
            return []

        # Convert to sync tools with error handling for individual tools
        self.logger.debug(
            f"Converting {len(async_tools)} async tools to sync wrappers..."
        )

        for async_tool in async_tools:
            try:
                if isinstance(async_tool, StructuredTool):
                    # Reduced verbosity: only log at debug level
                    self.logger.debug(
                        f"Converting async tool {async_tool.name} to sync wrapper"
                    )
                    sync_tool = create_sync_wrapper(async_tool)
                    sync_tools.append(sync_tool)
                else:
                    self.logger.debug(
                        f"Using async tool {getattr(async_tool, 'name', 'unknown')} directly"
                    )
                    sync_tools.append(async_tool)
            except Exception as e:
                tool_name = getattr(async_tool, "name", "unknown")
                self.logger.warning(
                    f"Failed to convert tool '{tool_name}' to sync: {e}"
                )
                failed_tools.append(tool_name)

        if failed_tools:
            self.logger.warning(
                f"Failed to load {len(failed_tools)} tools: {failed_tools}"
            )

        self.logger.info(
            f"Successfully converted {len(sync_tools)} tools to sync wrappers"
        )

        # Build server-to-tools mapping for efficient filtering
        # Note: This is simplified - in practice you'd need tool metadata to know which server each tool comes from
        # For now, we'll use a heuristic based on tool names/descriptions
        if sync_tools:
            self._build_server_mapping(sync_tools)
        else:
            self.logger.warning("No sync tools available for server mapping")

        return sync_tools

    def _build_server_mapping(self, tools: List[BaseTool]):
        """Build mapping of server names to tool names for efficient filtering."""
        server_tools = {
            "fetch": [],
            "context7": [],
            "exa": [],
            "desktop-commander": [],
            "pubmed-mcp-server": [],
            "todo-md-mcp": [],
        }

        for tool in tools:
            tool_name = tool.name.lower()
            assigned = False

            # Map tools to servers based on precise naming patterns
            # Fetch server - only fetch tool
            if tool_name == "fetch":
                server_tools["fetch"].append(tool.name)
                assigned = True

            # Context7 server - library and documentation tools
            elif any(
                pattern in tool_name
                for pattern in ["library", "resolve", "docs", "context7"]
            ):
                server_tools["context7"].append(tool.name)
                assigned = True

            # Exa server - search and research tools (very specific patterns)
            elif any(
                pattern in tool_name
                for pattern in [
                    "_exa",
                    "search_exa",
                    "research_paper_search",
                    "company_research",
                    "crawling_exa",
                    "competitor_finder",
                    "linkedin_search",
                    "wikipedia_search",
                    "github_search",
                ]
            ):
                server_tools["exa"].append(tool.name)
                assigned = True

            # PubMed server - pubmed tools
            elif "pubmed" in tool_name:
                server_tools["pubmed-mcp-server"].append(tool.name)
                assigned = True

            # Todo-md-mcp server - todo tools
            elif "todo" in tool_name:
                server_tools["todo-md-mcp"].append(tool.name)
                assigned = True

            # Desktop-commander - file system and execution tools
            elif any(
                pattern in tool_name
                for pattern in [
                    "read_",
                    "write_",
                    "create_",
                    "list_",
                    "move_",
                    "search_",
                    "get_",
                    "edit_",
                    "execute_",
                    "kill_",
                    "force_",
                ]
            ) or tool_name in ["get_config", "set_config_value"]:
                server_tools["desktop-commander"].append(tool.name)
                assigned = True

            # If not assigned, check description for clues
            if not assigned and hasattr(tool, "description") and tool.description:
                desc_lower = tool.description.lower()
                if (
                    "file" in desc_lower
                    or "directory" in desc_lower
                    or "command" in desc_lower
                    or "terminal" in desc_lower
                ):
                    server_tools["desktop-commander"].append(tool.name)
                    assigned = True

            # Last resort: assign to desktop-commander
            if not assigned:
                server_tools["desktop-commander"].append(tool.name)
                self.logger.warning(
                    f"Tool '{tool.name}' assigned to desktop-commander by default"
                )

        self.server_to_tools_map = server_tools

        # Log the mapping for verification
        for server, tools_list in server_tools.items():
            if tools_list:
                self.logger.info(
                    f"Server '{server}' provides {len(tools_list)} tools: {tools_list[:3]}{'...' if len(tools_list) > 3 else ''}"
                )

        self.logger.debug(
            f"Complete server-to-tools mapping: {self.server_to_tools_map}"
        )

    def get_tools_for_servers(self, server_names: List[str]) -> List[BaseTool]:
        """Get tools filtered by server names with caching."""
        if not server_names:
            return self.get_all_tools()

        # Check if we have cached tools for this exact server combination
        cache_key = ",".join(sorted(server_names))
        if cache_key in self.tool_cache:
            self.logger.debug(f"Returning cached tools for servers: {server_names}")
            return self.tool_cache[cache_key]

        # Get all tools first
        all_tools = self.get_all_tools()

        # Filter tools by server names
        filtered_tools = []
        for tool in all_tools:
            # Check if this tool belongs to any of the requested servers
            for server_name in server_names:
                if server_name in self.server_to_tools_map:
                    if tool.name in self.server_to_tools_map[server_name]:
                        filtered_tools.append(tool)
                        break

        # Cache the filtered result
        self.tool_cache[cache_key] = filtered_tools
        self.logger.info(
            f"Using {len(filtered_tools)} tools for servers {server_names}"
        )
        self.logger.debug(f"Tool details: {[t.name for t in filtered_tools]}")

        return filtered_tools

    def get_essential_tools_for_agent(
        self, agent_name: str, lazy: bool = False
    ) -> List[BaseTool]:
        """Get only essential tools for a specific agent (ultra-optimized filtering with lazy loading)."""
        from ..config.mcp_mapping import ESSENTIAL_TOOLS_BY_AGENT

        if agent_name not in ESSENTIAL_TOOLS_BY_AGENT:
            self.logger.warning(
                f"No essential tools mapping found for agent: {agent_name}"
            )
            return []

        # Check cache first
        cache_key = f"essential_{agent_name}"
        if cache_key in self.tool_cache:
            self.logger.debug(
                f"Returning cached essential tools for agent: {agent_name}"
            )
            return self.tool_cache[cache_key]

        if lazy:
            # For lazy loading, create tool placeholders that will load on first use
            essential_mapping = ESSENTIAL_TOOLS_BY_AGENT[agent_name]
            essential_tool_names = set()
            for server_name, tool_names in essential_mapping.items():
                essential_tool_names.update(tool_names)

            # Create placeholder tools that will lazy load
            placeholder_tools = []
            for tool_name in essential_tool_names:
                placeholder_tool = self._create_lazy_tool_placeholder(tool_name)
                placeholder_tools.append(placeholder_tool)

            # Cache the placeholders
            self.tool_cache[cache_key] = placeholder_tools
            self.logger.info(
                f"Agent '{agent_name}' using {len(placeholder_tools)} lazy-loaded essential tools"
            )
            return placeholder_tools

        # Regular loading: get all tools first
        all_tools = self.get_all_tools()

        # Get essential tools mapping for this agent
        essential_mapping = ESSENTIAL_TOOLS_BY_AGENT[agent_name]

        # Build list of essential tool names
        essential_tool_names = set()
        for server_name, tool_names in essential_mapping.items():
            essential_tool_names.update(tool_names)

        # Filter to only essential tools
        essential_tools = []
        for tool in all_tools:
            if tool.name in essential_tool_names:
                essential_tools.append(tool)

        # Cache the result
        self.tool_cache[cache_key] = essential_tools
        self.logger.info(
            f"Agent '{agent_name}' using {len(essential_tools)} essential tools: {[t.name for t in essential_tools]}"
        )

        return essential_tools

    def _create_lazy_tool_placeholder(self, tool_name: str):
        """Create a placeholder tool that will load the actual tool on first use."""
        from langchain_core.tools import StructuredTool

        def lazy_tool_func(**kwargs):
            # When the tool is actually called, force load all tools and find the real one
            self.logger.debug(f"Lazy loading tool '{tool_name}' on first use")
            all_tools = self.force_load_tools()

            real_tool = next(
                (tool for tool in all_tools if tool.name == tool_name), None
            )
            if real_tool:
                return real_tool.invoke(kwargs)
            else:
                raise ValueError(f"Tool '{tool_name}' not found after lazy loading")

        return StructuredTool.from_function(
            func=lazy_tool_func,
            name=tool_name,
            description=f"Lazy-loaded tool: {tool_name}",
        )

    def get_all_tools(self, lazy: bool = False) -> List[BaseTool]:
        """Get all MCP tools with caching and optional lazy loading."""
        if self.all_tools_cache is not None:
            self.logger.debug("Returning cached all tools")
            return self.all_tools_cache

        if lazy:
            # Return empty list for lazy loading - tools will be loaded on first actual use
            self.logger.debug("Lazy loading enabled - deferring tool loading")
            return []

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                nest_asyncio.apply()

            self.all_tools_cache = loop.run_until_complete(self._load_all_tools_async())
            self.logger.info(f"Cached {len(self.all_tools_cache)} total MCP tools")
            return self.all_tools_cache

        except Exception as e:
            self.logger.error(f"Failed to load MCP tools: {e}")
            import traceback

            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return []

    def force_load_tools(self) -> List[BaseTool]:
        """Force load tools even in lazy mode (for when tools are actually needed)."""
        if self.all_tools_cache is not None:
            return self.all_tools_cache

        self.logger.info("Force loading MCP tools on first use")
        return self.get_all_tools(lazy=False)

    def clear_cache(self):
        """Clear all tool caches (useful for testing or config changes)."""
        self.tool_cache.clear()
        self.all_tools_cache = None
        self.server_to_tools_map.clear()
        self.logger.info("Cleared all tool caches")


# Singleton instance
_tool_manager = MCPToolManager()


def emit_progress_event(event_type: str, tool_name: str, message: str, **kwargs):
    """Emit a progress event to the global queue."""
    event = {
        "event_type": event_type,
        "tool_name": tool_name,
        "message": message,
        "timestamp": time.time(),
        **kwargs,
    }
    try:
        _progress_event_queue.put_nowait(event)
    except Exception:
        pass  # Graceful degradation - don't break tool execution


def get_progress_events() -> List[Dict]:
    """Get all pending progress events from the queue."""
    events = []
    try:
        while True:
            event = _progress_event_queue.get_nowait()
            events.append(event)
    except Empty:
        pass
    return events


def _parse_r_output_line(line: str) -> Optional[str]:
    """Parse a line of R output and convert to user-friendly progress message."""
    line = line.strip()
    if not line:
        return None

    # Remove R prompt and common prefixes
    line = re.sub(r"^>\s*", "", line)
    line = re.sub(r"^\[1\]\s*", "", line)

    # Package installation patterns
    if "Installing package" in line:
        match = re.search(r"Installing package[s]?\s+['\"]([^'\"]+)['\"]", line)
        if match:
            return f"📦 Installing R package: {match.group(1)}"
        return "📦 Installing R packages..."

    if "BiocManager::install" in line:
        return "📦 Installing Bioconductor packages..."

    if "install.packages" in line:
        return "📦 Installing R packages..."

    # Data loading patterns
    if "Loading" in line and ("data" in line.lower() or "file" in line.lower()):
        return f"📊 {line}"

    if "Reading" in line and any(
        ext in line for ext in [".csv", ".txt", ".tsv", ".data"]
    ):
        return f"📊 Reading data file..."

    if re.search(r"\d+\s+(genes?|probes?|samples?)", line):
        return f"📊 {line}"

    # Processing patterns
    if any(
        word in line.lower()
        for word in ["processing", "analyzing", "computing", "calculating"]
    ):
        return f"🔄 {line}"

    # Error patterns
    if any(word in line.lower() for word in ["error", "failed", "cannot", "unable"]):
        return f"🚨 R Error: {line}"

    # Warning patterns
    if "warning" in line.lower():
        return f"⚠️ R Warning: {line}"

    # Progress indicators
    if "%" in line or "progress" in line.lower():
        return f"📈 {line}"

    # Filter out common R noise
    noise_patterns = [
        r"^\s*$",  # Empty lines
        r"^#",  # Comments
        r"^\s*\.",  # R continuation dots
        r"^Loading required package",  # Package loading messages
        r"^Attaching package",  # Package attach messages
    ]

    for pattern in noise_patterns:
        if re.match(pattern, line):
            return None

    # If line contains useful information but doesn't match patterns, include it
    if len(line) > 10 and any(char.isalpha() for char in line):
        return f"ℹ️ {line}"

    return None


def _is_r_command(command: str) -> bool:
    """Check if a command is an R script execution."""
    return any(
        indicator in command.lower()
        for indicator in ["rscript", "r -e", '.r"', ".r ", "r --", "/r ", "\\r "]
    )


def _heartbeat_worker(
    tool_name: str,
    thread_id: str,
    operation_context: str = "",
    heartbeat_interval: int = 15,
):
    """Background worker that emits heartbeat events for long-running operations."""
    logger = logging.getLogger(__name__)
    try:
        # Wait initial period before first heartbeat
        time.sleep(30)  # Only start heartbeats after 30 seconds

        heartbeat_count = 0
        while thread_id in _active_progress_threads:
            heartbeat_count += 1
            total_seconds = heartbeat_count * heartbeat_interval + 30

            # Create contextual heartbeat messages based on operation and duration
            if "Loading gene expression dataset" in operation_context:
                if heartbeat_count == 1:
                    message = "Processing large gene expression matrix (this may take 2-3 minutes)..."
                elif heartbeat_count <= 4:  # Up to 2 minutes
                    message = f"Still loading expression data - {total_seconds}s elapsed (normal for large datasets)..."
                elif heartbeat_count <= 8:  # Up to 4 minutes
                    message = f"⚠️ Data loading taking longer than expected ({total_seconds}s) - large dataset or missing packages..."
                else:  # Over 4 minutes
                    message = f"🚨 Operation unusually slow ({total_seconds}s) - possible issues with data file or R setup..."

            elif "Installing R" in operation_context:
                if heartbeat_count == 1:
                    message = (
                        "Installing R packages from BiocManager (first-time setup)..."
                    )
                elif heartbeat_count <= 6:  # Up to 3 minutes
                    message = f"Still installing R packages - {total_seconds}s elapsed (normal for bioinformatics packages)..."
                else:  # Over 3 minutes
                    message = f"⚠️ Package installation slow ({total_seconds}s) - checking network or repository issues..."

            elif "differential expression" in operation_context.lower():
                if heartbeat_count == 1:
                    message = (
                        "Running statistical analysis for differential expression..."
                    )
                elif heartbeat_count <= 6:  # Up to 3 minutes
                    message = f"Computing gene statistics - {total_seconds}s elapsed (normal for large gene sets)..."
                else:  # Over 3 minutes
                    message = (
                        f"⚠️ Analysis taking longer than expected ({total_seconds}s)..."
                    )

            elif "R" in operation_context:
                if heartbeat_count == 1:
                    message = f"{operation_context} - this may take a few minutes..."
                elif heartbeat_count <= 4:  # Up to 2 minutes
                    message = f"R script still running - {total_seconds}s elapsed..."
                else:  # Over 2 minutes
                    message = f"⚠️ R operation slow ({total_seconds}s) - checking for issues..."

            else:
                # Generic fallback
                if heartbeat_count == 1:
                    message = f"Tool '{tool_name}' is still processing..."
                elif heartbeat_count <= 3:
                    message = f"Tool '{tool_name}' continues working..."
                else:
                    message = f"Tool '{tool_name}' is handling a complex operation (running for {total_seconds}s)..."

            emit_progress_event("tool_heartbeat", tool_name, message)
            logger.debug(
                f"Emitted heartbeat for {tool_name} (count: {heartbeat_count})"
            )

            time.sleep(heartbeat_interval)

    except Exception as e:
        logger.debug(f"Heartbeat worker error for {tool_name}: {e}")
    finally:
        logger.debug(f"Heartbeat worker stopped for {tool_name}")


def _detect_operation_context(tool_name: str, kwargs: Dict) -> str:
    """Detect the type of operation for better progress context."""
    # Handle various command execution tool names
    if tool_name in ["execute_bash", "run_bash", "execute_command", "bash"]:
        command = kwargs.get("command", "")

        # R-specific detection
        if "Rscript" in command or "R -e" in command or command.endswith(".R"):
            if "BiocManager::install" in command or "install.packages" in command:
                return "Installing R bioinformatics packages"
            elif "01_load_data.R" in command or "load_data" in command:
                return "Loading gene expression dataset"
            elif "02_preprocessing.R" in command or "preprocessing" in command:
                return "Preprocessing expression data"
            elif "03_differential_expression.R" in command or "differential" in command:
                return "Running differential expression analysis"
            elif "04_functional_enrichment.R" in command or "enrichment" in command:
                return "Performing functional enrichment analysis"
            elif "read.csv" in command or "read.delim" in command or "load(" in command:
                return "Loading data files"
            elif "limma" in command:
                return "Running limma differential expression"
            else:
                return "Executing R bioinformatics script"

        # Python-specific detection
        elif "python" in command.lower():
            if "analysis" in command:
                return "Running Python analysis script"
            else:
                return "Executing Python script"

        # General system commands
        else:
            return "Executing system command"

    elif "python" in tool_name.lower():
        return "Running Python code"
    elif "r_" in tool_name.lower() or "rscript" in tool_name.lower():
        return "Executing R analysis"
    else:
        return f"Using {tool_name}"


async def _execute_with_r_streaming(async_tool: StructuredTool, kwargs: Dict) -> str:
    """Execute R commands with real-time output streaming."""
    logger = logging.getLogger(__name__)

    command = kwargs.get("command", "")

    try:
        # For R commands, we try to capture streaming output
        # Note: This is a simplified approach - actual implementation depends on MCP tool capabilities
        logger.info(f"Executing R command with streaming: {command[:100]}...")

        # Execute the tool normally but try to simulate streaming
        # In a real implementation, this would need MCP tool support for streaming
        result = await async_tool.ainvoke(kwargs)

        # Parse the final result for R output
        if isinstance(result, str):
            lines = result.split("\n")
            for line in lines:
                parsed_line = _parse_r_output_line(line)
                if parsed_line:
                    emit_progress_event("tool_progress", async_tool.name, parsed_line)
                    # Small delay to simulate streaming
                    await asyncio.sleep(0.1)

        return result

    except Exception as e:
        logger.error(f"Error in R streaming execution: {e}")
        raise


def create_sync_wrapper(async_tool: StructuredTool) -> BaseTool:
    """Create a sync-compatible wrapper for an async MCP tool."""
    logger = logging.getLogger(__name__)

    def sync_func(**kwargs) -> str:
        """Synchronous wrapper that runs the async tool with progress monitoring."""
        thread_id = f"{async_tool.name}_{int(time.time() * 1000)}"
        heartbeat_thread = None

        try:
            logger.debug(f"Executing MCP tool '{async_tool.name}' with args: {kwargs}")

            # Detect operation context for better progress messages
            operation_context = _detect_operation_context(async_tool.name, kwargs)
            command = kwargs.get("command", "")

            # Emit initial progress event
            emit_progress_event(
                "tool_progress", async_tool.name, f"Starting: {operation_context}"
            )

            # Check if this is an R command for special handling
            is_r_cmd = _is_r_command(command)

            # Start heartbeat thread for progress monitoring (reduced frequency for R commands)
            heartbeat_interval = 10 if is_r_cmd else 15
            heartbeat_thread = threading.Thread(
                target=_heartbeat_worker,
                args=(
                    async_tool.name,
                    thread_id,
                    operation_context,
                    heartbeat_interval,
                ),
                daemon=True,
            )
            _active_progress_threads[thread_id] = heartbeat_thread
            heartbeat_thread.start()

            # Execute the tool
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, use nest_asyncio
                import nest_asyncio

                nest_asyncio.apply()

            # Use streaming execution for R commands
            if is_r_cmd:
                result = loop.run_until_complete(
                    _execute_with_r_streaming(async_tool, kwargs)
                )
            else:
                result = loop.run_until_complete(async_tool.ainvoke(kwargs))

            logger.debug(
                f"MCP tool '{async_tool.name}' completed - result length: {len(str(result))}"
            )

            # Emit completion progress event
            emit_progress_event(
                "tool_progress", async_tool.name, f"Completed: {operation_context}"
            )

            return result

        except Exception as e:
            error_msg = f"Error executing {async_tool.name}: {str(e)}"
            logger.error(error_msg)

            # Emit error progress event
            emit_progress_event(
                "tool_progress",
                async_tool.name,
                f"Error in {operation_context}: {str(e)}",
            )

            return error_msg

        finally:
            # Clean up heartbeat thread
            try:
                if thread_id in _active_progress_threads:
                    del _active_progress_threads[thread_id]
                if heartbeat_thread and heartbeat_thread.is_alive():
                    # Thread will naturally exit when thread_id is removed from _active_progress_threads
                    pass
            except Exception as cleanup_error:
                logger.debug(f"Error during heartbeat cleanup: {cleanup_error}")
                # Don't let cleanup errors affect the main result

    # Create a new tool with the same schema but sync execution
    return StructuredTool.from_function(
        func=sync_func,
        name=async_tool.name,
        description=async_tool.description,
        args_schema=async_tool.args_schema,
    )


# Legacy function - now handled by MCPToolManager
def _get_or_create_mcp_client() -> MultiServerMCPClient:
    """Legacy function for backward compatibility. Use MCPToolManager instead."""
    return _tool_manager._get_or_create_client()


def def_mcp_tools(server_names: List[str] = None) -> List[BaseTool]:
    """Builds and returns a list of MCP tools, optionally filtered by server names.

    Now uses the singleton MCPToolManager for efficient caching and connection reuse.
    """
    return _tool_manager.get_tools_for_servers(server_names or [])
