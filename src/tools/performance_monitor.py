"""
Performance monitoring and metrics collection for cross-environment execution.

This module provides comprehensive performance tracking, alerting, and
optimization insights for biomni tools and MCP operations.
"""

import time
import threading
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json
import statistics

logger = logging.getLogger(__name__)


@dataclass
class ExecutionMetric:
    """Single execution metric record."""

    tool_name: str
    execution_time: float
    success: bool
    strategy_used: str
    error_message: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    data_size: int = 0  # Size of data transferred
    retry_count: int = 0


@dataclass
class PerformanceAlert:
    """Performance alert record."""

    alert_type: str
    tool_name: str
    metric_value: float
    threshold: float
    message: str
    timestamp: float = field(default_factory=time.time)


class PerformanceTracker:
    """
    Real-time performance tracking for tool executions.

    Provides detailed metrics collection, analysis, and alerting
    for execution performance optimization.
    """

    def __init__(self, retention_hours: int = 24):
        """
        Initialize performance tracker.

        Args:
            retention_hours: How long to retain metrics data
        """
        self.retention_hours = retention_hours
        self.retention_seconds = retention_hours * 3600

        # Metrics storage
        self.execution_metrics: deque = deque()
        self.tool_metrics: Dict[str, List[ExecutionMetric]] = defaultdict(list)
        self.strategy_metrics: Dict[str, List[ExecutionMetric]] = defaultdict(list)

        # Alert system
        self.alerts: deque = deque()
        self.alert_thresholds = {
            "execution_time": 30.0,  # Alert if execution > 30s
            "failure_rate": 0.3,  # Alert if failure rate > 30%
            "timeout_rate": 0.2,  # Alert if timeout rate > 20%
            "retry_rate": 0.4,  # Alert if retry rate > 40%
        }

        # Performance statistics cache
        self._stats_cache = {}
        self._cache_expiry = 0
        self._cache_ttl = 60  # Cache for 60 seconds

        # Thread safety
        self._lock = threading.RLock()

        # Start background cleanup
        self._start_cleanup_thread()

    def record_execution(self, metric: ExecutionMetric):
        """
        Record a tool execution metric.

        Args:
            metric: ExecutionMetric to record
        """
        with self._lock:
            # Add to global metrics
            self.execution_metrics.append(metric)

            # Add to tool-specific metrics
            self.tool_metrics[metric.tool_name].append(metric)

            # Add to strategy-specific metrics
            self.strategy_metrics[metric.strategy_used].append(metric)

            # Check for alerts
            self._check_performance_alerts(metric)

            # Invalidate cache
            self._stats_cache.clear()
            self._cache_expiry = 0

        logger.debug(
            f"Recorded execution metric for {metric.tool_name}: "
            f"{metric.execution_time:.2f}s, success={metric.success}"
        )

    def _check_performance_alerts(self, metric: ExecutionMetric):
        """
        Check if the metric triggers any performance alerts.

        Args:
            metric: ExecutionMetric to check
        """
        current_time = time.time()

        # Check execution time alert
        if metric.execution_time > self.alert_thresholds["execution_time"]:
            alert = PerformanceAlert(
                alert_type="execution_time",
                tool_name=metric.tool_name,
                metric_value=metric.execution_time,
                threshold=self.alert_thresholds["execution_time"],
                message=f"Tool {metric.tool_name} took {metric.execution_time:.2f}s to execute",
            )
            self.alerts.append(alert)
            logger.warning(f"Performance alert: {alert.message}")

        # Check failure rate for this tool (last 10 executions)
        tool_executions = self.tool_metrics[metric.tool_name][-10:]
        if len(tool_executions) >= 5:  # Only check if we have enough data
            failures = sum(1 for m in tool_executions if not m.success)
            failure_rate = failures / len(tool_executions)

            if failure_rate > self.alert_thresholds["failure_rate"]:
                alert = PerformanceAlert(
                    alert_type="failure_rate",
                    tool_name=metric.tool_name,
                    metric_value=failure_rate,
                    threshold=self.alert_thresholds["failure_rate"],
                    message=f"Tool {metric.tool_name} has {failure_rate:.1%} failure rate",
                )
                self.alerts.append(alert)
                logger.warning(f"Performance alert: {alert.message}")

        # Check retry rate
        if metric.retry_count > 0:
            recent_executions = [
                m for m in tool_executions[-20:] if current_time - m.timestamp < 3600
            ]  # Last hour
            if len(recent_executions) >= 5:
                retries = sum(1 for m in recent_executions if m.retry_count > 0)
                retry_rate = retries / len(recent_executions)

                if retry_rate > self.alert_thresholds["retry_rate"]:
                    alert = PerformanceAlert(
                        alert_type="retry_rate",
                        tool_name=metric.tool_name,
                        metric_value=retry_rate,
                        threshold=self.alert_thresholds["retry_rate"],
                        message=f"Tool {metric.tool_name} has {retry_rate:.1%} retry rate",
                    )
                    self.alerts.append(alert)
                    logger.warning(f"Performance alert: {alert.message}")

    def get_tool_statistics(
        self, tool_name: str, time_window_hours: int = 1
    ) -> Dict[str, Any]:
        """
        Get performance statistics for a specific tool.

        Args:
            tool_name: Name of the tool
            time_window_hours: Time window for statistics (hours)

        Returns:
            Dictionary with performance statistics
        """
        if time_window_hours <= 0:
            raise ValueError("time_window_hours must be positive")

        with self._lock:
            current_time = time.time()
            cutoff_time = current_time - (time_window_hours * 3600)

            # Filter metrics within time window
            tool_executions = [
                m
                for m in self.tool_metrics.get(tool_name, [])
                if m.timestamp >= cutoff_time
            ]

            if not tool_executions:
                return {
                    "tool_name": tool_name,
                    "total_executions": 0,
                    "time_window_hours": time_window_hours,
                }

            # Calculate statistics
            execution_times = [m.execution_time for m in tool_executions]
            successes = sum(1 for m in tool_executions if m.success)
            failures = len(tool_executions) - successes
            retries = sum(m.retry_count for m in tool_executions)

            # Strategy distribution
            strategy_counts = defaultdict(int)
            for m in tool_executions:
                strategy_counts[m.strategy_used] += 1

            return {
                "tool_name": tool_name,
                "time_window_hours": time_window_hours,
                "total_executions": len(tool_executions),
                "successful_executions": successes,
                "failed_executions": failures,
                "success_rate": successes / len(tool_executions),
                "failure_rate": failures / len(tool_executions),
                "total_retries": retries,
                "retry_rate": retries / len(tool_executions),
                "execution_time_stats": {
                    "min": min(execution_times),
                    "max": max(execution_times),
                    "mean": statistics.mean(execution_times),
                    "median": statistics.median(execution_times),
                    "std_dev": (
                        statistics.stdev(execution_times)
                        if len(execution_times) > 1
                        else 0
                    ),
                },
                "strategy_distribution": dict(strategy_counts),
                "last_execution_time": max(m.timestamp for m in tool_executions),
            }

    def get_strategy_performance(
        self, strategy_name: str, time_window_hours: int = 1
    ) -> Dict[str, Any]:
        """
        Get performance statistics for a specific execution strategy.

        Args:
            strategy_name: Name of the execution strategy
            time_window_hours: Time window for statistics (hours)

        Returns:
            Dictionary with strategy performance statistics
        """
        with self._lock:
            current_time = time.time()
            cutoff_time = current_time - (time_window_hours * 3600)

            # Filter metrics within time window
            strategy_executions = [
                m
                for m in self.strategy_metrics.get(strategy_name, [])
                if m.timestamp >= cutoff_time
            ]

            if not strategy_executions:
                return {
                    "strategy_name": strategy_name,
                    "total_executions": 0,
                    "time_window_hours": time_window_hours,
                }

            # Calculate statistics
            execution_times = [m.execution_time for m in strategy_executions]
            successes = sum(1 for m in strategy_executions if m.success)

            # Tool distribution
            tool_counts = defaultdict(int)
            for m in strategy_executions:
                tool_counts[m.tool_name] += 1

            return {
                "strategy_name": strategy_name,
                "time_window_hours": time_window_hours,
                "total_executions": len(strategy_executions),
                "successful_executions": successes,
                "success_rate": successes / len(strategy_executions),
                "execution_time_stats": {
                    "min": min(execution_times),
                    "max": max(execution_times),
                    "mean": statistics.mean(execution_times),
                    "median": statistics.median(execution_times),
                },
                "tool_distribution": dict(tool_counts),
            }

    def get_overall_performance(self, time_window_hours: int = 1) -> Dict[str, Any]:
        """
        Get overall system performance statistics.

        Args:
            time_window_hours: Time window for statistics (hours)

        Returns:
            Dictionary with overall performance statistics
        """
        with self._lock:
            # Check cache first
            cache_key = f"overall_{time_window_hours}"
            current_time = time.time()

            if cache_key in self._stats_cache and current_time < self._cache_expiry:
                return self._stats_cache[cache_key]

            cutoff_time = current_time - (time_window_hours * 3600)

            # Filter metrics within time window
            recent_executions = [
                m for m in self.execution_metrics if m.timestamp >= cutoff_time
            ]

            if not recent_executions:
                return {"total_executions": 0, "time_window_hours": time_window_hours}

            # Calculate statistics
            execution_times = [m.execution_time for m in recent_executions]
            successes = sum(1 for m in recent_executions if m.success)
            retries = sum(m.retry_count for m in recent_executions)

            # Tool and strategy distributions
            tool_counts = defaultdict(int)
            strategy_counts = defaultdict(int)
            for m in recent_executions:
                tool_counts[m.tool_name] += 1
                strategy_counts[m.strategy_used] += 1

            # Top performing tools and strategies
            top_tools = sorted(tool_counts.items(), key=lambda x: x[1], reverse=True)[
                :5
            ]
            top_strategies = sorted(
                strategy_counts.items(), key=lambda x: x[1], reverse=True
            )[:3]

            stats = {
                "time_window_hours": time_window_hours,
                "total_executions": len(recent_executions),
                "successful_executions": successes,
                "failed_executions": len(recent_executions) - successes,
                "overall_success_rate": successes / len(recent_executions),
                "total_retries": retries,
                "overall_retry_rate": retries / len(recent_executions),
                "execution_time_stats": {
                    "min": min(execution_times),
                    "max": max(execution_times),
                    "mean": statistics.mean(execution_times),
                    "median": statistics.median(execution_times),
                    "p95": (
                        statistics.quantiles(execution_times, n=20)[18]
                        if len(execution_times) >= 20
                        else max(execution_times)
                    ),
                },
                "top_tools": top_tools,
                "top_strategies": top_strategies,
                "unique_tools": len(tool_counts),
                "unique_strategies": len(strategy_counts),
            }

            # Cache the result
            self._stats_cache[cache_key] = stats
            self._cache_expiry = current_time + self._cache_ttl

            return stats

    def get_recent_alerts(self, hours: int = 1) -> List[PerformanceAlert]:
        """
        Get recent performance alerts.

        Args:
            hours: Number of hours to look back

        Returns:
            List of recent alerts
        """
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            return [alert for alert in self.alerts if alert.timestamp >= cutoff_time]

    def get_tool_health_status(self, tool_name: str) -> Dict[str, Any]:
        """
        Get health status assessment for a specific tool.

        Args:
            tool_name: Name of the tool

        Returns:
            Dictionary with health status information
        """
        stats = self.get_tool_statistics(tool_name, time_window_hours=1)

        if stats["total_executions"] == 0:
            return {
                "tool_name": tool_name,
                "health_status": "unknown",
                "reason": "No recent executions",
            }

        # Determine health status
        success_rate = stats["success_rate"]
        avg_execution_time = stats["execution_time_stats"]["mean"]
        retry_rate = stats["retry_rate"]

        if success_rate >= 0.95 and avg_execution_time <= 10 and retry_rate <= 0.1:
            health_status = "excellent"
        elif success_rate >= 0.8 and avg_execution_time <= 30 and retry_rate <= 0.3:
            health_status = "good"
        elif success_rate >= 0.6 and avg_execution_time <= 60 and retry_rate <= 0.5:
            health_status = "fair"
        else:
            health_status = "poor"

        # Identify issues
        issues = []
        if success_rate < 0.8:
            issues.append(f"Low success rate: {success_rate:.1%}")
        if avg_execution_time > 30:
            issues.append(f"Slow execution: {avg_execution_time:.1f}s average")
        if retry_rate > 0.3:
            issues.append(f"High retry rate: {retry_rate:.1%}")

        return {
            "tool_name": tool_name,
            "health_status": health_status,
            "success_rate": success_rate,
            "average_execution_time": avg_execution_time,
            "retry_rate": retry_rate,
            "total_executions": stats["total_executions"],
            "issues": issues,
            "last_execution": (
                datetime.fromtimestamp(stats["last_execution_time"]).isoformat()
                if "last_execution_time" in stats
                else None
            ),
        }

    def _start_cleanup_thread(self):
        """Start background thread for metric cleanup."""

        def cleanup_worker():
            while True:
                try:
                    current_time = time.time()
                    cutoff_time = current_time - self.retention_seconds

                    with self._lock:
                        # Clean up global metrics
                        self.execution_metrics = deque(
                            [
                                m
                                for m in self.execution_metrics
                                if m.timestamp >= cutoff_time
                            ]
                        )

                        # Clean up tool metrics
                        for tool_name in list(self.tool_metrics.keys()):
                            self.tool_metrics[tool_name] = [
                                m
                                for m in self.tool_metrics[tool_name]
                                if m.timestamp >= cutoff_time
                            ]
                            # Remove empty entries
                            if not self.tool_metrics[tool_name]:
                                del self.tool_metrics[tool_name]

                        # Clean up strategy metrics
                        for strategy_name in list(self.strategy_metrics.keys()):
                            self.strategy_metrics[strategy_name] = [
                                m
                                for m in self.strategy_metrics[strategy_name]
                                if m.timestamp >= cutoff_time
                            ]
                            # Remove empty entries
                            if not self.strategy_metrics[strategy_name]:
                                del self.strategy_metrics[strategy_name]

                        # Clean up alerts (keep for 24 hours regardless of retention setting)
                        alert_cutoff = current_time - (24 * 3600)
                        self.alerts = deque(
                            [
                                alert
                                for alert in self.alerts
                                if alert.timestamp >= alert_cutoff
                            ]
                        )

                    # Sleep for 1 hour between cleanups
                    time.sleep(3600)

                except Exception as e:
                    logger.error(f"Error in performance tracker cleanup: {e}")
                    time.sleep(300)  # Sleep 5 minutes on error

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("Performance tracker cleanup thread started")

    def export_metrics(self, format_type: str = "json") -> str:
        """
        Export metrics data for external analysis.

        Args:
            format_type: Export format ("json" or "csv")

        Returns:
            Formatted metrics data
        """
        with self._lock:
            if format_type == "json":
                export_data = {
                    "export_timestamp": time.time(),
                    "retention_hours": self.retention_hours,
                    "total_metrics": len(self.execution_metrics),
                    "overall_performance": self.get_overall_performance(
                        24
                    ),  # Last 24 hours
                    "tool_statistics": {
                        tool_name: self.get_tool_statistics(tool_name, 24)
                        for tool_name in self.tool_metrics.keys()
                    },
                    "strategy_performance": {
                        strategy_name: self.get_strategy_performance(strategy_name, 24)
                        for strategy_name in self.strategy_metrics.keys()
                    },
                    "recent_alerts": [
                        {
                            "alert_type": alert.alert_type,
                            "tool_name": alert.tool_name,
                            "metric_value": alert.metric_value,
                            "threshold": alert.threshold,
                            "message": alert.message,
                            "timestamp": alert.timestamp,
                        }
                        for alert in self.get_recent_alerts(24)
                    ],
                }
                return json.dumps(export_data, indent=2)

            else:
                raise ValueError(f"Unsupported format type: {format_type}")


# Global performance tracker instance
performance_tracker = PerformanceTracker()


def get_performance_tracker() -> PerformanceTracker:
    """Get the global performance tracker instance."""
    return performance_tracker


def record_execution_metric(
    tool_name: str,
    execution_time: float,
    success: bool,
    strategy_used: str,
    error_message: Optional[str] = None,
    data_size: int = 0,
    retry_count: int = 0,
):
    """
    Convenience function to record an execution metric.

    Args:
        tool_name: Name of the executed tool
        execution_time: Time taken for execution (seconds)
        success: Whether execution was successful
        strategy_used: Execution strategy used
        error_message: Error message if execution failed
        data_size: Size of data transferred (bytes)
        retry_count: Number of retries performed
    """
    metric = ExecutionMetric(
        tool_name=tool_name,
        execution_time=execution_time,
        success=success,
        strategy_used=strategy_used,
        error_message=error_message,
        data_size=data_size,
        retry_count=retry_count,
    )

    performance_tracker.record_execution(metric)


def get_tool_retriever_cache_metrics(time_window_hours: int = 1) -> Dict[str, Any]:
    """
    Get specific cache performance metrics for biomni_tool_retriever.

    Args:
        time_window_hours: Time window for metrics analysis

    Returns:
        Dictionary with cache performance statistics including hit rate and API reduction
    """
    tool_stats = performance_tracker.get_tool_statistics(
        "biomni_tool_retriever", time_window_hours
    )

    if tool_stats["total_executions"] == 0:
        return {
            "time_window_hours": time_window_hours,
            "total_executions": 0,
            "cache_hit_rate": 0.0,
            "api_reduction_percentage": 0.0,
            "new_retrievals": 0,
            "cached_retrievals": 0,
        }

    # Get strategy-specific metrics
    new_retrievals = 0
    cached_retrievals = 0

    current_time = time.time()
    cutoff_time = current_time - (time_window_hours * 3600)

    # Count executions by strategy
    tool_executions = [
        m
        for m in performance_tracker.tool_metrics.get("biomni_tool_retriever", [])
        if m.timestamp >= cutoff_time
    ]

    for execution in tool_executions:
        if execution.strategy_used == "new_step_retrieval":
            new_retrievals += 1
        elif execution.strategy_used == "cached_retrieval":
            cached_retrievals += 1

    total_executions = new_retrievals + cached_retrievals
    cache_hit_rate = (
        cached_retrievals / total_executions if total_executions > 0 else 0.0
    )
    api_reduction_percentage = (
        cache_hit_rate * 100
    )  # Cache hits represent API calls saved

    return {
        "time_window_hours": time_window_hours,
        "total_executions": total_executions,
        "cache_hit_rate": cache_hit_rate,
        "api_reduction_percentage": api_reduction_percentage,
        "new_retrievals": new_retrievals,
        "cached_retrievals": cached_retrievals,
        "average_retrieval_time": (
            tool_stats["execution_time_stats"]["mean"]
            if tool_stats["total_executions"] > 0
            else 0.0
        ),
        "success_rate": (
            tool_stats["success_rate"] if tool_stats["total_executions"] > 0 else 0.0
        ),
    }
