"""
Execution strategy framework with tool-specific optimization and monitoring.

This module provides a comprehensive framework for managing execution strategies,
performance optimization, and adaptive tool selection based on real-time metrics.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable, Type
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod

from .execution_strategies import (
    get_retry_executor,
    get_direct_executor,
    get_mcp_executor,
)
from .execution_fallback import get_fallback_manager
from .performance_monitor import get_performance_tracker, record_execution_metric
from .error_recovery import get_error_detector, get_error_recovery
from .connection_pool import get_connection_pool
from ..config.execution_config import (
    get_execution_config,
    ExecutionStrategy,
    ToolComplexity,
)

logger = logging.getLogger(__name__)


class OptimizationStrategy(Enum):
    """Available optimization strategies."""

    PERFORMANCE = "performance"  # Optimize for speed
    RELIABILITY = "reliability"  # Optimize for success rate
    RESOURCE = "resource"  # Optimize for resource usage
    BALANCED = "balanced"  # Balance all factors


@dataclass
class ExecutionContext:
    """Context information for tool execution."""

    tool_name: str
    tool_type: str
    complexity: ToolComplexity
    user_priority: str
    session_id: Optional[str] = None
    execution_count: int = 0
    last_execution_time: Optional[float] = None
    preferred_strategy: Optional[ExecutionStrategy] = None


class ExecutionStrategyInterface(ABC):
    """Abstract interface for execution strategies."""

    @abstractmethod
    async def execute(self, context: ExecutionContext, **kwargs) -> str:
        """Execute tool with this strategy."""
        pass

    @abstractmethod
    def get_strategy_name(self) -> str:
        """Get the name of this strategy."""
        pass

    @abstractmethod
    def estimate_performance(self, context: ExecutionContext) -> Dict[str, float]:
        """Estimate performance metrics for this strategy."""
        pass


class HybridExecutionStrategy(ExecutionStrategyInterface):
    """Hybrid execution strategy with intelligent fallback."""

    def __init__(self):
        self.fallback_manager = get_fallback_manager()

    async def execute(self, context: ExecutionContext, **kwargs) -> str:
        """Execute using hybrid strategy."""
        start_time = time.time()

        try:
            result = await self.fallback_manager.execute_with_fallback(
                context.tool_name, **kwargs
            )

            # Record performance metric
            execution_time = time.time() - start_time
            success = self._is_successful_result(result)

            record_execution_metric(
                tool_name=context.tool_name,
                execution_time=execution_time,
                success=success,
                strategy_used="hybrid",
                retry_count=0,  # Fallback manager handles retries internally
            )

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            record_execution_metric(
                tool_name=context.tool_name,
                execution_time=execution_time,
                success=False,
                strategy_used="hybrid",
                error_message=str(e),
            )
            raise

    def get_strategy_name(self) -> str:
        return "hybrid"

    def estimate_performance(self, context: ExecutionContext) -> Dict[str, float]:
        """Estimate performance based on historical data."""
        tracker = get_performance_tracker()
        stats = tracker.get_tool_statistics(context.tool_name, time_window_hours=24)

        if stats["total_executions"] > 0:
            return {
                "estimated_success_rate": stats["success_rate"],
                "estimated_execution_time": stats["execution_time_stats"]["mean"],
                "estimated_retry_rate": stats["retry_rate"],
            }
        else:
            # Default estimates for unknown tools
            return {
                "estimated_success_rate": 0.85,
                "estimated_execution_time": 15.0,
                "estimated_retry_rate": 0.2,
            }

    def _is_successful_result(self, result: str) -> bool:
        """Check if execution result indicates success."""
        try:
            import json

            result_data = json.loads(result)
            return result_data.get("success", False)
        except:
            return False


class DirectSubprocessStrategy(ExecutionStrategyInterface):
    """Direct subprocess execution strategy."""

    def __init__(self):
        self.direct_executor = get_direct_executor()

    async def execute(self, context: ExecutionContext, **kwargs) -> str:
        """Execute using direct subprocess."""
        start_time = time.time()

        try:
            result = await self.direct_executor.execute_tool(
                context.tool_name, **kwargs
            )

            execution_time = time.time() - start_time
            success = self._is_successful_result(result)

            record_execution_metric(
                tool_name=context.tool_name,
                execution_time=execution_time,
                success=success,
                strategy_used="direct_subprocess",
            )

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            record_execution_metric(
                tool_name=context.tool_name,
                execution_time=execution_time,
                success=False,
                strategy_used="direct_subprocess",
                error_message=str(e),
            )
            raise

    def get_strategy_name(self) -> str:
        return "direct_subprocess"

    def estimate_performance(self, context: ExecutionContext) -> Dict[str, float]:
        """Estimate performance for direct subprocess."""
        tracker = get_performance_tracker()
        strategy_stats = tracker.get_strategy_performance(
            "direct_subprocess", time_window_hours=24
        )

        if strategy_stats["total_executions"] > 0:
            return {
                "estimated_success_rate": strategy_stats["success_rate"],
                "estimated_execution_time": strategy_stats["execution_time_stats"][
                    "mean"
                ],
                "estimated_retry_rate": (
                    0.1  # Direct subprocess typically has low retry rate
                ),
            }
        else:
            return {
                "estimated_success_rate": 0.9,  # Direct subprocess usually reliable
                "estimated_execution_time": 8.0,  # Generally faster
                "estimated_retry_rate": 0.1,
            }

    def _is_successful_result(self, result: str) -> bool:
        """Check if execution result indicates success."""
        try:
            import json

            result_data = json.loads(result)
            return result_data.get("success", False)
        except:
            return False


class MCPExecutionStrategy(ExecutionStrategyInterface):
    """MCP (desktop-commander) execution strategy."""

    def __init__(self):
        self.mcp_executor = get_mcp_executor()

    async def execute(self, context: ExecutionContext, **kwargs) -> str:
        """Execute using MCP."""
        start_time = time.time()

        try:
            result = await self.mcp_executor.execute_tool(context.tool_name, **kwargs)

            execution_time = time.time() - start_time
            success = self._is_successful_result(result)

            record_execution_metric(
                tool_name=context.tool_name,
                execution_time=execution_time,
                success=success,
                strategy_used="desktop_commander",
            )

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            record_execution_metric(
                tool_name=context.tool_name,
                execution_time=execution_time,
                success=False,
                strategy_used="desktop_commander",
                error_message=str(e),
            )
            raise

    def get_strategy_name(self) -> str:
        return "desktop_commander"

    def estimate_performance(self, context: ExecutionContext) -> Dict[str, float]:
        """Estimate performance for MCP execution."""
        tracker = get_performance_tracker()
        strategy_stats = tracker.get_strategy_performance(
            "desktop_commander", time_window_hours=24
        )

        if strategy_stats["total_executions"] > 0:
            return {
                "estimated_success_rate": strategy_stats["success_rate"],
                "estimated_execution_time": strategy_stats["execution_time_stats"][
                    "mean"
                ],
                "estimated_retry_rate": 0.25,  # MCP typically has higher retry rate
            }
        else:
            return {
                "estimated_success_rate": 0.75,  # MCP can be less reliable
                "estimated_execution_time": 12.0,  # Usually slower than direct
                "estimated_retry_rate": 0.25,
            }

    def _is_successful_result(self, result: str) -> bool:
        """Check if execution result indicates success."""
        try:
            import json

            result_data = json.loads(result)
            return result_data.get("success", False)
        except:
            return False


class ExecutionStrategyManager:
    """
    Central manager for execution strategies with adaptive optimization.

    Provides intelligent strategy selection, performance monitoring,
    and automatic optimization based on real-time performance data.
    """

    def __init__(self):
        self.config = get_execution_config()
        self.performance_tracker = get_performance_tracker()
        self.error_detector = get_error_detector()

        # Available strategies
        self.strategies: Dict[str, ExecutionStrategyInterface] = {
            "hybrid": HybridExecutionStrategy(),
            "direct_subprocess": DirectSubprocessStrategy(),
            "desktop_commander": MCPExecutionStrategy(),
        }

        # Strategy selection preferences
        self.optimization_strategy = OptimizationStrategy.BALANCED

        # Performance thresholds for strategy switching
        self.performance_thresholds = {
            "min_success_rate": 0.7,
            "max_execution_time": 60.0,
            "max_retry_rate": 0.5,
        }

        # Strategy selection cache
        self._strategy_cache = {}
        self._cache_expiry = {}
        self._cache_ttl = 300  # 5 minutes

    async def execute_tool(
        self,
        tool_name: str,
        tool_type: str = "biomni",
        user_priority: str = "normal",
        **kwargs,
    ) -> str:
        """
        Execute tool using optimal strategy selection.

        Args:
            tool_name: Name of the tool to execute
            tool_type: Type of tool (biomni, filesystem, etc.)
            user_priority: User priority (low, normal, high, critical)
            **kwargs: Tool execution arguments

        Returns:
            JSON string with execution result
        """
        # Create execution context
        complexity = self.config.get_tool_complexity(tool_name, kwargs.get("command"))
        context = ExecutionContext(
            tool_name=tool_name,
            tool_type=tool_type,
            complexity=complexity,
            user_priority=user_priority,
        )

        # Select optimal strategy
        strategy = self._select_optimal_strategy(context)

        logger.info(
            f"Executing {tool_name} with strategy: {strategy.get_strategy_name()}"
        )

        try:
            # Execute with selected strategy
            result = await strategy.execute(context, **kwargs)

            # Update strategy cache based on success
            self._update_strategy_preference(
                context, strategy.get_strategy_name(), True
            )

            return result

        except Exception as e:
            # Update strategy cache based on failure
            self._update_strategy_preference(
                context, strategy.get_strategy_name(), False
            )

            logger.error(
                f"Strategy {strategy.get_strategy_name()} failed for {tool_name}: {e}"
            )
            raise

    def _select_optimal_strategy(
        self, context: ExecutionContext
    ) -> ExecutionStrategyInterface:
        """
        Select the optimal execution strategy for the given context.

        Args:
            context: ExecutionContext with tool information

        Returns:
            Selected ExecutionStrategyInterface
        """
        # Check cache first
        cache_key = f"{context.tool_name}_{context.user_priority}"
        current_time = time.time()

        if cache_key in self._strategy_cache and current_time < self._cache_expiry.get(
            cache_key, 0
        ):
            strategy_name = self._strategy_cache[cache_key]
            return self.strategies[strategy_name]

        # Evaluate all strategies
        strategy_scores = {}

        for strategy_name, strategy in self.strategies.items():
            score = self._calculate_strategy_score(strategy, context)
            strategy_scores[strategy_name] = score

        # Select best strategy
        best_strategy_name = max(strategy_scores, key=strategy_scores.get)
        best_strategy = self.strategies[best_strategy_name]

        # Cache the selection
        self._strategy_cache[cache_key] = best_strategy_name
        self._cache_expiry[cache_key] = current_time + self._cache_ttl

        logger.debug(f"Strategy scores for {context.tool_name}: {strategy_scores}")
        logger.info(f"Selected strategy {best_strategy_name} for {context.tool_name}")

        return best_strategy

    def _calculate_strategy_score(
        self, strategy: ExecutionStrategyInterface, context: ExecutionContext
    ) -> float:
        """
        Calculate a score for a strategy based on context and optimization preferences.

        Args:
            strategy: Strategy to evaluate
            context: Execution context

        Returns:
            Strategy score (higher is better)
        """
        performance_estimate = strategy.estimate_performance(context)

        success_rate = performance_estimate["estimated_success_rate"]
        execution_time = performance_estimate["estimated_execution_time"]
        retry_rate = performance_estimate["estimated_retry_rate"]

        # Base score components
        success_score = success_rate * 100  # 0-100
        speed_score = max(0, 100 - (execution_time / 2))  # Penalty for slow execution
        reliability_score = max(
            0, 100 - (retry_rate * 200)
        )  # Penalty for high retry rate

        # Apply optimization strategy weights
        if self.optimization_strategy == OptimizationStrategy.PERFORMANCE:
            score = (
                (speed_score * 0.5) + (success_score * 0.3) + (reliability_score * 0.2)
            )
        elif self.optimization_strategy == OptimizationStrategy.RELIABILITY:
            score = (
                (success_score * 0.5) + (reliability_score * 0.3) + (speed_score * 0.2)
            )
        elif self.optimization_strategy == OptimizationStrategy.RESOURCE:
            # Favor strategies that use fewer resources (direct subprocess)
            resource_score = (
                100 if strategy.get_strategy_name() == "direct_subprocess" else 80
            )
            score = (resource_score * 0.4) + (success_score * 0.3) + (speed_score * 0.3)
        else:  # BALANCED
            score = (
                (success_score * 0.4) + (speed_score * 0.3) + (reliability_score * 0.3)
            )

        # Apply context-specific adjustments
        score = self._apply_context_adjustments(score, strategy, context)

        return score

    def _apply_context_adjustments(
        self,
        base_score: float,
        strategy: ExecutionStrategyInterface,
        context: ExecutionContext,
    ) -> float:
        """
        Apply context-specific adjustments to strategy score.

        Args:
            base_score: Base strategy score
            strategy: Strategy being evaluated
            context: Execution context

        Returns:
            Adjusted score
        """
        adjusted_score = base_score

        # User priority adjustments
        if context.user_priority == "critical":
            # Favor more reliable strategies for critical tasks
            if strategy.get_strategy_name() == "hybrid":
                adjusted_score += 10
        elif context.user_priority == "low":
            # Favor faster strategies for low priority tasks
            if strategy.get_strategy_name() == "direct_subprocess":
                adjusted_score += 5

        # Tool complexity adjustments
        if context.complexity == ToolComplexity.INTENSIVE:
            # Favor hybrid strategy for intensive tasks
            if strategy.get_strategy_name() == "hybrid":
                adjusted_score += 8
        elif context.complexity == ToolComplexity.SIMPLE:
            # Favor direct execution for simple tasks
            if strategy.get_strategy_name() == "direct_subprocess":
                adjusted_score += 5

        # Tool type adjustments
        if context.tool_type == "biomni":
            # Biomni tools work better with certain strategies
            if strategy.get_strategy_name() in ["hybrid", "direct_subprocess"]:
                adjusted_score += 3

        # Recent performance adjustments
        recent_health = self.performance_tracker.get_tool_health_status(
            context.tool_name
        )
        if recent_health["health_status"] == "poor":
            # If tool has been performing poorly, try different strategies
            tool_stats = self.performance_tracker.get_tool_statistics(
                context.tool_name, 1
            )
            if tool_stats["total_executions"] > 0:
                # Favor strategies that haven't been used recently
                strategy_dist = tool_stats.get("strategy_distribution", {})
                strategy_usage = strategy_dist.get(strategy.get_strategy_name(), 0)
                usage_penalty = min(strategy_usage * 2, 15)  # Up to 15 point penalty
                adjusted_score -= usage_penalty

        return adjusted_score

    def _update_strategy_preference(
        self, context: ExecutionContext, strategy_name: str, success: bool
    ):
        """
        Update strategy preferences based on execution outcome.

        Args:
            context: Execution context
            strategy_name: Name of strategy that was used
            success: Whether execution was successful
        """
        # If execution failed, temporarily reduce preference for this strategy
        if not success:
            cache_key = f"{context.tool_name}_{context.user_priority}"
            # Expire cache entry to force re-evaluation
            if cache_key in self._cache_expiry:
                self._cache_expiry[cache_key] = 0

    def set_optimization_strategy(self, strategy: OptimizationStrategy):
        """
        Set the optimization strategy for strategy selection.

        Args:
            strategy: OptimizationStrategy to use
        """
        self.optimization_strategy = strategy
        # Clear cache to force re-evaluation with new optimization strategy
        self._strategy_cache.clear()
        self._cache_expiry.clear()

        logger.info(f"Optimization strategy set to: {strategy.value}")

    def get_strategy_recommendations(self, tool_name: str) -> Dict[str, Any]:
        """
        Get strategy recommendations for a specific tool.

        Args:
            tool_name: Name of the tool

        Returns:
            Dictionary with strategy recommendations and analysis
        """
        # Create context for analysis
        complexity = self.config.get_tool_complexity(tool_name)
        context = ExecutionContext(
            tool_name=tool_name,
            tool_type="biomni",
            complexity=complexity,
            user_priority="normal",
        )

        # Evaluate all strategies
        strategy_analysis = {}
        for strategy_name, strategy in self.strategies.items():
            performance_estimate = strategy.estimate_performance(context)
            score = self._calculate_strategy_score(strategy, context)

            strategy_analysis[strategy_name] = {
                "score": score,
                "estimated_performance": performance_estimate,
                "recommended_for": self._get_strategy_use_cases(strategy_name),
            }

        # Get current tool health
        health_status = self.performance_tracker.get_tool_health_status(tool_name)

        # Rank strategies by score
        ranked_strategies = sorted(
            strategy_analysis.items(), key=lambda x: x[1]["score"], reverse=True
        )

        return {
            "tool_name": tool_name,
            "tool_complexity": complexity.value,
            "current_health": health_status,
            "recommended_strategy": ranked_strategies[0][0],
            "strategy_analysis": strategy_analysis,
            "ranked_strategies": [
                (name, data["score"]) for name, data in ranked_strategies
            ],
            "optimization_strategy": self.optimization_strategy.value,
        }

    def _get_strategy_use_cases(self, strategy_name: str) -> List[str]:
        """Get recommended use cases for a strategy."""
        use_cases = {
            "hybrid": [
                "Unknown or unreliable tools",
                "Critical operations requiring fallback",
                "Complex bioinformatics analyses",
                "First-time tool execution",
            ],
            "direct_subprocess": [
                "Simple, reliable tools",
                "Performance-critical operations",
                "High-frequency tool calls",
                "Resource-constrained environments",
            ],
            "desktop_commander": [
                "File system operations",
                "Cross-environment compatibility needed",
                "Session persistence required",
                "Development and debugging",
            ],
        }
        return use_cases.get(strategy_name, [])

    def get_performance_dashboard(self) -> Dict[str, Any]:
        """
        Get comprehensive performance dashboard data.

        Returns:
            Dictionary with dashboard information
        """
        overall_performance = self.performance_tracker.get_overall_performance(24)
        recent_alerts = self.performance_tracker.get_recent_alerts(24)

        # Get strategy performance comparison
        strategy_comparison = {}
        for strategy_name in self.strategies.keys():
            strategy_perf = self.performance_tracker.get_strategy_performance(
                strategy_name, 24
            )
            if strategy_perf["total_executions"] > 0:
                strategy_comparison[strategy_name] = {
                    "success_rate": strategy_perf["success_rate"],
                    "average_time": strategy_perf["execution_time_stats"]["mean"],
                    "total_executions": strategy_perf["total_executions"],
                }

        # Get top performing and problematic tools
        tool_health = {}
        for tool_name in overall_performance.get("top_tools", [])[:10]:  # Top 10 tools
            if isinstance(tool_name, tuple):  # Handle (name, count) tuples
                tool_name = tool_name[0]
            tool_health[tool_name] = self.performance_tracker.get_tool_health_status(
                tool_name
            )

        return {
            "overall_performance": overall_performance,
            "strategy_comparison": strategy_comparison,
            "tool_health_overview": tool_health,
            "recent_alerts": [
                {
                    "type": alert.alert_type,
                    "tool": alert.tool_name,
                    "message": alert.message,
                    "timestamp": alert.timestamp,
                }
                for alert in recent_alerts
            ],
            "optimization_strategy": self.optimization_strategy.value,
            "cache_status": {
                "cached_strategies": len(self._strategy_cache),
                "cache_hit_rate": "N/A",  # Could implement cache hit tracking
            },
        }


# Global strategy manager instance
strategy_manager = ExecutionStrategyManager()


def get_strategy_manager() -> ExecutionStrategyManager:
    """Get the global execution strategy manager instance."""
    return strategy_manager


async def execute_tool_optimized(
    tool_name: str, tool_type: str = "biomni", user_priority: str = "normal", **kwargs
) -> str:
    """
    Convenience function for optimized tool execution.

    Args:
        tool_name: Name of the tool to execute
        tool_type: Type of tool
        user_priority: User priority level
        **kwargs: Tool execution arguments

    Returns:
        JSON string with execution result
    """
    return await strategy_manager.execute_tool(
        tool_name=tool_name, tool_type=tool_type, user_priority=user_priority, **kwargs
    )
