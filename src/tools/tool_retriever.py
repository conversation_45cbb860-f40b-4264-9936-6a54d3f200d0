import logging
import sys
import os
from typing import Annotated, List, Dict, Any
from langchain_core.tools import tool
from .decorators import log_io

# Add biomni-toolkit to sys.path with priority over installed package
# Navigate from src/tools/tool_retriever.py to project root, then to biomni-toolkit
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
BIOMNI_TOOLKIT_PATH = os.path.join(PROJECT_ROOT, "biomni-toolkit")
if os.path.exists(BIOMNI_TOOLKIT_PATH):
    # Insert at the beginning to prioritize local package over installed one
    if BIOMNI_TOOLKIT_PATH not in sys.path:
        sys.path.insert(0, BIOMNI_TOOLKIT_PATH)

    # Clear any cached biomni modules to force reload from local path
    for module_name in list(sys.modules.keys()):
        if module_name.startswith("biomni."):
            del sys.modules[module_name]

logger = logging.getLogger(__name__)


@tool
@log_io
def biomni_tool_retriever(
    query: Annotated[str, "The user query to find relevant tools for"],
    include_mcp_tools: Annotated[
        bool, "Whether to include current MCP tools in the retrieval"
    ] = True,
    include_biomni_tools: Annotated[
        bool, "Whether to include Biomni toolkit tools in the retrieval"
    ] = True,
) -> str:
    """
    Use the Biomni ToolRetriever to intelligently select the most relevant tools for a given query.

    This tool combines tools from both the current Omiy MCP system and the Biomni toolkit,
    then uses LLM-based selection to find the most relevant tools for the specific query.

    Args:
        query: The user's query to find relevant tools for
        include_mcp_tools: Whether to include current MCP tools in the retrieval
        include_biomni_tools: Whether to include Biomni toolkit tools in the retrieval

    Returns:
        A formatted string with the selected relevant tools
    """
    logger.info(f"Retrieving relevant tools for query: {query}")

    try:
        # Import Biomni ToolRetriever
        from src.tools.biomni.model.retriever import ToolRetriever

        # Initialize retriever
        retriever = ToolRetriever()

        # Prepare resources dictionary
        resources = {"tools": [], "data_lake": [], "libraries": []}

        # Get current MCP tools if requested
        if include_mcp_tools:
            try:
                from .mcp_tools import MCPToolManager

                manager = MCPToolManager()
                all_mcp_tools = manager.get_all_tools()

                # Convert MCP tools to Biomni format
                for tool in all_mcp_tools:
                    tool_dict = {
                        "name": tool.name,
                        "description": tool.description,
                        "required_parameters": [],
                        "optional_parameters": [],
                    }

                    # Extract parameters if available
                    if hasattr(tool, "args") and hasattr(tool.args, "__annotations__"):
                        for param_name, param_type in tool.args.__annotations__.items():
                            param_dict = {
                                "name": param_name,
                                "type": str(param_type),
                                "description": (
                                    f"Parameter {param_name} of type {param_type}"
                                ),
                                "default": None,
                            }
                            tool_dict["required_parameters"].append(param_dict)

                    resources["tools"].append(tool_dict)

                logger.info(f"Loaded {len(resources['tools'])} MCP tools")

            except Exception as e:
                logger.warning(f"Could not load MCP tools: {e}")

        # Get Biomni toolkit tools if requested
        if include_biomni_tools:
            try:
                from src.tools.biomni.tool_registry import ToolRegistry
                from src.tools.biomni.tool_modules import load_all_tools

                # Load all Biomni tools
                all_biomni_tools = load_all_tools()

                if all_biomni_tools:
                    registry = ToolRegistry(all_biomni_tools)

                    # Add Biomni tools to resources
                    for tool in registry.tools:
                        resources["tools"].append(tool)

                    logger.info(f"Loaded {len(registry.tools)} Biomni tools")
                else:
                    logger.warning(
                        "No Biomni tools were loaded (likely due to missing dependencies)"
                    )

            except Exception as e:
                logger.warning(f"Could not load Biomni tools: {e}")
                logger.info(
                    "Biomni tools are not available - this is expected if biomni environment is not fully configured"
                )

        # Use retriever to select relevant tools with system LLM
        try:
            from ..agents.llm import get_llm_by_type

            system_llm = get_llm_by_type("basic")
            selected_resources = retriever.prompt_based_retrieval(
                query, resources, llm=system_llm
            )
        except Exception as e:
            logger.warning(f"Could not use system LLM: {e}, falling back to default")
            selected_resources = retriever.prompt_based_retrieval(query, resources)

        # Format the results
        result_parts = []

        if selected_resources["tools"]:
            result_parts.append(
                f"## Selected Tools ({len(selected_resources['tools'])} tools)"
            )
            for i, tool in enumerate(selected_resources["tools"], 1):
                result_parts.append(f"{i}. **{tool['name']}**: {tool['description']}")

        if selected_resources["data_lake"]:
            result_parts.append(
                f"\n## Selected Data Lake Items ({len(selected_resources['data_lake'])} items)"
            )
            for i, item in enumerate(selected_resources["data_lake"], 1):
                result_parts.append(f"{i}. {item}")

        if selected_resources["libraries"]:
            result_parts.append(
                f"\n## Selected Libraries ({len(selected_resources['libraries'])} libraries)"
            )
            for i, library in enumerate(selected_resources["libraries"], 1):
                result_parts.append(f"{i}. {library}")

        if not result_parts:
            return "No relevant tools found for the given query."

        result = "\n".join(result_parts)
        logger.info(
            f"Successfully retrieved {len(selected_resources['tools'])} relevant tools"
        )
        return result

    except Exception as e:
        error_msg = f"Error in tool retrieval: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def list_all_available_tools() -> str:
    """
    List all available tools from both the current Omiy MCP system and the Biomni toolkit.

    Returns:
        A formatted string with all available tools organized by source
    """
    logger.info("Listing all available tools")

    try:
        result_parts = []

        # Get current MCP tools
        try:
            from .mcp_tools import MCPToolManager

            manager = MCPToolManager()
            all_mcp_tools = manager.get_all_tools()

            if all_mcp_tools:
                result_parts.append(
                    f"## Current MCP Tools ({len(all_mcp_tools)} tools)"
                )
                for i, tool in enumerate(all_mcp_tools, 1):
                    result_parts.append(f"{i}. **{tool.name}**: {tool.description}")

        except Exception as e:
            logger.warning(f"Could not load MCP tools: {e}")
            result_parts.append("## Current MCP Tools\n*Could not load MCP tools*")

        # Get Biomni toolkit tools
        try:
            from src.tools.biomni.tool_registry import ToolRegistry
            from src.tools.biomni.tool_modules import load_all_tools

            all_biomni_tools = load_all_tools()

            if all_biomni_tools:
                registry = ToolRegistry(all_biomni_tools)

                if registry.tools:
                    result_parts.append(
                        f"\n## Biomni Toolkit Tools ({len(registry.tools)} tools)"
                    )
                    for i, tool in enumerate(registry.tools, 1):
                        result_parts.append(
                            f"{i}. **{tool['name']}**: {tool['description']}"
                        )
                else:
                    result_parts.append(
                        "\n## Biomni Toolkit Tools\n*No tools available (registry is empty)*"
                    )
            else:
                result_parts.append(
                    "\n## Biomni Toolkit Tools\n*No tools loaded (likely due to missing dependencies)*"
                )

        except Exception as e:
            logger.warning(f"Could not load Biomni tools: {e}")
            result_parts.append(
                "\n## Biomni Toolkit Tools\n*Could not load Biomni tools*"
            )

        if not result_parts:
            return "No tools available."

        result = "\n".join(result_parts)
        logger.info("Successfully listed all available tools")
        return result

    except Exception as e:
        error_msg = f"Error listing tools: {str(e)}"
        logger.error(error_msg)
        return error_msg
