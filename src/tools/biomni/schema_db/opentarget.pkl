����      X��  type APIVersion {
  z: String!
  y: String!
  x: String!
}

"Significant adverse event entries"
type AdverseEvent {
  "Log-likelihood ratio"
  logLR: Float!

  "Number of reports mentioning drug and adverse event"
  count: Long!

  "8 digit unique meddra identification number"
  meddraCode: String

  "Meddra term on adverse event"
  name: String!
}

"Significant adverse events inferred from FAERS reports"
type AdverseEvents {
  "Total significant adverse events"
  count: Long!

  "Significant adverse event entries"
  rows: [AdverseEvent!]!

  "LLR critical value to define significance"
  criticalValue: Float!
}

type AlleleFrequency {
  populationName: String
  alleleFrequency: Float
}

"Associated Disease Entity"
type AssociatedDisease {
  datatypeScores: [ScoredComponent!]!
  datasourceScores: [ScoredComponent!]!
  score: Float!

  "Disease"
  disease: Disease!
}

type AssociatedDiseases {
  datasources: [DatasourceSettings!]!
  count: Long!

  "Associated Targets using (On the fly method)"
  rows: [AssociatedDisease!]!
}

"Associated Target Entity"
type AssociatedTarget {
  datatypeScores: [ScoredComponent!]!
  datasourceScores: [ScoredComponent!]!
  score: Float!

  "Target"
  target: Target!
}

type AssociatedTargets {
  datasources: [DatasourceSettings!]!
  count: Long!

  "Associated Targets using (On the fly method)"
  rows: [AssociatedTarget!]!
}

type BiologicalModels {
  id: String
  literature: [String!]
  geneticBackground: String!
  allelicComposition: String!
}

type BiomarkerGeneExpression {
  name: String
  id: GeneOntologyTerm
}

type Biosample {
  descendants: [String!]
  biosampleName: String!
  biosampleId: String!
  description: String
  children: [String!]
  xrefs: [String!]
  parents: [String!]
  synonyms: [String!]
  ancestors: [String!]
}

type CancerHallmark {
  description: String!
  label: String!
  pmid: Long!
  impact: String
}

type CellType {
  level: Int!
  reliability: Boolean!
  name: String!
}

type ChemicalProbe {
  probesDrugsScore: Float
  origin: [String!]
  id: String!
  targetFromSourceId: String!
  scoreInCells: Float
  probeMinerScore: Float
  drugId: String
  isHighQuality: Boolean!
  urls: [ChemicalProbeUrl!]!
  control: String
  scoreInOrganisms: Float
  mechanismOfAction: [String!]
}

type ChemicalProbeUrl {
  niceName: String!
  url: String
}

type Colocalisation {
  chromosome: String!
  numberColocalisingVariants: Long!
  h4: Float
  colocalisationMethod: String!
  betaRatioSignAverage: Float
  clpp: Float
  h3: Float
  rightStudyType: String!

  "Credible set"
  otherStudyLocus: CredibleSet
}

"Colocalisations"
type Colocalisations {
  count: Long!
  rows: [Colocalisation!]!
}

type Constraint {
  oe: Float
  constraintType: String!
  obs: Long
  upperBin6: Long
  score: Float
  upperRank: Long
  oeLower: Float
  exp: Float
  upperBin: Long
  oeUpper: Float
}

type CredibleSet {
  zScore: Float
  pValueExponent: Int
  studyId: String
  confidence: String
  pValueMantissa: Float
  isTransQtl: Boolean
  position: Int
  locusStart: Int
  credibleSetIndex: Int
  ldSet: [LdSet!]
  standardError: Float
  credibleSetlog10BF: Float
  chromosome: String
  subStudyDescription: String
  studyLocusId: String!
  qualityControls: [String!]
  purityMinR2: Float
  region: String
  effectAlleleFrequencyFromSource: Float
  beta: Float
  purityMeanR2: Float
  sampleSize: Int
  finemappingMethod: String
  qtlGeneId: String
  locusEnd: Int
  variant: Variant
  studyType: StudyTypeEnum
  l2GPredictions(
    "Pagination settings with index and size"
    page: Pagination): L2GPredictions!
  locus(
    "Variant IDs"
    variantIds: [String!],

    "Pagination settings with index and size"
    page: Pagination): Loci!
  colocalisation(
    "Study types"
    studyTypes: [StudyTypeEnum!],

    "Pagination settings with index and size"
    page: Pagination): Colocalisations!

  "Gwas study"
  study: Study
}

"Credible Sets"
type CredibleSets {
  count: Long!
  rows: [CredibleSet!]!
}

type DataVersion {
  iteration: String!
  month: String!
  year: String!
}

type DatasourceSettings {
  id: String!
  weight: Float!
  propagate: Boolean!
  required: Boolean!
}

input DatasourceSettingsInput {
  id: String!
  weight: Float!
  propagate: Boolean!
  required: Boolean = false
}

type DbXref {
  id: String
  source: String
}

type DepMapEssentiality {
  tissueId: String
  screens: [GeneEssentialityScreen!]!
  tissueName: String
}

"Disease or phenotype entity"
type Disease {
  "Open Targets disease id"
  id: String!
  descendants: [String!]!

  "List of obsolete diseases"
  obsoleteTerms: [String!]

  "Disease description"
  description: String

  "List of external cross reference IDs"
  dbXRefs: [String!]

  "List of direct location Disease terms"
  directLocationIds: [String!]

  "List of indirect location Disease terms"
  indirectLocationIds: [String!]

  "Disease synonyms"
  synonyms: [DiseaseSynonyms!]
  ancestors: [String!]!

  "Disease name"
  name: String!

  "Ancestor therapeutic area disease entities in ontology"
  therapeuticAreas: [Disease!]!

  "Disease parents entities in ontology"
  parents: [Disease!]!

  "Disease children entities in ontology"
  children: [Disease!]!

  "Direct Location disease terms"
  directLocations: [Disease!]!

  "Indirect Location disease terms"
  indirectLocations: [Disease!]!

  "Return similar labels using a model Word2CVec trained with PubMed"
  similarEntities(
    "List of IDs either EFO ENSEMBL CHEMBL"
    additionalIds: [String!],

    "List of entity names to search for (target, disease, drug,...)"
    entityNames: [String!],

    "Threshold similarity between 0 and 1"
    threshold: Float, size: Int): [Similarity!]!

  "Return the list of publications that mention the main entity, alone or in combination with other entities"
  literatureOcurrences(
    "List of IDs either EFO ENSEMBL CHEMBL"
    additionalIds: [String!],

    "Year at the lower end of the filter"
    startYear: Int,

    "Month at the lower end of the filter"
    startMonth: Int,

    "Year at the higher end of the filter"
    endYear: Int,

    "Month at the higher end of the filter"
    endMonth: Int, cursor: String): Publications!

  "Is disease a therapeutic area itself"
  isTherapeuticArea: Boolean!

  "Phenotype from HPO index"
  phenotypes(
    "Pagination settings with index and size"
    page: Pagination): DiseaseHPOs

  "The complete list of all possible datasources"
  evidences(
    "List of Ensembl IDs"
    ensemblIds: [String!]!,

    "Use the disease ontology to retrieve all its descendants and capture their associated evidence."
    enableIndirect: Boolean,

    "List of datasource ids"
    datasourceIds: [String!], size: Int, cursor: String): Evidences!

  "RNA and Protein baseline expression"
  otarProjects: [OtarProject!]!

  "Clinical precedence for investigational or approved drugs indicated for disease and curated mechanism of action"
  knownDrugs(
    "Query string"
    freeTextQuery: String, size: Int, cursor: String): KnownDrugs

  "associations on the fly"
  associatedTargets(
    "List of disease or target IDs"
    Bs: [String!],

    "Use the disease ontology to retrieve all its descendants and capture their associated evidence."
    enableIndirect: Boolean,

    "List of datasource settings"
    datasources: [DatasourceSettingsInput!],

    "List of the facet IDs to filter by (using AND)"
    facetFilters: [String!],

    "Filter to apply to the ids with string prefixes"
    BFilter: String,

    "Ordering for the associations. By default is score desc"
    orderByScore: String,

    "Pagination settings with index and size"
    page: Pagination): AssociatedTargets!
}

type DiseaseCellLine {
  id: String
  tissueId: String
  tissue: String
  name: String
}

"Disease and phenotypes annotations"
type DiseaseHPO {
  "List of phenotype annotations."
  evidence: [DiseaseHPOEvidences!]!

  "Phenotype entity"
  phenotypeHPO: HPO

  "Disease Entity"
  phenotypeEFO: Disease
}

"the HPO project provides a large set of phenotype annotations. Source: Phenotype.hpoa"
type DiseaseHPOEvidences {
  "This field refers to the database and database identifier. EG. OMIM"
  diseaseFromSourceId: String!

  "This field indicates the source of the information used for the annotation (phenotype.hpoa)"
  references: [String!]!

  "Related name from the field diseaseFromSourceId"
  diseaseFromSource: String!

  "This refers to the center or user making the annotation and the date on which the annotation was made"
  bioCuration: String

  "Possible source mapping: HPO or MONDO"
  resource: String!

  "This field contains the strings MALE or FEMALE if the annotation in question is limited to males or females."
  sex: String

  "One of P (Phenotypic abnormality), I (inheritance), C (onset and clinical course). Might be null (MONDO)"
  aspect: String

  "This field indicates the level of evidence supporting the annotation."
  evidenceType: String

  "A term-id from the HPO-sub-ontology"
  frequency: String

  "This optional field can be used to qualify the annotation. Values: [True or False]"
  qualifierNot: Boolean!

  "HP terms from the Clinical modifier subontology"
  modifiers: [HPO!]!

  "A term-id from the HPO-sub-ontology below the term Age of onset."
  onset: [HPO!]!

  "HPO Entity"
  frequencyHPO: HPO
}

"List of Phenotypes associated with the disease"
type DiseaseHPOs {
  "Number of entries"
  count: Long!

  "List of Disease and phenotypes annotations"
  rows: [DiseaseHPO!]!
}

type DiseaseSynonyms {
  relation: String!
  terms: [String!]!
}

"Drug/Molecule entity"
type Drug {
  "Open Targets molecule id"
  id: String!

  "Drug description"
  description: String

  "Alert on life-threteaning drug side effects provided by FDA"
  blackBoxWarning: Boolean!

  "Year drug was approved for the first time"
  yearOfFirstApproval: Int

  "Maximum phase observed in clinical trial records and post-marketing package inserts"
  maximumClinicalTrialPhase: Float

  "Drug modality"
  drugType: String!
  crossReferences: [DrugReferences!]

  "Alias for maximumClinicalTrialPhase == 4"
  isApproved: Boolean

  "Molecule synonyms"
  synonyms: [String!]!

  "Has drug been withdrawn from the market"
  hasBeenWithdrawn: Boolean!

  "Drug trade names"
  tradeNames: [String!]!

  "Molecule preferred name"
  name: String!

  "ChEMBL ID of parent molecule"
  parentMolecule: Drug

  "Chembl IDs of molecules that descend from current molecule."
  childMolecules: [Drug!]!

  "Indications for which there is a phase IV clinical trial"
  approvedIndications: [String!]

  "Warnings present on drug as identified by ChEMBL."
  drugWarnings: [DrugWarning!]!

  "Return similar labels using a model Word2CVec trained with PubMed"
  similarEntities(
    "List of IDs either EFO ENSEMBL CHEMBL"
    additionalIds: [String!],

    "List of entity names to search for (target, disease, drug,...)"
    entityNames: [String!],

    "Threshold similarity between 0 and 1"
    threshold: Float, size: Int): [Similarity!]!

  "Return the list of publications that mention the main entity, alone or in combination with other entities"
  literatureOcurrences(
    "List of IDs either EFO ENSEMBL CHEMBL"
    additionalIds: [String!],

    "Year at the lower end of the filter"
    startYear: Int,

    "Month at the lower end of the filter"
    startMonth: Int,

    "Year at the higher end of the filter"
    endYear: Int,

    "Month at the higher end of the filter"
    endMonth: Int, cursor: String): Publications!

  "Mechanisms of action to produce intended pharmacological effects. Curated from scientific literature and post-marketing package inserts"
  mechanismsOfAction: MechanismsOfAction

  "Investigational and approved indications curated from clinical trial records and post-marketing package inserts"
  indications: Indications

  "Curated Clinical trial records and and post-marketing package inserts with a known mechanism of action"
  knownDrugs(
    "Query string"
    freeTextQuery: String, size: Int, cursor: String): KnownDrugs

  "Significant adverse events inferred from FAERS reports"
  adverseEvents(
    "Pagination settings with index and size"
    page: Pagination): AdverseEvents

  "Pharmoacogenomics"
  pharmacogenomics(
    "Pagination settings with index and size"
    page: Pagination): [Pharmacogenomics!]!

  "Therapeutic indications for drug based on clinical trial data or post-marketed drugs, when mechanism of action is known""
  linkedDiseases: LinkedDiseases

  "Molecule targets based on drug mechanism of action"
  linkedTargets: LinkedTargets
}

type DrugReferences {
  reference: [String!]!
  source: String!
}

"Drug warnings as calculated by ChEMBL"
type DrugWarning {
  id: Long

  "ID of the curated EFO term that represents the high level warning class"
  efoIdForWarningClass: String

  "Source of withdrawal information"
  references: [DrugWarningReference!]

  "High level toxicity category by Meddra System Organ Class"
  toxicityClass: String

  " label of the curated EFO term that represents the adverse outcome"
  efoTerm: String

  "Reason for withdrawal"
  description: String

  "Country issuing warning"
  country: String

  "ID of the curated EFO term that represents the adverse outcome"
  efoId: String

  "Either 'black box warning' or 'withdrawn'"
  warningType: String!

  "Year of withdrawal"
  year: Int
  chemblIds: [String!]
}

type DrugWarningReference {
  id: String!
  source: String!
  url: String!
}

"Drug with drug identifiers"
type DrugWithIdentifiers {
  drugId: String
  drugFromSource: String

  "Drug entity"
  drug: Drug
}

union EntityUnionType = Target | Drug | Disease | Variant | Study

"Evidence for a Target-Disease pair"
type Evidence {
  diseaseFromSourceId: String
  biologicalModelId: String
  clinicalStatus: String

  "Evidence identifier"
  id: String!
  biomarkerList: [NameDescription!]
  targetFromSource: String
  biologicalModelGeneticBackground: String

  "Genetic origin of a population"
  ancestry: String
  geneticInteractionFDR: Float
  crisprScreenLibrary: String
  betaConfidenceIntervalUpper: Float
  pValueExponent: Long
  targetFromSourceId: String
  studyId: String
  confidence: String
  cohortId: String
  interactingTargetRole: String
  pValueMantissa: Float
  assays: [assays!]
  log2FoldChangeValue: Float
  studyStartDate: String
  pathways: [Pathway!]

  "Variant effect"
  variantEffect: String
  diseaseFromSource: String
  cohortDescription: String
  oddsRatio: Float
  cohortPhenotypes: [String!]

  "list of pub med publications ids"
  literature: [String!]

  "Overview of the statistical method used to calculate the association"
  statisticalMethodOverview: String
  interactingTargetFromSourceId: String

  "Direction On Trait"
  directionOnTrait: String

  "Confidence interval lower-bound"
  oddsRatioConfidenceIntervalLower: Float

  "Sample size"
  studySampleSize: Long

  "Evidence score"
  score: Float!
  cohortShortName: String

  "Primary Project Hit"
  primaryProjectHit: Boolean
  significantDriverMethods: [String!]

  "Identifier of the ancestry in the HANCESTRO ontology"
  ancestryId: String
  studyStopReason: String
  diseaseFromSourceMappedId: String
  publicationFirstAuthor: String
  phenotypicConsequencePValue: Float
  clinicalSignificances: [String!]

  "Predicted reason(s) why the study has been stopped based on studyStopReason"
  studyStopReasonCategories: [String!]
  diseaseModelAssociatedHumanPhenotypes: [LabelledElement!]
  biosamplesFromSource: [String!]
  phenotypicConsequenceLogFoldChange: Float
  targetModulation: String
  diseaseModelAssociatedModelPhenotypes: [LabelledElement!]
  phenotypicConsequenceFDR: Float

  "Variant dbSNP identifier"
  variantRsId: String
  diseaseCellLines: [DiseaseCellLine!]
  targetInModel: String
  datatypeId: String!
  publicationYear: Long
  reactionId: String
  urls: [LabelledUri!]
  biomarkers: biomarkers
  drugFromSource: String
  biologicalModelAllelicComposition: String
  biomarkerName: String
  log2FoldChangePercentileRank: Long

  "Assessments"
  assessments: [String!]
  mutatedSamples: [EvidenceVariation!]
  geneticInteractionPValue: Float

  "Release date"
  releaseDate: String
  allelicRequirements: [String!]
  contrast: String
  projectDescription: String
  reactionName: String

  "Warning message"
  warningMessage: String
  beta: Float
  textMiningSentences: [EvidenceTextMiningSentence!]
  cellType: String
  studyOverview: String
  geneInteractionType: String
  datasourceId: String!
  clinicalPhase: Float

  "Number of cases in a case-control study that carry at least one allele of the qualifying variant"
  studyCasesWithQualifyingVariants: Long
  targetRole: String
  projectId: String

  "Release version"
  releaseVersion: String
  variantAminoacidDescriptions: [String!]
  statisticalTestTail: String

  "The statistical method used to calculate the association"
  statisticalMethod: String

  "Primary Project Id"
  primaryProjectId: String
  alleleOrigins: [String!]
  geneticInteractionScore: Float
  oddsRatioConfidenceIntervalUpper: Float
  resourceScore: Float
  betaConfidenceIntervalLower: Float
  studyCases: Long
  cellLineBackground: String

  "Target evidence"
  target: Target!

  "Disease evidence"
  disease: Disease!
  credibleSet: CredibleSet
  variant: Variant
  drug: Drug
  drugResponse: Disease
  variantFunctionalConsequence: SequenceOntologyTerm
  variantFunctionalConsequenceFromQtlId: SequenceOntologyTerm

  "list of central pub med publications ids"
  pubMedCentralIds: [String!]
}

type EvidenceSource {
  datatype: String!
  datasource: String!
}

type EvidenceTextMiningSentence {
  tStart: Long!
  section: String!
  text: String!
  dStart: Long!
  dEnd: Long!
  tEnd: Long!
}

"Sequence Ontology Term"
type EvidenceVariation {
  numberMutatedSamples: Long
  numberSamplesWithMutationType: Long
  numberSamplesTested: Long
  functionalConsequence: SequenceOntologyTerm
}

"Evidence for a Target-Disease pair"
type Evidences {
  count: Long!
  cursor: String
  rows: [Evidence!]!
}

type Expression {
  rna: RNAExpression!
  protein: ProteinExpression!
  tissue: Tissue!
}

type GeneEssentialityScreen {
  diseaseFromSource: String
  depmapId: String
  cellLineName: String
  geneEffect: Float
  diseaseCellLineId: String
  expression: Float
  mutation: String
}

type GeneOntology {
  geneProduct: String!
  source: String!
  aspect: String!
  evidence: String!

  "Gene ontology term"
  term: GeneOntologyTerm!
}

type GeneOntologyTerm {
  id: String!
  name: String!
}

type GenomicLocation {
  chromosome: String!
  start: Long!
  end: Long!
  strand: Int!
}

"Phenotype entity"
type HPO {
  "Open Targets hpo id"
  id: String!

  "Phenotype description"
  description: String

  "namespace"
  namespace: [String!]

  "Phenotype name"
  name: String!
}

type HallmarkAttribute {
  description: String!
  pmid: Long
  name: String!
}

type Hallmarks {
  attributes: [HallmarkAttribute!]!
  cancerHallmarks: [CancerHallmark!]!
}

type Homologue {
  speciesId: String!
  homologyType: String!
  speciesName: String!
  queryPercentageIdentity: Float!
  targetGeneSymbol: String!
  targetPercentageIdentity: Float!
  isHighConfidence: String
  targetGeneId: String!
}

type IdAndSource {
  id: String!
  source: String!
}

type IndicationReference {
  source: String!
  ids: [String!]
}

type IndicationRow {
  maxPhaseForIndication: Float!
  references: [IndicationReference!]

  "Disease"
  disease: Disease!
}

type Indications {
  approvedIndications: [String!]
  count: Long!
  rows: [IndicationRow!]!
}

type Interaction {
  count: Long!
  speciesA: InteractionSpecies
  speciesB: InteractionSpecies
  sourceDatabase: String!
  intBBiologicalRole: String!
  intABiologicalRole: String!
  intA: String!
  intB: String!
  score: Float
  targetA: Target
  targetB: Target

  "List of evidences for this interaction"
  evidences: [InteractionEvidence!]!
}

type InteractionEvidence {
  hostOrganismScientificName: String
  participantDetectionMethodB: [InteractionEvidencePDM!]
  intBSource: String!
  intASource: String!
  hostOrganismTaxId: Long
  participantDetectionMethodA: [InteractionEvidencePDM!]
  interactionIdentifier: String
  interactionDetectionMethodMiIdentifier: String!
  interactionTypeMiIdentifier: String
  interactionTypeShortName: String
  pubmedId: String
  interactionDetectionMethodShortName: String!
  expansionMethodMiIdentifier: String
  expansionMethodShortName: String
  evidenceScore: Float
}

type InteractionEvidencePDM {
  shortName: String
  miIdentifier: String
}

type InteractionResources {
  sourceDatabase: String!
  databaseVersion: String!
}

type InteractionSpecies {
  taxonId: Long
  mnemonic: String
  scientificName: String
}

type Interactions {
  count: Long!
  rows: [Interaction!]!
}

"A key-value pair"
type KeyValue {
  key: String!
  value: String!
}

"An array of key-value pairs"
type KeyValueArray {
  items: [KeyValue!]!
}

"Clinical precedence entry for drugs with investigational or approved indications targeting gene products according to their curated mechanism of action. Entries are grouped by target, disease, drug, phase, status and mechanism of action"
type KnownDrug {
  "Clinical Trial phase"
  phase: Float!

  "Source urls for FDA or package inserts"
  references: [KnownDrugReference!]!

  "Drug name"
  prefName: String!

  "Trial status"
  status: String

  "Curated disease indication"
  label: String!

  "Open Targets drug id"
  drugId: String!

  "Drug target Open Targets id based on curated mechanism of action"
  targetId: String!

  "Drug modality"
  drugType: String!

  "Curated disease indication Open Targets id"
  diseaseId: String!

  "Source urls from clinical trials"
  urls: [URL!]!

  "Drug target approved symbol based on curated mechanism of action"
  approvedSymbol: String!

  "Clinicaltrials.gov identifiers on entry trials"
  ctIds: [String!]!
  approvedName: String!

  "Drug target class based on curated mechanism of action"
  targetClass: [String!]!

  "Mechanism of Action description"
  mechanismOfAction: String!

  "Curated disease indication entity"
  disease: Disease

  "Drug target entity based on curated mechanism of action"
  target: Target

  "Curated drug entity"
  drug: Drug
}

type KnownDrugReference {
  urls: [String!]!
  source: String!
  ids: [String!]!
}

"Set of clinical precedence for drugs with investigational or approved indications targeting gene products according to their curated mechanism of action"
type KnownDrugs {
  cursor: String

  "Total unique known mechanism of action targetsTotal unique known mechanism of action targets"
  uniqueTargets: Long!

  "Total number of entries"
  count: Long!

  "Clinical precedence entries with known mechanism of action"
  rows: [KnownDrug!]!

  "Total unique diseases or phenotypes"
  uniqueDiseases: Long!

  "Total unique drugs/molecules"
  uniqueDrugs: Long!
}

type L2GFeature {
  value: Float!
  shapValue: Float!
  name: String!
}

type L2GPrediction {
  score: Float!
  features: [L2GFeature!]
  studyLocusId: String!
  shapBaseValue: Float!

  "Target"
  target: Target
}

type L2GPredictions {
  id: String!
  count: Long!
  rows: [L2GPrediction!]!
}

type LabelAndSource {
  label: String!
  source: String!
}

type LabelledElement {
  id: String!
  label: String!
}

type LabelledUri {
  niceName: String
  url: String!
}

type LdPopulationStructure {
  ldPopulation: String
  relativeSampleSize: Float
}

type LdSet {
  tagVariantId: String
  r2Overall: Float
}

"Linked Disease Entities"
type LinkedDiseases {
  count: Int!

  "Disease List"
  rows: [Disease!]!
}

"Linked Target Entities"
type LinkedTargets {
  count: Int!

  "Target List"
  rows: [Target!]!
}

type LocationAndSource {
  labelSL: String
  termSL: String
  location: String!
  source: String!
}

type Loci {
  count: Long!
  rows: [Locus!]
}

type Locus {
  is95CredibleSet: Boolean
  pValueExponent: Int
  pValueMantissa: Float
  standardError: Float
  posteriorProbability: Float
  r2Overall: Float
  is99CredibleSet: Boolean
  logBF: Float
  beta: Float
  variant: Variant
}

type MappingResult {
  hits: [SearchResult!]
  term: String!
}

type MappingResults {
  aggregations: SearchResultAggs
  total: Long!

  "Mappings"
  mappings: [MappingResult!]!
}

type Match {
  mappedId: String!
  matchedLabel: String!
  sectionStart: Long
  sectionEnd: Long
  startInSentence: Long!
  endInSentence: Long!

  "Type of the matched label"
  matchedType: String!
}

type MechanismOfActionRow {
  references: [Reference!]
  actionType: String
  targetName: String
  mechanismOfAction: String!

  "Target List"
  targets: [Target!]!
}

type MechanismsOfAction {
  uniqueTargetTypes: [String!]!
  rows: [MechanismOfActionRow!]!
  uniqueActionTypes: [String!]!
}

type Meta {
  dataVersion: DataVersion!
  apiVersion: APIVersion!
  name: String!
}

type ModelPhenotypeClasses {
  id: String!
  label: String!
}

type MousePhenotype {
  modelPhenotypeId: String!
  biologicalModels: [BiologicalModels!]!
  targetInModelMgiId: String!
  targetInModel: String!
  modelPhenotypeLabel: String!
  targetInModelEnsemblId: String
  modelPhenotypeClasses: [ModelPhenotypeClasses!]!
}

type NameDescription {
  description: String!
  name: String!
}

type OtarProject {
  integratesInPPP: Boolean
  otarCode: String!
  status: String
  reference: String!
  projectName: String
}

input Pagination {
  index: Int!
  size: Int!
}

"Pathway entry"
type Pathway {
  id: String!
  name: String!
}

type Pharmacogenomics {
  isDirectTarget: Boolean!
  genotypeAnnotationText: String
  haplotypeFromSourceId: String
  phenotypeText: String
  pgxCategory: String
  genotypeId: String
  targetFromSourceId: String
  studyId: String
  literature: [String!]
  variantRsId: String
  datatypeId: String
  variantFunctionalConsequenceId: String
  phenotypeFromSourceId: String
  evidenceLevel: String
  datasourceId: String
  variantId: String
  genotype: String
  haplotypeId: String
  variantFunctionalConsequence: SequenceOntologyTerm

  "Target entity"
  target: Target

  "Drug List"
  drugs: [DrugWithIdentifiers!]!
}

type ProteinExpression {
  level: Int!
  reliability: Boolean!
  cellType: [CellType!]!
}

type Publication {
  pmid: String!
  pmcid: String

  "Publication Date"
  publicationDate: String

  "Unique counts per matched keyword"
  sentences: [Sentence!]
}

"Publication list"
type Publications {
  count: Long!
  filteredCount: Long!

  "Earliest publication year."
  earliestPubYear: Int!
  cursor: String
  rows: [Publication!]!
}

type Query {
  "Return Open Targets API metadata information"
  meta: Meta!

  "Return a Target"
  target(
    "Ensembl ID"
    ensemblId: String!): Target

  "Return Targets"
  targets(
    "List of Ensembl IDs"
    ensemblIds: [String!]!): [Target!]!

  "Return a Disease"
  disease(
    "EFO ID"
    efoId: String!): Disease

  "Return Diseases"
  diseases(
    "EFO ID"
    efoIds: [String!]!): [Disease!]!

  "Return a drug"
  drug(
    "Chembl ID"
    chemblId: String!): Drug

  "Return drugs"
  drugs(
    "List of Chembl IDs"
    chemblIds: [String!]!): [Drug!]!

  "Multi entity search"
  search(
    "Query string"
    queryString: String!,

    "List of entity names to search for (target, disease, drug,...)"
    entityNames: [String!],

    "Pagination settings with index and size"
    page: Pagination): SearchResults!

  "Search facets"
  facets(
    "Query string"
    queryString: String,

    "List of entity names to search for (target, disease, drug,...)"
    entityNames: [String!],

    "Category"
    category: String,

    "Pagination settings with index and size"
    page: Pagination): SearchFacetsResults!

  "Map terms to IDs"
  mapIds(
    "List of query terms to map"
    queryTerms: [String!]!,

    "List of entity names to search for (target, disease, drug,...)"
    entityNames: [String!]): MappingResults!

  "The complete list of all possible datasources"
  associationDatasources: [EvidenceSource!]!

  "The complete list of all possible datasources"
  interactionResources: [InteractionResources!]!

  "Gene ontology terms"
  geneOntologyTerms(
    "List of GO IDs, eg. GO:0005515"
    goIds: [String!]!): [GeneOntologyTerm]!

  "Return a Variant"
  variant(
    "Variant ID"
    variantId: String!): Variant

  "Return a Study"
  study(
    "Study ID"
    studyId: String): Study

  "Return a studies"
  studies(
    "Pagination settings with index and size"
    page: Pagination,

    "Study ID"
    studyId: String,

    "Disease IDs"
    diseaseIds: [String!],

    "Use the disease ontology to retrieve all its descendants and capture all their associated studies."
    enableIndirect: Boolean): Studies!

  "Return a Credible Set"
  credibleSet(
    "Study-locus ID"
    studyLocusId: String!): CredibleSet
  credibleSets(
    "Pagination settings with index and size"
    page: Pagination,

    "Study-locus IDs"
    studyLocusIds: [String!],

    "Study IDs"
    studyIds: [String!],

    "Variant IDs"
    variantIds: [String!],

    "Study types"
    studyTypes: [StudyTypeEnum!],

    "Regions"
    regions: [String!]): CredibleSets!
}

type RNAExpression {
  level: Int!
  unit: String!
  value: Float!
  zscore: Long!
}

type ReactomePathway {
  topLevelTerm: String!
  pathwayId: String!
  pathway: String!
}

type Reference {
  urls: [String!]
  source: String!
  ids: [String!]
}

type SafetyBiosample {
  tissueId: String
  cellLabel: String
  cellId: String
  cellFormat: String
  tissueLabel: String
}

type SafetyEffects {
  dosing: String
  direction: String!
}

type SafetyLiability {
  eventId: String
  event: String
  biosamples: [SafetyBiosample!]
  literature: String
  effects: [SafetyEffects!]
  studies: [SafetyStudy!]
  url: String
  datasource: String!
}

type SafetyStudy {
  description: String
  type: String
  name: String
}

type Sample {
  sampleSize: Int
  ancestry: String
}

type ScoredComponent {
  id: String!
  score: Float!
}

type SearchFacetsCategory {
  total: Long!
  name: String!
}

type SearchFacetsResult {
  id: String!
  entityIds: [String!]
  label: String!
  datasourceId: String
  category: String!
  score: Float!
  highlights: [String!]!
}

"Search facets results"
type SearchFacetsResults {
  "Return combined"
  hits: [SearchFacetsResult!]!

  "Total number or results given a entity filter"
  total: Long!

  "Categories"
  categories: [SearchFacetsCategory!]!
}

type SearchResult {
  id: String!
  description: String
  multiplier: Float!
  prefixes: [String!]
  keywords: [String!]
  category: [String!]!
  score: Float!
  entity: String!
  ngrams: [String!]
  highlights: [String!]!
  name: String!

  "Associations for a fixed target"
  object: EntityUnionType
}

type SearchResultAggCategory {
  total: Long!
  name: String!
}

type SearchResultAggEntity {
  categories: [SearchResultAggCategory!]!
  total: Long!
  name: String!
}

type SearchResultAggs {
  entities: [SearchResultAggEntity!]!
  total: Long!
}

"Search results"
type SearchResults {
  "Aggregations"
  aggregations: SearchResultAggs

  "Return combined"
  hits: [SearchResult!]!

  "Total number or results given a entity filter"
  total: Long!
}

type Sentence {
  "Section of the publication (either title or abstract)"
  section: String!

  "List of matches"
  matches: [Match!]!
}

"Sequence Ontology Term"
type SequenceOntologyTerm {
  id: String!
  label: String!
}

type Similarity {
  id: String!
  category: String!
  score: Float!

  "Similarity label optionally resolved into an entity"
  object: EntityUnionType
}

"Studies"
type Studies {
  count: Long!
  rows: [Study!]!
}

"A genome-wide association study"
type Study {
  cohorts: [String!]
  initialSampleSize: String
  hasSumstats: Boolean
  traitFromSource: String
  publicationDate: String
  sumstatQCValues: [SumStatQC!]
  replicationSamples: [Sample!]
  publicationFirstAuthor: String
  discoverySamples: [Sample!]
  nSamples: Int
  qualityControls: [String!]
  publicationJournal: String
  publicationTitle: String
  pubmedId: String
  nControls: Int
  nCases: Int

  "Condition"
  condition: String

  "The project identifier"
  projectId: String
  ldPopulationStructure: [LdPopulationStructure!]
  analysisFlags: [String!]
  summarystatsLocation: String

  "The study identifier"
  id: String!

  "The study type"
  studyType: StudyTypeEnum

  "Target"
  target: Target

  "biosample"
  biosample: Biosample
  diseases: [Disease!]
  backgroundTraits: [Disease!]

  "Credible sets"
  credibleSets(
    "Pagination settings with index and size"
    page: Pagination): CredibleSets!
}

enum StudyTypeEnum {
  eqtl
  gwas
  pqtl
  sceqtl
  scpqtl
  scsqtl
  sctuqtl
  sqtl
  tuqtl
}

type SumStatQC {
  QCCheckValue: Float!
  QCCheckName: String!
}

"Target entity"
type Target {
  "Alternative names"
  nameSynonyms: [LabelAndSource!]!

  "Open Targets target id"
  id: String!

  "Obsolete symbols"
  obsoleteSymbols: [LabelAndSource!]!

  "Known target safety effects and target safety risk information"
  safetyLiabilities: [SafetyLiability!]!

  "Symbol synonyms"
  geneticConstraint: [Constraint!]!

  "Chromosomic location"
  genomicLocation: GenomicLocation!

  "Database cross references"
  dbXrefs: [IdAndSource!]!

  "Target Enabling Package (TEP)"
  tep: Tep

  "Location of ..."
  subcellularLocations: [LocationAndSource!]!

  "Reactome pathways"
  pathways: [ReactomePathway!]!

  "Molecule biotype"
  biotype: String!

  "Gene Ontology annotations"
  geneOntology: [GeneOntology!]!

  "Related protein IDs"
  proteinIds: [IdAndSource!]!

  "Target-modulated essential alterations in cell physiology that dictate malignant growth"
  hallmarks: Hallmarks

  "Obsolete names"
  obsoleteNames: [LabelAndSource!]!

  "Ensembl transcript IDs"
  transcriptIds: [String!]!

  "HGNC approved symbol"
  approvedSymbol: String!

  "Alternative symbols"
  symbolSynonyms: [LabelAndSource!]!

  "Target druggability assessment"
  tractability: [Tractability!]!

  "Approved gene name"
  approvedName: String!
  targetClass: [TargetClass!]!

  "..."
  functionDescriptions: [String!]!

  "Alternative names and symbols"
  synonyms: [LabelAndSource!]!
  chemicalProbes: [ChemicalProbe!]!

  "Gene homologues"
  homologues: [Homologue!]!
  alternativeGenes: [String!]!

  "Return similar labels using a model Word2CVec trained with PubMed"
  similarEntities(
    "List of IDs either EFO ENSEMBL CHEMBL"
    additionalIds: [String!],

    "List of entity names to search for (target, disease, drug,...)"
    entityNames: [String!],

    "Threshold similarity between 0 and 1"
    threshold: Float, size: Int): [Similarity!]!

  "Return the list of publications that mention the main entity, alone or in combination with other entities"
  literatureOcurrences(
    "List of IDs either EFO ENSEMBL CHEMBL"
    additionalIds: [String!],

    "Year at the lower end of the filter"
    startYear: Int,

    "Month at the lower end of the filter"
    startMonth: Int,

    "Year at the higher end of the filter"
    endYear: Int,

    "Month at the higher end of the filter"
    endMonth: Int, cursor: String): Publications!

  "The complete list of all possible datasources"
  evidences(
    "EFO ID"
    efoIds: [String!]!,

    "List of datasource ids"
    datasourceIds: [String!], size: Int, cursor: String): Evidences!

  "Biological pathway membership from Reactome"
  interactions(
    "Threshold similarity between 0 and 1"
    scoreThreshold: Float,

    "Database name"
    sourceDatabase: String,

    "Pagination settings with index and size"
    page: Pagination): Interactions

  "Biological pathway membership from Reactome"
  mousePhenotypes: [MousePhenotype!]!

  "RNA and Protein baseline expression"
  expressions: [Expression!]!

  "Clinical precedence for drugs with investigational or approved indications targeting gene products according to their curated mechanism of action"
  knownDrugs(
    "Query string"
    freeTextQuery: String, size: Int, cursor: String): KnownDrugs

  "associations on the fly"
  associatedDiseases(
    "List of disease or target IDs"
    Bs: [String!],

    "Utilize the target interactions to retrieve all diseases associated with them and capture their respective evidence."
    enableIndirect: Boolean,

    "List of datasource settings"
    datasources: [DatasourceSettingsInput!],

    "List of the facet IDs to filter by (using AND)"
    facetFilters: [String!],

    "Filter to apply to the ids with string prefixes"
    BFilter: String,

    "Ordering for the associations. By default is score desc"
    orderByScore: String,

    "Pagination settings with index and size"
    page: Pagination): AssociatedDiseases!

  "Factors influencing target-specific properties informative in a target prioritisation strategy. Values range from -1 (deprioritised) to 1 (prioritised)."
  prioritisation: KeyValueArray

  "isEssential"
  isEssential: Boolean

  "depMapEssentiality"
  depMapEssentiality: [DepMapEssentiality!]

  "Pharmoacogenomics"
  pharmacogenomics(
    "Pagination settings with index and size"
    page: Pagination): [Pharmacogenomics!]!
}

type TargetClass {
  id: Long!
  label: String!
  level: String!
}

"Target Enabling Package (TEP)"
type Tep {
  description: String!
  name: String!
  therapeuticArea: String!
  uri: String!
}

"Tissue, organ and anatomical system"
type Tissue {
  "UBERON id"
  id: String!

  "UBERON tissue label"
  label: String!

  "Organs membership"
  organs: [String!]!

  "Anatomical systems membership"
  anatomicalSystems: [String!]!
}

type Tractability {
  label: String!
  modality: String!
  value: Boolean!
}

type TranscriptConsequence {
  aminoAcidChange: String
  transcriptId: String
  lofteePrediction: String
  uniprotAccessions: [String!]
  distanceFromTss: Int!
  codons: String
  impact: String
  polyphenPrediction: Float
  consequenceScore: Float!
  distanceFromFootprint: Int!
  transcriptIndex: Long!
  siftPrediction: Float
  isEnsemblCanonical: Boolean!

  "Target"
  target: Target

  "Most severe consequence sequence ontology"
  variantConsequences: [SequenceOntologyTerm!]!
}

"Source URL for clinical trials, FDA and package inserts"
type URL {
  "resource url"
  url: String!

  "resource name"
  name: String!
}

type Variant {
  variantDescription: String!
  chromosome: String!
  id: String!
  dbXrefs: [DbXref!]
  position: Int!
  variantEffect: [VariantEffect!]
  transcriptConsequences: [TranscriptConsequence!]
  referenceAllele: String!
  alleleFrequencies: [AlleleFrequency!]
  rsIds: [String!]
  alternateAllele: String!
  hgvsId: String

  "Most severe consequence sequence ontology"
  mostSevereConsequence: SequenceOntologyTerm

  "Credible sets"
  credibleSets(
    "Pagination settings with index and size"
    page: Pagination,

    "Study types"
    studyTypes: [StudyTypeEnum!]): CredibleSets!

  "Pharmoacogenomics"
  pharmacogenomics(
    "Pagination settings with index and size"
    page: Pagination): [Pharmacogenomics!]!

  "The complete list of all possible datasources"
  evidences(
    "List of datasource ids"
    datasourceIds: [String!], size: Int, cursor: String): Evidences!
}

type VariantEffect {
  normalisedScore: Float
  assessmentFlag: String
  assessment: String
  score: Float
  method: String

  "Target"
  target: Target
}

type assays {
  description: String
  shortName: String
  isHit: Boolean
}

type biomarkers {
  geneExpression: [BiomarkerGeneExpression!]
  geneticVariation: [geneticVariation!]
}

type geneticVariation {
  id: String
  name: String
  functionalConsequenceId: SequenceOntologyTerm
}�.