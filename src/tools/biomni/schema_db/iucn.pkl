���      }�(�base_url��$https://apiv3.iucnredlist.org/api/v4��	endpoints�}�(�
assessment�}�(�url��%{base_url}/assessment/{assessment_id}��description��Retrieves an assessment��required�]��
assessment_id�a�method��GET�u�biogeographical_realms_list�}�(h�!{base_url}/biogeographical_realms�h	�+Returns a list of biogeographic realm codes�h]�hhu�"biogeographical_realms_assessments�}�(h�({base_url}/biogeographical_realms/{code}�h	�DReturns a collection of assessments for a biogeographical realm code�h]��code�ahhu�comprehensive_groups_list�}�(h�{base_url}/comprehensive_groups�h	�&Returns a list of comprehensive groups�h]�hhu� comprehensive_groups_assessments�}�(h�&{base_url}/comprehensive_groups/{name}�h	�BReturns a collection of assessments for a comprehensive group name�h]��name�ahhu�conservation_actions_list�}�(h�{base_url}/conservation_actions�h	�&Returns a list of conservation actions�h]�hhu� conservation_actions_assessments�}�(h�&{base_url}/conservation_actions/{code}�h	�BReturns a collection of assessments for a conservation action code�h]�hahhu�countries_list�}�(h�{base_url}/countries�h	�/Returns a list of countries by ISO alpha-2 code�h]�hhu�countries_assessments�}�(h�{base_url}/countries/{code}�h	�HReturns a collection of assessments for a given country ISO alpha-2 code�h]�hahhu�	faos_list�}�(h�{base_url}/faos�h	�Returns a list of FAOs�h]�hhu�faos_assessments�}�(h�{base_url}/faos/{code}�h	�3Returns a collection of assessments for an FAO code�h]�hahhu�growth_forms_list�}�(h�{base_url}/growth_forms�h	�Returns a list of growth forms�h]�hhu�growth_forms_assessments�}�(h�{base_url}/growth_forms/{code}�h	�@Returns a collection of assessments for a given growth form code�h]�hahhu�green_status_all�}�(h�{base_url}/green_status/all�h	�.Returns a list of all Green Status assessments�h]�hhu�
habitats_list�}�(h�{base_url}/habitats�h	�Returns a list of habitat codes�h]�hhu�habitats_assessments�}�(h�{base_url}/habitats/{code}�h	�<Returns a collection of assessments for a given habitat code�h]�hahhu�information_api_version�}�(h�"{base_url}/information/api_version�h	�QReturns the current version number of the IUCN Red List of Threatened Species API�h]�hhu�information_red_list_version�}�(h�'{base_url}/information/red_list_version�h	�?Returns the current IUCN Red List of Threatened Species version�h]�hhu�population_trends_list�}�(h�{base_url}/population_trends�h	�#Returns a list of population trends�h]�hhu�population_trends_assessments�}�(h�#{base_url}/population_trends/{code}�h	�EReturns a collection of assessments for a given population trend code�h]�hahhu�red_list_categories_list�}�(h�{base_url}/red_list_categories�h	�%Returns a list of Red List categories�h]�hhu�red_list_categories_assessments�}�(h�%{base_url}/red_list_categories/{code}�h	�FReturns a collection of assessments for a given Red List category code�h]�hahhu�
research_list�}�(h�{base_url}/research�h	� Returns a list of research codes�h]�hhu�research_assessments�}�(h�{base_url}/research/{code}�h	�=Returns a collection of assessments for a given research code�h]�hahhu�scopes_list�}�(h�{base_url}/scopes�h	�Returns a list of scopes�h]�hhu�scopes_assessments�}�(h�{base_url}/scopes/{code}�h	�:Returns a collection of assessments for a given scope code�h]�hahhu�statistics_count�}�(h�{base_url}/statistics/count�h	�6Return count of the number of species with assessments�h]�hhu�
stresses_list�}�(h�{base_url}/stresses�h	�Returns a list of stressors�h]�hhu�stresses_assessments�}�(h�{base_url}/stresses/{code}�h	�;Returns a collection of assessments for a given stress code�h]�hahhu�systems_list�}�(h�{base_url}/systems�h	�Returns a list of systems�h]�hhu�systems_assessments�}�(h�{base_url}/systems/{code}�h	�;Returns a collection of assessments for a given system code�h]�hahhu�taxa_by_sis�}�(h�{base_url}/taxa/sis/{sis_id}�h	�6Returns a collection of assessments for a given SIS id�h]��sis_id�ahhu�taxa_by_scientific_name�}�(h�{base_url}/taxa/scientific_name�h	�cReturns a collection of assessments for a given genus_name and species_name and optional infra_name�h]�(�
genus_name��species_name�ehhu�taxa_kingdom_list�}�(h�{base_url}/taxa/kingdom�h	�#Returns a list of all kingdom names�hhu�taxa_kingdom_assessments�}�(h�&{base_url}/taxa/kingdom/{kingdom_name}�h	�GReturns a collection of the latest assessments for a given kingdom_name�h]��kingdom_name�ahhu�taxa_phylum_list�}�(h�{base_url}/taxa/phylum�h	�"Returns a list of all phylum names�h]�hhu�taxa_phylum_assessments�}�(h�${base_url}/taxa/phylum/{phylum_name}�h	�FReturns a collection of the latest assessments for a given phylum_name�h]��phylum_name�ahhu�taxa_class_list�}�(h�{base_url}/taxa/class�h	�!Returns a list of all class names�h]�hhu�taxa_class_assessments�}�(h�"{base_url}/taxa/class/{class_name}�h	�EReturns a collection of the latest assessments for a given class_name�h]��
class_name�ahhu�taxa_order_list�}�(h�{base_url}/taxa/order�h	�!Returns a list of all order names�h]�hhu�taxa_order_assessments�}�(h�"{base_url}/taxa/order/{order_name}�h	�EReturns a collection of the latest assessments for a given order_name�h]��
order_name�ahhu�taxa_family_list�}�(h�{base_url}/taxa/family�h	�"Returns a list of all family names�h]�hhu�taxa_family_assessments�}�(h�${base_url}/taxa/family/{family_name}�h	�FReturns a collection of the latest assessments for a given family_name�h]��family_name�ahhu�taxa_possibly_extinct�}�(h� {base_url}/taxa/possibly_extinct�h	�XReturns a collection of all latest global assessments for taxa that are possibly extinct�h]�hhu�!taxa_possibly_extinct_in_the_wild�}�(h�,{base_url}/taxa/possibly_extinct_in_the_wild�h	�dReturns a collection of all latest global assessments for taxa that are possibly extinct in the wild�h]�hhu�threats_list�}�(h�{base_url}/threats�h	�Returns a list of threats�h]�hhu�threats_assessments�}�(h�{base_url}/threats/{code}�h	�;Returns a collection of assessments for a given threat code�h]�hahhu�use_and_trade_list�}�(h�{base_url}/use_and_trade�h	� Returns a list of use and trades�h]�hhu�use_and_trade_assessments�}�(h�{base_url}/use_and_trade/{code}�h	�BReturns a collection of assessments for a given use and trade code�h]�hahhuu�parameter_details�}�(h
}�(h	�#Unique identifier for an assessment��format��String�uh}�(h	�2Code identifier for various classification schemes�j
  j  uh%}�(h	�,Name identifier for various taxonomic groups�j
  j  uh�}�(h	�,Species Information Service (SIS) identifier�j
  j  uh�}�(h	�"Genus component of scientific name�j
  j  uh�}�(h	�$Species component of scientific name�j
  j  u�
infra_name�}�(h	�<Infraspecific component of scientific name (e.g. subspecies)�j
  j  uh�}�(h	�Kingdom taxonomic name�j
  j  uh�}�(h	�Phylum taxonomic name�j
  j  uh�}�(h	�Class taxonomic name�j
  j  uh�}�(h	�Order taxonomic name�j
  j  uh�}�(h	�Family taxonomic name�j
  j  uuu.