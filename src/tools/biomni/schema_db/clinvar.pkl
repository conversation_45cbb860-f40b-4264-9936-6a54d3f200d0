��      X  {
    "search_fields": {
        "gene": "[gene]",
        "gene_id": "[geneid]",
        "gene_full_name": "[gene_full_name]",
        "variant": "[varnam]",
        "disease": "[dis]",
        "clinical_significance": "[clinsig]",
        "variation_id": "[uid]",
        "rs_id": "[varacc]",
        "hgvs": "[varnam]",
        "chromosome": "[chr]",
        "coordinate": "[chrpos]",
        "GRCh37_coordinate": "[chrpos37]",
        "GRCh38_coordinate": "[chrpos]",
        "allele_id": "[alleleid]",
        "phenotype": "[dis]",
        "property": "[prop]",
        "molecular_consequence": "[molcons]",
        "review_status": "[revstat]",
        "type_of_variation": "[vartype]",
        "origin": "[origin]",
        "pubmed_id": "[pmid]",
        "trait_identifier": "[traitid]",
        "clinvar_accession": "[clv_acc]",
        "common_name": "[commonname]",
        "canonical_spdi": "[cspdi]",
        "creation_date": "[cdat]",
        "modification_date": "[mdat]",
        "cytogenetic_band": "[cytgen]",
        "length_of_variant": "[varlen]"
    },
    "common_properties": {
        "clinsig_pathogenic": "clinsig pathogenic[prop]",
        "clinsig_likely_pathogenic": "clinsig likely pathogenic[prop]",
        "clinsig_uncertain_significance": "clinsig vus[prop]",
        "clinsig_likely_benign": "clinsig likely benign[prop]",
        "clinsig_benign": "clinsig benign[prop]",
        "clinsig_conflicting": "clinsig has conflicts[prop]",
        "clinsig_drug_response": "clinsig drug response[prop]",
        "clinsig_risk_factor": "clinsig risk factor[prop]",
        "clinsig_other": "clinsig other[prop]",
        "clinsig_not_provided": "clinsig not provided[prop]",
        
        "origin_germline": "origin germline[prop]",
        "origin_somatic": "origin somatic[prop]",
        "origin_de_novo": "origin de novo[prop]",
        "origin_maternal": "origin maternal[prop]",
        "origin_paternal": "origin paternal[prop]",
        "origin_biparental": "origin biparental[prop]",
        "origin_inherited": "origin inherited[prop]",
        "origin_uniparental": "origin uniparental[prop]",
        "origin_unknown": "origin unknown[prop]",
        "origin_not_provided": "origin not provided[prop]",
        
        "moi_autosomal_dominant": "moi autosomal dominant[prop]",
        "moi_autosomal_recessive": "moi autosomal recessive[prop]",
        "moi_autosomal_unknown": "moi autosomal unknown[prop]",
        "moi_x_linked_dominant": "moi X-linked dominant[prop]",
        "moi_x_linked_recessive": "moi X-linked recessive[prop]",
        "moi_mitochondrial": "moi mitochondrial[prop]",
        "moi_sporadic": "moi sporadic[prop]",
        "moi_sex_limited_autosomal_dominant": "moi sex-limited autosomal dominant[prop]",
        "moi_other": "moi other[prop]",
        
        "gene_single": "single gene[prop]",
        "gene_multiple": "multiple gene[prop]",
        "gene_spans_multiple": "spans multiple genes[prop]",
        "gene_in_overlapping": "in overlapping genes[prop]",
        "gene_acmg_incidental_2013": "gene acmg incidental 2013[prop]",
        "gene_asserted_not_computed": "gene asserted not computed[prop]"
    },
    "filters": {
        "all": "clinvar_all[filter]",
        "dbvar": "clinvar_dbvar[filter]",
        "gene": "clinvar_gene[filter]",
        "medgen": "clinvar_medgen[filter]",
        "omim": "clinvar_omim[filter]",
        "pmc": "clinvar_pmc[filter]",
        "pubmed": "clinvar_pubmed[filter]",
        "pubmed_calculated": "clinvar_pubmed_calculated[filter]",
        "snp": "clinvar_snp[filter]"
    }
}
�.