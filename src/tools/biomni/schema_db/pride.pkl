��I      }�(�base_url��)https://www.ebi.ac.uk/pride/ws/archive/v3��	endpoints�}�(�get_project_status�}�(�url��{base_url}/status/{accession}��description��Get project status by accession��required�]��	accession�a�optional�]��method��GET�u�search_projects�}�(h�{base_url}/search/projects�h	�Search projects�h]�h]�hhu�search_autocomplete�}�(h�{base_url}/search/autocomplete�h	� Autocomplete search for projects�h]�h]�hhu�get_project_by_accession�}�(h�&{base_url}/projects/{projectAccession}�h	�Get project by accession�h]��projectAccession�ah]�hhu�get_project_files_count�}�(h�2{base_url}/projects/{projectAccession}/files/count�h	� Get count of files for a project�h]�h#ah]�hhu�get_project_all_files�}�(h�0{base_url}/projects/{projectAccession}/files/all�h	�Get all files for a project�h]�h#ah]�hhu�get_project_files�}�(h�,{base_url}/projects/{projectAccession}/files�h	�Get files for a project�h]�h#ah]�hhu�get_similar_projects�}�(h�/{base_url}/projects/{accession}/similarProjects�h	�Get similar projects�h]�h
ah]�hhu�get_project_reanalysis�}�(h�1{base_url}/projects/reanalysis/{projectAccession}�h	�Get project reanalysis�h]�h#ah]�hhu�get_projects_metadata�}�(h�{base_url}/projects/metadata�h	�Get projects metadata�h]�h]�hhu�get_projects_files_path�}�(h�1{base_url}/projects/files-path/{projectAccession}�h	�Get project files path�h]�h#ah]�hhu�download_projects�}�(h�{base_url}/projects/download�h	�Download projects�h]�h]�hhu�download_projects_by_keyword�}�(h�'{base_url}/projects/download/by/keyword�h	�Download projects by keyword�h]�h]�hhu�get_projects_count�}�(h�{base_url}/projects/count�h	�Get count of projects�h]�h]�hhu�get_all_projects�}�(h�{base_url}/projects/all�h	�Get all projects�h]�h]�hhu�get_projects�}�(h�{base_url}/projects�h	�Get projects�h]�h]�hhu�get_files_checksum�}�(h�,{base_url}/files/checksum/{projectAccession}�h	� Get files checksum for a project�h]�h#ah]�hhu�facet_projects�}�(h�{base_url}/facet/projects�h	�"Get facet information for projects�h]�h]�hhu�get_stats_by_name�}�(h�{base_url}/stats/{name}�h	�Get statistics by name�h]��name�ah]�hhu�get_submitted_data_stats�}�(h�{base_url}/stats/submitted-data�h	�Get submitted data statistics�h]�h]�hhu�get_submissions_monthly_tsv�}�(h�({base_url}/stats/submissions-monthly-tsv�h	�%Get monthly submissions in TSV format�h]�h]�hhu�get_submissions_monthly�}�(h�${base_url}/stats/submissions-monthly�h	�Get monthly submissions�h]�h]�hhu�search_affinity_projects�}�(h�#{base_url}/pride-ap/search/projects�h	�Search affinity projects�h]�h]�hhu�affinity_search_autocomplete�}�(h�'{base_url}/pride-ap/search/autocomplete�h	�)Autocomplete search for affinity projects�h]�h]�hhu�get_similar_affinity_projects�}�(h�8{base_url}/pride-ap/projects/{accession}/similarProjects�h	�Get similar affinity projects�h]�h
ah]�hhu�get_affinity_projects�}�(h�{base_url}/pride-ap/projects�h	�Get affinity projects�h]�h]�hhu�facet_affinity_projects�}�(h�"{base_url}/pride-ap/facet/projects�h	�+Get facet information for affinity projects�h]�h]�hhu�get_chat_history�}�(h�{base_url}/getChatHistory�h	�Get chat history�h]�h]�hhu�
get_benchmark�}�(h�{base_url}/getBenchmark�h	�Get benchmark information�h]�h]�hhu�get_file_by_accession�}�(h� {base_url}/files/{fileAccession}�h	�Get file by accession�h]��
fileAccession�ah]�hhu�get_sdrf_files�}�(h�({base_url}/files/sdrf/{projectAccession}�h	�Get SDRF files for a project�h]�h#ah]�hhu�get_count_files_by_type�}�(h�9{base_url}/files/getCountOfFilesByType/{projectAccession}�h	�(Get count of files by type for a project�h]�h#ah]�hhu�get_files_count�}�(h�{base_url}/files/count�h	�Get count of files�h]�h]�hhu�
get_all_files�}�(h�{base_url}/files/all�h	�
Get all files�h]�h]�hhuuu.