��=      }�(�base_url��https://rest.ensembl.org��version��15.9��
categories�}�(�Archive�}�(�description��Archive resource endpoints��	endpoints�}��GET archive/id/:id�}�(h	�6Uses the given identifier to return its latest version��required_params�]��id�a�optional_params�]��example��/archive/id/ENSG00000157764�usu�Comparative Genomics�}�(h	�'Endpoints for comparative genomics data�h}�(�GET cafe/genetree/id/:id�}�(h	�LRetrieves a cafe tree of the gene tree using the gene tree stable identifier�h]�hah]�h�%/cafe/genetree/id/ENSGT00390000003602�u�0GET cafe/genetree/member/symbol/:species/:symbol�}�(h	�VRetrieves the cafe tree of the gene tree that contains the gene identified by a symbol�h]�(�species��symbol�eh]�h�//cafe/genetree/member/symbol/homo_sapiens/BRCA2�u�(GET cafe/genetree/member/id/:species/:id�}�(h	��Retrieves the cafe tree of the gene tree that contains the gene / transcript / translation stable identifier in the given species�h]�(h%heh]�h�5/cafe/genetree/member/id/homo_sapiens/ENSG00000139618�u�GET genetree/id/:id�}�(h	�7Retrieves a gene tree for a gene tree stable identifier�h]�hah]�(�aligned��sequence��	nh_format�eh� /genetree/id/ENSGT00390000003602�u�+GET genetree/member/symbol/:species/:symbol�}�(h	�ERetrieves the gene tree that contains the gene identified by a symbol�h]�(h%h&eh]�(h4h5h6eh�*/genetree/member/symbol/homo_sapiens/BRCA2�u�#GET genetree/member/id/:species/:id�}�(h	�pRetrieves the gene tree that contains the gene / transcript / translation stable identifier in the given species�h]�(h%heh]�(h4h5h6eh�0/genetree/member/id/homo_sapiens/ENSG00000139618�u�%GET alignment/region/:species/:region�}�(h	�MRetrieves genomic alignments as separate blocks based on a region and species�h]�(h%�region�eh]�(�method��species_set��display_species_set�eh�4/alignment/region/homo_sapiens/2:106040000-106040050�u�GET homology/id/:species/:id�}�(h	�IRetrieves homology information (orthologs) by species and Ensembl gene id�h]�(h%heh]�(�compara�h4h5�type��format��target_species�eh�)/homology/id/homo_sapiens/ENSG00000139618�u�$GET homology/symbol/:species/:symbol�}�(h	�4Retrieves homology information (orthologs) by symbol�h]�(h%h&eh]�(hSh4h5hThUhVeh�#/homology/symbol/homo_sapiens/BRCA2�uuu�Cross References�}�(h	� Cross-reference lookup endpoints�h}�(�!GET xrefs/symbol/:species/:symbol�}�(h	�HLooks up an external symbol and returns all Ensembl objects linked to it�h]�(h%h&eh]�(�external_db��db_type�eh� /xrefs/symbol/homo_sapiens/BRCA2�u�GET xrefs/id/:id�}�(h	�`Perform lookups of Ensembl Identifiers and retrieve their external references in other databases�h]�hah]�(hghheh�/xrefs/id/ENSG00000139618�u�GET xrefs/name/:species/:name�}�(h	�\Performs a lookup based upon the primary accession or display label of an external reference�h]�(h%�name�eh]�(hghheh�/xrefs/name/homo_sapiens/BRCA2�uuu�Information�}�(h	�Information resource endpoints�h}�(�GET info/analysis/:species�}�(h	�>List the names of analyses involved in generating Ensembl data�h]�h%ah]�h�/info/analysis/homo_sapiens�u�GET info/assembly/:species�}�(h	�5List the currently available assemblies for a species�h]�h%ah]��bands�ah�/info/assembly/homo_sapiens�u�'GET info/assembly/:species/:region_name�}�(h	�@Returns information about the specified toplevel sequence region�h]�(h%�region_name�eh]�h�/info/assembly/homo_sapiens/X�u�GET info/biotypes/:species�}�(h	�2List the functional classifications of gene models�h]�h%ah]�h�/info/biotypes/homo_sapiens�u�,GET info/biotypes/groups/:group/:object_type�}�(h	�.List the properties of biotypes within a group�h]�(�group��object_type�eh]�h�!/info/biotypes/groups/coding/gene�u�)GET info/biotypes/name/:name/:object_type�}�(h	�1List the properties of biotypes with a given name�h]�(hth�eh]�h�'/info/biotypes/name/protein_coding/gene�u�GET info/compara/methods�}�(h	�#List all compara analyses available�h]�h]��class�ah�/info/compara/methods�u�%GET info/compara/species_sets/:method�}�(h	�JList all collections of species analysed with the specified compara method�h]�hJah]�h�/info/compara/species_sets/EPO�u�
GET info/data�}�(h	�5Shows the data releases available on this REST server�h]�h]�h�
/info/data�u�GET info/eg_version�}�(h	�IReturns the Ensembl Genomes version of the databases backing this service�h]�h]�h�/info/eg_version�u�GET info/external_dbs/:species�}�(h	�2Lists all available external sources for a species�h]�h%ah]��filter�ah�/info/external_dbs/homo_sapiens�u�GET info/divisions�}�(h	�!Get list of all Ensembl divisions�h]�h]�h�/info/divisions�u�GET info/genomes/:genome_name�}�(h	�%Find information about a given genome�h]��genome_name�ah]��expand�ah�/info/genomes/homo_sapiens�u�%GET info/genomes/accession/:accession�}�(h	�EFind information about genomes containing a specified INSDC accession�h]��	accession�ah]�h�ah� /info/genomes/accession/U00096.3�u�&GET info/genomes/assembly/:assembly_id�}�(h	�9Find information about a genome with a specified assembly�h]��assembly_id�ah]�h�ah�/info/genomes/assembly/GRCh38�u�(GET info/genomes/division/:division_name�}�(h	�6Find information about all genomes in a given division�h]��
division_name�ah]�h�ah�)/info/genomes/division/EnsemblVertebrates�u�%GET info/genomes/taxonomy/:taxon_name�}�(h	�GFind information about all genomes beneath a given node of the taxonomy�h]��
taxon_name�ah]�h�ah�/info/genomes/taxonomy/Homo�u�
GET info/ping�}�(h	�Checks if the service is alive�h]�h]�h�
/info/ping�u�
GET info/rest�}�(h	�1Shows the current version of the Ensembl REST API�h]�h]�h�
/info/rest�u�GET info/software�}�(h	�DShows the current version of the Ensembl API used by the REST server�h]�h]�h�/info/software�u�GET info/species�}�(h	�ULists all available species, their aliases, available adaptor groups and data release�h]�h]�(�hide_strain��content-type�eh�
/info/species�u�GET info/variation/:species�}�(h	�8List the variation sources used in Ensembl for a species�h]�h%ah]�h�ah�/info/variation/homo_sapiens�u�$GET info/variation/consequence_types�}�(h	�#Lists all variant consequence types�h]�h]�h�!/info/variation/consequence_types�u�9GET info/variation/populations/:species:/:population_name�}�(h	�4List all individuals for a population from a species�h]�(h%�population_name�eh]�h�@/info/variation/populations/homo_sapiens/1000GENOMES:phase_3:CEU�u�'GET info/variation/populations/:species�}�(h	�"List all populations for a species�h]�h%ah]�h�(/info/variation/populations/homo_sapiens�uuu�Linkage Disequilibrium�}�(h	� Linkage disequilibrium endpoints�h}�(�$GET ld/:species/:id/:population_name�}�(h	�[Computes and returns LD values between the given variant and all other variants in a window�h]�(h%hj  eh]�(�window_size��d_prime�eh�2/ld/homo_sapiens/rs1042779/1000GENOMES:phase_3:CEU�u�"GET ld/:species/pairwise/:id1/:id2�}�(h	�9Computes and returns LD values between the given variants�h]�(h%�id1��id2�eh]�(j  j*  eh�-/ld/homo_sapiens/pairwise/rs1042779/rs1042781�u�/GET ld/:species/region/:region/:population_name�}�(h	�RComputes and returns LD values between all pairs of variants in the defined region�h]�(h%hHj  eh]�j*  ah�C/ld/homo_sapiens/region/1:15000000-15100000/1000GENOMES:phase_3:CEU�uuu�Lookup�}�(h	�Lookup resource endpoints�h}�(�GET lookup/id/:id�}�(h	�5Find the species and database for a single identifier�h]�hah]�(hhh�hU�
phenotypes�eh�/lookup/id/ENSG00000139618�u�"GET lookup/symbol/:species/:symbol�}�(h	�HFind the species and database for a symbol in a linked external database�h]�(h%h&eh]�(hhh�hUjC  eh�!/lookup/symbol/homo_sapiens/BRCA2�uuu�Mapping�}�(h	�Coordinate mapping endpoints�h}�(�GET map/cdna/:id/:region�}�(h	�4Convert from cDNA coordinates to genomic coordinates�h]�(hhHeh]�h�"/map/cdna/ENST00000288602/100..300�u�GET map/cds/:id/:region�}�(h	�3Convert from CDS coordinates to genomic coordinates�h]�(hhHeh]�h� /map/cds/ENST00000288602/1..1000�u�*GET map/:species/:asm_one/:region/:asm_two�}�(h	�3Convert the co-ordinates of one assembly to another�h]�(h%�asm_one�hH�asm_two�eh]�h�2/map/homo_sapiens/GRCh37/X:1000000..1000100/GRCh38�u�GET map/translation/:id/:region�}�(h	�7Convert from protein coordinates to genomic coordinates�h]�(hhHeh]�h�)/map/translation/ENSP00000288602/100..300�uuu�Ontologies and Taxonomy�}�(h	�(Ontology and taxonomy resource endpoints�h}�(�GET ontology/ancestors/:id�}�(h	�MReconstruct the entire ancestry of a term from is_a and part_of relationships�h]�hah]�h�/ontology/ancestors/GO:0005667�u� GET ontology/ancestors/chart/:id�}�(h	jo  h]�hah]�h�$/ontology/ancestors/chart/GO:0005667�u�GET ontology/descendants/:id�}�(h	�.Find all the terms descended from a given term�h]�hah]�(�ontology��subset�eh� /ontology/descendants/GO:0005667�u�GET ontology/id/:id�}�(h	�;Search for an ontological term by its namespaced identifier�h]�hah]�(�relation��simple�eh�/ontology/id/GO:0005667�u�GET ontology/name/:name�}�(h	�4Search for a list of ontological terms by their name�h]�htah]�(j}  j~  eh�/ontology/name/transcription�u�GET taxonomy/classification/:id�}�(h	�3Return the taxonomic classification of a taxon node�h]�hah]�h�/taxonomy/classification/9606�u�GET taxonomy/id/:id�}�(h	�5Search for a taxonomic term by its identifier or name�h]�hah]�h�/taxonomy/id/9606�u�GET taxonomy/name/:name�}�(h	�2Search for a taxonomic id by a non-scientific name�h]�htah]�h�/taxonomy/name/human�uuu�Overlap�}�(h	�Feature overlap endpoints�h}�(�GET overlap/id/:id�}�(h	�HRetrieves features that overlap a region defined by the given identifier�h]�hah]�(�feature�hhh%�biotype��
logic_name��variant_set�eh�/overlap/id/ENSG00000157764�u�#GET overlap/region/:species/:region�}�(h	�.Retrieves features that overlap a given region�h]�(h%hHeh]�(j�  hhj�  j�  j�  eh�2/overlap/region/homo_sapiens/7:*********-*********�u�GET overlap/translation/:id�}�(h	�3Retrieve features related to a specific Translation�h]�hah]�(j�  hhh%hTeh�$/overlap/translation/ENSP00000288602�uuu�Phenotype annotations�}�(h	�Phenotype annotation endpoints�h}�(�,GET /phenotype/accession/:species/:accession�}�(h	�VReturn phenotype annotations for genomic features given a phenotype ontology accession�h]�(h%h�eh]�(�include_children��include_pubmed��include_review��include_submitter�eh�,/phenotype/accession/homo_sapiens/HP:0000118�u�"GET /phenotype/gene/:species/:gene�}�(h	�-Return phenotype annotations for a given gene�h]�(h%�gene�eh]�(�include_overlap�j�  j�  j�  eh�"/phenotype/gene/homo_sapiens/BRCA2�u�&GET /phenotype/region/:species/:region�}�(h	�@Return phenotype annotations that overlap a given genomic region�h]�(h%hHeh]�(�feature_type�j�  j�  j�  eh�3/phenotype/region/homo_sapiens/13:32889611-32973805�u�"GET /phenotype/term/:species/:term�}�(h	�QReturn phenotype annotations for genomic features given a phenotype ontology term�h]�(h%�term�eh]�(j�  j�  j�  j�  eh�,/phenotype/term/homo_sapiens/breast%20cancer�uuu�
Regulation�}�(h	�Regulation resource endpoints�h}��>GET species/:species/binding_matrix/:binding_matrix_stable_id/�}�(h	�#Return the specified binding matrix�h]�(h%�binding_matrix_stable_id�eh]��unit�ah�-/species/homo_sapiens/binding_matrix/MA0139.1�usu�Sequence�}�(h	�Sequence resource endpoints�h}�(�GET sequence/id/:id�}�(h	�7Request multiple types of sequence by stable identifier�h]�hah]�(hTh%hhh�hU�mask��mask_feature��
expand_3prime��
expand_5prime�eh�/sequence/id/ENSG00000157764�u�$GET sequence/region/:species/:region�}�(h	�4Returns the genomic sequence of the specified region�h]�(h%hHeh]�(hUj�  j�  j�  j�  eh�0/sequence/region/homo_sapiens/X:1000000..1000100�uuu�Transcript Haplotypes�}�(h	�Transcript haplotype endpoints�h}��&GET transcript_haplotypes/:species/:id�}�(h	�NComputes observed transcript haplotype sequences based on phased genotype data�h]�(h%heh]�(j  �sample_name�eh�3/transcript_haplotypes/homo_sapiens/ENST00000288602�usu�VEP�}�(h	�"Variant Effect Predictor endpoints�h}�(�$GET vep/:species/hgvs/:hgvs_notation�}�(h	�3Fetch variant consequences based on a HGVS notation�h]�(h%�
hgvs_notation�eh]�(�consequences��domains��numbers��protein��xref_refseq�eh�//vep/homo_sapiens/hgvs/ENST00000257290.5:c.4G>T�u�GET vep/:species/id/:id�}�(h	�8Fetch variant consequences based on a variant identifier�h]�(h%heh]�(j  j  j  j  j  eh� /vep/homo_sapiens/id/rs116035550�u�(GET vep/:species/region/:region/:allele/�}�(h	�Fetch variant consequences�h]�(h%hH�allele�eh]�(j  j  j  j  j  eh�0/vep/homo_sapiens/region/9:22125503-22125502:1/C�uuu�	Variation�}�(h	�Variation resource endpoints�h}�(� GET variant_recoder/:species/:id�}�(h	�FTranslate a variant identifier, HGVS notation or genomic SPDI notation�h]�(h%heh]��fields�ah�)/variant_recoder/homo_sapiens/rs116035550�u�GET variation/:species/:id�}�(h	�:Uses a variant identifier to return the variation features�h]�(h%heh]�(�	genotypes�jC  �population_genotypes�eh�#/variation/homo_sapiens/rs116035550�u�#GET variation/:species/pmcid/:pmcid�}�(h	�CFetch variants by publication using PubMed Central reference number�h]�(h%�pmcid�eh]�h�'/variation/homo_sapiens/pmcid/PMC123456�u�!GET variation/:species/pmid/:pmid�}�(h	�;Fetch variants by publication using PubMed reference number�h]�(h%�pmid�eh]�h�%/variation/homo_sapiens/pmid/12345678�uuu�Variation GA4GH�}�(h	�"GA4GH variation resource endpoints�h}�(�GET ga4gh/beacon�}�(h	�Return Beacon information�h]�h]�h�
/ga4gh/beacon�u�GET ga4gh/beacon/query�}�(h	�1Return the Beacon response for allele information�h]�(�
referenceName��start��alternateBases��
assemblyId�eh]��includeDatasets�ah�Q/ga4gh/beacon/query?referenceName=1&start=1000&alternateBases=A&assemblyId=GRCh38�u�GET ga4gh/features/:id�}�(h	�7Return the GA4GH record for a specific sequence feature�h]�hah]�h�/ga4gh/features/ENSG00000157764�u�GET ga4gh/callsets/:id�}�(h	�.Return the GA4GH record for a specific CallSet�h]�hah]�h�+/ga4gh/callsets/1000GENOMES:phase_3:HG00096�u�GET ga4gh/datasets/:id�}�(h	�.Return the GA4GH record for a specific dataset�h]�hah]�h�/ga4gh/datasets/1000GENOMES�u�GET ga4gh/featuresets/:id�}�(h	�1Return the GA4GH record for a specific featureSet�h]�hah]�h�/ga4gh/featuresets/Ensembl�u�GET ga4gh/variants/:id�}�(h	�.Return the GA4GH record for a specific variant�h]�hah]�h�/ga4gh/variants/rs116035550�u�GET ga4gh/variantsets/:id�}�(h	�1Return the GA4GH record for a specific VariantSet�h]�hah]�h�/ga4gh/variantsets/1000GENOMES�u�GET ga4gh/references/:id�}�(h	�4Return data for a specific reference in GA4GH format�h]�hah]�h�/ga4gh/references/1�u�GET ga4gh/referencesets/:id�}�(h	�8Return data for a specific reference set in GA4GH format�h]�hah]�h�/ga4gh/referencesets/GRCh38�u�#GET ga4gh/variantannotationsets/:id�}�(h	�>Return meta data for a specific annotation set in GA4GH format�h]�hah]�h�$/ga4gh/variantannotationsets/Ensembl�uuuu�common_parameters�}�(j  ]�(�application/json��text/xml�ehU]�(�	condensed��full�ehh]�(�core��cdna��
otherfeatures��	variation�eh�]�(K Keu�http_status_codes�}�(�200�� OK - The request was successful.��400��2Bad Request - The request could not be understood.��404��6Not Found - The requested resource could not be found.��429��6Too Many Requests - You have exceeded your rate limit.��503��<Service Unavailable - The server is temporarily unavailable.�u�authentication�}�(�required��hJ�None�uu.