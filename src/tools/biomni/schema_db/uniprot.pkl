��|      }�(�base_url��https://rest.uniprot.org��web_url��https://www.uniprot.org��version��2023��
categories�}�(�	UniProtKB�}�(�description��%The UniProt Knowledgebase (UniProtKB)��	endpoints�}�(�GET /uniprotkb/{id}�}�(h�!Retrieve a single UniProtKB entry��required_params�]��id�a�optional_params�]�(�format��include�e�example��/uniprotkb/P12345�u�GET /uniprotkb/search�}�(h�Search UniProtKB entries�h]��query�ah]�(h�fields��includeIsoform��
compressed��size��cursor�eh�(/uniprotkb/search?query=organism_id:9606�u�GET /uniprotkb/stream�}�(h�-Stream search results (for large result sets)�h]�hah]�(hh!h"h#eh�(/uniprotkb/stream?query=organism_id:9606�uu�formats�]�(�json��xml��txt��fasta��gff��rdf��tsv��xlsx��list�e�
entry_pattern��P\d{5}��entry_examples�]�(�P12345��P99999�eu�UniRef�}�(h�The UniProt Reference Clusters�h
}�(�GET /uniref/{id}�}�(h�Retrieve a single UniRef entry�h]�hah]�hah�/uniref/UniRef90_P99999�u�GET /uniref/search�}�(h�Search UniRef entries�h]�hah]�(hh!h$h%eh�&/uniref/search?query=uniprot_id:P99999�uuh-]�(h0h4h2h5eh8�UniRef\d+_[A-Z0-9]+�h:]��UniRef90_P99999�au�UniParc�}�(h�The UniProt Archive�h
}�(�GET /uniparc/{id}�}�(h�Retrieve a single UniParc entry�h]�hah]�hah�/uniparc/UPI000000001F�u�GET /uniparc/search�}�(h�Search UniParc entries�h]�hah]�(hh!h$h%eh�'/uniparc/search?query=upi:UPI000000001F�uuh-]�(h0h4h2h5eh8�UPI[A-F0-9]+�h:]��
UPI000000001F�au�Taxonomy�}�(h�Taxonomy database�h
}�(�GET /taxonomy/{id}�}�(h� Retrieve a single Taxonomy entry�h]�hah]�hah�/taxonomy/9606�u�GET /taxonomy/search�}�(h�Search Taxonomy entries�h]�hah]�(hh!h$h%eh�!/taxonomy/search?query=name:human�uuh-]�(h/h0h4h5eh8�\d+�h:]��9606�auu�query_fields�}�h	}�(�	accession�}�(h�%UniProtKB primary/canonical accession�h�accession:P62988�u�sec_acc�}�(h�UniProtKB secondary accession�h�sec_acc:P02023�uh}�(h�UniProtKB entry name�h�
id:UBIQ_HUMAN�u�gene�}�(h�(Gene name (includes all gene name types)�h�	gene:HPSE�u�
gene_exact�}�(h�+Exact gene name match (excludes variations)�h�gene_exact:HPSE�u�protein_name�}�(h�Protein name�h�protein_name:"prion protein"�u�
organism_name�}�(h�
Organism name�h�organism_name:"Homo sapiens"�u�organism_id�}�(h�#Organism identifier (NCBI taxonomy)�h�organism_id:9606�u�
taxonomy_name�}�(h�Taxonomy name (any rank)�h�taxonomy_name:mammal�u�taxonomy_id�}�(h�Taxonomy identifier (any rank)�h�taxonomy_id:40674�u�reviewed�}�(h�Swiss-Prot reviewed status�h�
reviewed:true�u�active�}�(h�Entry active status�h�active:true�u�database�}�(h�Cross-referenced database�h�database:pdb�u�xref�}�(h�-Cross-reference to specific entry in database�h�
xref:pdb-1aut�u�
xref_count�}�(h�&Number of cross-references to database�h�xref_count_pdb:[20 TO *]�u�length�}�(h�Sequence length�h�length:[500 TO 700]�u�mass�}�(h�Protein mass in Daltons�h�mass:[50000 TO 100000]�u�ec�}�(h�Enzyme Commission number�h�ec:********�u�go�}�(h�Gene Ontology ID�h�
go:0015629�u�keyword�}�(h�UniProtKB keyword�h�
keyword:toxin�u�	existence�}�(h�Protein existence level�h�existence:1�u�family�}�(h�Protein family�h�
family:serpin�u�fragment�}�(h�Fragment status�h�
fragment:true�u�
interactor�}�(h�Interacting protein�h�interactor:P00520�u�	organelle�}�(h�Encoding organelle�h�organelle:mitochondrion�u�proteome�}�(h�Proteome ID�h�proteome:UP000005640�u�date_created�}�(h�Entry creation date�h�'date_created:[2020-01-01 TO 2020-12-31]�u�
date_modified�}�(h�Entry modification date�h�(date_modified:[2020-01-01 TO 2020-12-31]�u�date_sequence_modified�}�(h�Sequence modification date�h�1date_sequence_modified:[2020-01-01 TO 2020-12-31]�uus�
return_fields�}�h	}�(�names_taxonomy�}�(h}�Entry accession�h�
Entry name��
gene_names��
Gene names��gene_primary��Gene names (primary)��gene_synonym��Gene names (synonym)��gene_oln��Gene names (ordered locus)��gene_orf��Gene names (ORF)�h��Organism�h��Organism ID�h��
Protein names��xref_proteomes��	Proteomes��lineage��Taxonomic lineage��lineage_ids��Taxonomic lineage (IDs)��virus_hosts��Virus hosts�u�	sequences�}�(�cc_alternative_products��Alternative products��
ft_var_seq��Alternative sequence��error_gmodel_pred��Erroneous gene model prediction�hԌFragment�h܌Gene encoded by�h��Length�h��Mass��cc_mass_spectrometry��Mass spectrometry��
ft_variant��Natural variant��ft_non_cons��Non-adjacent residues��
ft_non_std��Non-standard residue��
ft_non_ter��Non-terminal residue��cc_polymorphism��Polymorphism��cc_rna_editing��RNA editing��sequence��Sequence��cc_sequence_caution��Sequence caution��ft_conflict��Sequence conflict��	ft_unsure��Sequence uncertainty��sequence_version��Sequence version�u�function�}�(�
absorption��
Absorption��ft_act_site��Active site��cc_activity_regulation��Activity regulation��
ft_binding��Binding site��cc_catalytic_activity��Catalytic activity��cc_cofactor��Cofactor��ft_dna_bind��DNA binding�h��	EC number��cc_function��
Function [CC]��kinetics��Kinetics��
cc_pathway��Pathway��
ph_dependence��
pH dependence��redox_potential��Redox potential��rhea��Rhea ID��ft_site��Site��temp_dependence��Temperature dependence�u�
miscellaneous�}�(�annotation_score��
Annotation��
cc_caution��Caution��
comment_count��
Comment Count��
feature_count��Features��	keywordid��
Keyword ID�hȌKeywords��cc_miscellaneous��Miscellaneous [CC]��protein_existence��Protein existence�h��Reviewed��tools��Tools��
uniparc_id�hRu�interaction�}�(�cc_interaction��Interacts with��
cc_subunit��Subunit structure [CC]�u�
expression�}�(�cc_developmental_stage��Developmental stage��cc_induction��	Induction��cc_tissue_specificity��Tissue specificity�u�
gene_ontology�}�(�go_p��"Gene Ontology (biological process)��go_c��"Gene Ontology (cellular component)�hČGene Ontology (GO)��go_f��"Gene Ontology (molecular function)��go_id��Gene Ontology IDs�uusu.