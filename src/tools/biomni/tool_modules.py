"""
Biomni tool module loader.

This module provides the load_all_tools() function that loads all biomni tools
from the tool description modules, making them available for the tool registry.
"""

import importlib
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)


def load_all_tools() -> Dict[str, List[Dict[str, Any]]]:
    """
    Load all biomni tools from the tool description modules.

    Returns:
        Dict mapping module names to lists of tool descriptions
    """
    fields = [
        "literature",
        "biochemistry",
        "bioengineering",
        "biophysics",
        "cancer_biology",
        "cell_biology",
        "molecular_biology",
        "genetics",
        "genomics",
        "immunology",
        "microbiology",
        "pathology",
        "pharmacology",
        "physiology",
        "synthetic_biology",
        "systems_biology",
        "support_tools",
        "database",
    ]

    module2api = {}

    for field in fields:
        try:
            # Import the tool description module
            # Try different import paths to handle various contexts
            module = None
            import_paths = [
                f"src.tools.biomni.tool_description.{field}",
                f"tool_description.{field}",
                f".tool_description.{field}",
            ]

            for module_name in import_paths:
                try:
                    if module_name.startswith("."):
                        # Relative import
                        module = importlib.import_module(
                            module_name, package="src.tools.biomni"
                        )
                    else:
                        # Absolute import
                        module = importlib.import_module(module_name)
                    break
                except ImportError:
                    continue

            if module is None:
                logger.warning(f"Could not import any form of {field} module")
                continue

            # Get the description from the module
            if hasattr(module, "description"):
                module_key = f"biomni.tool.{field}"
                module2api[module_key] = module.description
                logger.debug(f"Loaded {len(module.description)} tools from {field}")
            else:
                logger.warning(f"Module {field} has no description attribute")

        except ImportError as e:
            logger.warning(f"Could not load tools from {field}.py: {e}")
            continue
        except Exception as e:
            logger.error(f"Error loading tools from {field}: {e}")
            continue

    logger.info(f"Loaded tools from {len(module2api)} modules")
    return module2api


def read_module2api() -> Dict[str, List[Dict[str, Any]]]:
    """
    Alternative implementation of read_module2api using the same logic as utils.py.
    This is a fallback/compatibility function.
    """
    return load_all_tools()
