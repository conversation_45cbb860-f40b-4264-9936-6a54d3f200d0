"""
LLM helper for biomni tools that uses the system's configured LLM.
"""

import json
import os
import google.generativeai as genai
from typing import Optional, Dict, Any


def _query_llm_for_api(
    prompt: str,
    schema: Optional[Dict] = None,
    system_template: str = "",
    api_key: Optional[str] = None,
    model: Optional[str] = None,
) -> Dict[str, Any]:
    """Helper function to query the configured LLM for generating API calls based on natural language prompts.

    Parameters
    ----------
    prompt (str): Natural language query to process
    schema (dict, optional): API schema to include in the system prompt
    system_template (str): Template string for the system prompt (should have {schema} placeholder)
    api_key (str, optional): API key. If None, will use environment variables
    model (str, optional): Model to use. If None, will use REASONING_MODEL from environment

    Returns
    -------
    dict: Dictionary with 'success', 'data' (if successful), 'error' (if failed), and optional 'raw_response'
    """

    # Get configuration from environment
    provider = os.environ.get("REASONING_PROVIDER", "gemini")
    api_key = api_key or os.environ.get("REASONING_API_KEY")
    model = model or os.environ.get("REASONING_MODEL", "gemini-2.5-pro")

    if api_key is None:
        return {
            "success": False,
            "error": (
                f"No API key provided. Set REASONING_API_KEY environment variable or provide api_key parameter for {provider}."
            ),
        }

    try:
        if provider.lower() == "gemini":
            # Configure Gemini
            genai.configure(api_key=api_key)

            # Prepare the prompt
            if schema is not None:
                schema_json = json.dumps(schema, indent=2)
                system_prompt = system_template.format(schema=schema_json)
                full_prompt = f"{system_prompt}\n\nUser request: {prompt}"
            else:
                full_prompt = f"{system_template}\n\nUser request: {prompt}"

            # Create Gemini model
            gemini_model = genai.GenerativeModel(model)

            # Generate response
            response = gemini_model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=1000,
                    temperature=0.1,  # Low temperature for more deterministic API generation
                ),
            )

            # Extract response text
            response_text = response.text.strip()

            # Find JSON boundaries (in case model adds explanations)
            json_start = response_text.find("{")
            json_end = response_text.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_content = response_text[json_start:json_end]
                try:
                    parsed_json = json.loads(json_content)
                    return {
                        "success": True,
                        "data": parsed_json,
                        "raw_response": response_text,
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"Failed to parse JSON response: {e}",
                        "raw_response": response_text,
                    }
            else:
                return {
                    "success": False,
                    "error": "No valid JSON found in LLM response",
                    "raw_response": response_text,
                }

        else:
            # Fallback to Anthropic if configured
            from anthropic import Anthropic

            client = Anthropic(api_key=api_key)

            if schema is not None:
                schema_json = json.dumps(schema, indent=2)
                system_prompt = system_template.format(schema=schema_json)
            else:
                system_prompt = system_template

            response = client.messages.create(
                model=model,
                system=system_prompt,
                max_tokens=1000,
                messages=[{"role": "user", "content": prompt}],
            )

            # Parse response
            response_text = response.content[0].text.strip()

            # Find JSON boundaries
            json_start = response_text.find("{")
            json_end = response_text.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_content = response_text[json_start:json_end]
                try:
                    parsed_json = json.loads(json_content)
                    return {
                        "success": True,
                        "data": parsed_json,
                        "raw_response": response_text,
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"Failed to parse JSON response: {e}",
                        "raw_response": response_text,
                    }
            else:
                return {
                    "success": False,
                    "error": "No valid JSON found in LLM response",
                    "raw_response": response_text,
                }

    except Exception as e:
        return {
            "success": False,
            "error": f"LLM API error: {str(e)}",
        }
