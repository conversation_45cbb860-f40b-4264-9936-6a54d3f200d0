"""
Error logging and tracking system for pattern analysis.

This module provides structured error logging and analytics to identify
common failure patterns and track system reliability over time.
"""

import json
import logging
import time
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import defaultdict

from .error_recovery import <PERSON><PERSON>r<PERSON>eport, ErrorContext

logger = logging.getLogger(__name__)


@dataclass
class ErrorLogEntry:
    """Structured error log entry."""

    timestamp: float
    tool_name: str
    category: str
    severity: str
    error_code: str
    execution_method: str
    original_error: str
    recovery_attempted: bool = False
    recovery_successful: bool = False
    session_id: Optional[str] = None
    user_context: Dict[str, Any] = None
    environment_info: Dict[str, Any] = None


class ErrorTracker:
    """
    Error tracking and analytics system.

    Provides structured logging, pattern analysis, and trend monitoring
    for system errors and recovery attempts.
    """

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize error tracker.

        Args:
            db_path: Path to SQLite database file (optional)
        """
        self.db_path = db_path or "data/error_tracking.db"
        self._lock = threading.RLock()
        self._init_database()

        # In-memory caches for fast access
        self._recent_errors = []
        self._error_patterns = defaultdict(int)
        self._session_errors = defaultdict(list)

    def _init_database(self):
        """Initialize SQLite database for error tracking."""
        try:
            # Ensure directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS error_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp REAL NOT NULL,
                        tool_name TEXT NOT NULL,
                        category TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        error_code TEXT NOT NULL,
                        execution_method TEXT NOT NULL,
                        original_error TEXT NOT NULL,
                        recovery_attempted BOOLEAN DEFAULT FALSE,
                        recovery_successful BOOLEAN DEFAULT FALSE,
                        session_id TEXT,
                        user_context TEXT,
                        environment_info TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                conn.execute(
                    """
                    CREATE INDEX IF NOT EXISTS idx_error_logs_timestamp 
                    ON error_logs(timestamp)
                """
                )

                conn.execute(
                    """
                    CREATE INDEX IF NOT EXISTS idx_error_logs_tool_category 
                    ON error_logs(tool_name, category)
                """
                )

                # Create recovery attempts table
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS recovery_attempts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        error_log_id INTEGER NOT NULL,
                        recovery_method TEXT NOT NULL,
                        success BOOLEAN NOT NULL,
                        details TEXT,
                        timestamp REAL NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (error_log_id) REFERENCES error_logs (id)
                    )
                """
                )

                logger.info(f"Error tracking database initialized at {self.db_path}")

        except Exception as e:
            logger.error(f"Failed to initialize error tracking database: {e}")

    def log_error(
        self,
        error_report: ErrorReport,
        session_id: Optional[str] = None,
        user_context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Log an error with comprehensive tracking information.

        Args:
            error_report: ErrorReport containing error details
            session_id: Optional session identifier
            user_context: Optional user context information

        Returns:
            Unique error log ID
        """
        entry = ErrorLogEntry(
            timestamp=time.time(),
            tool_name=error_report.context.tool_name,
            category=error_report.context.category.value,
            severity=error_report.context.severity.value,
            error_code=error_report.context.error_code,
            execution_method=error_report.context.execution_method,
            original_error=error_report.context.original_error,
            session_id=session_id,
            user_context=user_context or {},
            environment_info=error_report.context.environment_info,
        )

        return self._store_error_entry(entry)

    def log_recovery_attempt(
        self,
        error_log_id: str,
        success: bool,
        recovery_method: str,
        details: Optional[str] = None,
    ):
        """
        Log a recovery attempt for a previously logged error.

        Args:
            error_log_id: ID of the original error log entry
            success: Whether the recovery was successful
            recovery_method: Method used for recovery
            details: Optional additional details
        """
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute(
                        """
                        UPDATE error_logs 
                        SET recovery_attempted = TRUE,
                            recovery_successful = ?
                        WHERE id = ?
                    """,
                        (success, error_log_id),
                    )

                    # Also log recovery details
                    conn.execute(
                        """
                        INSERT INTO recovery_attempts (
                            error_log_id, recovery_method, success, details, timestamp
                        ) VALUES (?, ?, ?, ?, ?)
                    """,
                        (error_log_id, recovery_method, success, details, time.time()),
                    )

                logger.debug(
                    f"Logged recovery attempt for error {error_log_id}: {success}"
                )

            except Exception as e:
                logger.error(f"Failed to log recovery attempt: {e}")

    def _store_error_entry(self, entry: ErrorLogEntry) -> str:
        """Store error entry in database and update caches."""
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute(
                        """
                        INSERT INTO error_logs (
                            timestamp, tool_name, category, severity, error_code,
                            execution_method, original_error, recovery_attempted,
                            recovery_successful, session_id, user_context,
                            environment_info
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                        (
                            entry.timestamp,
                            entry.tool_name,
                            entry.category,
                            entry.severity,
                            entry.error_code,
                            entry.execution_method,
                            entry.original_error,
                            entry.recovery_attempted,
                            entry.recovery_successful,
                            entry.session_id,
                            (
                                json.dumps(entry.user_context)
                                if entry.user_context
                                else None
                            ),
                            (
                                json.dumps(entry.environment_info)
                                if entry.environment_info
                                else None
                            ),
                        ),
                    )

                    error_id = str(cursor.lastrowid)

                # Update in-memory caches
                self._recent_errors.append(entry)
                if len(self._recent_errors) > 1000:  # Keep only recent 1000 errors
                    self._recent_errors = self._recent_errors[-1000:]

                # Update error patterns
                pattern_key = f"{entry.category}:{entry.error_code}"
                self._error_patterns[pattern_key] += 1

                # Update session errors
                if entry.session_id:
                    self._session_errors[entry.session_id].append(entry)

                logger.info(
                    f"Logged error: {entry.tool_name}/{entry.category} - {error_id}"
                )
                return error_id

            except Exception as e:
                logger.error(f"Failed to store error entry: {e}")
                return "unknown"

    def get_error_frequency(
        self,
        tool_name: Optional[str] = None,
        category: Optional[str] = None,
        time_window_hours: int = 24,
    ) -> Dict[str, int]:
        """
        Get error frequency statistics.

        Args:
            tool_name: Optional tool name filter
            category: Optional category filter
            time_window_hours: Time window in hours

        Returns:
            Dictionary with error frequency data
        """
        cutoff_time = time.time() - (time_window_hours * 3600)

        try:
            with sqlite3.connect(self.db_path) as conn:
                query = """
                    SELECT tool_name, category, COUNT(*) as count
                    FROM error_logs 
                    WHERE timestamp >= ?
                """
                params = [cutoff_time]

                if tool_name:
                    query += " AND tool_name = ?"
                    params.append(tool_name)

                if category:
                    query += " AND category = ?"
                    params.append(category)

                query += " GROUP BY tool_name, category ORDER BY count DESC"

                cursor = conn.execute(query, params)
                results = cursor.fetchall()

                return {f"{row[0]}:{row[1]}": row[2] for row in results}

        except Exception as e:
            logger.error(f"Failed to get error frequency: {e}")
            return {}

    def get_error_trends(self, days: int = 7) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get error trends over time.

        Args:
            days: Number of days to analyze

        Returns:
            Dictionary with trend data by day
        """
        cutoff_time = time.time() - (days * 24 * 3600)

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT 
                        DATE(datetime(timestamp, 'unixepoch')) as date,
                        tool_name,
                        category,
                        severity,
                        COUNT(*) as count
                    FROM error_logs 
                    WHERE timestamp >= ?
                    GROUP BY date, tool_name, category, severity
                    ORDER BY date DESC
                """,
                    (cutoff_time,),
                )

                results = cursor.fetchall()

                trends = defaultdict(list)
                for row in results:
                    date, tool_name, category, severity, count = row
                    trends[date].append(
                        {
                            "tool_name": tool_name,
                            "category": category,
                            "severity": severity,
                            "count": count,
                        }
                    )

                return dict(trends)

        except Exception as e:
            logger.error(f"Failed to get error trends: {e}")
            return {}

    def get_recovery_success_rates(self) -> Dict[str, Dict[str, float]]:
        """
        Get recovery success rates by tool and category.

        Returns:
            Dictionary with success rates
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT 
                        tool_name,
                        category,
                        SUM(CASE WHEN recovery_attempted THEN 1 ELSE 0 END) as attempted,
                        SUM(CASE WHEN recovery_successful THEN 1 ELSE 0 END) as successful
                    FROM error_logs 
                    WHERE recovery_attempted = TRUE
                    GROUP BY tool_name, category
                """
                )

                results = cursor.fetchall()

                success_rates = {}
                for row in results:
                    tool_name, category, attempted, successful = row
                    key = f"{tool_name}:{category}"

                    if attempted > 0:
                        success_rates[key] = {
                            "attempted": attempted,
                            "successful": successful,
                            "success_rate": successful / attempted,
                        }

                return success_rates

        except Exception as e:
            logger.error(f"Failed to get recovery success rates: {e}")
            return {}

    def get_session_error_summary(self, session_id: str) -> Dict[str, Any]:
        """
        Get error summary for a specific session.

        Args:
            session_id: Session identifier

        Returns:
            Dictionary with session error summary
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT 
                        tool_name,
                        category,
                        severity,
                        COUNT(*) as count,
                        SUM(CASE WHEN recovery_successful THEN 1 ELSE 0 END) as recovered
                    FROM error_logs 
                    WHERE session_id = ?
                    GROUP BY tool_name, category, severity
                """,
                    (session_id,),
                )

                results = cursor.fetchall()

                summary = {
                    "total_errors": sum(row[3] for row in results),
                    "total_recovered": sum(row[4] for row in results),
                    "by_tool": {},
                    "by_category": defaultdict(int),
                    "by_severity": defaultdict(int),
                }

                for row in results:
                    tool_name, category, severity, count, recovered = row

                    if tool_name not in summary["by_tool"]:
                        summary["by_tool"][tool_name] = {
                            "errors": 0,
                            "recovered": 0,
                            "categories": set(),
                        }

                    summary["by_tool"][tool_name]["errors"] += count
                    summary["by_tool"][tool_name]["recovered"] += recovered
                    summary["by_tool"][tool_name]["categories"].add(category)

                    summary["by_category"][category] += count
                    summary["by_severity"][severity] += count

                # Convert sets to lists for JSON serialization
                for tool_data in summary["by_tool"].values():
                    tool_data["categories"] = list(tool_data["categories"])

                return summary

        except Exception as e:
            logger.error(f"Failed to get session error summary: {e}")
            return {"total_errors": 0, "total_recovered": 0}

    def cleanup_old_logs(self, days_to_keep: int = 30):
        """
        Clean up old error logs to manage database size.

        Args:
            days_to_keep: Number of days of logs to keep
        """
        cutoff_time = time.time() - (days_to_keep * 24 * 3600)

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    DELETE FROM error_logs WHERE timestamp < ?
                """,
                    (cutoff_time,),
                )

                deleted_count = cursor.rowcount
                logger.info(f"Cleaned up {deleted_count} old error log entries")

        except Exception as e:
            logger.error(f"Failed to cleanup old logs: {e}")


# Global error tracker instance
error_tracker = ErrorTracker()


def log_error(
    error_report: ErrorReport,
    session_id: Optional[str] = None,
    user_context: Optional[Dict[str, Any]] = None,
) -> str:
    """
    Log an error with the global error tracker.

    Args:
        error_report: ErrorReport containing error details
        session_id: Optional session identifier
        user_context: Optional user context information

    Returns:
        Unique error log ID
    """
    return error_tracker.log_error(error_report, session_id, user_context)


def log_recovery_attempt(
    error_log_id: str,
    success: bool,
    recovery_method: str,
    details: Optional[str] = None,
):
    """
    Log a recovery attempt for a previously logged error.

    Args:
        error_log_id: ID of the original error log entry
        success: Whether the recovery was successful
        recovery_method: Method used for recovery
        details: Optional additional details
    """
    error_tracker.log_recovery_attempt(error_log_id, success, recovery_method, details)


def get_error_analytics() -> Dict[str, Any]:
    """
    Get comprehensive error analytics.

    Returns:
        Dictionary with error analytics data
    """
    return {
        "frequency_24h": error_tracker.get_error_frequency(time_window_hours=24),
        "frequency_7d": error_tracker.get_error_frequency(time_window_hours=168),
        "trends_7d": error_tracker.get_error_trends(days=7),
        "recovery_rates": error_tracker.get_recovery_success_rates(),
    }
