"""
Message type definitions for the multi-agent LangGraph system.

This module defines structured message types that adhere to the ag-ui protocol
for cleaner and more informative communication between agents and clients.
"""

from typing import Optional, Dict, Any, List, Literal
from pydantic import BaseModel, Field, ConfigDict
import uuid
from datetime import datetime, UTC


class BaseMessage(BaseModel):
    """
    Base message class following ag-ui protocol principles.

    All messages in the system inherit from this base class to ensure
    consistent structure and JSON serializability.
    """

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for the message",
    )
    role: str = Field(
        description="The role of the sender (user, assistant, system, tool, agent)"
    )
    content: Optional[str] = Field(
        default=None, description="Optional text content of the message"
    )
    name: Optional[str] = Field(default=None, description="Optional name of the sender")
    timestamp: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(UTC),
        description="When the message was created",
    )

    model_config = ConfigDict(
        json_encoders={datetime: lambda dt: dt.isoformat()},
        # Enable validation of default values
        validate_default=True,
        # Use enum values for serialization
        use_enum_values=True,
    )


class FinalAnswerMessage(BaseMessage):
    """
    Message type for final answers/conclusions from the multi-agent system.

    This message type is used when the system has completed its analysis
    and is providing the final response to the user's query.
    """

    role: Literal["assistant"] = "assistant"
    content: str = Field(description="The final answer or conclusion")
    agent_name: Optional[str] = Field(
        default=None, description="Name of the agent providing the final answer"
    )
    analysis_summary: Optional[str] = Field(
        default=None, description="Brief summary of the analysis performed"
    )
    confidence_level: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Confidence level in the answer (0.0 to 1.0)",
    )
    supporting_evidence: Optional[List[str]] = Field(
        default_factory=list, description="List of supporting evidence or sources"
    )


class AgentStepMessage(BaseMessage):
    """
    Message type for individual agent step communications.

    This message type tracks the progress and results of individual steps
    performed by agents in the multi-agent workflow.
    """

    role: Literal["agent"] = "agent"
    agent_name: str = Field(description="Name of the agent performing the step")
    step_description: str = Field(description="Description of what the agent is doing")
    step_status: Literal["started", "in_progress", "completed", "failed", "skipped"] = (
        Field(description="Current status of the step")
    )
    step_index: Optional[int] = Field(
        default=None, description="Index of the step in the overall plan"
    )
    results: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Results or outputs from the step"
    )
    error_message: Optional[str] = Field(
        default=None, description="Error message if the step failed"
    )
    duration_ms: Optional[int] = Field(
        default=None, description="Duration of the step in milliseconds"
    )


class ToolOutputMessage(BaseMessage):
    """
    Message type for tool execution results.

    This message type captures the results of tool executions,
    including the tool name as required by the acceptance criteria.
    """

    role: Literal["tool"] = "tool"
    tool_name: str = Field(description="Name of the tool that was executed")
    tool_call_id: Optional[str] = Field(
        default=None, description="ID of the tool call this message responds to"
    )
    tool_input: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Input parameters passed to the tool"
    )
    tool_output: Optional[str] = Field(
        default=None, description="Raw output from the tool execution"
    )
    execution_status: Literal["success", "error", "timeout", "cancelled"] = Field(
        description="Status of the tool execution"
    )
    execution_time_ms: Optional[int] = Field(
        default=None, description="Time taken for tool execution in milliseconds"
    )
    error_details: Optional[str] = Field(
        default=None, description="Detailed error information if execution failed"
    )


class ToDoListUpdateMessage(BaseMessage):
    """
    Message type for todo list state changes.

    This message type communicates updates to todo lists within the system,
    supporting the todo-md-mcp integration.
    """

    role: Literal["system"] = "system"
    action_type: Literal["add", "update", "complete", "delete", "reorder"] = Field(
        description="Type of action performed on the todo list"
    )
    todo_id: Optional[str] = Field(
        default=None, description="ID of the todo item being modified"
    )
    todo_title: Optional[str] = Field(
        default=None, description="Title or content of the todo item"
    )
    todo_status: Optional[
        Literal["pending", "in_progress", "completed", "cancelled"]
    ] = Field(default=None, description="Status of the todo item")
    todo_priority: Optional[Literal["low", "medium", "high", "critical"]] = Field(
        default=None, description="Priority level of the todo item"
    )
    list_name: Optional[str] = Field(
        default=None, description="Name of the todo list being modified"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Additional metadata about the todo update"
    )


# Message type detection and factory functions
def create_message_from_dict(message_data: Dict[str, Any]) -> BaseMessage:
    """
    Factory function to create appropriate message type from dictionary data.

    This function inspects the 'role' field to determine the correct message type
    and returns an instance of the appropriate class.

    Args:
        message_data: Dictionary containing message data with 'role' field

    Returns:
        Appropriate message instance based on role

    Raises:
        ValueError: If role is not recognized or data is invalid
    """
    role = message_data.get("role")

    if role == "assistant":
        # Check if this is a final answer by looking for confidence_level or analysis_summary
        if any(
            key in message_data
            for key in ["confidence_level", "analysis_summary", "supporting_evidence"]
        ):
            return FinalAnswerMessage(**message_data)
        # Default to base message for assistant role
        return BaseMessage(**message_data)
    elif role == "agent":
        return AgentStepMessage(**message_data)
    elif role == "tool":
        return ToolOutputMessage(**message_data)
    elif role == "system":
        # Check if this is a todo update by looking for action_type
        if "action_type" in message_data:
            return ToDoListUpdateMessage(**message_data)
        # Default to base message for system role
        return BaseMessage(**message_data)
    else:
        # For user, developer, or any other role, use BaseMessage
        return BaseMessage(**message_data)


def get_message_type_name(message: BaseMessage) -> str:
    """
    Get the human-readable message type name for logging/debugging.

    Args:
        message: Message instance

    Returns:
        String name of the message type
    """
    return message.__class__.__name__


# Export all message types for easy importing
__all__ = [
    "BaseMessage",
    "FinalAnswerMessage",
    "AgentStepMessage",
    "ToolOutputMessage",
    "ToDoListUpdateMessage",
    "create_message_from_dict",
    "get_message_type_name",
]
