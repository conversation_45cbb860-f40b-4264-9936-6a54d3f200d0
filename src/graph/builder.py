from langgraph.graph import StateGraph, START
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
from langgraph.checkpoint.memory import MemorySaver
from .types import State
from .nodes import (
    supervisor_node,
    research_node,
    code_node,
    browser_node,
    reporter_node,
    planner_node,
    execution_evaluator_node,
)
import os
import aiosqlite
import logging

logger = logging.getLogger(__name__)


async def create_checkpointer(use_memory_saver=False):
    """Create and setup a persistent checkpointer using SQLite."""
    if use_memory_saver:
        logger.info("Using in-memory checkpointer")
        return MemorySaver()

    try:
        # Create data directory if it doesn't exist
        data_dir = os.path.join(os.getcwd(), "data")
        os.makedirs(data_dir, exist_ok=True)

        # Setup SQLite database path
        db_path = os.getenv("SQLITE_DB_PATH", os.path.join(data_dir, "checkpoints.db"))
        logger.info(f"Setting up SQLite checkpointer at: {db_path}")

        # Create async SQLite connection
        conn = await aiosqlite.connect(db_path)
        checkpointer = AsyncSqliteSaver(conn=conn)

        logger.info("SQLite checkpointer setup successful")
        return checkpointer

    except Exception as e:
        logger.warning(
            f"Failed to setup SQLite checkpointer: {e}. Falling back to MemorySaver."
        )
        return MemorySaver()


def build_graph(checkpointer=None, start_node="planner"):
    """Builds the 'Living Plan' agent workflow graph."""
    builder = StateGraph(State)

    # ---------------------------------------------------------------------
    # 1️⃣  Register all agent nodes
    # ---------------------------------------------------------------------
    builder.add_node("planner", planner_node)
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("researcher", research_node)
    builder.add_node("coder", code_node)
    builder.add_node("browser", browser_node)
    builder.add_node("reporter", reporter_node)
    builder.add_node("execution_evaluator", execution_evaluator_node)

    # ---------------------------------------------------------------------
    # 2️⃣  Entry‑point: START → specified start_node (solid edge)
    # ---------------------------------------------------------------------
    builder.add_edge(START, start_node)

    # ---------------------------------------------------------------------
    # 3️⃣  Supervisor routes (now uses Command pattern)
    #     The supervisor node returns Command objects for routing
    # ---------------------------------------------------------------------
    # No conditional edges needed for supervisor - Command pattern handles routing automatically

    # ---------------------------------------------------------------------
    # 4️⃣  Routing for specialist agents (conditional edges)
    #     After execution, agents can either go to the evaluator or
    #     directly back to the supervisor.
    # ---------------------------------------------------------------------
    builder.add_edge("planner", "supervisor")

    # Helper function to decide the next step after a specialist node
    def after_specialist_step(state: State) -> str:
        plan = state.get("structured_plan")
        if not plan:
            return "supervisor"  # Default fallback

        current_step = plan.get_current_step()
        if current_step and current_step.requires_evaluation:
            return "execution_evaluator"
        return "supervisor"

    builder.add_conditional_edges("researcher", after_specialist_step)
    builder.add_conditional_edges("browser", after_specialist_step)
    builder.add_conditional_edges("reporter", after_specialist_step)
    builder.add_conditional_edges("coder", after_specialist_step)

    # ---------------------------------------------------------------------
    # 5️⃣  Evaluation cycle: execution_evaluator → agent OR supervisor
    #     The evaluator decides to retry the agent or hand control back.
    # ---------------------------------------------------------------------
    def after_evaluation_step(state: State) -> str:
        plan = state.get("structured_plan")
        if not plan:
            return "supervisor"

        current_step = plan.get_current_step()
        if current_step and current_step.status == "pending":
            # If the step is pending, retry the same agent
            return current_step.agent_name
        # Otherwise, return to the supervisor
        return "supervisor"

    builder.add_conditional_edges(
        "execution_evaluator",
        after_evaluation_step,
        {
            "researcher": "researcher",
            "coder": "coder",
            "browser": "browser",
            "reporter": "reporter",
            "supervisor": "supervisor",
        },
    )

    # ---------------------------------------------------------------------
    # 6️⃣  Compile the graph with the chosen checkpointer
    # ---------------------------------------------------------------------
    return builder.compile(checkpointer=checkpointer)
