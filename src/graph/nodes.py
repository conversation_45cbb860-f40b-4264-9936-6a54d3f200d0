import logging
import json
import os
import uuid
import datetime
import re
import time
from copy import deepcopy
from typing import Dict, List, Optional, Tuple, Any
from langchain_core.messages import HumanMessage
from langgraph.types import Command
from langgraph.graph import END

from src.agents import research_agent, coder_agent, browser_agent
from src.agents.llm import get_llm_by_type
from src.config import TEAM_MEMBERS
from src.config.agents import AGENT_LLM_MAP
from src.prompts.template import apply_prompt_template
from .types import State, PlanStep, StructuredPlan

logger = logging.getLogger(__name__)


def _create_enhanced_results(
    agent_name: str, current_step: PlanStep, response_content: str, result: dict
) -> dict:
    """
    Creates enhanced results with biomni tool integration tracking.

    This function eliminates code duplication between agent nodes by centralizing
    the logic for capturing tool usage, biomni-specific results, and filtered tools context.

    Args:
        agent_name: Name of the agent (e.g., "researcher", "coder")
        current_step: The current PlanStep being executed
        response_content: The response content from the agent
        result: The result object from agent execution

    Returns:
        Enhanced results dictionary with comprehensive tracking
    """
    enhanced_results = {
        "summary": response_content,
        "step_execution": {
            "agent": agent_name,
            "title": current_step.title,
            "status": current_step.status,
            "timestamp": datetime.datetime.now().isoformat(),
        },
    }

    # Extract tool usage information from agent result
    if hasattr(result, "get") and "messages" in result:
        tool_calls = []
        tool_results = []

        for message in result["messages"]:
            # Check for tool calls in message
            if hasattr(message, "tool_calls") and message.tool_calls:
                for tool_call in message.tool_calls:
                    tool_calls.append(
                        {
                            "tool_name": tool_call.get("name", "unknown"),
                            "tool_args": tool_call.get("args", {}),
                            "tool_id": tool_call.get("id", "unknown"),
                        }
                    )

            # Check for tool results in message
            if hasattr(message, "content") and isinstance(message.content, str):
                # Look for biomni tool indicators in content
                biomni_keywords = [
                    "selected tools",
                    "biomni",
                    "gene",
                    "protein",
                    "uniprot",
                    "pdb",
                    "kegg",
                ]
                if agent_name == "researcher":
                    biomni_keywords.extend(["pubmed", "literature"])

                if any(
                    keyword in message.content.lower() for keyword in biomni_keywords
                ):
                    tool_results.append(
                        {"content": message.content, "type": "biomni_related"}
                    )

        if tool_calls:
            enhanced_results["tool_calls"] = tool_calls
        if tool_results:
            enhanced_results["tool_results"] = tool_results

    # Add filtered tools information if available
    if current_step.filtered_tools:
        enhanced_results["filtered_tools_used"] = True
        enhanced_results["available_tools_summary"] = (
            current_step.filtered_tools[:200] + "..."
            if len(current_step.filtered_tools) > 200
            else current_step.filtered_tools
        )

    return enhanced_results


RESPONSE_FORMAT = "Response from {}:\n\n<response>\n{}\n</response>\n\n*Please execute the next step.*"


def research_node(state: State) -> dict:
    """
    Executes the research task, updates the plan, and returns to the supervisor.
    """
    plan = state["structured_plan"]
    current_step = plan.get_current_step()
    current_step.status = "in_progress"
    response_content = ""

    logger.info(f"Researcher starting step: {current_step.title}")

    # Mark todo as in progress
    _update_todo_status(current_step, completed=False)

    try:
        result = research_agent.invoke(state)
        response_content = result["messages"][-1].content

        if not current_step.requires_evaluation:
            current_step.status = "completed"
            logger.info(
                f"🎯 Step {current_step.step_index} completed without evaluation required: {current_step.title}"
            )
            # Mark todo as completed
            _update_todo_status(current_step, completed=True)
            plan.advance_to_next_step()
        else:
            logger.info(
                f"⏳ Step {current_step.step_index} requires evaluation before completion: {current_step.title}"
            )

        # Enhanced result capture for biomni tool integration
        current_step.results = _create_enhanced_results(
            "researcher", current_step, response_content, result
        )
        logger.info(
            f"Researcher completed step with enhanced results: {current_step.title}"
        )

    except Exception as e:
        logger.error(f"Researcher failed at step {current_step.title}: {e}")
        current_step.status = "failed"

        # Enhanced error handling with user-friendly messages
        try:
            from src.tools.error_recovery import (
                classify_and_report_error,
                format_error_for_user,
            )
            from src.tools.error_tracking import log_error

            error_report = classify_and_report_error(
                error_text=str(e),
                tool_name="researcher",
                execution_method="agent_execution",
            )

            # Log the error for analytics
            error_log_id = log_error(
                error_report,
                session_id=state.get("thread_id"),
                user_context={"step": current_step.title, "agent": "researcher"},
            )

            # Store comprehensive error information
            current_step.error_message = str(e)
            current_step.error_log_id = error_log_id
            current_step.error_report = {
                "category": error_report.context.category.value,
                "severity": error_report.context.severity.value,
                "recovery_actions": [
                    {
                        "type": action.action_type,
                        "description": action.description,
                        "priority": action.priority,
                    }
                    for action in error_report.recovery_actions[:3]
                ],
            }

            # Create user-friendly error message with recovery status
            recovery_status = "\n\n### Recovery Status\n"
            recovery_status += f"- **Error ID**: {error_log_id}\n"
            recovery_status += f"- **Automatic Recovery**: {'Available' if error_report.recovery_actions else 'Limited'}\n"
            recovery_status += "- **Workflow Continuation**: System will attempt to proceed with other available tools\n"

            response_content = format_error_for_user(error_report) + recovery_status

        except Exception as error_handling_error:
            logger.warning(f"Error handling system failed: {error_handling_error}")
            current_step.error_message = str(e)
            response_content = f"Error in researcher: {e}"

    return {
        "structured_plan": plan,
        "messages": [HumanMessage(content=response_content, name="researcher")],
    }


async def _code_node_interactive_debug(state: State, current_step, plan) -> Tuple[bool, str]:
    """
    Interactive debugging version of code execution.
    
    Uses step-by-step validation instead of write-and-fail pattern.
    Returns (success, response_content).
    """
    from src.tools.interactive_debugging import get_interactive_debugger
    from src.config.env import WORKSPACE_DIR
    
    debugger = get_interactive_debugger()
    session = None
    
    try:
        # Start interactive debugging session
        session = await debugger.start_debugging_session(
            command="python3 -i",
            working_directory=WORKSPACE_DIR
        )
        
        logger.info(f"🔧 Started interactive debugging session {session.session_id} for step: {current_step.title}")
        
        # Get the original coder agent response to understand what needs to be done
        result = coder_agent.invoke(state)
        original_response = result["messages"][-1].content
        
        # Extract code blocks from the response for step-by-step validation
        code_blocks = _extract_code_blocks(original_response)
        
        if not code_blocks:
            # If no code blocks found, fall back to original behavior
            logger.info("No code blocks found for interactive debugging, using original response")
            return True, original_response
        
        # Validate each code block step by step
        validated_outputs = []
        total_saved_calls = 0
        
        for i, code_block in enumerate(code_blocks):
            logger.debug(f"Validating code block {i+1}/{len(code_blocks)}")
            
            success, output, error_info = await debugger.validate_code_step(
                session, code_block, timeout_ms=10000
            )
            
            if success:
                validated_outputs.append(f"✅ Code block {i+1} executed successfully:\n```python\n{code_block}\n```\nOutput: {output}")
                total_saved_calls += 1
            else:
                # Attempt error recovery
                logger.warning(f"Code block {i+1} failed, attempting recovery")
                
                # Try to generate recovery code
                recovery_code = _generate_recovery_code(error_info, code_block)
                if recovery_code:
                    recovery_success, recovery_output = await debugger.recover_from_error(
                        session, error_info, recovery_code
                    )
                    
                    if recovery_success:
                        validated_outputs.append(f"🔧 Code block {i+1} recovered:\n```python\n{recovery_code}\n```\nOutput: {recovery_output}")
                        total_saved_calls += 1
                    else:
                        validated_outputs.append(f"❌ Code block {i+1} failed with recovery attempt:\n```python\n{code_block}\n```\nError: {output}\nRecovery failed: {recovery_output}")
                        # Don't fail completely, continue with next block
                else:
                    validated_outputs.append(f"❌ Code block {i+1} failed:\n```python\n{code_block}\n```\nError: {output}")
                    # Don't fail completely, continue with next block
        
        # Get session metrics for reporting
        metrics = debugger.get_session_metrics(session)
        
        # Create enhanced response with validation results
        response_content = f"""Interactive debugging completed for step: {current_step.title}

📊 **Session Metrics:**
- Session ID: {metrics['session_id']}
- Duration: {metrics['duration']:.1f}s
- Total interactions: {metrics['total_interactions']}
- Validated steps: {metrics['validated_steps']}
- API calls saved: {metrics['api_calls_saved']}
- Efficiency ratio: {metrics['efficiency_ratio']:.2f}

🔍 **Step-by-step Validation Results:**
{chr(10).join(validated_outputs)}

📋 **Original Analysis:**
{original_response}
"""
        
        # Store metrics in step results for tracking
        current_step.results = _create_enhanced_results(
            "coder", current_step, response_content, result
        )
        current_step.results["interactive_debug_metrics"] = metrics
        
        logger.info(f"🎯 Interactive debugging completed - saved {total_saved_calls} API calls")
        
        return True, response_content
        
    except Exception as e:
        logger.error(f"Interactive debugging failed: {e}")
        # Fall back to original behavior on failure
        result = coder_agent.invoke(state)
        return False, result["messages"][-1].content
        
    finally:
        # Always clean up the session
        if session:
            await debugger.terminate_session(session)


def _extract_code_blocks(text: str) -> List[str]:
    """Extract Python code blocks from text."""
    import re
    
    # Look for code blocks marked with ```python or ```
    code_pattern = r'```(?:python)?\s*\n(.*?)\n```'
    matches = re.findall(code_pattern, text, re.DOTALL)
    
    # Also look for single-line code that might be inline
    if not matches:
        # Look for lines that look like Python code
        lines = text.split('\n')
        potential_code = []
        for line in lines:
            line = line.strip()
            if (line.startswith(('import ', 'from ', 'def ', 'class ', 'print(', 'pd.', 'np.', 'plt.')) 
                or '=' in line and not line.startswith('#')):
                potential_code.append(line)
        
        if potential_code:
            matches = ['\n'.join(potential_code)]
    
    return [match.strip() for match in matches if match.strip()]


def _generate_recovery_code(error_info: Dict[str, Any], original_code: str) -> Optional[str]:
    """Generate recovery code based on error information."""
    error_text = error_info.get('error', '').lower()
    
    # Common recovery patterns
    if 'modulenotfounderror' in error_text or 'no module named' in error_text:
        # Try to install missing module
        if 'pandas' in error_text:
            return "import subprocess; subprocess.run(['pip', 'install', 'pandas'], capture_output=True)"
        elif 'numpy' in error_text:
            return "import subprocess; subprocess.run(['pip', 'install', 'numpy'], capture_output=True)"
        elif 'matplotlib' in error_text:
            return "import subprocess; subprocess.run(['pip', 'install', 'matplotlib'], capture_output=True)"
    
    elif 'nameerror' in error_text:
        # Try to define common variables
        if 'pd' in error_text:
            return "import pandas as pd"
        elif 'np' in error_text:
            return "import numpy as np"
        elif 'plt' in error_text:
            return "import matplotlib.pyplot as plt"
    
    elif 'syntaxerror' in error_text:
        # For syntax errors, try to fix common issues
        if original_code.count('(') != original_code.count(')'):
            return original_code + ')'
        elif original_code.count('[') != original_code.count(']'):
            return original_code + ']'
        elif original_code.count('{') != original_code.count('}'):
            return original_code + '}'
    
    return None


def code_node(state: State) -> dict:
    """
    Executes the coding task with interactive debugging, updates the plan, and returns to the supervisor.
    
    Enhanced version that uses step-by-step interactive debugging instead of write-and-fail pattern.
    Includes HITL (Human-in-the-Loop) support for code execution approval.
    """
    plan = state["structured_plan"]
    current_step = plan.get_current_step()
    current_step.status = "in_progress"
    response_content = ""

    logger.info(f"Coder starting step with interactive debugging: {current_step.title}")

    # ─── HITL Check for Code Execution ─────────────────────────────────
    from ..config.hitl_config import InterruptTrigger
    from ..graph.hitl_handlers import HITLInterruptHandler
    
    hitl_handler = HITLInterruptHandler()
    
    # Check if human approval is needed for code execution
    if hitl_handler.should_interrupt_for_step(state, current_step, InterruptTrigger.TOOL_EXECUTION):
        logger.info(f"HITL approval required for code execution: {current_step.title}")
        
        action_description = f"Execute code for step '{current_step.title}'"
        interrupt_payload = hitl_handler.create_interrupt_for_step(
            state, current_step, InterruptTrigger.TOOL_EXECUTION, action_description,
            additional_context={"execution_type": "code", "agent": "coder"}
        )
        
        # Trigger interrupt
        from langgraph.types import interrupt
        human_response = interrupt(interrupt_payload)
        
        # Process human response
        if human_response.get("action") == "reject":
            logger.info(f"Human rejected code execution for step: {current_step.title}")
            current_step.status = "skipped"
            current_step.results = {
                "summary": "Code execution rejected by human review",
                "step_execution": {
                    "agent": "coder",
                    "status": "skipped",
                    "reason": "human_rejection"
                }
            }
            plan.advance_to_next_step()
            return {
                "structured_plan": plan,
                "messages": [HumanMessage(content="Code execution was rejected by human review.", name="coder")],
            }
        elif human_response.get("action") == "modify":
            logger.info(f"Human requested modifications for code execution: {current_step.title}")
            # Apply any modifications to the step
            modifications = human_response.get("data", {})
            if "description" in modifications:
                current_step.description = modifications["description"]
        
        logger.info(f"Human approved code execution for step: {current_step.title}")

    # Mark todo as in progress
    _update_todo_status(current_step, completed=False)

    try:
        # Try interactive debugging first
        import asyncio
        
        # Run interactive debugging in async context
        success, response_content = asyncio.run(
            _code_node_interactive_debug(state, current_step, plan)
        )
        
        if not success:
            logger.warning("Interactive debugging failed, falling back to original method")
            # Fallback to original behavior if interactive debugging fails
            result = coder_agent.invoke(state)
            response_content = result["messages"][-1].content

        if not current_step.requires_evaluation:
            current_step.status = "completed"
            logger.info(
                f"🎯 Step {current_step.step_index} completed without evaluation required: {current_step.title}"
            )
            # Mark todo as completed
            _update_todo_status(current_step, completed=True)
            plan.advance_to_next_step()
        else:
            logger.info(
                f"⏳ Step {current_step.step_index} requires evaluation before completion: {current_step.title}"
            )

        # Enhanced result capture (may already be set by interactive debugging)
        if "interactive_debug_metrics" not in current_step.results:
            result = coder_agent.invoke(state)  # Get result for enhanced tracking
            current_step.results = _create_enhanced_results(
                "coder", current_step, response_content, result
            )
        
        logger.info(f"Coder completed step with enhanced results: {current_step.title}")

    except Exception as e:
        logger.error(f"Coder failed at step {current_step.title}: {e}")
        current_step.status = "failed"

        # Enhanced error handling with user-friendly messages
        try:
            from src.tools.error_recovery import (
                classify_and_report_error,
                format_error_for_user,
            )

            error_report = classify_and_report_error(
                error_text=str(e), tool_name="coder", execution_method="interactive_debugging"
            )

            # Store comprehensive error information
            current_step.error_message = str(e)
            current_step.error_report = {
                "category": error_report.context.category.value,
                "severity": error_report.context.severity.value,
                "recovery_actions": [
                    {
                        "type": action.action_type,
                        "description": action.description,
                        "priority": action.priority,
                    }
                    for action in error_report.recovery_actions[:3]
                ],
            }

            # Create user-friendly error message
            response_content = format_error_for_user(error_report)

        except Exception as error_handling_error:
            logger.warning(f"Error handling system failed: {error_handling_error}")
            current_step.error_message = str(e)
            response_content = f"Error in coder: {e}"

    return {
        "structured_plan": plan,
        "messages": [HumanMessage(content=response_content, name="coder")],
    }


def browser_node(state: State) -> dict:
    """
    Executes the browser task, updates the plan, and returns to the supervisor.
    """
    plan = state["structured_plan"]
    current_step = plan.get_current_step()
    current_step.status = "in_progress"
    response_content = ""

    logger.info(f"Browser agent starting step: {current_step.title}")

    # Mark todo as in progress
    _update_todo_status(current_step, completed=False)

    try:
        result = browser_agent.invoke(state)
        response_content = result["messages"][-1].content

        if not current_step.requires_evaluation:
            current_step.status = "completed"
            logger.info(
                f"🎯 Step {current_step.step_index} completed without evaluation required: {current_step.title}"
            )
            # Mark todo as completed
            _update_todo_status(current_step, completed=True)
            plan.advance_to_next_step()
        else:
            logger.info(
                f"⏳ Step {current_step.step_index} requires evaluation before completion: {current_step.title}"
            )

        current_step.results = {"summary": response_content}
        logger.info(f"Browser agent completed step: {current_step.title}")

    except Exception as e:
        logger.error(f"Browser agent failed at step {current_step.title}: {e}")
        current_step.status = "failed"

        # Enhanced error handling with user-friendly messages
        try:
            from src.tools.error_recovery import (
                classify_and_report_error,
                format_error_for_user,
            )

            error_report = classify_and_report_error(
                error_text=str(e),
                tool_name="browser",
                execution_method="agent_execution",
            )

            # Store comprehensive error information
            current_step.error_message = str(e)
            current_step.error_report = {
                "category": error_report.context.category.value,
                "severity": error_report.context.severity.value,
                "recovery_actions": [
                    {
                        "type": action.action_type,
                        "description": action.description,
                        "priority": action.priority,
                    }
                    for action in error_report.recovery_actions[:3]
                ],
            }

            # Create user-friendly error message
            response_content = format_error_for_user(error_report)

        except Exception as error_handling_error:
            logger.warning(f"Error handling system failed: {error_handling_error}")
            current_step.error_message = str(e)
            response_content = f"Error in browser: {e}"

    return {
        "structured_plan": plan,
        "messages": [HumanMessage(content=response_content, name="browser")],
    }


def _pick_agent(text: str) -> str:  # minimal heuristic
    t = text.lower()

    # Bioinformatics and computational biology tasks - route to coder for execution capabilities
    bioinformatics_keywords = [
        "gene",
        "protein",
        "dna",
        "rna",
        "sequence",
        "genomic",
        "transcriptomic",
        "differential expression",
        "pathway",
        "enrichment",
        "uniprot",
        "pdb",
        "kegg",
        "string",
        "reactome",
        "pubmed",
        "biomart",
        "ensembl",
        "variant",
        "snp",
        "structure",
        "fold",
        "binding",
        "molecular",
        "bioinformatics",
        "computational biology",
        "analysis",
        "algorithm",
        "phylogen",
        "evolution",
        "omics",
        "proteomics",
        "metabolomics",
    ]
    if any(w in t for w in bioinformatics_keywords):
        return "coder"

    # Research tasks - literature search, data discovery
    if any(
        w in t
        for w in [
            "research",
            "investig",
            "find",
            "look",
            "search",
            "literature",
            "paper",
            "study",
        ]
    ):
        return "researcher"

    # Browser tasks - web interaction
    if any(
        w in t
        for w in ["open", "browser", "web", "visit", "navigate", "download", "scrape"]
    ):
        return "browser"

    # Default fallback for coding and execution tasks
    return "coder"


def decompose_with_todo_mcp(state: State) -> list:
    """
    Decompose a supervisor step using the todo-md-mcp list_todos tool.

    Args:
        state: Current workflow state

    Returns:
        List of decomposed PlanStep objects

    Raises:
        Exception: If decomposition fails
    """
    from src.tools.mcp_tools import MCPToolManager

    try:
        tool_manager = MCPToolManager()
        list_todos = next(
            (t for t in tool_manager.get_all_tools() if t.name == "list_todos"), None
        )

        if not list_todos:
            raise Exception("list_todos tool not found in MCP tools")

        decomposed_plan_raw = list_todos.invoke({})
        decomposed_plan = (
            json.loads(decomposed_plan_raw)
            if isinstance(decomposed_plan_raw, str)
            else decomposed_plan_raw
        )

        if not isinstance(decomposed_plan, dict) or "todos" not in decomposed_plan:
            raise Exception(f"Invalid decomposed plan structure: {decomposed_plan}")

        todos = decomposed_plan.get("todos", [])
        if not todos:
            raise Exception("No todos returned from decomposition")

        new_steps = []
        for i, todo in enumerate(todos):
            if not isinstance(todo, dict) or "text" not in todo:
                logger.warning(f"Invalid todo structure: {todo}")
                continue

            new_steps.append(
                PlanStep(
                    step_index=i,  # Will be updated during plan replacement
                    title=todo["text"],
                    description=f"Sub-task from decomposed plan: {todo['text']}",
                    agent_name=_pick_agent(todo["text"]),
                )
            )

        if not new_steps:
            raise Exception("No valid steps created from decomposition")

        logger.info(f"Successfully decomposed into {len(new_steps)} steps")
        return new_steps

    except Exception as e:
        logger.error(f"Decomposition failed: {e}")
        raise


def _create_todos_from_plan(structured_plan: StructuredPlan, state: State) -> None:
    """
    Creates todo items from a structured plan using the todo-md-mcp add_todo tool.
    Links each todo item to the corresponding PlanStep via step_id.

    Args:
        structured_plan: The structured plan to create todos from
        state: Current workflow state
    """
    from src.tools.mcp_tools import MCPToolManager

    try:
        tool_manager = MCPToolManager()
        add_todo_tool = next(
            (t for t in tool_manager.get_all_tools() if t.name == "add_todo"), None
        )

        if not add_todo_tool:
            logger.warning(
                "add_todo tool not found in MCP tools. Skipping todo creation."
            )
            return

        logger.info(f"Creating {len(structured_plan.steps)} todos from structured plan")

        # Create todos for each plan step
        for step in structured_plan.steps:
            try:
                # Create todo item with step details
                todo_data = {
                    "text": f"{step.title} - {step.description}",
                    "metadata": {
                        "step_id": step.step_id,
                        "step_index": step.step_index,
                        "agent_name": step.agent_name,
                        "plan_title": structured_plan.title,
                    },
                }

                result = add_todo_tool.invoke(todo_data)
                logger.info(f"Created todo for step {step.step_index}: {step.title}")

                # Debug: log the actual response to understand its format
                logger.info(f"DEBUG: add_todo result type: {type(result)}")
                logger.info(f"DEBUG: add_todo result content: {result}")

                # Enhanced ID extraction with multiple patterns and fallbacks
                step.todo_id = _extract_todo_id(result, step)

                # If direct extraction failed, try fallback methods
                if not step.todo_id:
                    step.todo_id = _fallback_todo_id_retrieval(step, todo_data)

            except Exception as e:
                logger.error(f"Failed to create todo for step {step.step_index}: {e}")
                continue

        logger.info(
            f"Successfully created todos for structured plan: {structured_plan.title}"
        )

    except Exception as e:
        logger.error(f"Todo creation failed: {e}")
        # Don't raise - todo creation is optional and shouldn't break the workflow


def _extract_todo_id(result: any, step: PlanStep) -> str:
    """
    Extract todo ID from the add_todo response using multiple patterns and formats.

    Args:
        result: The response from the add_todo MCP tool
        step: The PlanStep object for logging context

    Returns:
        The extracted todo ID or None if extraction fails
    """
    import re

    # Try JSON format first
    if isinstance(result, str):
        try:
            result_data = json.loads(result)
            if "id" in result_data:
                logger.info(
                    f"Extracted todo ID {result_data['id']} from JSON response for step {step.step_index}"
                )
                return result_data["id"]
        except json.JSONDecodeError:
            pass

    # Try direct dict format
    elif isinstance(result, dict) and "id" in result:
        logger.info(
            f"Extracted todo ID {result['id']} from dict response for step {step.step_index}"
        )
        return result["id"]

    # If result is a string, try multiple regex patterns
    if isinstance(result, str):
        # Pattern 1: Standard HTML comment format <!-- id:uuid -->
        patterns = [
            r"<!-- id:([a-f0-9-]+) -->",
            r"<!-- id:([a-f0-9\-]+) -->",
            r"<!--\s*id:\s*([a-f0-9\-]+)\s*-->",
            r"id:\s*([a-f0-9\-]+)",
            r'"id":\s*"([a-f0-9\-]+)"',
            r"'id':\s*'([a-f0-9\-]+)'",
            r"id=([a-f0-9\-]+)",
            r"ID:\s*([a-f0-9\-]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, result, re.IGNORECASE)
            if match:
                todo_id = match.group(1)
                logger.info(
                    f"Extracted todo ID {todo_id} using pattern '{pattern}' for step {step.step_index}"
                )
                return todo_id

        # If no patterns match, log the result for debugging
        logger.warning(
            f"Could not extract todo ID from string result for step {step.step_index}. Result: {result}"
        )

    return None


def _fallback_todo_id_retrieval(step: PlanStep, todo_data: dict) -> str:
    """
    Fallback method to retrieve todo ID when direct extraction fails.

    Args:
        step: The PlanStep object
        todo_data: The original todo data sent to add_todo

    Returns:
        The retrieved todo ID or None if retrieval fails
    """
    from src.tools.mcp_tools import MCPToolManager

    try:
        logger.info(f"Attempting fallback todo ID retrieval for step {step.step_index}")

        tool_manager = MCPToolManager()
        list_todos_tool = next(
            (t for t in tool_manager.get_all_tools() if t.name == "list_todos"), None
        )

        if not list_todos_tool:
            logger.warning("list_todos tool not found for fallback retrieval")
            return None

        # Get all todos
        todos_result = list_todos_tool.invoke({})

        # Parse the result
        if isinstance(todos_result, str):
            try:
                todos_data = json.loads(todos_result)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse list_todos result: {todos_result}")
                return None
        else:
            todos_data = todos_result

        # Find the todo that matches our text
        target_text = todo_data.get("text", "")

        if isinstance(todos_data, dict) and "todos" in todos_data:
            for todo in todos_data["todos"]:
                if isinstance(todo, dict) and todo.get("text") == target_text:
                    todo_id = todo.get("id")
                    if todo_id:
                        logger.info(
                            f"Retrieved todo ID {todo_id} via fallback for step {step.step_index}"
                        )
                        return todo_id

        # Try file-based extraction as last resort
        return _file_based_todo_id_extraction(step, target_text)

    except Exception as e:
        logger.error(
            f"Fallback todo ID retrieval failed for step {step.step_index}: {e}"
        )
        return None


def _file_based_todo_id_extraction(step: PlanStep, target_text: str) -> str:
    """
    Extract todo ID by reading the todo.md file directly.

    Args:
        step: The PlanStep object
        target_text: The text of the todo to find

    Returns:
        The extracted todo ID or None if extraction fails
    """
    import re
    import os

    try:
        # Determine the todo file path based on environment
        todo_file_path = os.path.join(os.getcwd(), "todo.md")

        if not os.path.exists(todo_file_path):
            logger.warning(f"Todo file not found at {todo_file_path}")
            return None

        # Read the todo file
        with open(todo_file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Find the todo line that contains our target text
        lines = content.split("\n")
        for line in lines:
            if target_text in line:
                # Extract ID from the line
                id_match = re.search(r"<!-- id:([a-f0-9\-]+) -->", line)
                if id_match:
                    todo_id = id_match.group(1)
                    logger.info(
                        f"Retrieved todo ID {todo_id} via file extraction for step {step.step_index}"
                    )
                    return todo_id

        logger.warning(f"Could not find todo with text '{target_text}' in todo.md file")
        return None

    except Exception as e:
        logger.error(
            f"File-based todo ID extraction failed for step {step.step_index}: {e}"
        )
        return None


def _refresh_todo_id(step: PlanStep, todo_text: str) -> bool:
    """
    Refresh the todo ID by reading the current todo.md file and matching by text content.

    Args:
        step: The PlanStep object to update with the current todo ID
        todo_text: The text content of the todo to match

    Returns:
        True if the ID was successfully refreshed, False otherwise
    """
    import re
    import os

    try:
        # Determine the todo file path based on environment
        todo_file_path = os.path.join(os.getcwd(), "todo.md")

        if not os.path.exists(todo_file_path):
            logger.warning(f"Todo file not found at {todo_file_path}")
            return False

        # Read the todo file
        with open(todo_file_path, "r", encoding="utf-8") as f:
            content = f.read()

        logger.debug(f"Todo file content:\n{content}")
        logger.debug(f"Looking for step title: '{step.title}'")

        # Find the todo line that contains our target text (partial match)
        lines = content.split("\n")
        for line in lines:
            logger.debug(f"Checking line: {line}")
            # Check if this line contains the step title (most reliable identifier)
            if step.title in line:
                logger.debug(f"Found matching line: {line}")
                # Extract ID from the line
                id_match = re.search(r"<!-- id:([a-f0-9\-]+) -->", line)
                if id_match:
                    new_todo_id = id_match.group(1)
                    old_todo_id = step.todo_id
                    step.todo_id = new_todo_id
                    logger.info(
                        f"Refreshed todo ID for step {step.step_index}: {old_todo_id} -> {new_todo_id}"
                    )
                    return True

        logger.warning(f"Could not find todo with title '{step.title}' in todo.md file")
        return False

    except Exception as e:
        logger.error(f"Failed to refresh todo ID for step {step.step_index}: {e}")
        return False


def _update_todo_status(step: PlanStep, completed: bool = True) -> None:
    """
    Updates the status of a todo item using the todo-md-mcp update_todo tool.
    Includes automatic ID refresh on failure.

    Args:
        step: The PlanStep containing the todo_id to update
        completed: Whether to mark the todo as completed (default: True)
    """
    from src.tools.mcp_tools import MCPToolManager

    status_text = "completed" if completed else "in progress"
    logger.info(
        f"🔄 Attempting to update todo status to '{status_text}' for step {step.step_index}: {step.title}"
    )

    if not step.todo_id:
        logger.warning(
            f"❌ No todo_id found for step {step.step_index} ('{step.title}'), skipping todo update"
        )
        return

    tool_manager = MCPToolManager()
    update_todo_tool = next(
        (t for t in tool_manager.get_all_tools() if t.name == "update_todo"), None
    )

    if not update_todo_tool:
        logger.warning(
            "❌ update_todo tool not found in MCP tools. Skipping todo update."
        )
        return

    # Prepare the original todo text for potential ID refresh
    todo_text = f"{step.title} - {step.description}"

    # First attempt with current ID
    update_data = {"id": step.todo_id, "completed": completed}

    logger.debug(
        f"🔍 Attempting todo update with ID: {step.todo_id}, completed: {completed}"
    )

    try:
        result = update_todo_tool.invoke(update_data)
        logger.info(
            f"✅ Successfully updated todo {step.todo_id} to {status_text} for step: {step.title}"
        )
        logger.debug(f"📋 Todo update result: {result}")
        return

    except Exception as e:
        error_msg = str(e).lower()
        logger.warning(f"⚠️ Todo update failed for {step.todo_id}: {e}")

        if "not found" in error_msg or "does not exist" in error_msg:
            logger.warning(
                f"🔄 Todo ID {step.todo_id} not found, attempting to refresh ID for step: {step.title}"
            )

            # Attempt to refresh the todo ID
            if _refresh_todo_id(step, todo_text):
                # Retry with refreshed ID
                update_data["id"] = step.todo_id
                logger.info(
                    f"🔄 Retrying todo update with refreshed ID: {step.todo_id}"
                )
                try:
                    result = update_todo_tool.invoke(update_data)
                    logger.info(
                        f"✅ Successfully updated todo {step.todo_id} to {status_text} for step: {step.title} (after ID refresh)"
                    )
                    logger.debug(f"📋 Todo update result after refresh: {result}")
                    return
                except Exception as retry_e:
                    logger.error(
                        f"❌ Failed to update todo {step.todo_id} even after ID refresh: {retry_e}"
                    )
            else:
                logger.error(f"❌ Could not refresh todo ID for step: {step.title}")
        else:
            logger.error(
                f"❌ Failed to update todo {step.todo_id} with non-ID error: {e}"
            )

        # Don't raise - todo updates are optional and shouldn't break the workflow


def supervisor_node(state: State):
    """
    A pure orchestrator that routes to the next agent based on the 'living plan'.
    Uses LangGraph Command pattern for clean routing and state updates.
    Includes HITL (Human-in-the-Loop) support for conditional interrupts.
    """
    logger.info("Supervisor orchestrating based on the living plan.")
    
    # Import HITL functionality
    from ..config.hitl_config import get_hitl_config, InterruptTrigger
    from ..graph.hitl_handlers import HITLInterruptHandler, step_approval_interrupt
    
    plan = state.get("structured_plan")

    if not plan:
        logger.error("No structured plan found in the state. Ending workflow.")
        return Command(goto=END)

    # If the plan is complete, route to the reporter, unless the reporter was the last step.
    if plan.get_current_step() is None or plan.status == "completed":
        # Check if the last executed step was the reporter
        last_executed_step = None
        for step in reversed(plan.steps):
            if step.status == "completed":
                last_executed_step = step
                break

        if last_executed_step and last_executed_step.agent_name == "reporter":
            logger.info("Plan is complete and report was generated. Ending workflow.")
            return Command(goto=END)
        else:
            logger.info("Plan is complete. Adding a final reporting step.")
            report_step = PlanStep(
                step_index=len(plan.steps),
                title="Generate Final Report",
                description="Summarize the workflow execution and generate the final report.",
                agent_name="reporter",
            )
            plan.steps.append(report_step)
            plan.status = "active"
            plan.current_step_index = len(plan.steps) - 1
            return Command(update={"structured_plan": plan}, goto="reporter")

    current_step = plan.get_current_step()

    # Handle supervisor decomposition with proper error handling
    if current_step.agent_name == "supervisor":
        try:
            logger.info(f"Decomposing supervisor step: {current_step.title}")
            decomposed_steps = decompose_with_todo_mcp(state)

            # Replace the current supervisor step with decomposed steps
            original_step_index = plan.current_step_index
            plan.steps = (
                plan.steps[:original_step_index]
                + decomposed_steps
                + plan.steps[original_step_index + 1 :]
            )

            # Update step indices
            for i, step in enumerate(plan.steps):
                step.step_index = i

            # Set current step to the first decomposed step
            plan.current_step_index = original_step_index
            current_step = plan.get_current_step()

            logger.info(
                f"Successfully decomposed into {len(decomposed_steps)} steps. Next: {current_step.agent_name}"
            )

            return Command(
                update={"structured_plan": plan}, goto=current_step.agent_name
            )

        except Exception as e:
            logger.error(f"Decomposition failed: {e}. Falling back to coder agent.")
            # Fallback: convert supervisor step to coder task
            current_step.agent_name = "coder"
            return Command(update={"structured_plan": plan}, goto="coder")

    if current_step.agent_name not in TEAM_MEMBERS:
        logger.error(f"Invalid agent name: {current_step.agent_name}. Ending workflow.")
        return Command(goto=END)

    # Use tool retriever to select relevant tools for this step (cached optimization)
    try:
        # Skip tool retrieval if tools are already cached for this step
        if not current_step.filtered_tools:
            from ..tools.tool_retriever import biomni_tool_retriever
            from ..tools.performance_monitor import record_execution_metric

            # Create a comprehensive query from step details
            query_parts = [current_step.title]
            if current_step.description:
                query_parts.append(current_step.description)
            query = " ".join(query_parts)

            logger.info(f"Using tool retriever for step query: {query}")

            # Track tool retrieval performance
            start_time = time.time()
            try:
                # Get filtered tools for this specific step
                filtered_tools_result = biomni_tool_retriever.invoke(
                    {
                        "query": query,
                        "include_mcp_tools": True,
                        "include_biomni_tools": True,
                    }
                )

                execution_time = time.time() - start_time

                # Record successful tool retrieval metric
                record_execution_metric(
                    tool_name="biomni_tool_retriever",
                    execution_time=execution_time,
                    success=True,
                    strategy_used="new_step_retrieval",
                    retry_count=current_step.retry_count,
                )

                # Store the filtered tools in the current step
                current_step.filtered_tools = filtered_tools_result
                logger.info(
                    f"Successfully filtered tools for step {current_step.step_index}: {current_step.agent_name} (took {execution_time:.2f}s)"
                )

            except Exception as tool_error:
                execution_time = time.time() - start_time

                # Record failed tool retrieval metric
                record_execution_metric(
                    tool_name="biomni_tool_retriever",
                    execution_time=execution_time,
                    success=False,
                    strategy_used="new_step_retrieval",
                    error_message=str(tool_error),
                    retry_count=current_step.retry_count,
                )
                raise tool_error

        else:
            # Record cache hit metric
            from ..tools.performance_monitor import record_execution_metric

            record_execution_metric(
                tool_name="biomni_tool_retriever",
                execution_time=0.001,  # Minimal time for cache hit
                success=True,
                strategy_used="cached_retrieval",
                retry_count=current_step.retry_count,
            )

            logger.info(
                f"Using cached filtered tools for step {current_step.step_index}: {current_step.agent_name} (cache hit)"
            )

    except Exception as e:
        logger.warning(f"Tool retriever failed for step {current_step.step_index}: {e}")
        # Continue without filtered tools - this is not a blocking error
        current_step.filtered_tools = None

    # ─── HITL Check Before Agent Routing ────────────────────────────────
    hitl_handler = HITLInterruptHandler()
    
    # Check if human approval is needed for this step
    if hitl_handler.should_interrupt_for_step(state, current_step, InterruptTrigger.STATE_TRANSITION):
        logger.info(f"HITL approval required for step: {current_step.title}")
        
        # Create interrupt context
        action_description = f"Execute step '{current_step.title}' using {current_step.agent_name} agent"
        interrupt_payload = hitl_handler.create_interrupt_for_step(
            state, current_step, InterruptTrigger.STATE_TRANSITION, action_description
        )
        
        # Update state to indicate we're waiting for human input
        updated_state = {
            "structured_plan": plan,
            "awaiting_human_input": True,
            "interrupt_context": interrupt_payload
        }
        
        # Trigger interrupt - this will pause the workflow
        from langgraph.types import interrupt
        human_response = interrupt(interrupt_payload)
        
        # Process human response
        if human_response.get("action") == "approve":
            logger.info(f"Human approved step: {current_step.title}")
            # Continue with original routing
        elif human_response.get("action") == "reject":
            logger.info(f"Human rejected step: {current_step.title}")
            # Skip this step and mark as skipped
            current_step.status = "skipped"
            plan.advance_to_next_step()
            return Command(update={"structured_plan": plan}, goto="supervisor")
        elif human_response.get("action") == "modify":
            logger.info(f"Human requested modification for step: {current_step.title}")
            # Apply modifications if provided
            modifications = human_response.get("data", {})
            if "description" in modifications:
                current_step.description = modifications["description"]
            if "agent_name" in modifications and modifications["agent_name"] in TEAM_MEMBERS:
                current_step.agent_name = modifications["agent_name"]
        
        # Clear interrupt state
        updated_state["awaiting_human_input"] = False
        updated_state["interrupt_context"] = None
        
        # Add to interrupt history
        interrupt_history = state.get("interrupt_history", [])
        interrupt_history.append({
            "timestamp": datetime.datetime.now().isoformat(),
            "interrupt_payload": interrupt_payload,
            "human_response": human_response
        })
        updated_state["interrupt_history"] = interrupt_history
        
        logger.info(f"Routing to agent: {current_step.agent_name} for step: {current_step.title} (after HITL approval)")
        return Command(update=updated_state, goto=current_step.agent_name)
    
    # No HITL approval needed, proceed normally
    logger.info(
        f"Routing to agent: {current_step.agent_name} for step: {current_step.title}"
    )
    return Command(update={"structured_plan": plan}, goto=current_step.agent_name)


def planner_node(state: State) -> dict:
    """
    Generates a structured plan for the workflow, injecting programmatic IDs.
    Creates corresponding todo items for each plan step.
    """
    logger.info("Planner generating structured plan.")
    messages = apply_prompt_template("planner", state)

    # Use a reasoning model for planning
    llm = get_llm_by_type("reasoning")

    # Optional: Enhance prompt with search results if enabled
    # This part can be added back if search_before_planning is a desired feature
    # if state.get("search_before_planning"):
    #     # ... (search logic)

    response = llm.invoke(messages)

    try:
        # Enhanced JSON extraction
        import re

        json_match = re.search(r"```json\s*(.*?)\s*```", response.content, re.DOTALL)
        if not json_match:
            raise json.JSONDecodeError(
                "No JSON block found in the response.", response.content, 0
            )

        json_content = json_match.group(1).strip()
        plan_dict = json.loads(json_content)

        if "steps" not in plan_dict:
            raise TypeError("The 'steps' key is missing from the plan.")

        # Programmatically enrich the plan with IDs and indices
        for i, step in enumerate(plan_dict.get("steps", [])):
            step["step_index"] = i
            # step_id is handled by Pydantic's default_factory

        # Create and validate the StructuredPlan object
        structured_plan = StructuredPlan(**plan_dict)

        logger.info(
            f"Successfully created structured plan '{structured_plan.title}' with {len(structured_plan.steps)} steps."
        )

        # Create corresponding todo items for each plan step
        _create_todos_from_plan(structured_plan, state)

        return {
            "structured_plan": structured_plan,
            "messages": [
                HumanMessage(
                    content=f"New plan created: {structured_plan.title}", name="planner"
                )
            ],
        }
    except (json.JSONDecodeError, TypeError) as e:
        logger.error(f"Failed to parse or validate the plan: {e}")
        # Handle error: maybe route to a recovery state or end the workflow
        return {
            "messages": [
                HumanMessage(
                    content=f"Error: Could not create a valid plan. Raw response: {response.content}",
                    name="planner",
                )
            ]
        }


def _get_available_tools_for_planning() -> str:
    """
    Retrieve the list of available tools for planning context.

    Returns:
        Formatted string of available tools or error message
    """
    from src.tools.tool_retriever import list_all_available_tools

    logger.info("Getting list of all available tools for planning context.")
    try:
        available_tools = list_all_available_tools.invoke({})
        logger.info("Successfully retrieved available tools for planning.")
        return available_tools
    except Exception as e:
        logger.warning(f"Failed to retrieve available tools: {e}")
        return "No tools available due to retrieval error."


def _inject_tools_context_into_messages(messages: list, available_tools: str) -> None:
    """
    Inject available tools context into the planning messages.
    Limits context size to prevent LLM timeout issues.

    Args:
        messages: List of messages to modify
        available_tools: String containing available tools information
    """
    if not messages or not available_tools:
        return

    # TEMPORARY: Limit tools context size to prevent LLM hangs
    max_context_size = 5000  # characters
    if len(available_tools) > max_context_size:
        logger.warning(
            f"⚠️  Tools context too large ({len(available_tools)} chars), truncating to {max_context_size} chars"
        )
        truncated_tools = (
            available_tools[:max_context_size]
            + "\n\n... [Context truncated due to size limits]"
        )
        tools_context = f"\n\nAvailable Tools (truncated):\n{truncated_tools}"
    else:
        logger.info(f"📏 Tools context size: {len(available_tools)} characters")
        tools_context = f"\n\nAvailable Tools:\n{available_tools}"

    if hasattr(messages[-1], "content"):
        messages[-1].content += tools_context
    else:
        # Fallback if message structure is different
        messages.append(
            HumanMessage(content=f"Available tools context: {tools_context}")
        )


def _sanitize_filename(name: str) -> str:
    """Sanitizes a string to be used as a filename."""
    import re

    sanitized_name = re.sub(r"[^\w\s-]", "", name).strip()
    sanitized_name = re.sub(r"\s+", "_", sanitized_name)
    return sanitized_name[:100]


def _generate_session_content(
    content_data: dict, context: Dict[str, str], state: dict, user_context: dict
) -> str:
    """
    Generate session-specific content for appending to living documents.

    Args:
        content_data: Extracted content from workflow execution
        context: Semantic context for the document
        state: Current workflow state
        user_context: User context and intent

    Returns:
        Formatted session content ready for appending
    """
    # Extract key information from workflow execution with proper field mapping
    key_findings = content_data.get(
        "key_results",
        content_data.get(
            "primary_results", "No specific findings recorded for this session."
        ),
    )
    methodology_updates = content_data.get(
        "methodology_overview", "No methodology updates for this session."
    )
    technical_details = content_data.get(
        "technical_details", "No technical implementation details recorded."
    )
    data_analysis = content_data.get(
        "statistical_analysis", "No data analysis performed in this session."
    )
    results_summary = content_data.get(
        "executive_summary", "No results summary available for this session."
    )
    next_steps = content_data.get(
        "future_directions", "No specific next steps identified."
    )

    # Generate session conclusions
    session_conclusions = _generate_session_conclusions(content_data, context, state)

    # Format session content
    session_content = f"""### New Findings & Results
{key_findings}

### Methodology Updates  
{methodology_updates}

### Technical Implementation
{technical_details}

### Data Analysis
{data_analysis}

### Results Summary
{results_summary}

### Session Conclusions
{session_conclusions}

### Next Steps
{next_steps}"""

    return session_content


def _generate_session_conclusions(
    content_data: dict, context: Dict[str, str], state: dict
) -> str:
    """
    Generate intelligent conclusions for the current session based on workflow execution.

    Args:
        content_data: Extracted content from workflow execution
        context: Semantic context for the document
        state: Current workflow state

    Returns:
        Session conclusions summary
    """
    plan = state.get("structured_plan")
    if not plan:
        return "Session completed successfully with workflow execution."

    completed_steps = [step for step in plan.steps if step.status == "completed"]
    total_steps = len(plan.steps)

    # Generate summary based on completed work
    if completed_steps:
        step_summaries = []
        for step in completed_steps[-3:]:  # Last 3 completed steps
            if hasattr(step, "results") and step.results:
                summary = step.results.get("summary", step.title)
                step_summaries.append(f"- {step.title}: {summary}")
            else:
                step_summaries.append(f"- {step.title}: Completed successfully")

        conclusions = f"""This session completed {len(completed_steps)} of {total_steps} planned steps in the {context.get('analysis_type', 'analysis')} workflow.

Key accomplishments:
{chr(10).join(step_summaries)}

The analysis progressed effectively with {"good" if len(completed_steps) > total_steps * 0.5 else "moderate"} completion rate."""
    else:
        conclusions = f"Session initiated for {context.get('analysis_type', 'analysis')} analysis. Workflow setup completed."

    return conclusions


def reporter_node(state: State) -> dict:
    """
    Generates a living document with session-aware updates and intelligent content extraction.
    Supports both new document creation and session-based content appending.
    """
    from src.config.env import WORKSPACE_DIR
    from src.service.document_service import DocumentService
    import os
    import uuid

    plan = state["structured_plan"]
    current_step = plan.get_current_step()
    current_step.status = "in_progress"

    logger.info(f"Reporter starting step: {current_step.title}")

    # Mark todo as in progress
    _update_todo_status(current_step, completed=False)

    try:
        # Initialize document service
        doc_service = DocumentService(WORKSPACE_DIR)

        # Generate unique session ID for this workflow execution
        session_id = state.get("session_id", str(uuid.uuid4()))

        # Extract semantic context from state with enhanced context
        goal = state.get("goal", "untitled_workflow")
        plan_steps = [
            {"description": step.description, "title": step.title}
            for step in plan.steps
        ]

        # Extract user context and intent for better document identification
        user_context = _extract_user_context(state, plan)
        context = doc_service.identify_semantic_context(goal, plan.title, plan_steps)

        # Generate semantic filename
        semantic_filename = doc_service.generate_semantic_filename(context)

        # Check for existing document (with goal for identifier matching)
        existing_doc = doc_service.find_existing_document(
            semantic_filename, context, goal
        )
        document_path = os.path.join(WORKSPACE_DIR, semantic_filename)

        # Extract intelligent content from workflow execution
        content_data = _extract_content_from_workflow(state, plan)

        # Generate session content using the new session content generation
        session_content = _generate_session_content(
            content_data, context, state, user_context
        )

        # Use session-aware document creation/updating
        if existing_doc:
            # Document exists - append as new session
            document_path = existing_doc
            backup_path = doc_service.create_document_backup(existing_doc)
            logger.info(f"Created backup before session update: {backup_path}")
            operation_type = "session_appended"

            # Use the new session-aware method
            final_document_path = doc_service.create_or_update_document(
                document_path, session_content, context, session_id
            )
        else:
            # New document - create with session awareness
            operation_type = "created_with_session"

            # Use the new session-aware method for new document
            final_document_path = doc_service.create_or_update_document(
                document_path, session_content, context, session_id
            )

        # Story 4.3: Trigger executive summary update after document creation/update
        try:
            summary_updated = doc_service.trigger_summary_update(
                final_document_path, session_id
            )
            if summary_updated:
                logger.info(
                    f"Executive summary updated for document: {final_document_path}"
                )
            else:
                logger.debug(
                    f"Executive summary update not needed for document: {final_document_path}"
                )
        except Exception as e:
            logger.warning(
                f"Executive summary update failed for {final_document_path}: {e}"
            )

        logger.info(f"Living document {operation_type}: {final_document_path}")

        # Update State
        response_content = (
            f"Living document generated with session-aware updates: {semantic_filename}"
        )
        current_step.status = "completed"
        current_step.results = {
            "document_path": final_document_path,
            "semantic_filename": semantic_filename,
            "context": context,
            "session_id": session_id,
            "operation_type": operation_type,
            "backup_created": existing_doc is not None,
        }
        logger.info(
            f"📊 Reporter completed step {current_step.step_index}: {current_step.title}"
        )

        # Mark todo as completed
        _update_todo_status(current_step, completed=True)
        plan.advance_to_next_step()

    except Exception as e:
        logger.error(
            f"Reporter failed at step {current_step.title}: {e}", exc_info=True
        )
        current_step.status = "failed"
        current_step.error_message = str(e)
        response_content = f"Error in reporter: {e}"

    return {
        "structured_plan": plan,
        "messages": [HumanMessage(content=response_content, name="reporter")],
    }


def _extract_user_context(state: State, plan: StructuredPlan) -> Dict[str, str]:
    """
    Extract user context and intent from the original query and plan.
    Converts user queries into meaningful objectives and research questions.

    Args:
        state: Current workflow state
        plan: Structured plan with user's intent

    Returns:
        Dictionary with primary_objective, research_questions, and hypothesis
    """
    goal = state.get("goal", "")
    plan_title = plan.title if plan else ""

    # Extract primary objective from user's actual query
    primary_objective = _infer_primary_objective(goal, plan_title)

    # Generate research questions based on the query
    research_questions = _generate_research_questions(goal, plan_title)

    # Generate hypothesis based on the query type
    hypothesis = _generate_hypothesis(goal, plan_title)

    return {
        "primary_objective": primary_objective,
        "research_questions": research_questions,
        "hypothesis": hypothesis,
    }


def _infer_primary_objective(goal: str, plan_title: str) -> str:
    """Infer primary objective from user's goal and plan title."""
    if not goal or goal == "untitled_workflow":
        return "Not specified"

    # Clean and format the goal into a proper objective
    objective = goal.strip()

    # Add proper capitalization if needed
    if not objective[0].isupper():
        objective = objective.capitalize()

    # Add period if missing
    if not objective.endswith("."):
        objective += "."

    # Enhance with plan title context if available
    if plan_title and plan_title not in objective:
        objective = f"{objective} Specifically: {plan_title.lower()}."

    return objective


def _generate_research_questions(goal: str, plan_title: str) -> str:
    """Generate relevant research questions from user's goal."""
    if not goal or goal == "untitled_workflow":
        return "- Research questions will be derived from analysis objectives"

    questions = []
    goal_lower = goal.lower()

    # Query-specific research questions
    if "query" in goal_lower and ("uniprot" in goal_lower or "protein" in goal_lower):
        questions.append(
            "- What are the key functional characteristics of this protein?"
        )
        questions.append("- What biological processes is this protein involved in?")
        questions.append(
            "- What is the clinical or research significance of this protein?"
        )
    elif "query" in goal_lower and "alphafold" in goal_lower:
        questions.append("- What is the predicted 3D structure of this protein?")
        questions.append("- How reliable is the structural prediction?")
        questions.append(
            "- What functional insights can be derived from the structure?"
        )
    elif "analysis" in goal_lower:
        questions.append(
            f'- How can we effectively analyze {goal_lower.replace("analysis", "").strip()}?'
        )
        questions.append("- What patterns or insights will emerge from this analysis?")
        questions.append("- What are the implications of the analysis results?")
    else:
        # Generic questions based on the goal
        questions.append(f"- How to successfully accomplish: {goal}?")
        questions.append("- What key insights will be discovered?")
        questions.append("- What are the next steps after completing this objective?")

    return "\n".join(questions)


def _generate_hypothesis(goal: str, plan_title: str) -> str:
    """Generate a reasonable hypothesis based on the user's goal."""
    if not goal or goal == "untitled_workflow":
        return "Hypothesis will be formulated based on research objectives"

    goal_lower = goal.lower()

    # Query-specific hypotheses
    if "query" in goal_lower and ("uniprot" in goal_lower or "protein" in goal_lower):
        return "The protein query will reveal specific functional information that provides insights into biological mechanisms and potential research applications."
    elif "query" in goal_lower and "alphafold" in goal_lower:
        return "The AlphaFold structural data will provide high-confidence 3D structural information that complements functional data and enhances understanding of the protein."
    elif "analysis" in goal_lower:
        return f"The analysis will successfully identify meaningful patterns and generate actionable insights relevant to the research objective."
    else:
        return f"The workflow will successfully achieve the stated objective: {goal}"


def _extract_content_from_workflow(
    state: State, plan: StructuredPlan
) -> Dict[str, str]:
    """
    Extract intelligent content from workflow execution results.
    Replaces placeholder helper functions with real content extraction.
    """
    steps = [step for step in plan.steps if step.agent_name != "reporter"]

    return {
        "executive_summary": _extract_executive_summary(steps, state),
        "key_insights": _extract_key_insights(steps),
        "technical_details": _extract_technical_details(steps),
        "supporting_evidence": _extract_supporting_evidence(steps),
        "statistical_analysis": _extract_statistical_analysis(steps),
        "biological_interpretation": _generate_biological_interpretation(steps, state),
        "limitations": _identify_limitations(steps),
        "future_directions": _suggest_future_directions(steps, state),
        "methodology_overview": _extract_methodology_overview(steps),
        "primary_results": _extract_primary_results(steps),
        "tools_and_technologies": _extract_tools_and_technologies(steps),
        "data_sources": _extract_data_sources(steps),
        "literature_references": _extract_literature_references(steps),
        "performance_metrics": _extract_performance_metrics(steps),
        "validation_methods": _extract_validation_methods(steps),
        "quality_control": _extract_quality_control(steps),
        "reproducibility_info": _extract_reproducibility_info(steps),
        "key_results": _extract_key_results(steps),
    }


def _populate_template(
    template: str,
    content_data: Dict[str, str],
    context: Dict[str, str],
    state: State,
    user_context: Dict[str, str],
) -> str:
    """
    Populate template with extracted content and metadata.
    """
    # Basic metadata
    now = datetime.datetime.now()

    template_vars = {
        "document_title": (
            f"Analysis Report: {context.get('analysis_type', 'General')} - {context.get('subject', 'Analysis')}"
        ),
        "analysis_type": context.get("analysis_type", "General"),
        "subject": context.get("subject", "Analysis"),
        "domain": context.get("domain", "data_science"),
        "creation_date": now.strftime("%Y-%m-%d %H:%M:%S"),
        "last_updated": now.strftime("%Y-%m-%d %H:%M:%S"),
        "version": "1.0",
        "objective": user_context.get(
            "primary_objective", state.get("goal", "Not specified")
        ),
        "research_questions": user_context.get(
            "research_questions", _extract_research_questions(state)
        ),
        "hypothesis": user_context.get("hypothesis", _extract_hypothesis(state)),
        "analysis_pipeline": _extract_analysis_pipeline(state),
        "clinical_relevance": _extract_clinical_relevance(content_data, context),
        "comparative_analysis": _extract_comparative_analysis(content_data),
        "unexpected_findings": _extract_unexpected_findings(content_data),
        "implications": _extract_implications(content_data, context),
        "data_quality_issues": _extract_data_quality_issues(content_data),
        "methodological_constraints": _extract_methodological_constraints(content_data),
        "recommended_followup": _extract_recommended_followup(content_data, context),
        "method_improvements": _extract_method_improvements(content_data),
        "reference_data_sources": content_data.get(
            "data_sources", "No specific data sources identified"
        ),
        "reference_tools": content_data.get(
            "tools_and_technologies", "No specific tools identified"
        ),
        "supplementary_data": _extract_supplementary_data(content_data),
        "code_snippets": _extract_code_snippets(content_data),
        "additional_figures": _extract_additional_figures(content_data),
        "version_history": f"v1.0 - {now.strftime('%Y-%m-%d')} - Initial analysis",
        "change_log": (
            f"- {now.strftime('%Y-%m-%d')}: Document created from workflow execution"
        ),
        "contributors": "Multi-Agent Bioinformatics System",
        "visualizations": _extract_visualizations(content_data),
    }

    # Add content data
    template_vars.update(content_data)

    # Apply dynamic section hiding for empty/placeholder content
    populated_template = _apply_dynamic_section_hiding(template, template_vars)

    return populated_template


def _select_template(context: Dict[str, str], goal: str, plan: StructuredPlan) -> str:
    """
    Select appropriate template based on analysis type and complexity.
    Simple entity queries get the focused template, complex analyses get the full template.

    Args:
        context: Semantic context from document service
        goal: Original user goal/query
        plan: Structured plan with steps

    Returns:
        Path to the appropriate template file
    """
    templates_dir = os.path.join(os.path.dirname(__file__), "..", "templates")

    # Criteria for using the focused template
    use_focused_template = _should_use_focused_template(context, goal, plan)

    if use_focused_template:
        logger.info("Using focused entity template for simple query")
        return os.path.join(templates_dir, "entity_focused_document.md")
    else:
        logger.info("Using full analysis template for complex workflow")
        return os.path.join(templates_dir, "living_document.md")


def _should_use_focused_template(
    context: Dict[str, str], goal: str, plan: StructuredPlan
) -> bool:
    """
    Determine if we should use the focused template for simple entity queries.

    Args:
        context: Semantic context
        goal: User's original goal
        plan: Structured plan

    Returns:
        True if focused template should be used
    """
    goal_lower = goal.lower()
    analysis_type = context.get("analysis_type", "").lower()

    # Simple database query indicators
    is_simple_query = "query" in goal_lower and any(
        db in goal_lower for db in ["uniprot", "alphafold", "pdb", "ensembl", "pubmed"]
    )

    # Entity-specific analysis indicators
    is_entity_analysis = analysis_type in [
        "proteinanalysis",
        "genomicanalysis",
        "compoundanalysis",
    ]

    # Simple workflow indicators (few steps, mostly query-based)
    has_few_steps = len(plan.steps) <= 3
    mostly_queries = (
        sum(1 for step in plan.steps if "query" in step.title.lower())
        >= len(plan.steps) * 0.7
    )

    # No complex analysis steps
    has_complex_steps = any(
        keyword in step.title.lower()
        for step in plan.steps
        for keyword in [
            "analysis",
            "model",
            "statistical",
            "machine learning",
            "pipeline",
        ]
    )

    # Use focused template if it's a simple entity query without complex analysis
    return (
        is_simple_query or (is_entity_analysis and has_few_steps and mostly_queries)
    ) and not has_complex_steps


def _merge_with_existing_document(
    existing_doc_path: str,
    new_content_data: Dict[str, str],
    context: Dict[str, str],
    state: State,
    user_context: Dict[str, str],
    goal: str,
) -> str:
    """
    Merge new content with existing document instead of overwriting.
    This addresses the user's complaint about follow-up queries creating separate documents.

    Args:
        existing_doc_path: Path to existing document
        new_content_data: New content to merge
        context: Semantic context
        state: Current workflow state
        user_context: User context data
        goal: Original user goal

    Returns:
        Merged document content
    """
    # Read existing document
    with open(existing_doc_path, "r", encoding="utf-8") as f:
        existing_content = f.read()

    logger.info(f"Merging new content with existing document: {existing_doc_path}")

    # Parse existing document into sections
    existing_sections = _parse_document_sections(existing_content)

    # Create new template content
    template_path = _select_template(
        context, goal, None
    )  # Use simple context for template selection
    with open(template_path, "r", encoding="utf-8") as f:
        template = f.read()

    # Merge strategies based on content type
    merged_content_data = _merge_content_sections(
        existing_sections, new_content_data, goal
    )

    # Update metadata for merged document
    now = datetime.datetime.now()
    merged_content_data.update(
        {
            "last_updated": now.strftime("%Y-%m-%d %H:%M:%S"),
            "version": _increment_version(existing_sections.get("version", "1.0")),
            "change_log": _create_merged_change_log(
                existing_sections.get("change_log", ""), goal, now
            ),
            "document_title": (
                f"Analysis Report: {context.get('analysis_type', 'General')} - {context.get('subject', 'Analysis')}"
            ),
            "analysis_type": context.get("analysis_type", "General"),
            "subject": context.get("subject", "Analysis"),
            "domain": context.get("domain", "data_science"),
            "creation_date": existing_sections.get(
                "creation_date", now.strftime("%Y-%m-%d %H:%M:%S")
            ),
            "objective": user_context.get(
                "primary_objective", state.get("goal", "Not specified")
            ),
            "research_questions": user_context.get(
                "research_questions", _extract_research_questions(state)
            ),
            "hypothesis": user_context.get("hypothesis", _extract_hypothesis(state)),
            "contributors": "Multi-Agent Bioinformatics System",
        }
    )

    # Apply template population with merged data
    merged_document = _populate_template(
        template, merged_content_data, context, state, user_context
    )

    return merged_document


def _parse_document_sections(content: str) -> Dict[str, str]:
    """
    Parse existing document into sections for intelligent merging.

    Args:
        content: Existing document content

    Returns:
        Dictionary mapping section names to content
    """
    sections = {}
    lines = content.split("\n")
    current_section = None
    current_content = []

    for line in lines:
        # Check for metadata
        if line.startswith("- **") and ":**" in line:
            key = line.split(":**")[0].replace("- **", "").lower().replace(" ", "_")
            value = line.split(":**")[1].strip()
            sections[key] = value
        # Check for section headers
        elif line.startswith("##") and not line.startswith("###"):
            if current_section:
                sections[current_section] = "\n".join(current_content).strip()
            current_section = (
                line.strip("#").strip().lower().replace(" ", "_").replace("&", "and")
            )
            current_content = []
        elif current_section:
            current_content.append(line)

    # Don't forget the last section
    if current_section:
        sections[current_section] = "\n".join(current_content).strip()

    return sections


def _merge_content_sections(
    existing_sections: Dict[str, str], new_content_data: Dict[str, str], goal: str
) -> Dict[str, str]:
    """
    Intelligently merge content sections, prioritizing new data while preserving valuable existing content.

    Args:
        existing_sections: Parsed sections from existing document
        new_content_data: New content to merge
        goal: User's goal for context

    Returns:
        Merged content data
    """
    merged_data = {}

    # Merge strategy for different section types
    for key, new_value in new_content_data.items():
        existing_value = existing_sections.get(key, "")

        if key in ["key_results", "primary_results"]:
            # For results: append new findings to existing ones
            merged_data[key] = _merge_results_sections(existing_value, new_value, key)
        elif key in ["executive_summary"]:
            # For summary: combine both with new goal context
            merged_data[key] = _merge_summary_sections(existing_value, new_value, goal)
        elif key in ["tools_and_technologies", "data_sources"]:
            # For tools/data: combine unique items
            merged_data[key] = _merge_list_sections(existing_value, new_value)
        elif key in ["biological_interpretation", "clinical_relevance"]:
            # For interpretation: combine insights
            merged_data[key] = _merge_interpretation_sections(existing_value, new_value)
        else:
            # For other sections: use new value if meaningful, otherwise keep existing
            if (
                new_value
                and len(new_value.strip()) > 50
                and not _is_placeholder_content(new_value)
            ):
                merged_data[key] = new_value
            elif existing_value:
                merged_data[key] = existing_value
            else:
                merged_data[key] = new_value

    # Preserve any existing content not in new_content_data
    for key, existing_value in existing_sections.items():
        if key not in merged_data and existing_value:
            merged_data[key] = existing_value

    return merged_data


def _merge_results_sections(existing: str, new: str, section_type: str) -> str:
    """Merge results sections by appending new findings."""
    if not existing or _is_placeholder_content(existing):
        return new
    if not new or _is_placeholder_content(new):
        return existing

    # Combine results with clear separation
    separator = f"\n\n### Additional Findings\n\n"
    return f"{existing}{separator}{new}"


def _merge_summary_sections(existing: str, new: str, goal: str) -> str:
    """Merge summary sections with context about multiple analyses."""
    if not existing or _is_placeholder_content(existing):
        return new
    if not new or _is_placeholder_content(new):
        return existing

    # Create combined summary
    return f"{existing}\n\n**Follow-up Analysis ({goal}):** {new}"


def _merge_list_sections(existing: str, new: str) -> str:
    """Merge list-based sections by combining unique items."""
    if not existing or _is_placeholder_content(existing):
        return new
    if not new or _is_placeholder_content(new):
        return existing

    # Simple combination for now - could be made smarter to deduplicate
    return f"{existing}\n\n**Additional Resources:**\n{new}"


def _merge_interpretation_sections(existing: str, new: str) -> str:
    """Merge interpretation sections by combining insights."""
    if not existing or _is_placeholder_content(existing):
        return new
    if not new or _is_placeholder_content(new):
        return existing

    return f"{existing}\n\n**Additional Insights:**\n{new}"


def _increment_version(current_version: str) -> str:
    """Increment document version number."""
    try:
        if "v" in current_version:
            version_num = float(current_version.replace("v", ""))
        else:
            version_num = float(current_version)

        new_version = version_num + 0.1
        return f"v{new_version:.1f}"
    except (ValueError, AttributeError):
        return "v1.1"


def _create_merged_change_log(
    existing_log: str, goal: str, timestamp: datetime.datetime
) -> str:
    """Create updated change log for merged document."""
    new_entry = f"- {timestamp.strftime('%Y-%m-%d')}: Document updated with additional analysis: {goal}"

    if existing_log and not _is_placeholder_content(existing_log):
        return f"{existing_log}\n{new_entry}"
    else:
        return new_entry


def _is_placeholder_content(content: str) -> bool:
    """Check if content is placeholder text."""
    if not content or len(content.strip()) < 20:
        return True

    placeholder_indicators = [
        "will be extracted",
        "will be generated",
        "not specified",
        "from workflow execution",
        "compiled from analysis",
        "generated during workflow",
    ]

    content_lower = content.lower()
    return any(indicator in content_lower for indicator in placeholder_indicators)


def _apply_dynamic_section_hiding(template: str, template_vars: Dict[str, str]) -> str:
    """
    Apply dynamic section hiding to remove empty or placeholder-only sections.
    This addresses the user's complaint about "lots of empty sections" by only showing
    sections that contain meaningful content.

    Args:
        template: Template string with placeholder variables
        template_vars: Dictionary of template variables and their values

    Returns:
        Template with empty sections removed and variables replaced
    """
    # First, replace all template variables
    populated_template = template
    for key, value in template_vars.items():
        populated_template = populated_template.replace(f"{{{key}}}", str(value))

    # Define sections that should be hidden if they contain only placeholder content
    sections_to_check = [
        ("### 2.3 Statistical Analysis", "### 2.4"),
        ("### 2.4 Visualizations & Figures", "## 3"),
        ("### 4.1 Code Implementation", "### 4.2"),
        ("### 4.2 Performance Metrics", "### 4.3"),
        ("### 4.3 Validation Methods", "## 5"),
        ("### 5.2 Clinical Relevance", "### 5.3"),
        ("### 5.3 Comparative Analysis", "## 6"),
        ("### 6.2 Unexpected Findings", "### 6.3"),
        ("### 7.1 Supporting Data", "### 7.2"),
        ("### 7.2 Quality Control Metrics", "### 7.3"),
        ("### 7.3 Reproducibility Information", "## 8"),
        ("### 8.1 Technical Limitations", "### 8.2"),
        ("### 8.2 Data Quality Issues", "### 8.3"),
        ("### 8.3 Methodological Constraints", "## 9"),
        ("### 9.2 Recommended Follow-up Studies", "### 9.3"),
        ("### 9.3 Method Improvements", "## 10"),
        ("### 10.1 Literature References", "### 10.2"),
        ("### 11.1 Supplementary Data", "### 11.2"),
        ("### 11.2 Code Snippets", "### 11.3"),
        ("### 11.3 Additional Figures", "---"),
    ]

    # Check each section and hide if it contains only placeholder content
    for start_marker, end_marker in sections_to_check:
        populated_template = _hide_section_if_placeholder(
            populated_template, start_marker, end_marker
        )

    return populated_template


def _hide_section_if_placeholder(
    template: str, start_marker: str, end_marker: str
) -> str:
    """
    Hide a specific section if it contains only placeholder content.

    Args:
        template: Template content
        start_marker: Starting marker for the section (e.g., "### 3.2 Statistical Analysis")
        end_marker: Ending marker for the section (e.g., "### 3.3")

    Returns:
        Template with section removed if it was placeholder-only
    """
    start_idx = template.find(start_marker)
    if start_idx == -1:
        return template

    # Find the end of this section
    end_idx = template.find(end_marker, start_idx + len(start_marker))
    if end_idx == -1:
        end_idx = len(template)

    # Extract section content
    section_content = template[start_idx:end_idx]

    # Check if section contains only placeholder content
    if _is_placeholder_only_section(section_content):
        # Remove the entire section
        return template[:start_idx] + template[end_idx:]

    return template


def _is_placeholder_only_section(section_content: str) -> bool:
    """
    Determine if a section contains only placeholder content and should be hidden.

    Args:
        section_content: Content of the section to analyze

    Returns:
        True if section should be hidden, False if it contains meaningful content
    """
    # Remove the section header and whitespace for analysis
    lines = section_content.strip().split("\n")
    if len(lines) <= 1:  # Only header line
        return True

    # Get content lines (skip the header)
    content_lines = [line.strip() for line in lines[1:] if line.strip()]

    if not content_lines:  # No content after header
        return True

    # Join all content for analysis
    content = " ".join(content_lines).lower()

    # Common placeholder patterns that indicate empty sections
    placeholder_patterns = [
        "will be extracted from",
        "will be generated",
        "will be compiled",
        "will be synthesized",
        "results from computational steps",
        "generated during workflow execution",
        "compiled during research phase",
        "identified during workflow execution",
        "analysis results from",
        "generated during analysis workflow",
        "available from computational analysis",
        "assessed and validated throughout analysis",
        "applied throughout analysis",
        "identified and addressed during analysis",
        "compiled from analysis execution",
        "from workflow execution",
        "metrics from workflow execution",
        "from executed steps",
        "based on identified limitations",
        "no specific",
        "not specified",
    ]

    # Check if content is mostly placeholder text
    placeholder_matches = sum(
        1 for pattern in placeholder_patterns if pattern in content
    )

    # If more than half the patterns match, or content is very short generic text
    if placeholder_matches >= 2 or len(content) < 50:
        return True

    # Check for very generic content that provides no value
    generic_phrases = [
        "technical implementation details",
        "statistical analysis results",
        "performance metrics",
        "quality control measures",
        "validation methods applied",
        "biological significance",
        "clinical relevance",
        "supporting evidence",
        "limitations identified",
        "data quality assessed",
        "methodological constraints",
    ]

    # If content is mostly generic phrases, hide it
    generic_matches = sum(1 for phrase in generic_phrases if phrase in content)
    if generic_matches >= 2 and len(content) < 200:
        return True

    return False


# Intelligent Content Extraction Helper Functions
# These replace placeholder functions with real content extraction from step.results


def _extract_executive_summary(steps: List[PlanStep], state: State) -> str:
    """Extract executive summary from workflow execution results."""
    summaries = []
    for step in steps:
        if step.results and "summary" in step.results:
            summary = step.results["summary"]
            if isinstance(summary, str) and len(summary.strip()) > 50:
                summaries.append(f"- {step.title}: {summary[:200]}...")

    if summaries:
        return (
            f"This analysis achieved the objective: {state.get('goal', 'Not specified')}.\n\n"
            + "\n".join(summaries)
        )

    return f"Executive summary for: {state.get('goal', 'Analysis objective not specified')}"


def _extract_key_insights(steps: List[PlanStep]) -> str:
    """Extract key insights from step results."""
    insights = []
    for step in steps:
        if step.results:
            # Look for key findings, insights, or important results
            if "key_findings" in step.results:
                findings = step.results["key_findings"]
                if isinstance(findings, list):
                    insights.extend([f"- {finding}" for finding in findings])
                elif isinstance(findings, str):
                    insights.append(f"- {findings}")

            # Extract insights from summary
            if "summary" in step.results:
                summary = step.results["summary"]
                if "significant" in summary.lower() or "important" in summary.lower():
                    insights.append(f"- From {step.title}: {summary}")

    return (
        "\n".join(insights)
        if insights
        else "Key insights will be extracted from completed analysis steps."
    )


def _extract_technical_details(steps: List[PlanStep]) -> str:
    """Extract technical implementation details from step results."""
    details = []
    for step in steps:
        if step.results:
            # Code execution details
            if "code" in step.results:
                details.append(
                    f"**{step.title}:**\n```python\n{step.results['code'][:500]}...\n```"
                )

            # Tool usage
            if "tool_calls" in step.results:
                tools = step.results["tool_calls"]
                if isinstance(tools, list) and tools:
                    tool_names = [
                        tool.get("name", "unknown")
                        for tool in tools
                        if isinstance(tool, dict)
                    ]
                    details.append(
                        f"- {step.title}: Used tools: {', '.join(tool_names)}"
                    )

            # Parameters used
            if "parameters" in step.results:
                params = step.results["parameters"]
                if params:
                    details.append(
                        f"- {step.title}: Parameters: {str(params)[:200]}..."
                    )

    return (
        "\n\n".join(details)
        if details
        else "Technical implementation details from executed steps."
    )


def _extract_supporting_evidence(steps: List[PlanStep]) -> str:
    """Extract supporting evidence from analysis results."""
    evidence = []
    for step in steps:
        if step.results:
            # Look for evidence, validation, or supporting data
            if "evidence" in step.results:
                evidence.append(f"- {step.title}: {step.results['evidence']}")

            # Output files as evidence
            if "output_files" in step.results and step.results["output_files"]:
                files = step.results["output_files"]
                evidence.append(f"- {step.title}: Generated files: {', '.join(files)}")

            # Results with numerical data
            if "results" in step.results:
                results = step.results["results"]
                if isinstance(results, dict):
                    for key, value in results.items():
                        if isinstance(value, (int, float)):
                            evidence.append(f"- {step.title}: {key} = {value}")

    return (
        "\n".join(evidence)
        if evidence
        else "Supporting evidence compiled from analysis execution."
    )


def _extract_statistical_analysis(steps: List[PlanStep]) -> str:
    """Extract statistical analysis results."""
    stats = []
    for step in steps:
        if step.results:
            # Look for statistical results
            if "statistics" in step.results:
                stats.append(f"**{step.title}:**\n{step.results['statistics']}")

            # P-values, correlations, etc.
            if "p_value" in step.results:
                stats.append(f"- {step.title}: p-value = {step.results['p_value']}")

            if "correlation" in step.results:
                stats.append(
                    f"- {step.title}: correlation = {step.results['correlation']}"
                )

            # Look for numerical results that might be statistical
            if isinstance(step.results, dict):
                for key, value in step.results.items():
                    if any(
                        stat_term in key.lower()
                        for stat_term in [
                            "mean",
                            "std",
                            "median",
                            "correlation",
                            "pvalue",
                        ]
                    ):
                        stats.append(f"- {step.title}: {key} = {value}")

    return (
        "\n\n".join(stats)
        if stats
        else "Statistical analysis results from computational steps."
    )


def _generate_biological_interpretation(steps: List[PlanStep], state: State) -> str:
    """Generate biological interpretation based on domain and results."""
    domain_terms = []
    results_summary = []

    for step in steps:
        if step.results and "summary" in step.results:
            summary = step.results["summary"].lower()
            # Look for biological terms
            bio_terms = [
                "gene",
                "protein",
                "cell",
                "pathway",
                "expression",
                "regulation",
                "function",
            ]
            found_terms = [term for term in bio_terms if term in summary]
            domain_terms.extend(found_terms)
            results_summary.append(step.results["summary"])

    goal = state.get("goal", "").lower()
    interpretation = []

    if "differential" in goal or "expression" in goal:
        interpretation.append(
            "Differential expression analysis reveals changes in gene regulation patterns."
        )

    if "protein" in goal or any("protein" in term for term in domain_terms):
        interpretation.append(
            "Protein analysis provides insights into functional mechanisms."
        )

    if "pathway" in goal or any("pathway" in term for term in domain_terms):
        interpretation.append(
            "Pathway analysis illuminates biological process alterations."
        )

    if results_summary:
        interpretation.append(
            f"Based on the analysis results, biological significance is evident in the observed patterns."
        )

    return (
        "\n\n".join(interpretation)
        if interpretation
        else "Biological interpretation based on analysis domain and results."
    )


def _identify_limitations(steps: List[PlanStep]) -> str:
    """Identify limitations from step execution."""
    limitations = []

    for step in steps:
        # Check for errors or retries
        if step.error_message:
            limitations.append(f"- {step.title}: {step.error_message}")

        if step.retry_count > 0:
            limitations.append(
                f"- {step.title}: Required {step.retry_count} retries to complete"
            )

        # Look for limitations in results
        if step.results and "limitations" in step.results:
            limitations.append(f"- {step.title}: {step.results['limitations']}")

        # Look for warnings or issues
        if step.results and "warnings" in step.results:
            warnings = step.results["warnings"]
            if isinstance(warnings, list):
                for warning in warnings:
                    limitations.append(f"- {step.title}: {warning}")

    return (
        "\n".join(limitations)
        if limitations
        else "Analysis limitations identified during execution."
    )


def _suggest_future_directions(steps: List[PlanStep], state: State) -> str:
    """Suggest future directions based on results and domain."""
    suggestions = []
    goal = state.get("goal", "").lower()

    # Domain-specific suggestions
    if "differential" in goal or "expression" in goal:
        suggestions.append("- Validate findings with independent datasets")
        suggestions.append("- Perform functional validation experiments")

    if "protein" in goal:
        suggestions.append("- Investigate protein-protein interactions")
        suggestions.append("- Analyze structural implications")

    # Based on step results
    for step in steps:
        if step.results and "next_steps" in step.results:
            next_steps = step.results["next_steps"]
            if isinstance(next_steps, list):
                suggestions.extend([f"- {suggestion}" for suggestion in next_steps])
            elif isinstance(next_steps, str):
                suggestions.append(f"- {next_steps}")

    if not suggestions:
        suggestions = [
            "- Expand analysis to additional datasets",
            "- Investigate alternative methodologies",
            "- Validate results with experimental approaches",
        ]

    return "\n".join(suggestions)


def _extract_methodology_overview(steps: List[PlanStep]) -> str:
    """Extract methodology overview from planned steps."""
    methods = []
    for i, step in enumerate(steps, 1):
        agent_desc = {
            "researcher": "Literature research and data gathering",
            "coder": "Computational analysis and data processing",
            "browser": "Web-based data extraction and verification",
        }.get(step.agent_name, "Analysis step")

        methods.append(f"{i}. **{step.title}** ({step.agent_name}): {agent_desc}")

    return (
        "\n".join(methods)
        if methods
        else "Multi-step computational workflow executed by specialized agents."
    )


def _extract_primary_results(steps: List[PlanStep]) -> str:
    """Extract primary results from step executions."""
    results = []
    for step in steps:
        if step.results and step.status == "completed":
            if "summary" in step.results:
                results.append(f"**{step.title}:** {step.results['summary']}")
            elif "output" in step.results:
                results.append(f"**{step.title}:** {step.results['output']}")

    return (
        "\n\n".join(results)
        if results
        else "Primary results extracted from completed workflow steps."
    )


def _extract_tools_and_technologies(steps: List[PlanStep]) -> str:
    """Extract only tools that were actually used during execution."""
    used_tools = set()

    for step in steps:
        if step.results:
            # Extract actual tool usage from tool_calls
            if "tool_calls" in step.results:
                tool_calls = step.results["tool_calls"]
                if isinstance(tool_calls, list):
                    for call in tool_calls:
                        if isinstance(call, dict) and "name" in call:
                            used_tools.add(call["name"])

            # Extract tools mentioned in summaries (common pattern)
            if "summary" in step.results:
                summary = step.results["summary"]
                if isinstance(summary, str):
                    # Look for common tool usage patterns in summaries
                    tool_patterns = [
                        r"query_(\w+)",
                        r"search_(\w+)",
                        r"fetch\b",
                        r"crawl\b",
                        r"extract\b",
                        r"browse\b",
                        r"retrieve\b",
                    ]
                    for pattern in tool_patterns:
                        matches = re.findall(pattern, summary.lower())
                        for match in matches:
                            if isinstance(match, str):
                                used_tools.add(
                                    f"query_{match}" if "query_" in pattern else match
                                )
                            else:
                                used_tools.add(
                                    pattern.replace(r"\b", "").replace("(\\w+)", "")
                                )

    # Only add system components if we have actual tool usage
    if used_tools:
        used_tools.add("Multi-Agent System (LangGraph)")
        used_tools.add("Python-based computational environment")

        # Format tools with more specific descriptions where possible
        formatted_tools = []
        for tool in sorted(used_tools):
            if tool.startswith("query_"):
                db_name = tool.replace("query_", "").upper()
                formatted_tools.append(f"{tool}: Query the {db_name} database")
            else:
                formatted_tools.append(tool)

        return "- " + "\n- ".join(formatted_tools)
    else:
        return "System-level computational environment"


def _extract_data_sources(steps: List[PlanStep]) -> str:
    """Extract data sources from step results."""
    sources = set()
    for step in steps:
        if step.results:
            if "data_sources" in step.results:
                ds = step.results["data_sources"]
                if isinstance(ds, list):
                    sources.update(ds)
                elif isinstance(ds, str):
                    sources.add(ds)

            # Look for URLs or databases in summaries
            if "summary" in step.results:
                summary = step.results["summary"]
                if "http" in summary or "database" in summary.lower():
                    sources.add(f"Data from {step.title}")

    return (
        "- " + "\n- ".join(sorted(sources))
        if sources
        else "Data sources identified during workflow execution."
    )


def _extract_literature_references(steps: List[PlanStep]) -> str:
    """Extract literature references from research steps."""
    references = []
    for step in steps:
        if step.agent_name == "researcher" and step.results:
            if "references" in step.results:
                refs = step.results["references"]
                if isinstance(refs, list):
                    references.extend(refs)

            # Look for citation patterns in summaries
            if "summary" in step.results:
                summary = step.results["summary"]
                if any(
                    term in summary.lower()
                    for term in ["pubmed", "doi", "citation", "paper"]
                ):
                    references.append(f"References from {step.title}")

    return (
        "\n".join(references)
        if references
        else "Literature references compiled during research phase."
    )


def _extract_performance_metrics(steps: List[PlanStep]) -> str:
    """Extract performance metrics from execution."""
    metrics = []
    total_time = 0

    for step in steps:
        if step.results:
            if "execution_time" in step.results:
                time_val = step.results["execution_time"]
                metrics.append(f"- {step.title}: {time_val}s execution time")
                if isinstance(time_val, (int, float)):
                    total_time += time_val

            if "memory_usage" in step.results:
                metrics.append(
                    f"- {step.title}: {step.results['memory_usage']} memory usage"
                )

    if total_time > 0:
        metrics.insert(0, f"- Total workflow execution time: {total_time:.2f}s")

    return (
        "\n".join(metrics)
        if metrics
        else "Performance metrics from workflow execution."
    )


def _extract_validation_methods(steps: List[PlanStep]) -> str:
    """Extract validation methods used."""
    validations = []
    for step in steps:
        if step.results and "validation" in step.results:
            validations.append(f"- {step.title}: {step.results['validation']}")

        # Look for quality control or validation in step descriptions
        if (
            "validat" in step.description.lower()
            or "quality" in step.description.lower()
        ):
            validations.append(f"- {step.title}: {step.description}")

    return (
        "\n".join(validations)
        if validations
        else "Validation methods applied during analysis workflow."
    )


def _extract_quality_control(steps: List[PlanStep]) -> str:
    """Extract quality control information."""
    qc_info = []
    for step in steps:
        if step.results:
            if "quality_control" in step.results:
                qc_info.append(f"- {step.title}: {step.results['quality_control']}")

            if "qc_metrics" in step.results:
                metrics = step.results["qc_metrics"]
                qc_info.append(f"- {step.title}: QC Metrics: {metrics}")

    return (
        "\n".join(qc_info)
        if qc_info
        else "Quality control measures applied throughout analysis."
    )


def _extract_reproducibility_info(steps: List[PlanStep]) -> str:
    """Extract reproducibility information."""
    repro_info = []
    for step in steps:
        if step.results:
            if "code" in step.results:
                repro_info.append(f"- {step.title}: Code available for reproduction")

            if "parameters" in step.results:
                repro_info.append(
                    f"- {step.title}: Parameters documented for reproduction"
                )

    repro_info.append("- Full workflow defined in structured plan")
    repro_info.append("- Multi-agent system ensures consistent execution")

    return "\n".join(repro_info)


def _extract_key_results(steps: List[PlanStep]) -> str:
    """
    Extract the most important and valuable results from workflow execution.
    This replaces buried information in executive summary with prominent key findings.

    Args:
        steps: List of completed plan steps

    Returns:
        Formatted string with key results prominently displayed
    """
    key_results = []

    for step in steps:
        if step.results and step.status == "completed":
            step_key_results = _extract_step_key_results(step)
            if step_key_results:
                key_results.append(f"## {step.title}\n\n{step_key_results}")

    return (
        "\n\n".join(key_results)
        if key_results
        else "Key results will be extracted from completed analysis steps."
    )


def _extract_step_key_results(step: PlanStep) -> str:
    """
    Extract key results from a single step based on step type and content.

    Args:
        step: Individual plan step to analyze

    Returns:
        Formatted key results for this step
    """
    if not step.results:
        return ""

    results = []
    summary = step.results.get("summary", "")

    # Database Query Results (UniProt, AlphaFold, etc.)
    if "query" in step.title.lower() or "query" in summary.lower():
        query_results = _extract_database_query_results(step, summary)
        if query_results:
            results.append(query_results)

    # Analysis Results
    if "analysis" in step.title.lower():
        analysis_results = _extract_analysis_results(step, summary)
        if analysis_results:
            results.append(analysis_results)

    # Research/Literature Results
    if step.agent_name == "researcher":
        research_results = _extract_research_results(step, summary)
        if research_results:
            results.append(research_results)

    # Code Execution Results
    if step.agent_name == "coder" and "code" in step.results:
        code_results = _extract_code_execution_results(step, summary)
        if code_results:
            results.append(code_results)

    # Browser/Web Results
    if step.agent_name == "browser":
        web_results = _extract_web_results(step, summary)
        if web_results:
            results.append(web_results)

    return "\n\n".join(results)


def _extract_database_query_results(step: PlanStep, summary: str) -> str:
    """Extract key results from database queries (UniProt, AlphaFold, etc.)"""
    results = []

    # UniProt Results
    if "uniprot" in summary.lower():
        # Extract protein information
        if "protein" in summary.lower():
            # Look for protein name and function information
            lines = summary.split("\n")
            protein_info = []
            for line in lines:
                line = line.strip()
                if any(
                    keyword in line.lower()
                    for keyword in ["function", "name:", "identified as", "acts as"]
                ):
                    protein_info.append(f"• {line}")
                elif any(
                    keyword in line.lower()
                    for keyword in ["location:", "domain", "modification"]
                ):
                    protein_info.append(f"• {line}")

            if protein_info:
                results.append(
                    "**Protein Information:**\n" + "\n".join(protein_info[:5])
                )  # Limit to top 5

    # AlphaFold Results
    if "alphafold" in summary.lower():
        # Extract structure information
        if "structure" in summary.lower() or "entry" in summary.lower():
            # Look for AlphaFold links or structure info
            lines = summary.split("\n")
            structure_info = []
            for line in lines:
                line = line.strip()
                if "alphafold" in line.lower() or "structure" in line.lower():
                    structure_info.append(f"• {line}")
                elif "confidence" in line.lower() or "prediction" in line.lower():
                    structure_info.append(f"• {line}")

            if structure_info:
                results.append(
                    "**Structural Information:**\n" + "\n".join(structure_info[:3])
                )

    # PDB Results
    if "pdb" in summary.lower():
        lines = summary.split("\n")
        pdb_info = []
        for line in lines:
            line = line.strip()
            if any(
                keyword in line.lower()
                for keyword in ["pdb", "crystal", "resolution", "structure"]
            ):
                pdb_info.append(f"• {line}")

        if pdb_info:
            results.append("**PDB Structure Data:**\n" + "\n".join(pdb_info[:3]))

    return "\n\n".join(results)


def _extract_analysis_results(step: PlanStep, summary: str) -> str:
    """Extract key results from analysis steps."""
    results = []

    # Statistical results
    if any(
        term in summary.lower()
        for term in ["significant", "correlation", "p-value", "statistic"]
    ):
        lines = summary.split("\n")
        stats = []
        for line in lines:
            line = line.strip()
            if any(
                term in line.lower()
                for term in ["significant", "correlation", "p-value", "mean", "std"]
            ):
                stats.append(f"• {line}")

        if stats:
            results.append("**Statistical Results:**\n" + "\n".join(stats[:3]))

    # Generated files
    if "output_files" in step.results and step.results["output_files"]:
        files = step.results["output_files"]
        results.append(f"**Generated Files:**\n" + "\n".join([f"• {f}" for f in files]))

    return "\n\n".join(results)


def _extract_research_results(step: PlanStep, summary: str) -> str:
    """Extract key results from research steps."""
    results = []

    # Literature findings
    if any(
        term in summary.lower()
        for term in ["found", "research", "study", "paper", "article"]
    ):
        lines = summary.split("\n")
        research_findings = []
        for line in lines:
            line = line.strip()
            if any(
                term in line.lower()
                for term in ["found", "study shows", "research indicates", "evidence"]
            ):
                research_findings.append(f"• {line}")

        if research_findings:
            results.append(
                "**Research Findings:**\n" + "\n".join(research_findings[:4])
            )

    # References
    if "references" in step.results:
        refs = step.results["references"]
        if isinstance(refs, list) and refs:
            results.append(
                "**Key References:**\n" + "\n".join([f"• {ref}" for ref in refs[:3]])
            )

    return "\n\n".join(results)


def _extract_code_execution_results(step: PlanStep, summary: str) -> str:
    """Extract key results from code execution."""
    results = []

    # Execution summary
    if summary and len(summary.strip()) > 20:
        results.append(f"**Execution Summary:**\n{summary}")

    # Code snippet (if not too long)
    code = step.results.get("code", "")
    if code and len(code) < 500:  # Only show short code snippets
        results.append(f"**Code Executed:**\n```python\n{code}\n```")

    return "\n\n".join(results)


def _extract_web_results(step: PlanStep, summary: str) -> str:
    """Extract key results from web browsing/data extraction."""
    results = []

    # Web content summary
    if summary and len(summary.strip()) > 30:
        # Look for valuable information
        lines = summary.split("\n")
        valuable_info = []
        for line in lines:
            line = line.strip()
            if any(
                keyword in line.lower()
                for keyword in ["found", "retrieved", "extracted", "obtained"]
            ):
                valuable_info.append(f"• {line}")

        if valuable_info:
            results.append("**Web Data Retrieved:**\n" + "\n".join(valuable_info[:4]))
        elif len(summary) < 300:  # Show short summaries directly
            results.append(f"**Retrieved Information:**\n{summary}")

    return "\n\n".join(results)


# Additional helper functions for template population


def _extract_research_questions(state: State) -> str:
    """Extract research questions from goal."""
    goal = state.get("goal", "")
    if "?" in goal:
        return f"- {goal}"
    else:
        return f"- How to achieve: {goal}"


def _extract_hypothesis(state: State) -> str:
    """Extract or generate hypothesis from goal."""
    goal = state.get("goal", "").lower()
    if "hypothesis" in goal:
        return goal
    elif "analysis" in goal:
        return f"Analysis will reveal significant patterns related to: {state.get('goal', 'the research objective')}"
    else:
        return f"Expected outcome: {state.get('goal', 'successful completion of analysis')}"


def _extract_analysis_pipeline(state: State) -> str:
    """Extract analysis pipeline description."""
    plan = state.get("structured_plan")
    if plan and hasattr(plan, "steps"):
        pipeline_steps = []
        for i, step in enumerate(plan.steps, 1):
            if step.agent_name != "reporter":
                pipeline_steps.append(f"{i}. {step.title} (Agent: {step.agent_name})")
        return "\n".join(pipeline_steps)
    return "Multi-agent workflow pipeline with specialized analysis steps."


def _extract_clinical_relevance(
    content_data: Dict[str, str], context: Dict[str, str]
) -> str:
    """Extract clinical relevance based on domain and content."""
    domain = context.get("domain", "")
    subject = context.get("subject", "").lower()

    if "bioinformatics" in domain and any(
        term in subject for term in ["cancer", "disease", "treatment"]
    ):
        return "Analysis provides insights relevant to clinical understanding and potential therapeutic applications."
    elif "protein" in subject:
        return "Protein analysis results may inform drug target identification and therapeutic development."
    else:
        return (
            "Clinical relevance to be determined based on specific analysis outcomes."
        )


def _extract_comparative_analysis(content_data: Dict[str, str]) -> str:
    """Extract comparative analysis information."""
    if "differential" in content_data.get("executive_summary", "").lower():
        return "Comparative analysis performed to identify differential patterns and significant changes."
    return "Comparative analysis context derived from workflow execution."


def _extract_unexpected_findings(content_data: Dict[str, str]) -> str:
    """Extract unexpected findings from analysis."""
    limitations = content_data.get("limitations", "")
    if "unexpected" in limitations.lower() or "surprising" in limitations.lower():
        return limitations
    return "Unexpected findings documented during analysis execution."


def _extract_implications(content_data: Dict[str, str], context: Dict[str, str]) -> str:
    """Extract implications based on results and domain."""
    domain = context.get("domain", "")
    insights = content_data.get("key_insights", "")

    implications = []
    if "bioinformatics" in domain:
        implications.append("- Implications for understanding biological mechanisms")
    if "significant" in insights.lower():
        implications.append(
            "- Results suggest important biological or technical significance"
        )

    implications.append(
        "- Findings contribute to broader understanding of the research domain"
    )
    return "\n".join(implications)


def _extract_data_quality_issues(content_data: Dict[str, str]) -> str:
    """Extract data quality issues from analysis."""
    limitations = content_data.get("limitations", "")
    if "data quality" in limitations.lower() or "missing" in limitations.lower():
        return limitations
    return "Data quality assessed and validated throughout analysis workflow."


def _extract_methodological_constraints(content_data: Dict[str, str]) -> str:
    """Extract methodological constraints."""
    limitations = content_data.get("limitations", "")
    if "method" in limitations.lower() or "approach" in limitations.lower():
        return limitations
    return "Methodological constraints identified and addressed during analysis."


def _extract_recommended_followup(
    content_data: Dict[str, str], context: Dict[str, str]
) -> str:
    """Extract recommended follow-up studies."""
    future_dirs = content_data.get("future_directions", "")
    domain = context.get("domain", "")

    recommendations = []
    if future_dirs and "validate" in future_dirs.lower():
        recommendations.append("- Validation studies recommended")

    if "bioinformatics" in domain:
        recommendations.append("- Experimental validation of computational findings")
        recommendations.append("- Extension to larger datasets or cohorts")

    return (
        "\n".join(recommendations)
        if recommendations
        else "Follow-up studies based on analysis outcomes."
    )


def _extract_method_improvements(content_data: Dict[str, str]) -> str:
    """Extract method improvements suggestions."""
    limitations = content_data.get("limitations", "")
    if limitations:
        return f"Method improvements based on identified limitations: {limitations}"
    return "Continuous improvement of analytical methods based on execution experience."


def _extract_supplementary_data(content_data: Dict[str, str]) -> str:
    """Extract supplementary data information."""
    evidence = content_data.get("supporting_evidence", "")
    if "files" in evidence.lower():
        return evidence
    return "Supplementary data generated during workflow execution."


def _extract_code_snippets(content_data: Dict[str, str]) -> str:
    """Extract code snippets from technical details."""
    tech_details = content_data.get("technical_details", "")
    if "```" in tech_details:
        return tech_details
    return "Code snippets available from computational analysis steps."


def _extract_additional_figures(content_data: Dict[str, str]) -> str:
    """Extract additional figures information."""
    return "Additional figures and visualizations generated during analysis workflow."


def _extract_visualizations(content_data: Dict[str, str]) -> str:
    """Extract visualization information."""
    evidence = content_data.get("supporting_evidence", "")
    if "plot" in evidence.lower() or "figure" in evidence.lower():
        return evidence
    return "Visualizations and plots generated during computational analysis."


def execution_evaluator_node(state: State) -> dict:
    """
    Evaluates the output of the previous step, updates the plan, and returns.
    This node is multimodal and can handle images.
    """
    import base64
    import os

    plan = state["structured_plan"]
    # The step to evaluate is the one *before* the current one
    step_to_evaluate_index = plan.current_step_index
    step_to_evaluate = plan.steps[step_to_evaluate_index]
    response_content = ""

    logger.info(f"Evaluator starting evaluation of step: {step_to_evaluate.title}")

    # Prepare the messages for the evaluator agent
    messages = apply_prompt_template("execution_evaluator", state)

    # Check for image outputs to include in the evaluation
    output_file = step_to_evaluate.results.get("summary")
    if isinstance(output_file, str) and output_file.endswith(".png"):
        try:
            with open(output_file, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
                image_message = {
                    "type": "image_url",
                    "image_url": f"data:image/png;base64,{encoded_image}",
                }
                # Add the image to the content of the last message
                if isinstance(messages[-1].content, list):
                    messages[-1].content.append(image_message)
                else:
                    messages[-1].content = [
                        {"type": "text", "text": messages[-1].content},
                        image_message,
                    ]
                logger.info(
                    f"Successfully encoded and added image {output_file} to the evaluation message."
                )
        except FileNotFoundError:
            logger.warning(
                f"Image file not found at path: {output_file}. Cannot include it in the evaluation."
            )
        except Exception as e:
            logger.error(f"Error encoding image file {output_file}: {e}")

    try:
        # Assuming an execution_evaluator_agent is defined in src/agents/agents.py
        from src.agents.agents import execution_evaluator_agent

        result = execution_evaluator_agent.invoke({"messages": messages})
        response_message = result["messages"][-1]
        response_content = (
            response_message.content
            if hasattr(response_message, "content")
            else str(response_message)
        )

        # Store evaluation results in the step that was evaluated
        step_to_evaluate.results["evaluation"] = response_content
        logger.info(
            f"Evaluator completed evaluation for step: {step_to_evaluate.title}"
        )

        # Check if the evaluation was an approval
        if "approved" in response_content.lower():
            step_to_evaluate.status = "completed"
            logger.info(
                f"✅ Evaluation APPROVED for step {step_to_evaluate.step_index}: {step_to_evaluate.title}"
            )
            # Mark todo as completed
            _update_todo_status(step_to_evaluate, completed=True)
            plan.advance_to_next_step()
        else:
            logger.warning(
                f"❌ Evaluation NOT APPROVED for step {step_to_evaluate.step_index}: {step_to_evaluate.title}"
            )
            logger.debug(f"Evaluation response: {response_content}")
            if step_to_evaluate.retry_count >= 3:  # Retry limit
                step_to_evaluate.status = "failed"
                logger.error(
                    f"💀 Step {step_to_evaluate.step_index} FAILED after 3 retries: {step_to_evaluate.title}"
                )
                plan.advance_to_next_step()
            else:
                step_to_evaluate.status = "pending"  # Re-queue for the same agent
                step_to_evaluate.retry_count += 1
                logger.info(
                    f"🔄 Step {step_to_evaluate.step_index} queued for retry {step_to_evaluate.retry_count}/3: {step_to_evaluate.title}"
                )

    except Exception as e:
        logger.error(f"Evaluator failed for step {step_to_evaluate.title}: {e}")
        step_to_evaluate.results["evaluation_error"] = str(e)
        response_content = f"Error in evaluator: {e}"

    return {
        "structured_plan": plan,
        "messages": [
            HumanMessage(content=response_content, name="execution_evaluator")
        ],
    }
