"""
Human-in-the-loop (HITL) interrupt handlers and reusable patterns.

This module provides the core functionality for implementing human intervention
in the multi-agent workflow, including interrupt triggers, approval flows,
and resumption logic.
"""

import logging
import datetime
from typing import Dict, Any, Optional, List, Tuple
from langchain_core.messages import HumanMessage

from langgraph.types import interrupt, Command
from .types import State, PlanStep
from ..config.hitl_config import (
    get_hitl_config, 
    HITLMode, 
    InterruptTrigger, 
    RiskLevel
)

logger = logging.getLogger(__name__)


class HITLInterruptHandler:
    """Core handler for human-in-the-loop interrupts."""
    
    def __init__(self):
        self.config = get_hitl_config()
    
    def should_interrupt_for_step(
        self, 
        state: State, 
        current_step: PlanStep,
        trigger: InterruptTrigger
    ) -> bool:
        """
        Determine if the current step should trigger human intervention.
        
        Args:
            state: Current workflow state
            current_step: The step being evaluated
            trigger: Type of interrupt trigger
            
        Returns:
            <PERSON><PERSON>an indicating whether to interrupt
        """
        # Check if HITL is enabled for this workflow (handle both dict and object)
        hitl_mode = self._get_state_value(state, "hitl_mode") or self.config.mode.value
        if hitl_mode == HITLMode.FULLY_AUTONOMOUS.value:
            return False
        
        # Create step context for risk assessment
        step_context = {
            "step_title": current_step.title,
            "step_description": current_step.description,
            "agent_name": current_step.agent_name,
            "step_index": current_step.step_index,
            "requires_evaluation": current_step.requires_evaluation,
            "retry_count": current_step.retry_count
        }
        
        return self.config.should_interrupt(
            trigger=trigger,
            agent_name=current_step.agent_name,
            step_context=step_context,
            mode=HITLMode(hitl_mode) if hitl_mode else None
        )
    
    def should_interrupt_for_tool(
        self,
        state: State,
        tool_name: str,
        tool_args: Dict[str, Any],
        agent_name: str = None
    ) -> bool:
        """
        Determine if a tool execution should trigger human intervention.
        
        Args:
            state: Current workflow state
            tool_name: Name of the tool to be executed
            tool_args: Arguments for the tool
            agent_name: Name of the agent using the tool
            
        Returns:
            Boolean indicating whether to interrupt
        """
        hitl_mode = self._get_state_value(state, "hitl_mode") or self.config.mode.value
        if hitl_mode == HITLMode.FULLY_AUTONOMOUS.value:
            return False
        
        # Create tool context for risk assessment
        tool_context = {
            "tool_args": tool_args,
            "execution_context": "tool_execution"
        }
        
        return self.config.should_interrupt(
            trigger=InterruptTrigger.TOOL_EXECUTION,
            agent_name=agent_name,
            tool_name=tool_name,
            step_context=tool_context,
            mode=HITLMode(hitl_mode) if hitl_mode else None
        )
    
    def create_interrupt_for_step(
        self,
        state: State,
        current_step: PlanStep,
        trigger: InterruptTrigger,
        action_description: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create an interrupt payload for step-level intervention.
        
        Args:
            state: Current workflow state
            current_step: The step requiring intervention
            trigger: Type of interrupt trigger
            action_description: Description of the action requiring approval
            additional_context: Additional context to include
            
        Returns:
            Interrupt payload dictionary
        """
        # Build comprehensive step context
        step_context = {
            "step_id": current_step.step_id,
            "step_index": current_step.step_index,
            "step_title": current_step.title,
            "step_description": current_step.description,
            "agent_name": current_step.agent_name,
            "status": current_step.status,
            "retry_count": current_step.retry_count,
            "requires_evaluation": current_step.requires_evaluation
        }
        
        if additional_context:
            step_context.update(additional_context)
        
        # Create interrupt payload
        payload = self.config.create_interrupt_payload(
            trigger=trigger,
            action_description=action_description,
            agent_name=current_step.agent_name,
            step_context=step_context
        )
        
        # Add workflow-specific context
        structured_plan = self._get_state_value(state, "structured_plan")
        if structured_plan:
            payload["plan_context"] = {
                "plan_id": structured_plan.plan_id,
                "plan_title": structured_plan.title,
                "total_steps": len(structured_plan.steps),
                "current_step_index": structured_plan.current_step_index,
                "plan_status": structured_plan.status
            }
        
        payload["timestamp"] = datetime.datetime.now().isoformat()
        
        return payload
    
    def create_interrupt_for_tool(
        self,
        state: State,
        tool_name: str,
        tool_args: Dict[str, Any],
        agent_name: str = None,
        action_description: str = None
    ) -> Dict[str, Any]:
        """
        Create an interrupt payload for tool execution intervention.
        
        Args:
            state: Current workflow state
            tool_name: Name of the tool to be executed
            tool_args: Arguments for the tool
            agent_name: Name of the agent using the tool
            action_description: Description of the tool action
            
        Returns:
            Interrupt payload dictionary
        """
        if not action_description:
            action_description = f"Execute {tool_name} tool with provided arguments"
        
        # Build tool context
        tool_context = {
            "tool_name": tool_name,
            "tool_args": tool_args,
            "execution_context": "tool_execution"
        }
        
        payload = self.config.create_interrupt_payload(
            trigger=InterruptTrigger.TOOL_EXECUTION,
            action_description=action_description,
            agent_name=agent_name,
            tool_name=tool_name,
            step_context=tool_context
        )
        
        payload["timestamp"] = datetime.datetime.now().isoformat()
        
        return payload
    
    def _get_state_value(self, state, key: str):
        """Helper method to get state value from both dict and object."""
        if hasattr(state, key):
            return getattr(state, key)
        elif isinstance(state, dict):
            return state.get(key)
        else:
            return None


def human_approval_node(state: State) -> Command:
    """
    Generic human approval node for workflow intervention.
    
    This node can be used to pause the workflow and request human input
    at any point in the execution flow.
    
    Args:
        state: Current workflow state
        
    Returns:
        Command object with routing and state updates
    """
    logger.info("Human approval node triggered")
    
    # Check if we have interrupt context
    if not state.interrupt_context:
        logger.warning("Human approval node called without interrupt context")
        return Command(goto="supervisor")
    
    # Create interrupt payload if not already created
    interrupt_payload = state.interrupt_context
    
    # Update state to indicate we're waiting for human input
    state_update = {
        "awaiting_human_input": True,
        "interrupt_context": interrupt_payload
    }
    
    # Trigger the interrupt
    logger.info(f"Triggering interrupt for: {interrupt_payload.get('action_description', 'unknown action')}")
    human_response = interrupt(interrupt_payload)
    
    # Process human response
    response_data = _process_human_response(human_response, interrupt_payload)
    
    # Update state with human feedback
    state_update.update({
        "awaiting_human_input": False,
        "human_feedback": response_data,
        "interrupt_history": state.interrupt_history + [{
            "timestamp": datetime.datetime.now().isoformat(),
            "interrupt_payload": interrupt_payload,
            "human_response": response_data
        }]
    })
    
    # Determine next action based on human response
    next_node = _determine_next_action(response_data, interrupt_payload)
    
    logger.info(f"Human response processed, routing to: {next_node}")
    
    return Command(update=state_update, goto=next_node)


def tool_approval_interrupt(
    state: State,
    tool_name: str,
    tool_args: Dict[str, Any],
    agent_name: str = None
) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Interrupt for tool execution approval.
    
    This function can be called before tool execution to request human approval.
    It returns immediately if no approval is needed, or triggers an interrupt.
    
    Args:
        state: Current workflow state
        tool_name: Name of the tool to be executed
        tool_args: Arguments for the tool
        agent_name: Name of the agent using the tool
        
    Returns:
        Tuple of (should_proceed, modified_args)
    """
    handler = HITLInterruptHandler()
    
    # Check if interrupt is needed
    if not handler.should_interrupt_for_tool(state, tool_name, tool_args, agent_name):
        return True, None  # Proceed without interruption
    
    # Create interrupt payload
    interrupt_payload = handler.create_interrupt_for_tool(
        state, tool_name, tool_args, agent_name
    )
    
    logger.info(f"Tool approval interrupt triggered for: {tool_name}")
    
    # Trigger interrupt
    human_response = interrupt(interrupt_payload)
    
    # Process response
    response_data = _process_human_response(human_response, interrupt_payload)
    
    # Update interrupt history
    state.interrupt_history.append({
        "timestamp": datetime.datetime.now().isoformat(),
        "interrupt_payload": interrupt_payload,
        "human_response": response_data
    })
    
    # Determine if tool should proceed
    if response_data.get("action") == "approve":
        return True, response_data.get("modified_args")
    elif response_data.get("action") == "modify":
        return True, response_data.get("modified_args", tool_args)
    else:  # reject or other
        return False, None


def step_approval_interrupt(
    state: State,
    current_step: PlanStep,
    action_description: str,
    additional_context: Dict[str, Any] = None
) -> str:
    """
    Interrupt for step-level approval.
    
    This function handles step-level human intervention, allowing humans
    to approve, modify, or reject the execution of a workflow step.
    
    Args:
        state: Current workflow state
        current_step: The step requiring approval
        action_description: Description of the action
        additional_context: Additional context to include
        
    Returns:
        String indicating the decision: "approve", "modify", "reject", "retry"
    """
    handler = HITLInterruptHandler()
    
    # Check if interrupt is needed
    if not handler.should_interrupt_for_step(state, current_step, InterruptTrigger.STATE_TRANSITION):
        return "approve"  # Proceed without interruption
    
    # Create interrupt payload
    interrupt_payload = handler.create_interrupt_for_step(
        state, current_step, InterruptTrigger.STATE_TRANSITION, 
        action_description, additional_context
    )
    
    logger.info(f"Step approval interrupt triggered for: {current_step.title}")
    
    # Trigger interrupt
    human_response = interrupt(interrupt_payload)
    
    # Process response
    response_data = _process_human_response(human_response, interrupt_payload)
    
    # Update state
    state.interrupt_history.append({
        "timestamp": datetime.datetime.now().isoformat(),
        "interrupt_payload": interrupt_payload,
        "human_response": response_data
    })
    
    return response_data.get("action", "approve")


def _process_human_response(
    human_response: Any, 
    interrupt_payload: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Process and validate human response to an interrupt.
    
    Args:
        human_response: Raw human response from interrupt
        interrupt_payload: Original interrupt payload
        
    Returns:
        Processed response data
    """
    try:
        # Handle different response formats
        if isinstance(human_response, dict):
            response_data = human_response
        elif isinstance(human_response, str):
            # Try to parse as simple action
            action = human_response.lower().strip()
            if action in ["approve", "reject", "modify"]:
                response_data = {"action": action}
            else:
                # Treat as feedback text
                response_data = {"action": "feedback", "text": human_response}
        else:
            # Default handling
            response_data = {"action": "approve", "raw_response": str(human_response)}
        
        # Ensure required fields
        if "action" not in response_data:
            response_data["action"] = "approve"
        
        if "timestamp" not in response_data:
            response_data["timestamp"] = datetime.datetime.now().isoformat()
        
        return response_data
        
    except Exception as e:
        logger.error(f"Error processing human response: {e}")
        return {
            "action": "approve",  # Default to approve on error
            "error": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }


def _determine_next_action(
    response_data: Dict[str, Any], 
    interrupt_payload: Dict[str, Any]
) -> str:
    """
    Determine the next workflow action based on human response.
    
    Args:
        response_data: Processed human response
        interrupt_payload: Original interrupt payload
        
    Returns:
        Name of the next node to route to
    """
    action = response_data.get("action", "approve").lower()
    
    if action == "approve":
        return "supervisor"  # Continue with normal workflow
    elif action == "reject":
        return "reporter"  # Skip to final report
    elif action == "modify":
        return "supervisor"  # Continue with modifications
    elif action == "retry":
        # Return to the same agent that triggered the interrupt
        agent_name = interrupt_payload.get("agent_name")
        return agent_name if agent_name else "supervisor"
    else:
        # Default fallback
        return "supervisor"


# Convenience functions for common HITL patterns

def create_manual_approval_step(
    step_title: str,
    step_description: str,
    action_description: str,
    agent_name: str = "supervisor"
) -> Dict[str, Any]:
    """
    Create a manual approval step configuration.
    
    Args:
        step_title: Title of the approval step
        step_description: Description of what needs approval
        action_description: Description of the action requiring approval
        agent_name: Agent responsible for the step
        
    Returns:
        Step configuration dictionary
    """
    return {
        "title": step_title,
        "description": step_description,
        "agent_name": agent_name,
        "requires_evaluation": False,
        "hitl_config": {
            "requires_approval": True,
            "action_description": action_description,
            "trigger": InterruptTrigger.STATE_TRANSITION.value
        }
    }


def should_auto_approve(
    risk_level: RiskLevel,
    confidence_score: float = 1.0,
    hitl_mode: HITLMode = None
) -> bool:
    """
    Determine if an action should be automatically approved.
    
    Args:
        risk_level: Assessed risk level of the action
        confidence_score: Confidence in the action (0.0 to 1.0)
        hitl_mode: HITL mode override
        
    Returns:
        Boolean indicating if auto-approval is appropriate
    """
    if hitl_mode is None:
        hitl_mode = get_hitl_config().mode
    
    if hitl_mode == HITLMode.FULLY_AUTONOMOUS:
        return True
    elif hitl_mode == HITLMode.FULL_MANUAL:
        return False
    else:  # SEMI_AUTONOMOUS
        # Auto-approve low-risk actions with high confidence
        if risk_level == RiskLevel.LOW and confidence_score >= 0.8:
            return True
        # Auto-approve medium-risk actions with very high confidence
        elif risk_level == RiskLevel.MEDIUM and confidence_score >= 0.95:
            return True
        else:
            return False