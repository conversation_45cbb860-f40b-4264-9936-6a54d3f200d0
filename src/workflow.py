import logging
from src.config import TEAM_MEMBERS
from src.graph.builder import build_graph, create_checkpointer

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Default level is INFO
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

# Suppress specific warnings
logging.getLogger("langchain_google_genai._function_utils").setLevel(logging.ERROR)


def enable_debug_logging():
    """Enable debug level logging for more detailed execution information."""
    logging.getLogger("src").setLevel(logging.DEBUG)


logger = logging.getLogger(__name__)

# Create the graph
graph = None


async def get_workflow_graph():
    """Get or create the workflow graph with a checkpointer."""
    global graph
    if graph is None:
        checkpointer = await create_checkpointer()
        graph = build_graph(checkpointer)
    return graph


async def run_agent_workflow(user_input: str, thread_id: str, debug: bool = False):
    """Run the agent workflow with the given user input."""
    graph = await get_workflow_graph()
    if not user_input:
        raise ValueError("Input could not be empty")

    if debug:
        enable_debug_logging()

    logger.info(
        f"Starting workflow with user input: {user_input}, thread_id: {thread_id}"
    )

    config = {
        "configurable": {
            "thread_id": thread_id,
        },
        "recursion_limit": 5,
    }

    try:
        # Stream events from the graph
        async for event in graph.astream(
            {
                "goal": user_input,
                "messages": [("user", user_input)],
            },
            config,
        ):
            yield event
    except Exception as e:
        logger.error(f"Workflow failed with error: {e}")
        yield {"error": str(e)}

    logger.info("Workflow completed successfully")


if __name__ == "__main__":
    import asyncio

    async def main():
        # This is for generating the graph image, so no checkpointer is needed
        graph_for_viz = build_graph()
        with open("assets/agent_graph.png", "wb") as f:
            f.write(graph_for_viz.get_graph().draw_mermaid_png())

    asyncio.run(main())
