# Multi-Agent System Persistence Implementation

This document describes the persistence implementation for the multi-agent LangGraph system using SQLite checkpointer.

## Overview

The system now supports conversation persistence using LangGraph's `AsyncSqliteSaver` checkpointer, allowing conversations to be resumed across requests and sessions.

## Key Features

- **Thread-based Conversations**: Each conversation gets a unique `thread_id` that persists all messages, tool calls, and agent state
- **SQLite Storage**: Conversation data is stored in `/app/data/checkpoints.db`
- **Fallback Strategy**: Falls back to `MemorySaver` (in-memory) if SQLite setup fails
- **Conversation Management**: APIs to retrieve, resume, and delete conversations

## Implementation Details

### Core Components

1. **Checkpointer Setup** (`src/graph/builder.py`)
   - `create_checkpointer()`: Creates AsyncSqliteSaver with fallback to MemorySaver
   - Database path: `/app/data/checkpoints.db`
   - Auto-creates directory structure if needed

2. **Graph Integration** (`src/service/workflow_service.py`)
   - `initialize_graph()`: Sets up graph with persistence
   - `run_agent_workflow()`: Accepts `thread_id` for conversation continuity
   - Global graph and checkpointer instances for efficiency

3. **API Integration** (`src/api/app.py`)
   - `RunAgentInput` model includes `thread_id`, `deep_thinking_mode`, `search_before_planning`
   - Automatic graph initialization on first request
   - Thread ID passed to workflow service

4. **Persistence Service** (`src/service/persistence_service.py`)
   - Utilities for conversation state management
   - History retrieval and conversation deletion
   - Thread listing and resumption helpers

### Dependencies Added

```toml
"langgraph-checkpoint-sqlite>=2.0.0"
"aiosqlite>=0.20.0"
```

## Usage Examples

### Starting a New Conversation

```python
# API Request
{
    "messages": [{"role": "user", "content": "Hello, I need help with a project"}],
    "thread_id": "conversation-123",  # Optional - will be generated if not provided
    "deep_thinking_mode": false,
    "search_before_planning": false
}
```

### Resuming a Conversation

```python
# Use the same thread_id to continue the conversation
{
    "messages": [{"role": "user", "content": "Can you continue with the previous task?"}],
    "thread_id": "conversation-123",  # Same thread ID
    "deep_thinking_mode": false,
    "search_before_planning": false
}
```

### Retrieving Conversation State

```python
from src.service.persistence_service import get_conversation_state, get_conversation_history

# Get current state
state = await get_conversation_state("conversation-123")

# Get conversation history
history = await get_conversation_history("conversation-123", limit=10)
```

### Deleting a Conversation

```python
from src.service.persistence_service import delete_conversation

success = await delete_conversation("conversation-123")
```

## Configuration

### Environment Variables

- `SQLITE_DB_PATH`: Custom path for SQLite database (default: `/app/data/checkpoints.db`)

### Database Structure

The SQLite database automatically creates the necessary tables for:
- Checkpoint storage
- Thread management
- State serialization
- Metadata tracking

## Error Handling

1. **SQLite Setup Failure**: Automatically falls back to `MemorySaver`
2. **Database Connection Issues**: Logged with appropriate error messages
3. **Thread Not Found**: Returns None/empty results gracefully
4. **Serialization Errors**: Handled by LangGraph's built-in error handling

## Performance Considerations

- **Connection Pooling**: Uses `aiosqlite` for async database operations
- **Global Instances**: Graph and checkpointer are created once and reused
- **Lazy Initialization**: Components are created only when needed
- **Memory Fallback**: Ensures system continues working even if persistence fails

## Monitoring and Logging

- All persistence operations are logged with appropriate levels
- Database setup success/failure is logged
- Thread operations include thread ID in log messages
- Error conditions are logged with full context

## Migration from Non-Persistent System

Existing conversations will start fresh with the new persistence system. To migrate:

1. Deploy the updated system
2. Existing in-memory conversations will be lost (expected)
3. New conversations will automatically use persistence
4. No manual migration steps required

## Troubleshooting

### Common Issues

1. **Database Permission Errors**
   - Ensure `/app/data/` directory is writable
   - Check file permissions on `checkpoints.db`

2. **Memory Fallback Active**
   - Check logs for SQLite setup errors
   - Verify `aiosqlite` dependency is installed
   - Ensure database path is accessible

3. **Thread Not Found**
   - Verify thread ID is correct
   - Check if conversation was deleted
   - Ensure database file exists and is readable

### Debug Mode

Enable debug logging to see detailed persistence operations:

```python
import logging
logging.getLogger('src.graph.builder').setLevel(logging.DEBUG)
logging.getLogger('src.service.workflow_service').setLevel(logging.DEBUG)
logging.getLogger('src.service.persistence_service').setLevel(logging.DEBUG)
```

## Future Enhancements

- **Conversation Metadata**: Add conversation titles, tags, and timestamps
- **Export/Import**: Conversation backup and restore functionality
- **Cleanup Jobs**: Automatic deletion of old conversations
- **Analytics**: Conversation usage statistics and insights
- **Multi-User Support**: User-scoped conversation isolation