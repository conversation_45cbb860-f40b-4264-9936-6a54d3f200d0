------------------------------------------------------------------------
r1605 | lh3 | 2010-12-29 20:20:20 -0500 (Wed, 29 Dec 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.9rc1-2 (r1605)
 * fixed a typo/bug in bwasw

------------------------------------------------------------------------
r1587 | lh3 | 2010-12-21 18:48:30 -0500 (Tue, 21 Dec 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

a typo in the manual

------------------------------------------------------------------------
r1586 | lh3 | 2010-12-21 18:47:48 -0500 (Tue, 21 Dec 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/utils.c
   M /branches/prog/bwa/utils.h

 * bwa-0.5.9rc1-1 (r1586)
 * a few patches by John

------------------------------------------------------------------------
r1562 | lh3 | 2010-12-10 01:02:06 -0500 (Fri, 10 Dec 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c

documentation on specifying @RG

------------------------------------------------------------------------
r1561 | lh3 | 2010-12-10 00:45:40 -0500 (Fri, 10 Dec 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.5.9rc1 (r1561)

------------------------------------------------------------------------
r1560 | lh3 | 2010-12-10 00:29:08 -0500 (Fri, 10 Dec 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/main.c

 * fixed a small memory leak caused by the BAM reader
 * fixed a memory violation, also in the BAM reader

------------------------------------------------------------------------
r1559 | lh3 | 2010-12-10 00:10:48 -0500 (Fri, 10 Dec 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/Makefile

change Makefile gcc options

------------------------------------------------------------------------
r1558 | lh3 | 2010-12-10 00:09:22 -0500 (Fri, 10 Dec 2010) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.8-6 (r1557)
 * added a little more comments to BWA-SW
 * randomly choosing a mapping if there are more than one

------------------------------------------------------------------------
r1557 | lh3 | 2010-12-09 21:58:00 -0500 (Thu, 09 Dec 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2_aux.c

sometimes unmapped reads may not be printed...

------------------------------------------------------------------------
r1556 | lh3 | 2010-12-09 21:50:26 -0500 (Thu, 09 Dec 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2_aux.c

print unmapped reads

------------------------------------------------------------------------
r1555 | lh3 | 2010-12-09 21:17:20 -0500 (Thu, 09 Dec 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.8-5 (r1555)
 * BAM input documentation

------------------------------------------------------------------------
r1544 | lh3 | 2010-11-23 11:01:41 -0500 (Tue, 23 Nov 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.8-4 (r1544)
 * supporting adding RG tags and RG lines

------------------------------------------------------------------------
r1543 | lh3 | 2010-11-23 00:16:40 -0500 (Tue, 23 Nov 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.8-3 (r1543)
 * fixed a memory leak

------------------------------------------------------------------------
r1542 | lh3 | 2010-11-22 23:50:56 -0500 (Mon, 22 Nov 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.8-2 (r1542)
 * fixed a long existing bug in random placement of reads

------------------------------------------------------------------------
r1541 | lh3 | 2010-11-22 23:27:29 -0500 (Mon, 22 Nov 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   A /branches/prog/bwa/bamlite.c
   A /branches/prog/bwa/bamlite.h
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

preliminary BAM input support

------------------------------------------------------------------------
r1537 | lh3 | 2010-10-16 23:46:20 -0400 (Sat, 16 Oct 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwa.1

change version number and ChangeLog

------------------------------------------------------------------------
r1536 | lh3 | 2010-10-16 23:35:10 -0400 (Sat, 16 Oct 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c

 * fixed a bug in the scoring matrix
 * release bwa-0.5.8c (r1536)

------------------------------------------------------------------------
r1451 | lh3 | 2010-06-15 09:43:52 -0400 (Tue, 15 Jun 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

version change

------------------------------------------------------------------------
r1450 | lh3 | 2010-06-15 09:42:21 -0400 (Tue, 15 Jun 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c

 * bwa-0.5.8b (r1450)
 * fixed a bug in scoring matrix

------------------------------------------------------------------------
r1445 | lh3 | 2010-06-11 08:58:33 -0400 (Fri, 11 Jun 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c

fixed a serious bug

------------------------------------------------------------------------
r1442 | lh3 | 2010-06-08 10:22:14 -0400 (Tue, 08 Jun 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.5.8 (r1442)

------------------------------------------------------------------------
r1440 | lh3 | 2010-05-19 13:43:50 -0400 (Wed, 19 May 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-r1440
 * sorry, forget to remove a debugging line

------------------------------------------------------------------------
r1439 | lh3 | 2010-05-19 13:43:08 -0400 (Wed, 19 May 2010) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-r1439
 * fixed a bug in bwasw caused by a recent modification
 * throwing insane insert size when estimating isize

------------------------------------------------------------------------
r1425 | lh3 | 2010-04-29 15:15:23 -0400 (Thu, 29 Apr 2010) | 10 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.7-7 (r1425)
 * fixed a minor bug in bwasw command-line parsing
 * When band-width is not large enough, bwasw may find two highly
   overlapping but not completely overlapping alignments. The old
   version will filter out one of them, which leads to false
   negatives. The current outputs both. This solution is obviously not
   ideal. The ideal one would be to increase the band-width and redo the
   alignment.


------------------------------------------------------------------------
r1399 | lh3 | 2010-04-16 09:20:49 -0400 (Fri, 16 Apr 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.7-6 (r1399)
 * fixed a typo/bug (by Vaughn Iverson)

------------------------------------------------------------------------
r1329 | lh3 | 2010-03-19 23:32:46 -0400 (Fri, 19 Mar 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

small correction

------------------------------------------------------------------------
r1328 | lh3 | 2010-03-19 23:28:44 -0400 (Fri, 19 Mar 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.7-4 (r1328)
 * automatically adjust ap_prior based on alignment

------------------------------------------------------------------------
r1327 | lh3 | 2010-03-19 23:02:40 -0400 (Fri, 19 Mar 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.5.7-3 (r1327)
 * evaluate hits obtained from SW alignment in a more proper way.

------------------------------------------------------------------------
r1320 | lh3 | 2010-03-17 15:13:22 -0400 (Wed, 17 Mar 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwape.c

fixed a potential out-of-boundary error. Need more testing.

------------------------------------------------------------------------
r1319 | lh3 | 2010-03-14 22:44:46 -0400 (Sun, 14 Mar 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwape.c

insert size is `weird' if the 3rd quatile larger than 100,000bp

------------------------------------------------------------------------
r1318 | lh3 | 2010-03-14 22:37:35 -0400 (Sun, 14 Mar 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.5.7-2 (r1318)
 * in sampe, allow to disable insert size estimate

------------------------------------------------------------------------
r1317 | lh3 | 2010-03-14 22:14:14 -0400 (Sun, 14 Mar 2010) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/solid2fastq.pl

 * bwa-0.5.7-1 (r1317)
 * fixed a potential bug in solid2fastq.pl
 * fixed a bug in calculating mapping quality (by Rodrigo Goya)
 * fixed a very rare bug (if ever occur) about pairing

------------------------------------------------------------------------
r1310 | lh3 | 2010-03-01 10:35:45 -0500 (Mon, 01 Mar 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.5.7

------------------------------------------------------------------------
r1309 | lh3 | 2010-02-26 21:42:22 -0500 (Fri, 26 Feb 2010) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.6-2 (r1309)
 * fixed an unfixed bug (by Carol Scott)
 * fixed some tiny formatting

------------------------------------------------------------------------
r1305 | lh3 | 2010-02-25 13:47:58 -0500 (Thu, 25 Feb 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.6-1 (r1304)
 * optionally write output to a file (by Tim Fennel)

------------------------------------------------------------------------
r1303 | lh3 | 2010-02-10 23:43:48 -0500 (Wed, 10 Feb 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

Release bwa-0.5.6

------------------------------------------------------------------------
r1302 | lh3 | 2010-02-10 11:11:49 -0500 (Wed, 10 Feb 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-10 (r1302)
 * improve max insert size estimate (method suggested by Gerton Lunter)

------------------------------------------------------------------------
r1301 | lh3 | 2010-02-09 16:15:28 -0500 (Tue, 09 Feb 2010) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-9 (r1301)
 * improve mapping quality calculation for abnomalous pairs
 * fixed a bug in multiple hits
 * SOLiD multiple hits should work now

------------------------------------------------------------------------
r1300 | lh3 | 2010-02-09 12:50:02 -0500 (Tue, 09 Feb 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-8 (r1300)
 * output kurtosis

------------------------------------------------------------------------
r1299 | lh3 | 2010-02-09 12:33:34 -0500 (Tue, 09 Feb 2010) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-7 (r1299)
 * calculate skewness in sampe
 * increase min_len in SW to 20
 * perform more SW to fix discordant pairs

------------------------------------------------------------------------
r1298 | lh3 | 2010-02-08 12:40:31 -0500 (Mon, 08 Feb 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/cs2nt.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.5.5-6 (r1297)
 * prepare to replace all 16-bit CIGAR (patches by Rodrigo Goya)

------------------------------------------------------------------------
r1297 | lh3 | 2010-02-05 22:26:11 -0500 (Fri, 05 Feb 2010) | 2 lines
Changed paths:
   M /branches/prog/bwa/solid2fastq.pl

the old fix seems not working!

------------------------------------------------------------------------
r1296 | lh3 | 2010-02-05 21:51:03 -0500 (Fri, 05 Feb 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-5 (r1296)
 * fixed a minor issue that the lower bound of insert size is not correctly set.

------------------------------------------------------------------------
r1295 | lh3 | 2010-02-05 21:01:10 -0500 (Fri, 05 Feb 2010) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-4 (r1295)
 * fixed a memory leak
 * change the behaviour of -n (samse and sampe)
 * change the default of -n

------------------------------------------------------------------------
r1294 | lh3 | 2010-02-05 17:24:06 -0500 (Fri, 05 Feb 2010) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-3 (r1294)
 * improved multi-hit report

------------------------------------------------------------------------
r1293 | lh3 | 2010-02-05 12:57:38 -0500 (Fri, 05 Feb 2010) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/cs2nt.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/solid2fastq.pl

 * bwa-0.5.5-2 (r1293)
 * bugfix: truncated quality string
 * bugfix: quality -1 in solid->fastq conversion
 * bugfix: color reads on the reverse strand is not complemented

------------------------------------------------------------------------
r1279 | lh3 | 2009-11-23 22:42:34 -0500 (Mon, 23 Nov 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bntseq.h
   M /branches/prog/bwa/bwase.c
   A /branches/prog/bwa/bwase.h
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.5-1 (r1279)
 * incorporate changes from Matt Hanna for Java bindings.

------------------------------------------------------------------------
r1275 | lh3 | 2009-11-10 22:13:10 -0500 (Tue, 10 Nov 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog

update ChangeLog

------------------------------------------------------------------------
r1273 | lh3 | 2009-11-10 22:08:16 -0500 (Tue, 10 Nov 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c
   A /branches/prog/bwa/qualfa2fq.pl

Release bwa-0.5.5 (r1273)

------------------------------------------------------------------------
r1272 | lh3 | 2009-11-10 22:02:50 -0500 (Tue, 10 Nov 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.4-3 (r1272)
 * fixed another typo which may lead to incorrect single-end mapping quality

------------------------------------------------------------------------
r1271 | lh3 | 2009-11-10 21:59:47 -0500 (Tue, 10 Nov 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.4-2 (r1271)
 * fixed a serious typo/bug which does not hurt if we allow one gap open
   and work with <200bp reads, but causes segfault for long reads.

------------------------------------------------------------------------
r1270 | lh3 | 2009-11-09 23:12:42 -0500 (Mon, 09 Nov 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/cs2nt.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.4-1 (r1270)
 * fixed a bug in color alignment

------------------------------------------------------------------------
r1245 | lh3 | 2009-10-09 07:42:52 -0400 (Fri, 09 Oct 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/main.c

Release bwa-0.5.4

------------------------------------------------------------------------
r1244 | lh3 | 2009-10-09 05:53:52 -0400 (Fri, 09 Oct 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c

 * bwa-0.5.3-4 (r1244)
 * output the clipped length in XC:i: tag
 * skip mate alignment when stdaln is buggy
 * fixed a bug in NM:i: tag

------------------------------------------------------------------------
r1243 | lh3 | 2009-10-07 08:15:04 -0400 (Wed, 07 Oct 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.3-3 (r1243)
 * sampe: fixed a bug when a read sequence is identical its reverse complement.

------------------------------------------------------------------------
r1242 | lh3 | 2009-10-07 07:49:13 -0400 (Wed, 07 Oct 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.5.3-2 (r1242)
 * sampe: optionall preload the full index into memory
 * aln: change the default seed length to 32bp

------------------------------------------------------------------------
r1238 | lh3 | 2009-09-26 18:38:15 -0400 (Sat, 26 Sep 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/khash.h

Improve portability of khash.h

------------------------------------------------------------------------
r1228 | lh3 | 2009-09-15 09:20:22 -0400 (Tue, 15 Sep 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/main.c

fixed a typo

------------------------------------------------------------------------
r1227 | lh3 | 2009-09-15 09:19:35 -0400 (Tue, 15 Sep 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.3-1 (r1226)
 * in dBWT-SW, optionall use hard clipping instead of soft clipping

------------------------------------------------------------------------
r1225 | lh3 | 2009-09-15 08:32:30 -0400 (Tue, 15 Sep 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

Release bwa-0.5.3 (r1225)

------------------------------------------------------------------------
r1223 | lh3 | 2009-09-13 07:30:41 -0400 (Sun, 13 Sep 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.5.2

------------------------------------------------------------------------
r1222 | lh3 | 2009-09-11 09:11:39 -0400 (Fri, 11 Sep 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.1-5 (r1222)
 * fixed a typo. No real change

------------------------------------------------------------------------
r1221 | lh3 | 2009-09-11 09:09:44 -0400 (Fri, 11 Sep 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.5.1-4 (r1221)
 * trim reads before alignment

------------------------------------------------------------------------
r1216 | lh3 | 2009-09-08 17:50:15 -0400 (Tue, 08 Sep 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.5.1-3 (r1216)
 * fixed a bug about NM tags for gapped alignment
 * print SAM header

------------------------------------------------------------------------
r1215 | lh3 | 2009-09-08 17:14:42 -0400 (Tue, 08 Sep 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.1-2 (r1215)
 * fixed a bug when read lengths vary (by John Marshall)

------------------------------------------------------------------------
r1213 | lh3 | 2009-09-06 18:58:15 -0400 (Sun, 06 Sep 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.1-1 (r1213)
 * change default -T to 30

------------------------------------------------------------------------
r1209 | lh3 | 2009-09-02 06:06:02 -0400 (Wed, 02 Sep 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.5.1

------------------------------------------------------------------------
r1208 | lh3 | 2009-09-02 05:56:33 -0400 (Wed, 02 Sep 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog

 * ChangeLog

------------------------------------------------------------------------
r1206 | lh3 | 2009-08-30 18:27:30 -0400 (Sun, 30 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.0-6 (r1206)
 * fixed two bugs caused by previous modification

------------------------------------------------------------------------
r1205 | lh3 | 2009-08-30 17:28:36 -0400 (Sun, 30 Aug 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.0-4 (r1205)
 * reduce false coordinates and CIGAR when a query bridges two reference
   sequences, although some very rare cases may fail bwa.

------------------------------------------------------------------------
r1204 | lh3 | 2009-08-30 06:06:16 -0400 (Sun, 30 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.0-3 (r1204)
 * choose one repetitive hit to extend

------------------------------------------------------------------------
r1203 | lh3 | 2009-08-29 18:11:51 -0400 (Sat, 29 Aug 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.0-2 (r1203)
 * dBWT-SW: change a parameter in calculating mapping quality
 * fixed a bug in samse

------------------------------------------------------------------------
r1202 | lh3 | 2009-08-28 19:48:41 -0400 (Fri, 28 Aug 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.5.0-1 (r1202)
 * change default band width to 50
 * improve mapping quality a bit

------------------------------------------------------------------------
r1200 | lh3 | 2009-08-20 06:21:24 -0400 (Thu, 20 Aug 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.5.0 (r1200)

------------------------------------------------------------------------
r1199 | lh3 | 2009-08-20 04:49:15 -0400 (Thu, 20 Aug 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwa.1

Updated ChangeLog and the manual

------------------------------------------------------------------------
r1198 | lh3 | 2009-08-19 11:09:15 -0400 (Wed, 19 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-36 (r1198)
 * simplify duphits removal. The accuracy is changed a tiny bit, sometimes better, sometimes worse.

------------------------------------------------------------------------
r1197 | lh3 | 2009-08-19 08:15:05 -0400 (Wed, 19 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2_aux.c
   A /branches/prog/bwa/bwtsw2_chain.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-35 (r1197)
 * further heuristic acceleration for long queries

------------------------------------------------------------------------
r1196 | lh3 | 2009-08-18 06:54:03 -0400 (Tue, 18 Aug 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-34 (r1196)
 * updated the manual page
 * output base quality if the input is fastq

------------------------------------------------------------------------
r1195 | lh3 | 2009-08-18 06:23:00 -0400 (Tue, 18 Aug 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/simple_dp.c

 * bwa-0.4.9-33 (r1191)
 * fixed a bug in sampe/samse when gaps occur to the 5'-end in SW alignment
 * in dbwtsw adjust -T and -c according to -a

------------------------------------------------------------------------
r1192 | lh3 | 2009-08-13 05:37:28 -0400 (Thu, 13 Aug 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update manual

------------------------------------------------------------------------
r1191 | lh3 | 2009-08-12 19:40:51 -0400 (Wed, 12 Aug 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwtsw2_main.c

update documentation

------------------------------------------------------------------------
r1190 | lh3 | 2009-08-12 08:56:10 -0400 (Wed, 12 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-32 (r1190)
 * only help messages are changed

------------------------------------------------------------------------
r1189 | lh3 | 2009-08-11 09:28:55 -0400 (Tue, 11 Aug 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-31 (r1189)
 * in bwape/bwase, print CIGAR "*" if the read is unmapped
 * improved the calculation of mapping quality

------------------------------------------------------------------------
r1181 | lh3 | 2009-08-03 12:09:41 -0400 (Mon, 03 Aug 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c

fflush()

------------------------------------------------------------------------
r1180 | lh3 | 2009-08-03 12:08:46 -0400 (Mon, 03 Aug 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-30 (r1180)
 * fixed a memory problem
 * multi-threading sometimes does not work...

------------------------------------------------------------------------
r1179 | lh3 | 2009-08-03 11:04:39 -0400 (Mon, 03 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-29 (r1179)
 * preliminary mutli-threading support in dbwtsw

------------------------------------------------------------------------
r1178 | lh3 | 2009-08-03 09:14:54 -0400 (Mon, 03 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-28 (r1178)
 * fixed a bug in printing repetitive hits

------------------------------------------------------------------------
r1177 | lh3 | 2009-08-03 05:03:42 -0400 (Mon, 03 Aug 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-27 (r1177)
 * bwtsw2: fixed a hidden memory leak

------------------------------------------------------------------------
r1176 | lh3 | 2009-07-31 10:58:24 -0400 (Fri, 31 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-26
 * change the way mapping quality is calculated

------------------------------------------------------------------------
r1175 | lh3 | 2009-07-31 09:15:54 -0400 (Fri, 31 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-25
 * code clean up
 * automatically adjust ->t and ->is_rev based on input

------------------------------------------------------------------------
r1174 | lh3 | 2009-07-30 08:50:25 -0400 (Thu, 30 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-24
 * fixed a bug in printing the hits

------------------------------------------------------------------------
r1173 | lh3 | 2009-07-29 18:32:43 -0400 (Wed, 29 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-23
 * allow to skip reverse alignment
 * increase opt->t to 37

------------------------------------------------------------------------
r1172 | lh3 | 2009-07-29 17:22:39 -0400 (Wed, 29 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-22
 * report if the hit is found in both directions

------------------------------------------------------------------------
r1171 | lh3 | 2009-07-29 17:12:02 -0400 (Wed, 29 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-21
 * dbwtsw: map to both forward and reverse BWT to reduce false alignment

------------------------------------------------------------------------
r1170 | lh3 | 2009-07-29 15:25:14 -0400 (Wed, 29 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

save hits before cut_tail()

------------------------------------------------------------------------
r1169 | lh3 | 2009-07-29 08:06:01 -0400 (Wed, 29 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.4.9-19
 * use a global memory pool to reduce the CPU time spent on malloc/free().

------------------------------------------------------------------------
r1168 | lh3 | 2009-07-29 06:13:29 -0400 (Wed, 29 Jul 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-18
 * reduce unnecessary extension to the 5'-end
 * allow to use different interval size for the 2 rounds
 * change default parameters

------------------------------------------------------------------------
r1167 | lh3 | 2009-07-28 19:06:17 -0400 (Tue, 28 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-17
 * dbwtsw: fixed THE memory leak.

------------------------------------------------------------------------
r1166 | lh3 | 2009-07-28 16:31:41 -0400 (Tue, 28 Jul 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c

 * bwa-0.4.9-16
 * fixed a memory leak
 * a small memory leak still occurs to bwtsw2_core(). I will work on that later.
 * changed the default parameters

------------------------------------------------------------------------
r1165 | lh3 | 2009-07-28 10:15:40 -0400 (Tue, 28 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c

 * bwa-0.4.9-15
 * generate CIGAR right before output. This saves unnecessary computation.
 * this version may be buggy as I have not tested it.

------------------------------------------------------------------------
r1164 | lh3 | 2009-07-28 09:04:14 -0400 (Tue, 28 Jul 2009) | 11 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.4.9-14

 * deplete unique hits in dbwtsw and postprocess them with standard sw

 * in principle, this stratgy should be faster and more accurate, but I
   have not tested this point. I may switch back to the old method if
   this does not work.

 * the code looks quite nasty now. it needs clean up...


------------------------------------------------------------------------
r1163 | lh3 | 2009-07-27 17:41:10 -0400 (Mon, 27 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c

change a default parameter

------------------------------------------------------------------------
r1162 | lh3 | 2009-07-27 17:04:35 -0400 (Mon, 27 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-13
 * dbwtsw: switch between small and large Z-best

------------------------------------------------------------------------
r1161 | lh3 | 2009-07-27 12:17:41 -0400 (Mon, 27 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-12
 * changed the default -z to 100
 * heuristically speed up alignments for polyA reads

------------------------------------------------------------------------
r1160 | lh3 | 2009-07-27 07:50:57 -0400 (Mon, 27 Jul 2009) | 6 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-11

 * dbwtsw potentially generates less false alignments, although in
   practice, the modification brings no improvement.


------------------------------------------------------------------------
r1159 | lh3 | 2009-07-27 04:37:02 -0400 (Mon, 27 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-10
 * disabled debugging code
 * add "BAM_FMU" if both ends are unmapped

------------------------------------------------------------------------
r1158 | lh3 | 2009-07-24 09:36:52 -0400 (Fri, 24 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/main.c

nothing, really

------------------------------------------------------------------------
r1157 | lh3 | 2009-07-24 09:05:44 -0400 (Fri, 24 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-9
 * bwtsw2: generate SAM output

------------------------------------------------------------------------
r1156 | lh3 | 2009-07-24 05:42:47 -0400 (Fri, 24 Jul 2009) | 6 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-8

 * fixed a weird deadloop which only happens to icc -O3. Thanks John
   Marshall for the fix.


------------------------------------------------------------------------
r1155 | lh3 | 2009-07-24 05:28:40 -0400 (Fri, 24 Jul 2009) | 8 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-7

 * fixed a typo in bwtsw2 alignment. Now score from the standard SW
   seems to agree with score from bwtsw2, except that in reporting
   alignments, bwtsw2 may report non-optimal segments. This is expected,
   though. I will improve in future.


------------------------------------------------------------------------
r1154 | lh3 | 2009-07-23 17:40:20 -0400 (Thu, 23 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * aln_left_core() seems to work properly
 * aln_local_core() has a bug... AN EVER EXISTING BUG!!!!!!!!!!!

------------------------------------------------------------------------
r1153 | lh3 | 2009-07-23 17:06:09 -0400 (Thu, 23 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/stdaln.c

removed debugging code...

------------------------------------------------------------------------
r1152 | lh3 | 2009-07-23 17:01:00 -0400 (Thu, 23 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/stdaln.c

 * radical changes failed...
 * fixed a bug

------------------------------------------------------------------------
r1151 | lh3 | 2009-07-23 14:46:35 -0400 (Thu, 23 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/stdaln.c

temporary changes. Will apply some radical changes to this file...

------------------------------------------------------------------------
r1150 | lh3 | 2009-07-23 10:09:56 -0400 (Thu, 23 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/stdaln.c

fixed a long-existing bug in Smith-Waterman alignment

------------------------------------------------------------------------
r1149 | lh3 | 2009-07-23 08:50:52 -0400 (Thu, 23 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/simple_dp.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.4.9-6
 * unexplained inconsistency still occurs, but the results largely look reasonable.

------------------------------------------------------------------------
r1148 | lh3 | 2009-07-23 08:07:29 -0400 (Thu, 23 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/stdaln.c

half DP

------------------------------------------------------------------------
r1147 | lh3 | 2009-07-22 08:03:06 -0400 (Wed, 22 Jul 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c

a bit code clean up

------------------------------------------------------------------------
r1145 | lh3 | 2009-07-21 15:52:05 -0400 (Tue, 21 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-5
 * fixed a bug in determining sub-optimal hits
 * removed some debugging codes

------------------------------------------------------------------------
r1144 | lh3 | 2009-07-21 10:17:29 -0400 (Tue, 21 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-4
 * better cmd interface
 * faster speed

------------------------------------------------------------------------
r1143 | lh3 | 2009-07-20 16:38:18 -0400 (Mon, 20 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

bwtsw2 (dBWT-SW) is working apparently...


------------------------------------------------------------------------
r1139 | lh3 | 2009-07-15 05:52:18 -0400 (Wed, 15 Jul 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.9-2
 * bwtsw2: change cut_tail() such that it is faster but more likely to
   miss true hits

------------------------------------------------------------------------
r1138 | lh3 | 2009-07-15 05:18:42 -0400 (Wed, 15 Jul 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   A /branches/prog/bwa/bwt_lite.c
   A /branches/prog/bwa/bwt_lite.h
   A /branches/prog/bwa/bwtsw2.h
   A /branches/prog/bwa/bwtsw2_aux.c
   A /branches/prog/bwa/bwtsw2_core.c
   A /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * bwa-0.4.9-1
 * added back bwtsw2

------------------------------------------------------------------------
r1075 | lh3 | 2009-05-19 05:14:50 -0400 (Tue, 19 May 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

Release bwa-0.4.9

------------------------------------------------------------------------
r1073 | lh3 | 2009-05-18 17:13:19 -0400 (Mon, 18 May 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.4.8

------------------------------------------------------------------------
r1069 | lh3 | 2009-05-14 09:54:54 -0400 (Thu, 14 May 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.7-2
 * change the default of "aln -R" to 30

------------------------------------------------------------------------
r1068 | lh3 | 2009-05-14 09:27:55 -0400 (Thu, 14 May 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.7-1
 * search for suboptimal hits if the top hit is not so repetitive

------------------------------------------------------------------------
r1066 | lh3 | 2009-05-12 15:31:31 -0400 (Tue, 12 May 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

Release bwa-0.4.7

------------------------------------------------------------------------
r1065 | lh3 | 2009-05-12 15:20:40 -0400 (Tue, 12 May 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.6-9
 * fixed compiling errors on some Linux machines

------------------------------------------------------------------------
r1064 | lh3 | 2009-05-12 07:30:46 -0400 (Tue, 12 May 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.6-8
 * avoid compilation error on some systems.

------------------------------------------------------------------------
r1035 | lh3 | 2009-05-09 05:41:33 -0400 (Sat, 09 May 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.6-7
 * fixed an integer overflow caused by previous modifications
 * made insert size estimation more robust

------------------------------------------------------------------------
r1008 | lh3 | 2009-04-29 05:41:58 -0400 (Wed, 29 Apr 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.6-5
 * fixed a integer overflow problem which may cause seg fault in very rare cases
 * made XN tags more accurate

------------------------------------------------------------------------
r1005 | lh3 | 2009-04-27 07:37:23 -0400 (Mon, 27 Apr 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/simple_dp.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.4.6-4
 * heuristic rules to detect suboptimal alignment
 * stdsw: support double-strand and protein alignment

------------------------------------------------------------------------
r1003 | lh3 | 2009-04-26 12:48:19 -0400 (Sun, 26 Apr 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/simple_dp.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.4.6-2
 * improve the functionality of stdsw
 * allow to add a threshold on SW alignment. Hope this does not incur new bugs...

------------------------------------------------------------------------
r1002 | lh3 | 2009-04-22 03:56:15 -0400 (Wed, 22 Apr 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.4.6-1
 * output SM and AM tag

------------------------------------------------------------------------
r914 | lh3 | 2009-03-09 17:53:50 -0400 (Mon, 09 Mar 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.4.6

------------------------------------------------------------------------
r913 | lh3 | 2009-03-09 17:23:24 -0400 (Mon, 09 Mar 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   A /branches/prog/bwa/solid2fastq.pl

 * added notes to bwa
 * added a script to convert SOLiD reads
 * updated documentations

------------------------------------------------------------------------
r912 | lh3 | 2009-03-09 16:57:05 -0400 (Mon, 09 Mar 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/kstring.c
   M /branches/prog/bwa/main.c

fixed a bug in kstring

------------------------------------------------------------------------
r881 | lh3 | 2009-03-02 15:36:06 -0500 (Mon, 02 Mar 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtmisc.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.5-7
 * fixed a bug in pac2cspac

------------------------------------------------------------------------
r880 | lh3 | 2009-03-01 16:34:08 -0500 (Sun, 01 Mar 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile

disable debugging

------------------------------------------------------------------------
r879 | lh3 | 2009-03-01 16:28:04 -0500 (Sun, 01 Mar 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/cs2nt.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.5-6
 * fixed problems with coordinates for color gapped alignment

------------------------------------------------------------------------
r878 | lh3 | 2009-03-01 13:43:09 -0500 (Sun, 01 Mar 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/cs2nt.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.5-5
 * added support for gapped color alignment

------------------------------------------------------------------------
r877 | lh3 | 2009-03-01 10:27:52 -0500 (Sun, 01 Mar 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/cs2nt.c
   M /branches/prog/bwa/main.c

 * convert cs read to nt read (for ungapped alignment only)

------------------------------------------------------------------------
r860 | lh3 | 2009-02-27 08:58:39 -0500 (Fri, 27 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwase.c
   A /branches/prog/bwa/cs2nt.c

prepare to implement cs->nt conversion (have not yet...)

------------------------------------------------------------------------
r859 | lh3 | 2009-02-27 07:00:03 -0500 (Fri, 27 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bntseq.h
   M /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/bwtmisc.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * bwa-0.4.5-3
 * generate color index from nucleotide fasta reference

------------------------------------------------------------------------
r857 | lh3 | 2009-02-26 10:22:58 -0500 (Thu, 26 Feb 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.5-2
 * improved mapping quality a bit if one end falls in a tandem repeat
   but the mate is unique.

------------------------------------------------------------------------
r856 | lh3 | 2009-02-26 10:02:29 -0500 (Thu, 26 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.4.5-1
 * make bwa work for SOLiD reads

------------------------------------------------------------------------
r828 | lh3 | 2009-02-18 17:36:41 -0500 (Wed, 18 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.4.5

------------------------------------------------------------------------
r827 | lh3 | 2009-02-18 16:48:48 -0500 (Wed, 18 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.4.4-6
 * fixed a bug in SW alignment when no residue matches

------------------------------------------------------------------------
r824 | lh3 | 2009-02-17 05:33:07 -0500 (Tue, 17 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.4-5
 * fixed that bounary bug

------------------------------------------------------------------------
r823 | lh3 | 2009-02-17 04:54:18 -0500 (Tue, 17 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwape.c

just change some logging information

------------------------------------------------------------------------
r822 | lh3 | 2009-02-17 04:20:39 -0500 (Tue, 17 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update manual

------------------------------------------------------------------------
r821 | lh3 | 2009-02-17 04:11:14 -0500 (Tue, 17 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.4-4
 * fixed a bug on boundary check in pair_sw

------------------------------------------------------------------------
r820 | lh3 | 2009-02-16 17:43:37 -0500 (Mon, 16 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.4-3
 * allow to change mismatch penalty

------------------------------------------------------------------------
r819 | lh3 | 2009-02-16 17:40:28 -0500 (Mon, 16 Feb 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.4-2
 * remove timer
 * allow to change default gapo and gape penalty at the command line

------------------------------------------------------------------------
r818 | lh3 | 2009-02-16 09:30:51 -0500 (Mon, 16 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update benchmark

------------------------------------------------------------------------
r817 | lh3 | 2009-02-16 08:44:40 -0500 (Mon, 16 Feb 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/kvec.h
   M /branches/prog/bwa/main.c

 * bwa-0.4.4-1
 * automatically detect insert size
 * use insert size in pairing. This may potentially improve accuracy (untested!)

------------------------------------------------------------------------
r814 | lh3 | 2009-02-15 11:10:23 -0500 (Sun, 15 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.4.4

------------------------------------------------------------------------
r813 | lh3 | 2009-02-15 10:22:50 -0500 (Sun, 15 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.3-5
 * impose boundary check in refine_gapped

------------------------------------------------------------------------
r811 | lh3 | 2009-02-14 09:46:13 -0500 (Sat, 14 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.3-4
 * change MD tag to match the latest SAM specification

------------------------------------------------------------------------
r810 | lh3 | 2009-02-13 04:46:04 -0500 (Fri, 13 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog

update ChangeLog

------------------------------------------------------------------------
r799 | lh3 | 2009-02-05 12:01:17 -0500 (Thu, 05 Feb 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

change MD tag to meet the latest SAM specification

------------------------------------------------------------------------
r796 | lh3 | 2009-02-05 08:35:13 -0500 (Thu, 05 Feb 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.3-2
 * fixed a bug on counting 'N'

------------------------------------------------------------------------
r795 | lh3 | 2009-02-05 07:41:27 -0500 (Thu, 05 Feb 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.3-1
 * fixed potential boundary problems
 * update benchmark result

------------------------------------------------------------------------
r791 | lh3 | 2009-01-25 05:20:47 -0500 (Sun, 25 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update some numbers

------------------------------------------------------------------------
r790 | lh3 | 2009-01-24 15:13:03 -0500 (Sat, 24 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update benchmark

------------------------------------------------------------------------
r789 | lh3 | 2009-01-22 10:18:44 -0500 (Thu, 22 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtindex.c

a warning message for index

------------------------------------------------------------------------
r788 | lh3 | 2009-01-22 09:54:06 -0500 (Thu, 22 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/main.c

forget to change release number

------------------------------------------------------------------------
r786 | lh3 | 2009-01-22 06:27:39 -0500 (Thu, 22 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS

Release bwa-0.4.3

------------------------------------------------------------------------
r785 | lh3 | 2009-01-22 06:27:16 -0500 (Thu, 22 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS

Release bwa-0.4.3

------------------------------------------------------------------------
r784 | lh3 | 2009-01-22 06:19:59 -0500 (Thu, 22 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-10
 * update documentation
 * fixed a bug on generating MD tags for SW alignment

------------------------------------------------------------------------
r782 | lh3 | 2009-01-19 12:08:38 -0500 (Mon, 19 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-9
 * fixed a bug in samse -n...

------------------------------------------------------------------------
r781 | lh3 | 2009-01-19 11:26:37 -0500 (Mon, 19 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-8
 * given -N, the previous version would stop if the top hit is a repeat. Now changed.

------------------------------------------------------------------------
r780 | lh3 | 2009-01-19 11:20:18 -0500 (Mon, 19 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-7
 * use a bit-wise flag to replace some member variables in the option struct
 * allow to switch off the iterative strategy

------------------------------------------------------------------------
r779 | lh3 | 2009-01-19 10:45:57 -0500 (Mon, 19 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-6
 * allow to dump multiple hits from samse, in another format, though

------------------------------------------------------------------------
r778 | lh3 | 2009-01-19 06:24:29 -0500 (Mon, 19 Jan 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/kseq.h
   A /branches/prog/bwa/kstring.c
   A /branches/prog/bwa/kstring.h
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/simple_dp.c

 * bwa-0.4.2-5
 * update kseq.h to the latest version
 * generate MD tag
 * print mate coordinate if only one end is unmapped

------------------------------------------------------------------------
r775 | lh3 | 2009-01-18 05:40:35 -0500 (Sun, 18 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-4
 * fixed a bug for SAM format

------------------------------------------------------------------------
r774 | lh3 | 2009-01-17 13:48:52 -0500 (Sat, 17 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-3
 * change default fnr to 0.04
 * print max_diff for valid fnr

------------------------------------------------------------------------
r773 | lh3 | 2009-01-17 05:54:37 -0500 (Sat, 17 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-2
 * automatically choose max_diff

------------------------------------------------------------------------
r772 | lh3 | 2009-01-16 18:16:14 -0500 (Fri, 16 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.4.2-1
 * take N as a mismatch

------------------------------------------------------------------------
r768 | lh3 | 2009-01-09 11:57:23 -0500 (Fri, 09 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/main.c

Release bwa-0.4.2

------------------------------------------------------------------------
r759 | lh3 | 2009-01-07 09:55:43 -0500 (Wed, 07 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

Release bwa-0.4.1

------------------------------------------------------------------------
r758 | lh3 | 2009-01-07 05:36:06 -0500 (Wed, 07 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.4.0-2
 * make mate_sw fully working

------------------------------------------------------------------------
r757 | lh3 | 2009-01-06 18:04:29 -0500 (Tue, 06 Jan 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.4.0-1
 * do SW alignment for unmapped mate. It is working.
 * I still need to do some extra work for SW alignment, but it is too late
   and I am getting tired... I will do tomorrow.

------------------------------------------------------------------------
r755 | lh3 | 2009-01-06 10:23:29 -0500 (Tue, 06 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.4.0

------------------------------------------------------------------------
r754 | lh3 | 2009-01-06 07:45:02 -0500 (Tue, 06 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtgap.h
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-12
 * better lock

------------------------------------------------------------------------
r753 | lh3 | 2009-01-06 06:17:21 -0500 (Tue, 06 Jan 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-11
 * fixed a small memory leak in bwa_seq_close()
 * fixed "uninitialized memory" from bwt_aln1_t
 * multithreading for "aln" command

------------------------------------------------------------------------
r752 | lh3 | 2009-01-05 17:34:13 -0500 (Mon, 05 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   D /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwt_gen/bwt_gen.c
   A /branches/prog/bwa/bwtmisc.c (from /branches/prog/bwa/pac2bwt.c:748)
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   D /branches/prog/bwa/pac2bwt.c

 * bwa-0.3.0-10
 * a little bit code clean up

------------------------------------------------------------------------
r751 | lh3 | 2009-01-05 17:19:04 -0500 (Mon, 05 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-9
 * use 64-bit integer to speed up Occ calculate, although just a little bit

------------------------------------------------------------------------
r750 | lh3 | 2009-01-05 16:44:26 -0500 (Mon, 05 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-8
 * a little bit code cleanup

------------------------------------------------------------------------
r749 | lh3 | 2009-01-05 16:37:28 -0500 (Mon, 05 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-7
 * accelerate Occ calculation

------------------------------------------------------------------------
r748 | lh3 | 2009-01-05 16:12:28 -0500 (Mon, 05 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   M /branches/prog/bwa/pac2bwt.c

 * bwa-0.3.0-6
 * put occ table along with bwt to save another cache miss
 * this version is already faster than the previous and I can still improve it...

------------------------------------------------------------------------
r747 | lh3 | 2009-01-05 10:16:18 -0500 (Mon, 05 Jan 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-5
 * remove occ_major to save a cache miss; however, OCC_INTERVAL has to be
   increased to keep the same memory. As a result, the speed is a little
   slower in fact.

------------------------------------------------------------------------
r746 | lh3 | 2009-01-05 09:50:53 -0500 (Mon, 05 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-4
 * added back optimization codes (it is a pain...)

------------------------------------------------------------------------
r745 | lh3 | 2009-01-05 08:23:00 -0500 (Mon, 05 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-3
 * faster bit operations

------------------------------------------------------------------------
r744 | lh3 | 2009-01-05 05:58:46 -0500 (Mon, 05 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-2
 * removed optimization codes again...
 * use a new method to count the bits

------------------------------------------------------------------------
r743 | lh3 | 2009-01-04 17:18:38 -0500 (Sun, 04 Jan 2009) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.3.0-1
 * added back the optimization codes
 * added a new option to aln: max_entries, although this is disabled by default
 * updated benchmark

------------------------------------------------------------------------
r742 | lh3 | 2009-01-04 07:56:12 -0500 (Sun, 04 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

add URL

------------------------------------------------------------------------
r740 | lh3 | 2009-01-04 07:39:43 -0500 (Sun, 04 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.3.0

------------------------------------------------------------------------
r739 | lh3 | 2009-01-04 06:55:06 -0500 (Sun, 04 Jan 2009) | 2 lines
Changed paths:
   A /branches/prog/bwa/COPYING
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bntseq.h
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/utils.c
   M /branches/prog/bwa/utils.h

added licensing information

------------------------------------------------------------------------
r738 | lh3 | 2009-01-04 06:18:25 -0500 (Sun, 04 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-31
 * better mapping quality
 * update benchmark

------------------------------------------------------------------------
r737 | lh3 | 2009-01-03 16:00:58 -0500 (Sat, 03 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwa.1

update documentation

------------------------------------------------------------------------
r736 | lh3 | 2009-01-02 10:26:38 -0500 (Fri, 02 Jan 2009) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update documentation

------------------------------------------------------------------------
r735 | lh3 | 2009-01-02 07:10:20 -0500 (Fri, 02 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-30
 * reduce memory a little bit
 * update documentation

------------------------------------------------------------------------
r734 | lh3 | 2009-01-01 13:45:45 -0500 (Thu, 01 Jan 2009) | 8 lines
Changed paths:
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-29
 * sampe: removed -O option; changed default -o to 100000
 * sampe: fixed a bug in calculating paired mapping quality
 * aln: added an option to search for suboptimal hits even if the best is a repeat.
   This option will make sampe MUCH SLOWER.
 * sampe: set isize as zero if mapped to two different chr
 * update manual (unfinished)

------------------------------------------------------------------------
r733 | lh3 | 2009-01-01 11:01:20 -0500 (Thu, 01 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-28
 * fixed a bug in calculating paired mapping quality

------------------------------------------------------------------------
r732 | lh3 | 2009-01-01 09:27:46 -0500 (Thu, 01 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   A /branches/prog/bwa/khash.h (from /branches/prog/sclib/khash/khash.h:675)
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-27
 * accelerate sampe by storing visited large intervals

------------------------------------------------------------------------
r731 | lh3 | 2009-01-01 06:51:21 -0500 (Thu, 01 Jan 2009) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-26
 * remove the optimation codes

------------------------------------------------------------------------
r730 | lh3 | 2009-01-01 06:48:59 -0500 (Thu, 01 Jan 2009) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-25
 * accelerate OCC calculation by ~7%. However, it seems not worth doing
   this by complicate the codes. I will change back later.

------------------------------------------------------------------------
r729 | lh3 | 2008-12-31 16:43:56 -0500 (Wed, 31 Dec 2008) | 6 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-24
 * change command "sai2sam_pe" to "sampe"
 * print usage for sampe command
 * in sampe: change default max_occ to 1000
 * fixed a few compiling warnings in bntseq.c

------------------------------------------------------------------------
r728 | lh3 | 2008-12-27 07:14:59 -0500 (Sat, 27 Dec 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-22
 * mating information can be printed to SAM

------------------------------------------------------------------------
r727 | lh3 | 2008-12-26 18:10:59 -0500 (Fri, 26 Dec 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-21
 * implement pairing (still UNFINISHED)
 * output all reads even if full of N

------------------------------------------------------------------------
r726 | lh3 | 2008-12-26 13:31:27 -0500 (Fri, 26 Dec 2008) | 5 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   A /branches/prog/bwa/bwape.c
   M /branches/prog/bwa/bwase.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * bwa-0.2.0-20
 * remove "-t" from aln cmd
 * code clean up: move some functions in bwt2fmv.c to other source files
 * added sai2sam_pe cmd: *UNFINISHED*

------------------------------------------------------------------------
r725 | lh3 | 2008-12-26 07:04:11 -0500 (Fri, 26 Dec 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   A /branches/prog/bwa/bwase.c
   A /branches/prog/bwa/bwaseqio.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/kseq.h
   A /branches/prog/bwa/ksort.h (from /branches/prog/sclib/ksort/ksort.h:712)
   A /branches/prog/bwa/kvec.h (from /branches/prog/sclib/kvec/kvec.h:537)
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-19
 * considerable code cleanup; no actual changes

------------------------------------------------------------------------
r724 | lh3 | 2008-12-25 11:32:11 -0500 (Thu, 25 Dec 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-18
 * generate SAM output

------------------------------------------------------------------------
r723 | lh3 | 2008-12-25 10:48:31 -0500 (Thu, 25 Dec 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * bwa-0.2.0-17
 * remove bwtsw2 related codes
 * separate searching for SA interval from generating alignments

------------------------------------------------------------------------
r722 | lh3 | 2008-12-25 08:57:13 -0500 (Thu, 25 Dec 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt2fmv.c
   D /branches/prog/bwa/bwt_lite.c
   D /branches/prog/bwa/bwt_lite.h
   M /branches/prog/bwa/bwtgap.c
   D /branches/prog/bwa/bwtsw2.h
   D /branches/prog/bwa/bwtsw2_aux.c
   D /branches/prog/bwa/bwtsw2_core.c
   D /branches/prog/bwa/bwtsw2_main.c
   D /branches/prog/bwa/khash.h
   D /branches/prog/bwa/ksort.h
   D /branches/prog/bwa/kvec.h
   M /branches/prog/bwa/main.c

 * added interface to "aln -t"
 * remove bwtsw2 related codes

------------------------------------------------------------------------
r666 | lh3 | 2008-11-18 18:34:29 -0500 (Tue, 18 Nov 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-16
 * allow to set max mismatches based on read length, but I do not know
   whether this really works

------------------------------------------------------------------------
r665 | lh3 | 2008-11-18 08:34:03 -0500 (Tue, 18 Nov 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-15
 * fixed a bug in sequence parser.

------------------------------------------------------------------------
r612 | lh3 | 2008-10-28 06:50:53 -0400 (Tue, 28 Oct 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/utils.c

 * bwa-0.2.0-14
 * fixed a bug caused by the change of the FASTA/Q parser

------------------------------------------------------------------------
r611 | lh3 | 2008-10-28 06:24:56 -0400 (Tue, 28 Oct 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bntseq.h
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtsw2_core.c
   A /branches/prog/bwa/kseq.h
   D /branches/prog/bwa/seq.c
   D /branches/prog/bwa/seq.h
   M /branches/prog/bwa/simple_dp.c
   M /branches/prog/bwa/utils.c
   M /branches/prog/bwa/utils.h

replace seq.* with kseq.h

------------------------------------------------------------------------
r610 | lh3 | 2008-10-27 13:00:04 -0400 (Mon, 27 Oct 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-13
 * make bwtsw2 output sub-optimal hits. not completed

------------------------------------------------------------------------
r609 | lh3 | 2008-10-24 16:52:00 -0400 (Fri, 24 Oct 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/kvec.h

little...

------------------------------------------------------------------------
r532 | lh3 | 2008-09-19 05:28:45 -0400 (Fri, 19 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/khash.h

improve interface of khash

------------------------------------------------------------------------
r531 | lh3 | 2008-09-18 06:52:59 -0400 (Thu, 18 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

improve minor things, which make bwtsw2 slower, but should miss less true hits

------------------------------------------------------------------------
r530 | lh3 | 2008-09-17 18:19:26 -0400 (Wed, 17 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

 * fixed a bug in calculating ->D
 * enforce band-width checking

------------------------------------------------------------------------
r529 | lh3 | 2008-09-17 18:06:49 -0400 (Wed, 17 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

delete a line of code that is never visited

------------------------------------------------------------------------
r528 | lh3 | 2008-09-17 17:58:51 -0400 (Wed, 17 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

a bit code clean up

------------------------------------------------------------------------
r527 | lh3 | 2008-09-17 10:55:45 -0400 (Wed, 17 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-12
 * max-depth can be set, although it does not help the speed at all

------------------------------------------------------------------------
r526 | lh3 | 2008-09-16 17:59:36 -0400 (Tue, 16 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

cut_tail after remove duplicate

------------------------------------------------------------------------
r525 | lh3 | 2008-09-16 17:56:11 -0400 (Tue, 16 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/khash.h
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-11
 * improved cut_tail()

------------------------------------------------------------------------
r524 | lh3 | 2008-09-15 16:53:22 -0400 (Mon, 15 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-10
 * fixed a bug in cut_tail()

------------------------------------------------------------------------
r518 | lh3 | 2008-09-15 04:35:59 -0400 (Mon, 15 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

a bit code clean up

------------------------------------------------------------------------
r517 | lh3 | 2008-09-14 18:18:11 -0400 (Sun, 14 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

improve speed (<1%)

------------------------------------------------------------------------
r516 | lh3 | 2008-09-14 18:08:55 -0400 (Sun, 14 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

 * fixed two potential bugs, although I have not seen their effects
 * improve speed a bit (<2%)

------------------------------------------------------------------------
r515 | lh3 | 2008-09-14 17:26:49 -0400 (Sun, 14 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c

nothing, really

------------------------------------------------------------------------
r514 | lh3 | 2008-09-14 17:10:13 -0400 (Sun, 14 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

disable X-drop, which has to be reimplemented in the current algorithm

------------------------------------------------------------------------
r513 | lh3 | 2008-09-14 16:49:42 -0400 (Sun, 14 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt_lite.c
   M /branches/prog/bwa/bwt_lite.h
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c

 * temporarily disable cut_tail()
 * calculate SA in bwt_lite.c
 * fixed a bug in reversing the sequence

------------------------------------------------------------------------
r512 | lh3 | 2008-09-13 17:35:40 -0400 (Sat, 13 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   A /branches/prog/bwa/ksort.h

n-best method

------------------------------------------------------------------------
r507 | lh3 | 2008-09-13 09:06:54 -0400 (Sat, 13 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2_core.c

give correct result again

------------------------------------------------------------------------
r506 | lh3 | 2008-09-13 08:12:07 -0400 (Sat, 13 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

I think I know the reason. It needs more work...

------------------------------------------------------------------------
r505 | lh3 | 2008-09-13 06:20:43 -0400 (Sat, 13 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2_core.c

fixed another bug, but still have

------------------------------------------------------------------------
r504 | lh3 | 2008-09-12 18:13:37 -0400 (Fri, 12 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

fixed another bug

------------------------------------------------------------------------
r503 | lh3 | 2008-09-12 17:15:56 -0400 (Fri, 12 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/khash.h

 * do not segfault, but the result is WRONG!
 * prepare to remove bsw2_connectivity_check()

------------------------------------------------------------------------
r502 | lh3 | 2008-09-12 15:52:41 -0400 (Fri, 12 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/kvec.h

more revisions

------------------------------------------------------------------------
r501 | lh3 | 2008-09-11 18:06:15 -0400 (Thu, 11 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

further simply codes with kvec.h

------------------------------------------------------------------------
r500 | lh3 | 2008-09-11 17:42:15 -0400 (Thu, 11 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

part of revisions... have not finished

------------------------------------------------------------------------
r499 | lh3 | 2008-09-11 17:24:15 -0400 (Thu, 11 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/khash.h
   A /branches/prog/bwa/kvec.h

prepare for abrupt change

------------------------------------------------------------------------
r496 | lh3 | 2008-09-11 10:34:38 -0400 (Thu, 11 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

fixed a bug; now "bwtsw2 -d" is useless

------------------------------------------------------------------------
r495 | lh3 | 2008-09-11 09:22:03 -0400 (Thu, 11 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/simple_dp.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

improve speed a little bit

------------------------------------------------------------------------
r494 | lh3 | 2008-09-11 08:28:08 -0400 (Thu, 11 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

remove debug codes

------------------------------------------------------------------------
r493 | lh3 | 2008-09-11 07:49:53 -0400 (Thu, 11 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

 * improve the speed a little bit (<5%)
 * prepare to remove BSW_DEBUG

------------------------------------------------------------------------
r492 | lh3 | 2008-09-11 06:15:56 -0400 (Thu, 11 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-9
 * support reverse strand
 * fixed a bug that causes missing hits

------------------------------------------------------------------------
r491 | lh3 | 2008-09-11 05:46:16 -0400 (Thu, 11 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-8
 * better progress report

------------------------------------------------------------------------
r490 | lh3 | 2008-09-10 17:04:49 -0400 (Wed, 10 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-7
 * avoid some missing hits
 * add maximum depth

------------------------------------------------------------------------
r489 | lh3 | 2008-09-10 11:51:13 -0400 (Wed, 10 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-6
 * bwtsw2 works although on the forward strand only for now
 * better progress information

------------------------------------------------------------------------
r488 | lh3 | 2008-09-10 10:21:53 -0400 (Wed, 10 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

 * implement memory pool
 * avoid some rehashing

------------------------------------------------------------------------
r487 | lh3 | 2008-09-10 09:23:38 -0400 (Wed, 10 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_main.c

 * fixed a memory leak
 * prepare to implement mempool

------------------------------------------------------------------------
r486 | lh3 | 2008-09-10 09:10:09 -0400 (Wed, 10 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/khash.h

 * add X-dropoff
 * remove duplicated results
 * switch to simple stack

------------------------------------------------------------------------
r485 | lh3 | 2008-09-10 06:31:20 -0400 (Wed, 10 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c

 * check whether t-node has been visited
 * prepare to remove two-level stack

------------------------------------------------------------------------
r484 | lh3 | 2008-09-10 05:00:57 -0400 (Wed, 10 Sep 2008) | 2 lines
Changed paths:
   A /branches/prog/bwa/khash.h

khash library

------------------------------------------------------------------------
r483 | lh3 | 2008-09-10 04:22:53 -0400 (Wed, 10 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

add inline

------------------------------------------------------------------------
r482 | lh3 | 2008-09-09 16:34:57 -0400 (Tue, 09 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c

improve speed

------------------------------------------------------------------------
r481 | lh3 | 2008-09-09 13:13:00 -0400 (Tue, 09 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2_core.c

Use a 128bit hash table to keep all (tk,tl,qk,ql). This is slow. Just
keep a copy in case I may need this in future.


------------------------------------------------------------------------
r480 | lh3 | 2008-09-09 12:53:32 -0400 (Tue, 09 Sep 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_core.c

 * no principal modification

------------------------------------------------------------------------
r479 | lh3 | 2008-09-09 11:01:45 -0400 (Tue, 09 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2_core.c

 * fixed a bug which may cause duplicated matching
 * accelerate the speed a bit, although using hash in avoiding duplications
   slows the speed down in the end

------------------------------------------------------------------------
r474 | lh3 | 2008-09-03 17:22:57 -0400 (Wed, 03 Sep 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtsw2.h
   M /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-5
 * indel seems to work on toy example
 * add band

------------------------------------------------------------------------
r469 | lh3 | 2008-09-01 09:18:45 -0400 (Mon, 01 Sep 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt_lite.c
   M /branches/prog/bwa/bwt_lite.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtsw2.h
   A /branches/prog/bwa/bwtsw2_aux.c
   M /branches/prog/bwa/bwtsw2_core.c
   M /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/is.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   M /branches/prog/bwa/simple_dp.c

 * bwa-0.2.0-4
 * updated bwtsw2, which seems to work properly on toy examples

------------------------------------------------------------------------
r447 | lh3 | 2008-08-27 10:05:09 -0400 (Wed, 27 Aug 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-3
 * tune for longer gaps, but it does not really work with kilo-bp gaps...

------------------------------------------------------------------------
r446 | lh3 | 2008-08-26 13:30:41 -0400 (Tue, 26 Aug 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-2
 * changed the way to extend long deletions. Now use max_del_occ.

------------------------------------------------------------------------
r445 | lh3 | 2008-08-26 13:05:58 -0400 (Tue, 26 Aug 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt_lite.c
   M /branches/prog/bwa/bwt_lite.h

updated from bwtsw2_lite

------------------------------------------------------------------------
r436 | lh3 | 2008-08-23 12:28:44 -0400 (Sat, 23 Aug 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.h
   A /branches/prog/bwa/bwt_lite.c
   A /branches/prog/bwa/bwt_lite.h
   A /branches/prog/bwa/bwtsw2.h
   A /branches/prog/bwa/bwtsw2_core.c
   A /branches/prog/bwa/bwtsw2_main.c
   M /branches/prog/bwa/main.c

 * bwa-0.2.0-1
 * add bwt_lite: a light-weighted version of bwt (NOT TESTED!)
 * add core codes for bwtsw2: NOT TESTED!!!

------------------------------------------------------------------------
r427 | lh3 | 2008-08-15 05:38:12 -0400 (Fri, 15 Aug 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

Release bwa-0.2.0

------------------------------------------------------------------------
r426 | lh3 | 2008-08-14 11:26:19 -0400 (Thu, 14 Aug 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c

 * bwa-0.1.6-7
 * change default seed length to 31
 * add incomplete support to color sequences (not tested yet!)

------------------------------------------------------------------------
r425 | lh3 | 2008-08-14 06:23:11 -0400 (Thu, 14 Aug 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.6-6
 * change default seed length to 33bp

------------------------------------------------------------------------
r424 | lh3 | 2008-08-14 05:55:33 -0400 (Thu, 14 Aug 2008) | 6 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.6-5
 * fixed a bug that may miss true alignments. this bugs exists in most
   early versions.
 * fixed a bug that yields wrong coordinates for reads mapped on the forward
   strands with gaps.

------------------------------------------------------------------------
r423 | lh3 | 2008-08-14 04:07:28 -0400 (Thu, 14 Aug 2008) | 2 lines
Changed paths:
   D /branches/prog/bwa/Makefile.div

useless

------------------------------------------------------------------------
r422 | lh3 | 2008-08-13 19:21:14 -0400 (Wed, 13 Aug 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.6-4
 * fixed one bug
 * there is another one...

------------------------------------------------------------------------
r421 | lh3 | 2008-08-13 18:23:33 -0400 (Wed, 13 Aug 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtgap.h
   M /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.6-3
 * almost there, but not quite right

------------------------------------------------------------------------
r419 | lh3 | 2008-08-13 17:27:02 -0400 (Wed, 13 Aug 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtgap.h
   M /branches/prog/bwa/main.c

 * improve the seeding method
 * prepare to load two BWTs into memory. A BIG change!

------------------------------------------------------------------------
r418 | lh3 | 2008-08-13 10:56:54 -0400 (Wed, 13 Aug 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtgap.h
   M /branches/prog/bwa/main.c

 * added seeding
 * unfinished yet

------------------------------------------------------------------------
r413 | lh3 | 2008-08-08 11:48:35 -0400 (Fri, 08 Aug 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.1.6

------------------------------------------------------------------------
r410 | lh3 | 2008-08-06 15:48:22 -0400 (Wed, 06 Aug 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/simple_dp.c

sw: output alignment score

------------------------------------------------------------------------
r407 | lh3 | 2008-08-04 10:01:20 -0400 (Mon, 04 Aug 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   A /branches/prog/bwa/simple_dp.c
   M /branches/prog/bwa/stdaln.c
   M /branches/prog/bwa/stdaln.h

 * bwa-0.1.5-3
 * added a simple interface to SW/NW alignment
 * stdaln-0.9.8 (see header for more details)

------------------------------------------------------------------------
r406 | lh3 | 2008-08-01 19:21:59 -0400 (Fri, 01 Aug 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c
   A /branches/prog/bwa/stdaln.c
   A /branches/prog/bwa/stdaln.h

 * bwa-0.1.5-2
 * give accurate gap positions

------------------------------------------------------------------------
r405 | lh3 | 2008-08-01 19:06:19 -0400 (Fri, 01 Aug 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h

unfinished, but I am tired...

------------------------------------------------------------------------
r401 | lh3 | 2008-07-30 05:59:24 -0400 (Wed, 30 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.5-1
 * fixed a potential bug which may produce an alignment in N regions,
   although extremely rare.

------------------------------------------------------------------------
r399 | lh3 | 2008-07-27 11:41:52 -0400 (Sun, 27 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.1.5

------------------------------------------------------------------------
r398 | lh3 | 2008-07-25 12:14:47 -0400 (Fri, 25 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update documentation

------------------------------------------------------------------------
r397 | lh3 | 2008-07-25 09:58:56 -0400 (Fri, 25 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * 

------------------------------------------------------------------------
r396 | lh3 | 2008-07-25 06:42:01 -0400 (Fri, 25 Jul 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.4-4
 * add timer for debugging

------------------------------------------------------------------------
r395 | lh3 | 2008-07-24 05:46:21 -0400 (Thu, 24 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.4-3
 * fixed a bug in the previous code
 * this version gives identical result to bwa-0.1.4, just 10% faster

------------------------------------------------------------------------
r394 | lh3 | 2008-07-24 05:18:53 -0400 (Thu, 24 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtgap.h
   M /branches/prog/bwa/main.c

 * bwa-0.1.4-2
 * further improve the speed
 * The result is slightly different from bwa-0.1.4 now. I need to check...

------------------------------------------------------------------------
r393 | lh3 | 2008-07-23 12:04:16 -0400 (Wed, 23 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt.c

comments only

------------------------------------------------------------------------
r392 | lh3 | 2008-07-23 10:34:03 -0400 (Wed, 23 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

further improve the speed in Occ functions

------------------------------------------------------------------------
r386 | lh3 | 2008-07-22 10:03:54 -0400 (Tue, 22 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/main.c

Release bwa-0.1.4

------------------------------------------------------------------------
r385 | lh3 | 2008-07-22 09:44:50 -0400 (Tue, 22 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwa.1

update documentation and ChangeLog

------------------------------------------------------------------------
r384 | lh3 | 2008-07-22 08:50:03 -0400 (Tue, 22 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.3-2
 * fixed the bug in the last modification
 * now the alignment should be more clearly defined

------------------------------------------------------------------------
r383 | lh3 | 2008-07-21 18:32:21 -0400 (Mon, 21 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.3-1
 * this is a buggy verion!
 * i will fix the bug tomorrow. It is late...

------------------------------------------------------------------------
r381 | lh3 | 2008-07-21 06:45:32 -0400 (Mon, 21 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.1.3

------------------------------------------------------------------------
r380 | lh3 | 2008-07-21 06:07:43 -0400 (Mon, 21 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.2-3
 * improve the speed for gcc on Intel Mac OS X, but not really on icc on Linux
 * aln: more command-line options

------------------------------------------------------------------------
r373 | lh3 | 2008-07-17 09:09:46 -0400 (Thu, 17 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.2-2
 * further improve the speed
 * this version gives exactly the same result as bwa-0.1.2

------------------------------------------------------------------------
r372 | lh3 | 2008-07-17 07:51:08 -0400 (Thu, 17 Jul 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.2-1
 * speed up by about 5%

------------------------------------------------------------------------
r370 | lh3 | 2008-07-17 05:12:00 -0400 (Thu, 17 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.1.2

------------------------------------------------------------------------
r368 | lh3 | 2008-07-16 08:51:25 -0400 (Wed, 16 Jul 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   D /branches/prog/bwa/bwt1away.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtgap.h
   D /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-9
 * some code cleanup
 * remove 1away and top2

------------------------------------------------------------------------
r367 | lh3 | 2008-07-16 08:24:34 -0400 (Wed, 16 Jul 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/is.c

Yuta Mori's implementation of IS algorithm.

------------------------------------------------------------------------
r365 | lh3 | 2008-07-16 06:58:04 -0400 (Wed, 16 Jul 2008) | 6 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwtgap.h
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-8
 * improve gapped alignment
 * this version will miss more gapped alignments, but the speed is much faster
 * prepare to remove top2 and 1away algorithms
 * prepare to add SAIS algorithm for bwt construction

------------------------------------------------------------------------
r358 | lh3 | 2008-06-09 06:03:04 -0400 (Mon, 09 Jun 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-7
 * change END_SKIP from 3 to 5, but still gaps may be wrongly added
 * change default '-g' from 5 to 3

------------------------------------------------------------------------
r357 | lh3 | 2008-06-09 05:18:36 -0400 (Mon, 09 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-6
 * fix a bug in nested stack

------------------------------------------------------------------------
r356 | lh3 | 2008-06-08 18:43:13 -0400 (Sun, 08 Jun 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   A /branches/prog/bwa/bwtgap.h
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-5
 * replace heap with nested stacks
 * there are still obvious bugs...

------------------------------------------------------------------------
r355 | lh3 | 2008-06-08 17:13:44 -0400 (Sun, 08 Jun 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * bwa-0.1.1-4
 * add interface to affine gap alignment
 * there are obvious bugs and I will fix them later

------------------------------------------------------------------------
r354 | lh3 | 2008-06-08 15:39:05 -0400 (Sun, 08 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-3
 * affine gap seems to work, at least partially

------------------------------------------------------------------------
r353 | lh3 | 2008-06-08 09:27:18 -0400 (Sun, 08 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   A /branches/prog/bwa/bwtgap.c
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-2
 * initial gapped alignment. not work at the moment

------------------------------------------------------------------------
r352 | lh3 | 2008-06-06 04:37:34 -0400 (Fri, 06 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.1-1
 * ungap: remove a useless varible in top2_entry_t

------------------------------------------------------------------------
r348 | lh3 | 2008-06-03 09:04:12 -0400 (Tue, 03 Jun 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/ChangeLog
   A /branches/prog/bwa/NEWS
   M /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/main.c

Release bwa-0.1.1

------------------------------------------------------------------------
r347 | lh3 | 2008-06-03 05:45:08 -0400 (Tue, 03 Jun 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwa.1

update documentation

------------------------------------------------------------------------
r346 | lh3 | 2008-06-02 18:59:50 -0400 (Mon, 02 Jun 2008) | 5 lines
Changed paths:
   A /branches/prog/bwa/ChangeLog
   A /branches/prog/bwa/bwa.1
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-11
 * improve approximating mapping qualities
 * add documentation
 * add ChangeLog

------------------------------------------------------------------------
r345 | lh3 | 2008-06-02 16:04:39 -0400 (Mon, 02 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-10
 * output a random position for repetitive reads

------------------------------------------------------------------------
r344 | lh3 | 2008-06-02 15:03:54 -0400 (Mon, 02 Jun 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/pac2bwt.c

 * bwa-0.1.0-9
 * fix memory leaks
 * fix a potential bug in coverting to the real coordinate

------------------------------------------------------------------------
r343 | lh3 | 2008-06-02 13:44:51 -0400 (Mon, 02 Jun 2008) | 5 lines
Changed paths:
   M /branches/prog/bwa/Makefile.div
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-8
 * fix a bug about strand
 * update Makefile.div
 * change top2b as the default method

------------------------------------------------------------------------
r342 | lh3 | 2008-06-02 11:23:26 -0400 (Mon, 02 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt1away.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-7
 * use bwt_2occ() and bwt_2occ4() in other functions

------------------------------------------------------------------------
r341 | lh3 | 2008-06-02 09:31:39 -0400 (Mon, 02 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-6
 * fix a bug for missing hits

------------------------------------------------------------------------
r340 | lh3 | 2008-06-02 09:10:18 -0400 (Mon, 02 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-5
 * accelerate comparisons in heap, a bit

------------------------------------------------------------------------
r339 | lh3 | 2008-06-02 08:41:31 -0400 (Mon, 02 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-4
 * avoid marginal repeated calculation in occ

------------------------------------------------------------------------
r338 | lh3 | 2008-06-02 06:46:51 -0400 (Mon, 02 Jun 2008) | 5 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-3
 * fix a bug caused by previours change
 * fix a bug in heap
 * order the heap by more criteria

------------------------------------------------------------------------
r337 | lh3 | 2008-06-01 19:11:15 -0400 (Sun, 01 Jun 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c

 * bwa-0.1.0-2
 * also sort sa range in heapsort, in attempt to improve cache performance.
   Unfortunately, it does not work well at all.

------------------------------------------------------------------------
r336 | lh3 | 2008-06-01 17:45:23 -0400 (Sun, 01 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/Makefile.div
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/main.c

 * 0.1.0-1
 * fix a bug in calculating the real coordinate

------------------------------------------------------------------------
r335 | lh3 | 2008-06-01 16:03:09 -0400 (Sun, 01 Jun 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile

nothing, really

------------------------------------------------------------------------
r334 | lh3 | 2008-06-01 15:59:13 -0400 (Sun, 01 Jun 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   A /branches/prog/bwa/Makefile.div
   M /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/pac2bwt.c

use IS algorithm by default

------------------------------------------------------------------------
r333 | lh3 | 2008-06-01 15:05:15 -0400 (Sun, 01 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/is.c
   M /branches/prog/bwa/pac2bwt.c

 * a bit code clean up in is.c
 * add IS algorithm for constructing BWT, albeit slower

------------------------------------------------------------------------
r332 | lh3 | 2008-06-01 13:23:08 -0400 (Sun, 01 Jun 2008) | 2 lines
Changed paths:
   A /branches/prog/bwa/is.c

IS linear-time algorithm for constructing SA/BWT

------------------------------------------------------------------------
r331 | lh3 | 2008-06-01 10:35:26 -0400 (Sun, 01 Jun 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bntseq.c
   A /branches/prog/bwa/bwtindex.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * fix a bug in generating .pac
 * index in one go

------------------------------------------------------------------------
r330 | lh3 | 2008-06-01 09:17:05 -0400 (Sun, 01 Jun 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bntseq.h
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwttop2.c

real coordinates can be ouput

------------------------------------------------------------------------
r329 | lh3 | 2008-05-31 19:21:02 -0400 (Sat, 31 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwttop2.c

add top2e which is similar to 1away

------------------------------------------------------------------------
r328 | lh3 | 2008-05-31 18:46:12 -0400 (Sat, 31 May 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwttop2.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * unified cmd-line interface for ungapped alignment
 * add two alternatives to top2 algorithm

------------------------------------------------------------------------
r327 | lh3 | 2008-05-31 18:14:46 -0400 (Sat, 31 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

add cmd-line interface to alntop2

------------------------------------------------------------------------
r326 | lh3 | 2008-05-31 17:59:31 -0400 (Sat, 31 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt1away.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   A /branches/prog/bwa/bwttop2.c

top2 algorithm seems to work. I need to change interface, though

------------------------------------------------------------------------
r325 | lh3 | 2008-05-31 15:11:49 -0400 (Sat, 31 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt1away.c

change the variable in the structure

------------------------------------------------------------------------
r324 | lh3 | 2008-05-31 14:52:13 -0400 (Sat, 31 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt1away.c

set a slightly better bound on the maximum allowed mismatches

------------------------------------------------------------------------
r323 | lh3 | 2008-05-30 18:40:21 -0400 (Fri, 30 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c

 * output time statistics

------------------------------------------------------------------------
r322 | lh3 | 2008-05-30 17:58:25 -0400 (Fri, 30 May 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   A /branches/prog/bwa/bwt1away.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h

 * presumably better way to make use of prefix. But for the moment I do
   not know whether it is correct or not.
 * a bit code clean up: separate alignment part

------------------------------------------------------------------------
r321 | lh3 | 2008-05-30 13:57:43 -0400 (Fri, 30 May 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt_gen/Makefile
   M /branches/prog/bwa/bwt_gen/bwt_gen.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   M /branches/prog/bwa/pac2bwt.c

 * a bit code clean up
 * put bwt_gen in bwa

------------------------------------------------------------------------
r320 | lh3 | 2008-05-30 11:40:11 -0400 (Fri, 30 May 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtio.c

 * improve cmd-line interface
 * fix a bug in loading .sa
 * change default sa interval to 32

------------------------------------------------------------------------
r319 | lh3 | 2008-05-30 10:31:37 -0400 (Fri, 30 May 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwtaln.c

 * fix memory leak (I know that. Just a bit lazy)
 * change to another method to do 1-away alignment

------------------------------------------------------------------------
r318 | lh3 | 2008-05-30 09:21:49 -0400 (Fri, 30 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

best unique match is partially finished

------------------------------------------------------------------------
r317 | lh3 | 2008-05-30 06:33:28 -0400 (Fri, 30 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

remove "ungapped" command and related codes

------------------------------------------------------------------------
r316 | lh3 | 2008-05-30 06:05:20 -0400 (Fri, 30 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h

change variable name thick to width

------------------------------------------------------------------------
r315 | lh3 | 2008-05-29 19:06:13 -0400 (Thu, 29 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   M /branches/prog/bwa/pac2bwt.c

revised algorithm for ungapped alignment. the old one can still be used.

------------------------------------------------------------------------
r314 | lh3 | 2008-05-29 16:36:11 -0400 (Thu, 29 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwt_gen/bwt_gen.c
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/pac2bwt.c

 * make commands more independent, but ungapped does not work at the moment

------------------------------------------------------------------------
r313 | lh3 | 2008-05-29 15:56:14 -0400 (Thu, 29 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt_gen/bwt_gen.c

little...

------------------------------------------------------------------------
r312 | lh3 | 2008-05-29 15:54:01 -0400 (Thu, 29 May 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt_gen/bwt_gen.c
   M /branches/prog/bwa/bwt_gen/bwt_gen.h

 * add CopyRight information from the original codes
 * do not dump .fmv files

------------------------------------------------------------------------
r311 | lh3 | 2008-05-29 15:44:36 -0400 (Thu, 29 May 2008) | 2 lines
Changed paths:
   A /branches/prog/bwa/bwt_gen
   A /branches/prog/bwa/bwt_gen/Makefile
   A /branches/prog/bwa/bwt_gen/QSufSort.c
   A /branches/prog/bwa/bwt_gen/QSufSort.h
   A /branches/prog/bwa/bwt_gen/bwt_gen.c
   A /branches/prog/bwa/bwt_gen/bwt_gen.h

codes from BWT-SW, for building BWT from packed file

------------------------------------------------------------------------
r310 | lh3 | 2008-05-28 17:03:35 -0400 (Wed, 28 May 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * change OCC_INTERVAL to 0x40, which makes bwa twice as fast.
 * write Occ file as ".occ" as it is using a different interval from
   .fmv, the BWT-SW correspondance of .occ

------------------------------------------------------------------------
r309 | lh3 | 2008-05-28 11:39:37 -0400 (Wed, 28 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt2fmv.c

fix a bug

------------------------------------------------------------------------
r308 | lh3 | 2008-05-28 09:56:16 -0400 (Wed, 28 May 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt2fmv.c

add heuristics to improve the speed, but I have not tested whether the
results are correct or not.


------------------------------------------------------------------------
r307 | lh3 | 2008-05-28 06:31:34 -0400 (Wed, 28 May 2008) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/bwtaln.c
   M /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * make ungapped alignment basically works...
 * but it is very slow in comparison to others...
 * also I need to improve the interface...
 * a lot of things to keep me busy today...

------------------------------------------------------------------------
r306 | lh3 | 2008-05-27 18:41:27 -0400 (Tue, 27 May 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwtaln.c

 * remove recursion
 * fixed a bug in bwt_occ()

------------------------------------------------------------------------
r305 | lh3 | 2008-05-27 16:59:44 -0400 (Tue, 27 May 2008) | 5 lines
Changed paths:
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwtaln.c

 * bwa now tells whether a sequenced can be mapped with maximum allowed
   mismatches. ONLY ungapped.
 * this is a recursive version. I will remove recursion later.


------------------------------------------------------------------------
r304 | lh3 | 2008-05-27 09:12:17 -0400 (Tue, 27 May 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c
   A /branches/prog/bwa/bwtaln.c
   A /branches/prog/bwa/bwtaln.h
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   M /branches/prog/bwa/utils.c

 * load .sa and .fmv files
 * exact alignment now works

------------------------------------------------------------------------
r303 | lh3 | 2008-05-27 06:33:38 -0400 (Tue, 27 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/utils.c
   M /branches/prog/bwa/utils.h

add xassert and fix a bug

------------------------------------------------------------------------
r302 | lh3 | 2008-05-27 06:23:20 -0400 (Tue, 27 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwtio.c
   A /branches/prog/bwa/utils.c
   A /branches/prog/bwa/utils.h

improve error message and error handling

------------------------------------------------------------------------
r301 | lh3 | 2008-05-27 05:37:51 -0400 (Tue, 27 May 2008) | 4 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c
   A /branches/prog/bwa/bwtio.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h

 * move I/O codes to bwtio.c
 * SA can be dumped and interestingly, it is identical to BWTSW
 * now, .fmv is still different from BWTSW

------------------------------------------------------------------------
r299 | lh3 | 2008-05-26 18:07:44 -0400 (Mon, 26 May 2008) | 2 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c

generate/retrieve SA and Occ

------------------------------------------------------------------------
r298 | lh3 | 2008-05-26 13:16:49 -0400 (Mon, 26 May 2008) | 3 lines
Changed paths:
   M /branches/prog/bwa/bntseq.h
   M /branches/prog/bwa/bwt.c
   M /branches/prog/bwa/bwt.h
   M /branches/prog/bwa/bwt2fmv.c

 * retrieve occ value at any position
 * move bwt_cal_occ() to bwt.c

------------------------------------------------------------------------
r297 | lh3 | 2008-05-25 17:43:58 -0400 (Sun, 25 May 2008) | 6 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   A /branches/prog/bwa/bwt.c
   A /branches/prog/bwa/bwt.h
   A /branches/prog/bwa/bwt2fmv.c
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   M /branches/prog/bwa/pac2bwt.c

 * add bwt2fmv. It works to some extend. However, I do not understand
   the purpose of some weird codes in BWT-SW. As a consequence, bwt2fmv
   could generate a file almost identical, but not exactly identical, to
   the .fmv file from BWT-SW.


------------------------------------------------------------------------
r296 | lh3 | 2008-05-24 18:35:02 -0400 (Sat, 24 May 2008) | 5 lines
Changed paths:
   M /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bntseq.c
   M /branches/prog/bwa/bntseq.h
   M /branches/prog/bwa/main.c
   M /branches/prog/bwa/main.h
   A /branches/prog/bwa/pac2bwt.c

Burrows-Wheeler Transform now works. At least on one example, the
current code generates the same BWT as BWT-SW. Kind of magical, I would
say. :)


------------------------------------------------------------------------
r295 | lh3 | 2008-05-24 11:25:31 -0400 (Sat, 24 May 2008) | 3 lines
Changed paths:
   A /branches/prog/bwa/Makefile
   M /branches/prog/bwa/bntseq.c
   A /branches/prog/bwa/main.c
   A /branches/prog/bwa/main.h

 * add Makefile and main.*
 * improve interface to fa2bns, a bit

------------------------------------------------------------------------
r293 | lh3 | 2008-05-24 10:57:03 -0400 (Sat, 24 May 2008) | 3 lines
Changed paths:
   A /branches/prog/bwa
   A /branches/prog/bwa/bntseq.c
   A /branches/prog/bwa/bntseq.h
   A /branches/prog/bwa/seq.c
   A /branches/prog/bwa/seq.h

 * Burrow-Wheeler Alignment
 * initial codes

------------------------------------------------------------------------
