# Use Python 3.12 slim image as base
FROM python:3.12-slim

# Set working directory
WORKDIR /app
RUN mkdir -p /workspace && chmod -R 755 /workspace
RUN mkdir -p /data && chmod -R 755 /data

RUN cd ../

# Install system dependencies (for Playwright and Node.js)
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    ca-certificates \
    fonts-liberation \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js v20 (needed for MCP tools)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# Install uv (includes uvx)
RUN pip install uv

# Note: Python/R execution happens inside Docker container via Desktop Commander MCP tool
# Container provides complete bioinformatics environment

# Install miniforge for Python and R bioinformatics environment
RUN curl -L -O "https://github.com/conda-forge/miniforge/releases/latest/download/Miniforge3-$(uname)-$(uname -m).sh" \
    && bash Miniforge3-$(uname)-$(uname -m).sh -b -p /opt/miniforge3 \
    && rm Miniforge3-$(uname)-$(uname -m).sh \
    && /opt/miniforge3/bin/conda clean --all -y

# Copy and create the biomni environment setup
COPY biomni-toolkit/biomni_env/ ./biomni_env/
WORKDIR /app/biomni_env

# Set PATH for conda in Docker build
ENV PATH="/opt/miniforge3/bin:$PATH"

# Run comprehensive multi-stage Biomni E1 setup
RUN bash setup_biomni_e1.sh

# Set PATH for CLI tools (if installed)
RUN if [ -f setup_path.sh ]; then chmod +x setup_path.sh; fi

WORKDIR /app

# Data lake download removed for faster builds

# Copy project files
COPY pyproject.toml README.md ./
COPY src/ ./src/
COPY server.py ./
COPY mcp_servers_docker.json ./

# Install multi-agent system dependencies with uv
RUN uv sync

# Install Playwright browsers
RUN uv run playwright install chromium

# Set environment variables for desktop-commander to use biomni_e1 for execution
ENV PYTHON_EXECUTION_ENV=biomni_e1
ENV PYTHON_EXECUTION_PATH=/opt/miniforge3/envs/biomni_e1/bin/python
ENV CONDA_ENV_PATH=/opt/miniforge3
ENV R_HOME=/opt/miniforge3/envs/biomni_e1/lib/R
ENV R_LIBS_USER=/opt/miniforge3/envs/biomni_e1/lib/R/library
ENV MCP_WORKSPACE_PATH=/workspace

# Expose port 8000
EXPOSE 8000

# Run the server
CMD ["uv", "run", "server.py"]
