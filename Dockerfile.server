###############################################################################
# Use pre-built multiagent_base with all biomni dependencies
###############################################################################
FROM ghcr.io/omiydev/multiagent_base:dev

# Set working directory
WORKDIR /app

# Install uv in system Python (base image uses condaforge/mambaforge)
RUN pip install uv

###############################################################################
# Multi-agent system dependencies
###############################################################################
COPY pyproject.toml uv.lock requirements.txt README.md ./

# Install multi-agent system dependencies with uv
# Note: Playwright already installed in biomni_e1 environment in base image
RUN uv sync

###############################################################################
# Application code
###############################################################################
COPY src/ ./src/
COPY server.py ./
COPY mcp_servers_docker.json ./

RUN mkdir -p /app/workspace

# Note: All environment variables already set in base image:
# PYTHON_EXECUTION_ENV=biomni_e1
# PYTHON_EXECUTION_PATH=/opt/conda/envs/biomni_e1/bin/python
# CONDA_ENV_PATH=/opt/conda
# R_HOME=/opt/conda/envs/biomni_e1/lib/R
# R_LIBS_USER=/opt/conda/envs/biomni_e1/lib/R/library
# MCP_WORKSPACE_PATH=/workspace

###############################################################################
# Runtime
###############################################################################
EXPOSE 8000
CMD ["uv", "run", "server.py"]
